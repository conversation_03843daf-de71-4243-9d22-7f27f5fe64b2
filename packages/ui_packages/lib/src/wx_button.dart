import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_packages/ui_packages.dart';

class WxButton extends StatelessWidget {
  final Widget? child;
  final void Function()? onPressed;
  final BorderSide? borderSide;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;
  final String? text;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final LinearGradient? linearGradient;
  final EdgeInsets? margin;
  final List<BoxShadow>? shadows;

  const WxButton({
    super.key,
    this.child,
    this.height,
    this.text,
    this.textStyle,
    this.onPressed,
    this.borderSide,
    this.borderRadius,
    this.backgroundColor,
    this.width,
    this.linearGradient,
    this.margin,
    this.shadows,
  });

  @override
  Widget build(BuildContext context) {
    BorderSide side = borderSide ?? BorderSide.none;
    return GestureDetector(
      onTap: () {
        if (onPressed != null) {
          onPressed!();
        }
      },
      child: Container(
        width: width ?? double.infinity,
        height: height ?? 44.w,
        margin: margin,
        alignment: Alignment.center,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          gradient: linearGradient,
          color: linearGradient == null
              ? (backgroundColor ?? Colours.app_main)
              : null,
          borderRadius: borderRadius ?? BorderRadius.circular(6.w),
          border: Border(left: side, right: side, top: side, bottom: side),
          boxShadow: shadows,
          // gradient:
        ),
        child: child == null
            ? Text(
                text ?? "",
                textAlign: TextAlign.center,
                textScaleFactor: 1.0,
                style: textStyle ??
                    TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: AppFontWeight.semiBold()),
              )
            : child!,
      ),
    );
  }
}
