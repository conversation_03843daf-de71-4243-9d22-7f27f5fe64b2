import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LucaThemeUtils {
  /// 设置整个应用的状态栏为亮色
  static setStatusBarLight() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent, // 状态栏透明
        // statusBarIconBrightness: Brightness.light,//图标和文字为浅色（通常是白色），适用于深色背景。
        statusBarBrightness:
            Brightness.dark, //仅在 iOS 上有效，用于设置状态栏的背景亮度，用于决定系统的视觉过渡效果（如从浅色到深色过渡）。
        //Brightness.light：状态栏视为浅色背景（适合深色文字）。
        //Brightness.dark：状态栏视为深色背景（适合浅色文字）。
        //在 iOS 上，状态栏的图标颜色是通过 背景亮度（statusBarBrightness） 决定的，必须结合状态栏的背景色来适配。
        //如果使用 MyAppBar，需要通过 MyAppBarTheme.systemOverlayStyle 或直接设置 MyAppBar.systemOverlayStyle 来控制状态栏样式。安卓也一样
      ),
    );
  }

  /// 设置安卓全屏
  static setAndroidFullScreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode
        .edgeToEdge); //让内容延伸到底部导航栏区域 在此模式下，应用内容将会延伸到屏幕的边缘，覆盖导航栏和状态栏。导航栏和状态栏仍然存在，但是在应用内部不可见。
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // Android 状态栏浅色图标（白色）
        systemNavigationBarColor: Colors.transparent, //Android 设备屏幕底部的导航栏区域背景颜色
        //iOS 没有系统导航栏，因此 systemNavigationBarColor 对 iOS 无效。
        systemNavigationBarIconBrightness:
            Brightness.light, //底部导航栏图标为浅色（白色）。通常包含返回键、主页键和最近任务键（虚拟按键）
      ),
    );
  }
}
