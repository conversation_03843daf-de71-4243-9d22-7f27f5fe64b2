import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_packages/ui_packages.dart';

class TextStyles {
  static const TextStyle textSize12 = TextStyle(
    fontSize: Dimens.font_sp12,
  );
  static const TextStyle textSize14 = TextStyle(
    fontSize: Dimens.font_sp14,
  );
  static const TextStyle textSize16 = TextStyle(
    fontSize: Dimens.font_sp16,
  );
  static const TextStyle textBold14 =
      TextStyle(fontSize: Dimens.font_sp14, fontWeight: FontWeight.bold);
  static const TextStyle textBold16 =
      TextStyle(fontSize: Dimens.font_sp16, fontWeight: FontWeight.bold);
  static const TextStyle textBold18 =
      TextStyle(fontSize: Dimens.font_sp18, fontWeight: FontWeight.bold);
  static const TextStyle textBold24 =
      TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold);
  static const TextStyle textBold26 =
      TextStyle(fontSize: 26.0, fontWeight: FontWeight.bold);

  static const TextStyle textGray14 = TextStyle(
    fontSize: Dimens.font_sp14,
    color: Colours.text_gray,
  );
  static const TextStyle textDarkGray14 = TextStyle(
    fontSize: Dimens.font_sp14,
    color: Colours.dark_text_gray,
  );

  static const TextStyle textWhite14 = TextStyle(
    fontSize: Dimens.font_sp14,
    color: Colors.white,
  );

  static const TextStyle text = TextStyle(
      fontSize: Dimens.font_sp14,
      color: Colours.text,
      // https://github.com/flutter/flutter/issues/40248
      textBaseline: TextBaseline.alphabetic);
  static const TextStyle textDark = TextStyle(
      fontSize: Dimens.font_sp14,
      color: Colours.dark_text,
      textBaseline: TextBaseline.alphabetic);

  static const TextStyle textGray12 = TextStyle(
      fontSize: Dimens.font_sp12,
      color: Colours.text_gray,
      fontWeight: FontWeight.normal);
  static const TextStyle textDarkGray12 = TextStyle(
      fontSize: Dimens.font_sp12,
      color: Colours.dark_text_gray,
      fontWeight: FontWeight.normal);

  static const TextStyle textHint14 = TextStyle(
      fontSize: Dimens.font_sp14, color: Colours.dark_unselected_item_color);

  static TextStyle display10 = TextStyle(
    fontSize: 10.sp,
    color: Colours.text,
    fontWeight: AppFontWeight.regular(),
  );

  static TextStyle display12 = TextStyle(
    fontSize: 12.sp,
    color: Colours.color5C5C6E,
    fontWeight: AppFontWeight.regular(),
  );

  static TextStyle display14 = TextStyle(
    fontSize: 14.sp,
    color: Colours.text,
    fontWeight: AppFontWeight.regular(),
  );
  static TextStyle display15 = TextStyle(
    fontSize: 15.sp,
    color: Colours.text,
    fontWeight: AppFontWeight.regular(),
  );
  static TextStyle display16 = TextStyle(
    fontSize: 16.sp,
    color: Colours.text,
    fontWeight: AppFontWeight.regular(),
  );
  static TextStyle titleMedium18 = TextStyle(
    fontSize: 18.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.medium(),
  );

  static TextStyle titleSemiBold16 = TextStyle(
    fontSize: 16.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.semiBold(),
  );

  static TextStyle titleBold22 = TextStyle(
    fontSize: 22.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.bold(),
  );

  static TextStyle semiBold = TextStyle(
    fontSize: 16.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.semiBold(),
    height: 1,
  );

  static TextStyle semiBold14 = TextStyle(
    fontSize: 14.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.semiBold(),
    height: 1,
  );

  static TextStyle bold = TextStyle(
    fontSize: 16.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.bold(),
    height: 1,
  );

  static TextStyle medium = TextStyle(
    fontSize: 16.sp,
    color: Colors.white,
    fontWeight: AppFontWeight.medium(),
    height: 1,
  );

  static TextStyle regular = TextStyle(
    fontSize: 14.sp,
    color: Colours.white,
    fontWeight: AppFontWeight.regular(),
    height: 1,
  );
  static TextStyle din = TextStyle(
    fontSize: 14.sp,
    color: Colours.white,
    fontFamily: 'DIN',
    height: 1,
  );
}
