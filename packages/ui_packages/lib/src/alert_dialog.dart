import 'package:flutter/cupertino.dart';
import 'package:ui_packages/ui_packages.dart';

class CustomAlertDialog extends StatelessWidget {
  final String title;
  final VoidCallback? onPressed;
  final String? content;
  final bool hideCancel;
  final String? sureText;
  final bool? hideAllCancel;
  const CustomAlertDialog(
      {super.key,
      required this.title,
      this.onPressed,
      this.content,
      this.hideCancel = false,
      this.sureText,
      this.hideAllCancel});

  @override
  Widget build(BuildContext context) {
    return BaseDialog(
      title: title,
      onPressed: onPressed,
      hideAllCancel: hideAllCancel ?? hideCancel,
      isShowCannel: !hideCancel,
      sureText: sureText,
      child: Visibility(
        visible: content != null,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text(content ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colours.color9393A5,
                fontWeight: AppFontWeight.regular(),
              )),
        ),
      ),
    );
  }
}
