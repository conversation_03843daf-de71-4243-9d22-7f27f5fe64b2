import 'dart:io';
import 'package:flutter/material.dart';

class AppFontWeight {
  static regular() {
    return FontWeight.w400;
  }

  static medium() {
    if (Platform.isAndroid) {
      return FontWeight.w500;
    } else {
      return semiBold();
    }
  }

  static semiBold() {
    return FontWeight.w600;
  }

  static bold() {
    return FontWeight.w700;
  }

  static extraBold() {
    return FontWeight.w800;
  }
}
