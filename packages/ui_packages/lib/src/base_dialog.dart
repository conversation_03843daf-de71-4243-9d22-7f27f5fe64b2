import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:utils_package/utils_package.dart';
import '../generated/l10n.dart';

/// 自定义dialog的模板
// ignore: must_be_immutable
class BaseDialog extends StatelessWidget {
  BaseDialog(
      {super.key,
      this.title,
      this.onPressed,
      this.hiddenTitle = false,
      this.isShowCannel = true,
        this.hideAllCancel = false,
      this.topImage,
      this.bottomHintWidget,
      this.sureText,
      required this.child});

  final String? title;
  final VoidCallback? onPressed;
  final Widget child;
  final bool hiddenTitle;
  final bool isShowCannel;
  final bool hideAllCancel;
  final Widget? topImage;
  final Widget? bottomHintWidget; //按钮下的提示
  String? sureText;
  @override
  Widget build(BuildContext context) {
    sureText ??= UiS.current.ok;
    final Widget dialogTitle = Visibility(
      visible: !hiddenTitle,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 25.0),
        child: Text(
          hiddenTitle ? '' : title ?? '',
          style: TextStyles.semiBold.copyWith(fontSize: 18),
        ),
      ),
    );

    final Widget bottomButton = Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment:
            MainAxisAlignment.center,
        children: <Widget>[
          if (isShowCannel)
            WxButton(
              onPressed: () => Navigator.of(context).pop(),
              text: UiS.current.cancel,
              textStyle: TextStyle(
                fontSize: 15,
                color: Colours.text,
                fontWeight: AppFontWeight.regular(),
              ),
              backgroundColor: Colours.color2C2C39,
              borderRadius: BorderRadius.circular(23),
              height: 46,
              width: 125,
            ),
          if (isShowCannel)
            SizedBox(
              width: 15.w,
            ),
          WxButton(
            text: sureText,
            textStyle: TextStyle(
              fontSize: 15,
              color: Colours.text,
              fontWeight: AppFontWeight.regular(),
            ),
            borderRadius: BorderRadius.circular(23),
            height: 46,
            width: isShowCannel ? 125 : 265,
            linearGradient: GradientUtils.mainGradient,
            onPressed: onPressed ?? (){
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );

    final Widget content = Material(
      color: Colours.color191921,
      borderRadius: BorderRadius.circular(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          const SizedBox(
            height: 35,
          ),
          dialogTitle,
          child,
          const SizedBox(
            height: 25,
          ),
          // Gaps.line,
          bottomButton,
          const SizedBox(
            height: 30,
          ),
          if (bottomHintWidget != null) SizedBox(child: bottomHintWidget),
          if (bottomHintWidget != null)
            const SizedBox(
              height: 25,
            )
        ],
      ),
    );

    final Widget body = MediaQuery.removeViewInsets(
      removeLeft: true,
      removeTop: true,
      removeRight: true,
      removeBottom: true,
      context: context,
      child: Center(
        child: Stack(
          alignment: Alignment.center,
          fit: StackFit.loose,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 305.w,
                  child: content,
                ),
                SizedBox(
                  height: 25.w,
                ),
                Visibility(
                  visible: !isShowCannel && !hideAllCancel,
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      width: 30.w,
                      height: 30.w,
                      decoration: BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: Colors.white,
                          ),
                          borderRadius: BorderRadius.circular(16.w)),
                      alignment: Alignment.center,
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 19,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (topImage != null) SizedBox(child: topImage),
          ],
        ),
      ),
    );

    /// Android 11添加了键盘弹出动画，这与我添加的过渡动画冲突（原先iOS、Android 没有相关过渡动画，相关问题跟踪：https://github.com/flutter/flutter/issues/19279）。
    /// 因为在Android 11上，viewInsets的值在键盘弹出过程中是变化的（以前只有开始结束的值）。
    /// 所以解决方法就是在Android 11及以上系统中使用Padding代替AnimatedPadding。

    if (WxAppInfoUtils.instance.androidSdkInt >= 30) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: body,
      );
    } else {
      return AnimatedPadding(
        padding: MediaQuery.of(context).viewInsets,
        duration: const Duration(milliseconds: 120),
        curve: Curves.easeInCubic, // easeOutQuad
        child: body,
      );
    }
  }
}
