import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_logger.dart';

import 'generated/l10n.dart';

T? safeCast<T>(dynamic value) {
  return value is T ? value : null;
}

// 下载进度
typedef OnDownloadProgress = void Function(int count, int total);
// 下载成功
typedef OnDownloaded = void Function();
// 请求失败
typedef OnFailure = void Function(WxApiException error);

class ApiResp {
  late int code;
  late String message;
  late dynamic data;

  ApiResp({required this.code, required this.message, this.data});

  /// 协议是否正确
  isSuccessful() {
    return code == 0;
  }
}

class WxApiException implements Exception {
  int code;
  String message;
  late dynamic data;
  DioError? e;

  WxApiException(
      {required this.code, required this.message, this.data, this.e});

  @override
  String toString() {
    return "API Exception: code: $code, message: $message";
  }
}

/// 默认dio配置
Duration _connectTimeout = const Duration(seconds: 120);
Duration _receiveTimeout = const Duration(seconds: 120);
Duration _sendTimeout = const Duration(seconds: 120);

class Api {
  static const String onUnauthorized = "onUnauthorized";
  static final Api _singleton = Api._();

  static Api get instance => _singleton;

  factory Api() => _singleton;
  late final Dio dio;

  Api.refreshToken() {
    final BaseOptions options = BaseOptions(
      connectTimeout: _connectTimeout,
      receiveTimeout: _receiveTimeout,
      sendTimeout: _sendTimeout,
      responseType: ResponseType.json,
//      contentType: Headers.formUrlEncodedContentType, // 适用于post form表单提交
    );
    dio = Dio(options);
  }

  Api._() {
    final BaseOptions options = BaseOptions(
      connectTimeout: _connectTimeout,
      receiveTimeout: _receiveTimeout,
      sendTimeout: _sendTimeout,
      responseType: ResponseType.json,
//      contentType: Headers.formUrlEncodedContentType, // 适用于post form表单提交
    );
    dio = Dio(options);
  }

  Future<ApiResp> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    bool showError = true,
  }) async {
    return _request(path, "GET",
        queryParameters: queryParameters,
        headers: headers,
        cancelToken: cancelToken,
        showError: showError);
  }

  Future<ApiResp> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    bool showError = true,
  }) async {
    return _request(path, "POST",
        data: data,
        headers: headers,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        showError: showError);
  }

  Future<ApiResp> PUT(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    bool showError = true,
  }) async {
    return _request(path, "PUT",
        data: data,
        headers: headers,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        showError: showError);
  }

  Future<ApiResp> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    bool showError = true,
  }) async {
    return _request(path, "DELETE",
        data: data,
        headers: headers,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        showError: showError);
  }

  Future<ApiResp> _request(
    String path,
    String method, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    dynamic data,
    CancelToken? cancelToken,
    bool showError = true,
  }) async {
    try {
      //拼接上域名
      if (!path.contains("https://") && !path.contains("http://")) {
        if (path.indexOf("/") != 0) {
          path = "/$path";
        }
        path = WxEnv.instance.apiUrl + path;
      }

      var sendHeaders = makeHeaders(headers);

      // WxLogger.instance.info(
      // "request, path: $path, method: $method, queryParameters: $queryParameters, data:$data ");

      (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
          (client) {
        if (WxEnv.instance.httpProxy != null) {
          //尝试配置代理
          client.findProxy = (url) {
            return WxEnv.instance.httpProxy!;
          };
        }

        /// 忽略证书
        client.badCertificateCallback = (cert, host, port) => true;
      };

      var dataJsonStr =
          data != null && data is! FormData ? jsonEncode(data) : null;

      var uri = Uri.parse(path);
      if (uri.queryParameters.isNotEmpty) {
        queryParameters ??= {};
        queryParameters.addAll(uri.queryParameters);
      }

      var response = await dio.request<dynamic>(path,
          cancelToken: cancelToken,
          queryParameters: queryParameters,
          data: dataJsonStr ?? data,
          options: Options(
              headers: sendHeaders,
              method: method,
              responseType: ResponseType.json));

      // dio.close();

      if (cancelToken?.isCancelled ?? false) {
        return ApiResp(
            code: -1000,
            message: CommonS.current.request_cancelled,
            data: null);
      }

      var resData = response.data;

      // if (resData['code'].runtimeType == String) {
      //   resData['code'] = int.parse(resData['code']);
      // }

      var resp = ApiResp(code: 0, message: '', data: resData);

      // WxLogger.instance.info(
      // "response, path: $path, method: $method,  data: ${jsonEncode(resData)}");

      return resp;
    } on DioError catch (e) {
      WxLogger.instance.warn("api error  ${e.message}");
      if (CancelToken.isCancel(e)) {
        print('Request canceled: ${e.message}');
        return ApiResp(code: -1000, message: '请求已取消', data: null);
      } else if (e.response != null) {
        var statusCode = e.response?.statusCode;
        var map = safeCast<Map>(e.response?.data);
        String? statusMessage = map?["message"];
        statusMessage ??= safeCast<String>(e.response?.data);
        statusMessage ??= e.message;
        // var e1 = WxApiException(
        //     code: statusCode ?? -1000, message: statusMessage ?? '', e: e);
        if (statusCode == 520) {
          var resData = e.response!.data;
          var resp = ApiResp(
              code: resData['code'],
              message: resData['message'],
              data: resData['data']);
          if (showError) WxLoading.showToast(resp.message);
          return resp;
        } else if (statusCode == 401) {
          BusUtils.instance.fire(EventAction(key: onUnauthorized));
          return ApiResp(
              code: 401, message: CommonS.current.login_expired, data: e);
        } else if (statusCode == 400001) {
          return ApiResp(
              code: 400001, message: CommonS.current.vip_hint, data: e);
        } else {
          if (showError)
            WxLoading.showToast(
                statusMessage ?? CommonS.current.network_anomaly);
          return ApiResp(
              code: statusCode ?? -1000,
              message: statusMessage ?? CommonS.current.network_anomaly,
              data: e);
        }
      } else {
        if (showError) WxLoading.showToast(CommonS.current.network_anomaly);
        return ApiResp(
            code: -1000, message: CommonS.current.network_anomaly, data: e);
      }
    } catch (e) {
      //  if (showError) WxLoading.showToast(e.toString());
      return ApiResp(code: -1000, message: e.toString(), data: e);
    }
  }

  // 下载文件
  Future<Response?> doDownload(String path, String savePath,
      {Map<String, dynamic>? headers,
      CancelToken? cancelToken,
      Map<String, dynamic>? params,
      dynamic? data,
      // Options? options,
      OnDownloadProgress? progress,
      OnDownloaded? completion,
      OnFailure? failure}) async {
    //拼接上域名
    if (!path.contains("https://") && !path.contains("http://")) {
      if (path.indexOf("/") != 0) {
        path = "/$path";
      }
      path = WxEnv.instance.downloadUrl + path;
    }
    try {
      if (WxEnv.instance.httpProxy != null) {
        //尝试配置代理
        (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
            (client) {
          client.findProxy = (url) {
            return WxEnv.instance.httpProxy!;
          };

          /// 忽略证书
          client.badCertificateCallback = (cert, host, port) => true;
        };
      }

      var sendHeaders = makeHeaders(headers);
      var res = await dio.download(
        path,
        savePath,
        options: Options(
            headers: sendHeaders,
            sendTimeout: const Duration(seconds: 10),
            receiveTimeout: const Duration(seconds: 10)),
        queryParameters: params,
        cancelToken: cancelToken,
        onReceiveProgress: (int count, int total) {
          if (total != -1) {
            if (!(cancelToken?.isCancelled ?? false)) {
              double downloadRatio = (count / total);
              if (downloadRatio == 1) {
                if (completion != null) {
                  completion();
                }
              } else {
                if (progress != null) {
                  progress(count, total);
                }
              }
            }
          } else {
            WxApiException apiHttpError = WxApiException(
                code: -1000, message: CommonS.current.Unable_file_size);

            if (failure != null) {
              failure(apiHttpError);
            }
          }
        },
      );
      return res;
    } on DioError catch (e) {
      WxApiException apiHttpError =
          WxApiException(code: -1000, message: e.toString());
      if (CancelToken.isCancel(e)) {
        apiHttpError = WxApiException(
            code: -1000, message: CommonS.current.request_cancelled);
      } else {
        if (e.response != null) {
          apiHttpError =
              WxApiException(code: -1000, message: e.type.toString() + path);
        } else {
          apiHttpError =
              WxApiException(code: -1000, message: (e.message ?? "") + path);
        }
      }

      if (failure != null) {
        failure(apiHttpError);
      }
    } on Exception catch (e) {
      WxApiException apiHttpError =
          WxApiException(code: -1000, message: e.toString());

      if (failure != null) {
        failure(apiHttpError);
      }
    } catch (e) {
      // 可以捕获任意异常
      WxApiException apiHttpError =
          WxApiException(code: -1000, message: e.toString());

      if (failure != null) {
        failure(apiHttpError);
      }
    }
  }

  makeHeaders(Map<String, dynamic>? addHeaders) {
    var headers = <String, dynamic>{};
    if (WxEnv.instance.currentToken != null) {
      headers["Authorization"] = 'Bearer ${WxEnv.instance.currentToken}';
    }
    if (addHeaders != null) {
      headers.addAll(addHeaders);
    }

    return headers;
  }
}
