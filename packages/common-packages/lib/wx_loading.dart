import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class WxLoading {
  static int? lastShowTime;

  static TransitionBuilder init() {
    return EasyLoading.init();
  }

  static show({
    String? status,
    Widget? indicator,
    EasyLoadingMaskType? maskType = EasyLoadingMaskType.clear,
    bool? dismissOnTap,
    int timeout = 60,
  }) {
    WxLoading.lastShowTime = DateTime.now().millisecondsSinceEpoch;

    EasyLoading.show(
        status: status,
        indicator: indicator,
        maskType: maskType,
        dismissOnTap: dismissOnTap);

    Future.delayed(Duration(seconds: timeout)).then((value) {
      if (WxLoading.lastShowTime != null) {
        int now = DateTime.now().millisecondsSinceEpoch;
        if (now - WxLoading.lastShowTime! >= timeout * 1000) {
          EasyLoading.dismiss();
          //EasyLoading.showToast("响应超时，请重试");
        }
      }
    });
  }

  static dismiss({
    bool animation = true,
  }) {
    WxLoading.lastShowTime = null;
    EasyLoading.dismiss(animation: animation);
  }

  static showToast(String status,{Duration? duration}) {
    EasyLoading.showToast(status,duration: duration);
  }

  static get isShow {
    return EasyLoading.isShow;
  }
}
