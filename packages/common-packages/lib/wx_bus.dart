import 'dart:async';

import 'package:event_bus/event_bus.dart';

class EventAction {
  String key;
  dynamic action;
  EventAction({required this.key, this.action});
}

class BusUtils {
  BusUtils._privateConstructor();
  static final BusUtils _instance = BusUtils._privateConstructor();
  static BusUtils get instance {
    return _instance;
  }

  EventBus eventBus = EventBus();

  /// 发送通知
  void fire(EventAction action) {
    eventBus.fire(action);
  }

  /// 接受通知
  StreamSubscription<EventAction> on(Function(EventAction) handle) {
    return eventBus.on<EventAction>().listen((event) {
      handle(event);
    });
  }

  /// 销毁
  void onDestroy() {}
}
