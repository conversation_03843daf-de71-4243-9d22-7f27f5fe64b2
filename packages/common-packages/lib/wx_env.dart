import 'package:flutter/foundation.dart';
import 'package:localstorage/localstorage.dart';

class WxEnv {
  final LocalStorage storage = LocalStorage('WxEnv');
  String? _currentToken;
  String? _refreshToken;
  dynamic _configData;

  String? httpProxy;

  WxEnv._privateConstructor();

  static final WxEnv _instance = WxEnv._privateConstructor();

  static const env = String.fromEnvironment('env', defaultValue: 'dev');

  static final Map<String, String> envs = {
    "dev": "https://idev.shootz.tech", //https://idev2.shootz.tech
    "test": "https://i.shootz.tech",
    "pro": "https://i.shootz.tech"
  };

  static WxEnv get instance {
    return _instance;
  }

  void switchEnv(String envName) {
    if (kDebugMode) {
      host = envs[envName] ?? host;
    }
  }

  String get host {
    // return storage.getItem("host") ?? "https://i.shootz.tech";
    return envs[env] ?? 'dev';
  }

  set host(String host) {
    storage.setItem("host", host);
    _configData = {
      'web_url': host,
      'api_url': host,
      'share_url': '$host/shared-h5',
      'download_url': '$host/api/cloud-file-center-service/sysFile/download',
      'upload_url': '$host/cloud-file-center-service/sysFile/upload',
    };
  }

  setup() async {
    await storage.ready;
    host = host;
  }

  Future<void> setToken(String accessToken, String refreshToken) async {
    _currentToken = accessToken;
    await storage.setItem("TOKEN", accessToken);
    await storage.setItem("REFRESH-TOKEN", refreshToken);
  }

  Future<void> clearToken() async {
    _currentToken = null;
    await storage.setItem("TOKEN", null);
    await storage.setItem("REFRESH-TOKEN", null);
  }

  String? get currentToken {
    _currentToken ??= storage.getItem("TOKEN");
    return _currentToken;
  }

  String? get refreshToken {
    _refreshToken ??= storage.getItem("REFRESH-TOKEN");
    return _refreshToken;
  }

  dynamic get apiUrl {
    return _configData['api_url'];
  }

  dynamic get downloadUrl {
    return _configData['download_url'];
  }

  dynamic get shareUrl {
    return _configData['share_url'];
  }

  dynamic get webUrl {
    return _configData['web_url'];
  }
}
