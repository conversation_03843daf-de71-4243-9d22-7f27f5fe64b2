import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

class MultiLevelFilter extends LogFilter {
  MultiLevelFilter();

  @override
  bool shouldLog(LogEvent event) {
    return !kReleaseMode;
  }
}

class WxLogger {
  final _logger = Logger(
    filter: MultiLevelFilter(),
    printer: PrettyPrinter(methodCount: 0, errorMethodCount: 8),
  );

  WxLogger._privateConstructor();

  static final WxLogger _instance = WxLogger._privateConstructor();

  static WxLogger get instance {
    return _instance;
  }

  verbose(dynamic msg) {
    _logger.v(msg);
  }

  debug(dynamic msg) {
    _logger.d(msg);
  }

  info(dynamic msg) {
    _logger.i(msg);
  }

  warn(dynamic msg) {
    _logger.w(msg);
  }

  error(Error error, dynamic log) {
    _logger.e(log, error, error.stackTrace);
  }

  exception(Exception exception, dynamic log) {
    _logger.e(log, exception.toString());
  }
}

/// 简便调用

verbose(dynamic msg) {
  WxLogger.instance.debug(msg);
}

debug(dynamic msg) {
  if (kDebugMode) {
    WxLogger.instance.debug(msg);
  }
}

info(dynamic msg) {
  WxLogger.instance.info(msg);
}

warn(dynamic msg) {
  WxLogger.instance.warn(msg);
}

error(Error error, dynamic log) {
  WxLogger.instance.error(error, log);
}

exception(Exception exception, dynamic log) {
  WxLogger.instance.exception(exception, log);
}
