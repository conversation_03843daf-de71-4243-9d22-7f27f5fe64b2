// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class CommonS {
  CommonS();

  static CommonS? _current;

  static CommonS get current {
    assert(_current != null,
        'No instance of CommonS was loaded. Try to initialize the CommonS delegate before accessing CommonS.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<CommonS> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = CommonS();
      CommonS._current = instance;

      return instance;
    });
  }

  static CommonS of(BuildContext context) {
    final instance = CommonS.maybeOf(context);
    assert(instance != null,
        'No instance of CommonS present in the widget tree. Did you add CommonS.delegate in localizationsDelegates?');
    return instance!;
  }

  static CommonS? maybeOf(BuildContext context) {
    return Localizations.of<CommonS>(context, CommonS);
  }

  /// `网络不稳定，请稍后再试`
  String get network_anomaly {
    return Intl.message(
      '网络不稳定，请稍后再试',
      name: 'network_anomaly',
      desc: '',
      args: [],
    );
  }

  /// `登录已过期`
  String get login_expired {
    return Intl.message(
      '登录已过期',
      name: 'login_expired',
      desc: '',
      args: [],
    );
  }

  /// `请先开通vip`
  String get vip_hint {
    return Intl.message(
      '请先开通vip',
      name: 'vip_hint',
      desc: '',
      args: [],
    );
  }

  /// `请求已取消`
  String get request_cancelled {
    return Intl.message(
      '请求已取消',
      name: 'request_cancelled',
      desc: '',
      args: [],
    );
  }

  /// `无法获取文件大小，下载失败!`
  String get Unable_file_size {
    return Intl.message(
      '无法获取文件大小，下载失败!',
      name: 'Unable_file_size',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<CommonS> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'zh'),
      Locale.fromSubtags(languageCode: 'en'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<CommonS> load(Locale locale) => CommonS.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
