import 'package:shared_preferences/shared_preferences.dart';

class WxStorage {
  WxStorage._privateConstructor();

  static final WxStorage _instance = WxStorage._privateConstructor();
  static WxStorage get instance {
    return _instance;
  }

  /// 用户标识
  String _userId = '';

  /// 切换用户标识
  void exchangeUser(String userId) {
    _userId = userId;
  }

  Future<bool> setString(String key, String value,
      {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setString(k, value);
  }

  Future<bool> setInt(String key, int value, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setInt(k, value);
  }

  Future<bool> setBool(String key, bool value, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setBool(k, value);
  }

  Future<bool> setDouble(String key, double value,
      {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setDouble(k, value);
  }

  Future<bool> setStringList(String key, List<String> value,
      {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setStringList(k, value);
  }

  Future<int?> getInt(String key, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.getInt(k);
  }

  Future<double?> getDouble(String key, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.getDouble(k);
  }

  Future<bool?> getBool(String key, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.getBool(k);
  }

  Future<String?> getString(String key, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.getString(k);
  }

  Future<List<String>?> getStringList(String key, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.getStringList(k);
  }

  Future<bool> remove(String key, {bool token = false}) async {
    String k = token ? _appToken(key) : key;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.remove(k);
  }

  Future<bool> clean() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.clear();
  }

  String _appToken(String key) {
    return _userId + key;
  }
}
