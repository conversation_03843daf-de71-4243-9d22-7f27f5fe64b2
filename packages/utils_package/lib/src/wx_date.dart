import 'package:intl/intl.dart';

class WxDateUtils {
  WxDateUtils._privateConstructor();

  static final WxDateUtils _instance = WxDateUtils._privateConstructor();

  static WxDateUtils get instance {
    return _instance;
  }

  /// 当前时间
  DateTime get now {
    return DateTime.now();
  }

  /// 当前时间戳 秒
  int get currentIntervalSecond {
    return now.millisecondsSinceEpoch ~/ 1000;
  }

  /// 当前时间戳 毫秒
  int get currentIntervalMillisecond {
    return now.millisecondsSinceEpoch;
  }

  /// 根据时间格式，生成字符串
  String formatterString(String format) {
    var formatter = DateFormat(format);
    return formatter.format(now);
  }

  /// 根据时间，生成对应的字符出
  String formatterDateTime(DateTime dateTime, String format) {
    var formatter = DateFormat(format);
    return formatter.format(dateTime);
  }

  /// 时间戳转换成时间
  String formatterIntervalSecond(int intervalSecond, String format) {
    var dateTime = DateTime.fromMillisecondsSinceEpoch(intervalSecond);
    var formatter = DateFormat(format);
    return formatter.format(dateTime);
  }

  /// 根据时间戳，计算时间差
  /// 超过60秒，按多少分钟前展示
  /// 超过60分钟，按多少小时前展示
  String calculateTimeDifference(int intervalSecond) {
    var difference = currentIntervalSecond - intervalSecond;
    if (difference < 60) {
      return '刚刚';
    } else if (difference < 60 * 60) {
      return '${difference ~/ 60}分钟前';
    } else if (difference < 60 * 60 * 24) {
      return '${difference ~/ (60 * 60)}小时前';
    } else {
      return formatterDateTime(
          DateTime.fromMillisecondsSinceEpoch(intervalSecond * 1000), 'MM-dd');
    }
  }

  /// 根据时间差，计算时间差
  /// 小与60秒，展示xx秒前
  /// 超过60秒，按xx分钟前
  String calculateTimeDifferenceByDifference(int difference) {
    if (difference < 60) {
      return '$difference秒前';
    } else if (difference < 60 * 60) {
      return '${difference ~/ 60}分钟前';
    } else if (difference < 60 * 60 * 24) {
      return '${difference ~/ (60 * 60)}小时前';
    } else {
      return formatterDateTime(
          DateTime.fromMillisecondsSinceEpoch(difference * 1000), 'MM-dd');
    }
  }

  /// 根据秒数，计算时间
  /// 如果x小于60s, 展示 mm:01
  /// 如果x大于60s, 展示 mm:ss, m，前面补0
  /// 如果大于60分钟。展示 hh:mm:ss, m不满足10，前面补0, h不满足10，前面补0
  String calculateTimeBySecond(int second) {
    if (second <= 60) {
      return '00:${second.toString().padLeft(2, '0')}';
    } else if (second < 60 * 60) {
      return '${(second ~/ 60).toString().padLeft(2, '0')}:${(second % 60).toString().padLeft(2, '0')}';
    } else {
      return '${(second ~/ (60 * 60)).toString().padLeft(2, '0')}:${((second % (60 * 60)) ~/ 60).toString().padLeft(2, '0')}:${(second % 60).toString().padLeft(2, '0')}';
    }
  }

  /// 根据时间戳，
  ///  如果是今天，展示今天
  ///  如果是明天，展示明天
  /// 如果是后天，展示2天后
  /// 否则暂时 yyyy-MM-dd
  String calculateDateDifferenceByIntervalSecond(int intervalMillisecond) {
    var dateTime = DateTime.fromMillisecondsSinceEpoch(intervalMillisecond);
    var current = now;
    if (dateTime.year == current.year &&
        dateTime.month == current.month &&
        dateTime.day == current.day) {
      return '今天';
    } else if (dateTime.year == current.year &&
        dateTime.month == current.month &&
        (dateTime.day - current.day) == 1) {
      return '明天';
    } else if (dateTime.year == current.year &&
        dateTime.month == current.month &&
        (dateTime.day - current.day) == 2) {
      return '2天后';
    } else {
      return formatterDateTime(dateTime, 'MM月dd日');
    }
  }

  /// 判断时间是否在今天之后
  bool isAfterToday(DateTime dateTime) {
    var current = now;
    if (dateTime.year == current.year &&
        dateTime.month == current.month &&
        dateTime.day == current.day) {
      return false;
    } else if (dateTime.isAfter(current)) {
      return true;
    } else {
      return false;
    }
  }
}
