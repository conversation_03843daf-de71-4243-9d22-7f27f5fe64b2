import 'dart:async';
import 'package:flutter/material.dart';

/// 权限提示工具类
/// 用于显示浮动权限提示信息
class PermissionToastUtils {
  /// 显示权限提示
  /// [context] - 上下文
  /// [message] - 提示消息
  /// [duration] - 显示时长，默认3秒
  /// [backgroundColor] - 背景颜色，默认蓝色
  /// [textStyle] - 文本样式
  static Future<void> showPermissionToast({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
    Color backgroundColor = const Color(0xFF1976D2), // Colors.blue.shade700
    TextStyle textStyle = const TextStyle(
      color: Colors.white,
      fontSize: 16.0,
      fontWeight: FontWeight.bold,
    ),
  }) async {
    if (!context.mounted) return;

    // 保存 context 的引用
    final BuildContext currentContext = context;
    final overlayEntry = _createOverlayEntry(
      message: message,
      backgroundColor: backgroundColor,
      textStyle: textStyle,
    );

    // 检查 context 是否仍然有效
    if (currentContext.mounted) {
      Overlay.of(currentContext).insert(overlayEntry);

      // 延迟一段时间后移除提示
      Future.delayed(duration).then((value) {
        overlayEntry.remove();
      });
    }
  }

  /// 创建浮层条目
  static OverlayEntry _createOverlayEntry({
    required String message,
    required Color backgroundColor,
    required TextStyle textStyle,
  }) {
    return OverlayEntry(
      builder: (context) => Positioned(
        top: 50.0,
        left: 0,
        width: MediaQuery.of(context).size.width,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8.0,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: textStyle,
            ),
          ),
        ),
      ),
    );
  }
}
