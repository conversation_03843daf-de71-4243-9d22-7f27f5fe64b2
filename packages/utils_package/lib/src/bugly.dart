// import 'package:flutter_bugly/flutter_bugly.dart';
//
// class BuglyUtils {
//   static void setup({
//     required String androidAppId,
//     required String iOSAppId,
//     required Function() handle,
//   }) {
//     FlutterBugly.postCatchedException(() {
//       handle();
//       FlutterBugly.init(
//         androidAppId: androidAppId,
//         iOSAppId: iOSAppId,
//       );
//     });
//   }
//
//   /// 设置用户id
//   static setUserId(String id) {
//     FlutterBugly.setUserId(id);
//   }
// }
