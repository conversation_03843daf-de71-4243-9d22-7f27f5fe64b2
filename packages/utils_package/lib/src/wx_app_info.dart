import 'dart:convert';

// import 'package:device_identity/device_identity.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

class WxAppInfoUtils {
  WxAppInfoUtils._privateConstructor();
  static final WxAppInfoUtils _instance =
      WxAppInfoUtils._privateConstructor();
  static WxAppInfoUtils get instance {
    return _instance;
  }

  // app名字
  String appName = "";

  // app版本
  String version = "";

  // 平台 platform
  String platform = "ios";

  // 项目code
  String projectCode = "1701139698061348864";

  /// build号
  String buildNumber = "";

  /// 手机型号
  String model = "";

  // String deviceId = "";
  int androidSdkInt = 0;

  Future<void> setup() async {
    PackageInfo info = await PackageInfo.fromPlatform();
    appName = info.appName;
    version = info.version;
    buildNumber = info.buildNumber;

    if (GetPlatform.isAndroid) {
      platform = "android";
      var info = await DeviceInfoPlugin().androidInfo;
      Map map = {};
      map["brand"] = info.brand;
      map["model"] = info.model;
      model = json.encode(map);
      // deviceId = await DeviceIdentity.oaid;
      // deviceId = info.id;
      //DeviceIdentity.oaid (来自 device_identity)
      androidSdkInt = info.version.sdkInt;
    } else {
      platform = "ios";
      var info = await DeviceInfoPlugin().iosInfo;
      model = json.encode(info.data);
      // deviceId = info.identifierForVendor ?? "";//IDFV
    }
  }
}
// 1.	androidInfo.id (来自 device_info_plus)
// •	来源: 它获取的是设备的 Android ID (Settings.Secure.ANDROID_ID)。
// •	特点:
// •	Android ID 是设备首次启动时生成的。
// •	恢复出厂设置后会改变。
// •	在 Android 8.0（API 26）之前，设备所有者（比如不同的用户）之间 Android ID 可能相同。
// •	并非所有设备都是唯一的，有些厂商可能返回固定值。
// •	适用场景:
// •	用于基本的设备区分或统计，不适合安全场景（如用户身份验证）。
// 2.	DeviceIdentity.oaid (来自 device_identity)
// •	来源: OAID (Open Advertising ID) 是中国安卓设备厂商联盟（如华为、小米、OPPO、Vivo 等）推出的一种广告标识符。
// •	特点:
// •	主要设计用于广告追踪。
// •	用户可以重置 OAID，也可以选择关闭广告个性化。
// •	与 Google 的广告 ID 类似，但更适用于国内设备厂商。
// •	适用场景:
// •	广告跟踪和营销用途。
// •	避免使用 IMEI 或其他敏感标识符，符合隐私保护需求。
// 选择建议
//
// •	如果你的应用是面向国际市场，且只需要一个基础设备标识符用于统计，androidInfo.id 更加通用。
// •	如果你的应用是面向中国市场，尤其需要广告跟踪或特定的厂商标识，使用 DeviceIdentity.oaid 是更合适的选择。
// 兼容性:
// •	androidInfo.id 通常可在所有 Android 设备上使用。
// •	DeviceIdentity.oaid 则需设备厂商支持，某些非国内厂商设备可能不支持。
// •	隐私限制：根据 Google 的隐私政策，androidId 可能会受到限制，需确保使用符合政策。
// •	如果需要更可靠的唯一标识，可以考虑 Google Play 服务的广告 ID 或生成应用内唯一 UUID。
