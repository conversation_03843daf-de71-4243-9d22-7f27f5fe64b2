## 2.0.0

> Note: This release has breaking changes.

 - **REFACTOR**(all): Use range of flutter_lints for broader compatibility ([#3371](https://github.com/fluttercommunity/plus_plugins/issues/3371)). ([8a303add](https://github.com/fluttercommunity/plus_plugins/commit/8a303add3dee1acb8bac5838246490ed8a0fe408))
 - **REFACTOR**(device_info_plus): Migrate Android example to use the new plugins declaration ([#2769](https://github.com/fluttercommunity/plus_plugins/issues/2769)). ([6103b155](https://github.com/fluttercommunity/plus_plugins/commit/6103b1559d6f9383bd66460bf7717afeeeb51d86))
 - **REFACTOR**(device_info_plus): Refactor Windows implementation ([#1772](https://github.com/fluttercommunity/plus_plugins/issues/1772)). ([ab920889](https://github.com/fluttercommunity/plus_plugins/commit/ab92088983196245ff5638d9e78b925451fbbb0c))
 - **REFACTOR**(device_info_plus): Declare proper nullability for iOS properties ([#1728](https://github.com/fluttercommunity/plus_plugins/issues/1728)). ([9f433037](https://github.com/fluttercommunity/plus_plugins/commit/9f433037f7b7664c0be98570b6c63109e23e10c4))
 - **REFACTOR**(all): Remove all manual dependency_overrides ([#1628](https://github.com/fluttercommunity/plus_plugins/issues/1628)). ([f7673fb3](https://github.com/fluttercommunity/plus_plugins/commit/f7673fb33091c65c204dda6f3476d01f5d914cdb))
 - **REFACTOR**(device_info_plus): Migrate Android part to Kotlin, update Android dependencies ([#1245](https://github.com/fluttercommunity/plus_plugins/issues/1245)). ([a6e93ace](https://github.com/fluttercommunity/plus_plugins/commit/a6e93ace13405407f825e2c29a9274a9fcd373ba))
 - **FIX**(device_info_plus): fix the error in the e2e test. ([#3382](https://github.com/fluttercommunity/plus_plugins/issues/3382)). ([3d06bf0e](https://github.com/fluttercommunity/plus_plugins/commit/3d06bf0ed8f1029df1230e4be6e75537abfcb19f))
 - **FIX**(device_info_plus): Set correct Flutter and Dart versions requirements ([#3362](https://github.com/fluttercommunity/plus_plugins/issues/3362)). ([77861523](https://github.com/fluttercommunity/plus_plugins/commit/778615231c376c829d6241e7988f15a77bcaeb55))
 - **FIX**(device_info_plus): fix integration_test iOS ([#2958](https://github.com/fluttercommunity/plus_plugins/issues/2958)). ([93ab854e](https://github.com/fluttercommunity/plus_plugins/commit/93ab854ee76a3de48387b6c54ddaeccb01cf49a9))
 - **FIX**(device_info_plus): Ensure use of Activity Context to obtain WindowManager ([#2688](https://github.com/fluttercommunity/plus_plugins/issues/2688)). ([13248d97](https://github.com/fluttercommunity/plus_plugins/commit/13248d97e617ca7e6850c1775e06db597c26cbf1))
 - **FIX**(device_info_plus): Change Kotlin version from 1.9.10 to 1.7.22 ([#2256](https://github.com/fluttercommunity/plus_plugins/issues/2256)). ([313ec2c3](https://github.com/fluttercommunity/plus_plugins/commit/313ec2c328f34b278f197ee1f2d896f8820ac789))
 - **FIX**(device_info_plus): Revert bump compileSDK to 34 ([#2230](https://github.com/fluttercommunity/plus_plugins/issues/2230)). ([2ba5b054](https://github.com/fluttercommunity/plus_plugins/commit/2ba5b054948f48a9aae72c8a63b39f6536ab678d))
 - **FIX**(device_info_plus): Regenerate iOS and MacOS example apps ([#1868](https://github.com/fluttercommunity/plus_plugins/issues/1868)). ([6e1111ac](https://github.com/fluttercommunity/plus_plugins/commit/6e1111acff40fef6f77fe2561810d679bafe938c))
 - **FIX**(all): Revert addition of namespace to avoid build fails on old AGPs ([#1725](https://github.com/fluttercommunity/plus_plugins/issues/1725)). ([b136d7ec](https://github.com/fluttercommunity/plus_plugins/commit/b136d7ec8a87095a6008e29f69431e67ed98b37d))
 - **FIX**(device_info_plus): Add compatibility with AGP 8 (Android Gradle Plugin) ([#1702](https://github.com/fluttercommunity/plus_plugins/issues/1702)). ([aa25069d](https://github.com/fluttercommunity/plus_plugins/commit/aa25069d3fd8e516ebd846cc244fff06096010d6))
 - **FIX**(device_info_plus): Upgrade gradle, android compile version & implement code improvements ([#511](https://github.com/fluttercommunity/plus_plugins/issues/511)). ([e3c7c21a](https://github.com/fluttercommunity/plus_plugins/commit/e3c7c21a7acd50e38267f06c49bad14fedefdc60))
 - **FEAT**(device_info_plus): Return model name for iOS and MacOS devices ([#3358](https://github.com/fluttercommunity/plus_plugins/issues/3358)). ([63ca4cd8](https://github.com/fluttercommunity/plus_plugins/commit/63ca4cd8127e010650468a79532dd3a6047d2b31))
 - **FEAT**(device_info_plus): Add the isiOSAppOnMac property for the iOS platform. ([#3383](https://github.com/fluttercommunity/plus_plugins/issues/3383)). ([e9077845](https://github.com/fluttercommunity/plus_plugins/commit/e9077845342023d325280985234b6a09d245ac02))
 - **FEAT**(device_info_plus): Add isLowRamDevice property to AndroidDeviceInfo ([#2765](https://github.com/fluttercommunity/plus_plugins/issues/2765)). ([1376b035](https://github.com/fluttercommunity/plus_plugins/commit/1376b0359fd39172cfb54595178313c73f5d1942))
 - **FEAT**(device_info_plus): Update min iOS target to 12 ([#2658](https://github.com/fluttercommunity/plus_plugins/issues/2658)). ([a3436100](https://github.com/fluttercommunity/plus_plugins/commit/a3436100fabd04a4d4db7ac09128b5b5962579d3))
 - **FEAT**(device_info_plus): add major, minor and patch versions to macos ([#1649](https://github.com/fluttercommunity/plus_plugins/issues/1649)). ([e871e1b1](https://github.com/fluttercommunity/plus_plugins/commit/e871e1b1a13ae793ad5513aeae078b722d748061))
 - **FEAT**(device_info_plus): Add serialNumber property to AndroidDeviceInfo ([#1349](https://github.com/fluttercommunity/plus_plugins/issues/1349)). ([c3368f73](https://github.com/fluttercommunity/plus_plugins/commit/c3368f73e647e15076e155fdaf8001f31e6a9ca1))
 - **FEAT**: more info for Windows (re-land [#814](https://github.com/fluttercommunity/plus_plugins/issues/814)) ([#1156](https://github.com/fluttercommunity/plus_plugins/issues/1156)). ([387652a5](https://github.com/fluttercommunity/plus_plugins/commit/387652a5fa512cfc3e2596e9bf29d56d14ef66b9))
 - **DOCS**(device_info_plus): Add note about arch returned value on MacOS ([#2220](https://github.com/fluttercommunity/plus_plugins/issues/2220)). ([80409e2a](https://github.com/fluttercommunity/plus_plugins/commit/80409e2ab13a6379b9101034ad453517151a719a))
 - **DOCS**(device_info_plus): Add info about iOS 16 changes to device name ([#1356](https://github.com/fluttercommunity/plus_plugins/issues/1356)). ([8026b496](https://github.com/fluttercommunity/plus_plugins/commit/8026b4963d748e897831c9e336137e34fe7f005f))
 - **BREAKING** **REFACTOR**(device_info_plus): two-package federated architecture ([#1228](https://github.com/fluttercommunity/plus_plugins/issues/1228)). ([30f9bf60](https://github.com/fluttercommunity/plus_plugins/commit/30f9bf60e5fdc8914a8b40e80148ce4eaee38fc0))
 - **BREAKING** **FIX**(device_info_plus): fixed webasm compliance ([#3254](https://github.com/fluttercommunity/plus_plugins/issues/3254)). ([e35e2123](https://github.com/fluttercommunity/plus_plugins/commit/e35e2123451fc103bbb6f6d94f71ebced2ae8af5))
 - **BREAKING** **FIX**(device_info_plus): Remove Display Metrics from Android Device Info ([#2731](https://github.com/fluttercommunity/plus_plugins/issues/2731)). ([c5af3322](https://github.com/fluttercommunity/plus_plugins/commit/c5af332207e44902ac92765da72d2acb213fae91))
 - **BREAKING** **FIX**(all): Add support of namespace property to support AGP 8 ([#1727](https://github.com/fluttercommunity/plus_plugins/issues/1727)). ([4faa5d8a](https://github.com/fluttercommunity/plus_plugins/commit/4faa5d8a9c5d3ebbfe16ce30d3a54d25c84b208a))
 - **BREAKING** **FEAT**(device_info_plus): Add support of Android display metrics ([#829](https://github.com/fluttercommunity/plus_plugins/issues/829)). ([0f136c0f](https://github.com/fluttercommunity/plus_plugins/commit/0f136c0f434a4ba5964601941517ae2882cde96f))
 - **BREAKING** **CHORE**(device_info_plus): Bump min Android and iOS versions, update podspec file ([#1781](https://github.com/fluttercommunity/plus_plugins/issues/1781)). ([bfcecac8](https://github.com/fluttercommunity/plus_plugins/commit/bfcecac8873e038e12e5b0eed62386612ef882dc))
 - **BREAKING** **BUILD**(device_info_plus): Target Java 17 on Android ([#2725](https://github.com/fluttercommunity/plus_plugins/issues/2725)). ([aa826dea](https://github.com/fluttercommunity/plus_plugins/commit/aa826deac5ef8136ce922f5823be2e7f90f828e9))

