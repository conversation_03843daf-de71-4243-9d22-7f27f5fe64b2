// ignore_for_file: unnecessary_null_comparison

import 'package:floor/floor.dart';
import 'package:shoot_z/network/model/option_goal_model.dart';
import 'dart:convert';

class OptionGoalModelCamerasConverter
    extends TypeConverter<List<OptionGoalModelCameras?>?, String> {
  @override
  List<OptionGoalModelCameras?>? decode(String databaseValue) {
    if (databaseValue == null || databaseValue.isEmpty) {
      return null;
    }
    final List<dynamic> list = json.decode(databaseValue);
    return list
        .map((item) =>
            item == null ? null : OptionGoalModelCameras.fromJson(item))
        .toList();
  }

  @override
  String encode(List<OptionGoalModelCameras?>? value) {
    if (value == null) {
      return '';
    }
    return json.encode(value.map((item) => item?.toJson()).toList());
  }
}
