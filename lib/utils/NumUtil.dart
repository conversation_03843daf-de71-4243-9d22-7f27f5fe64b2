// ignore_for_file: unnecessary_null_comparison

import 'package:decimal/decimal.dart';
import 'package:intl/intl.dart';
import 'package:rational/rational.dart';

/// Num Util. 计算金额
class NumUtil {
  /// The parameter [fractionDigits] must be an integer satisfying: `0 <= fractionDigits <= 20`.

  /// get int by value str.
  static int getIntByValueStr(String valueStr, {int defValue = 0}) {
    return int.tryParse(valueStr) ?? defValue;
  }

  /// get double by value str.
  static double getDoubleByValueStr(String valueStr, {double defValue = 0}) {
    return double.tryParse(valueStr) ?? defValue;
  }

  ///isZero
  static bool isZero(num value) {
    return value == null || value == 0;
  }

  /// 加 (精确相加,防止精度丢失).
  /// add (without loosing precision).
  static double add(num a, num b) {
    return addDec(a, b).toDouble();
  }

  /// 减 (精确相减,防止精度丢失).
  /// subtract (without loosing precision).
  static double subtract(num a, num b) {
    return subtractDec(a, b).toDouble();
  }

  /// 乘 (精确相乘,防止精度丢失).
  /// multiply (without loosing precision).
  static double multiply(num a, num b) {
    return multiplyDec(a, b).toDouble();
  }

  /// 除 (精确相除,防止精度丢失).
  /// divide (without loosing precision).
  // static double divide(num a, num b) {
  //   return divideDec(a, b).toDouble();
  // }

  /// 加 (精确相加,防止精度丢失).
  /// add (without loosing precision).
  static Decimal addDec(num a, num b) {
    return addDecStr(a.toString(), b.toString());
  }

  /// 减 (精确相减,防止精度丢失).
  /// subtract (without loosing precision).
  static Decimal subtractDec(num a, num b) {
    return subtractDecStr(a.toString(), b.toString());
  }

  /// 乘 (精确相乘,防止精度丢失).
  /// multiply (without loosing precision).
  static Decimal multiplyDec(num a, num b) {
    return multiplyDecStr(a.toString(), b.toString());
  }

  /// 除 (精确相除,防止精度丢失).
  /// divide (without loosing precision).
  // static Decimal divideDec(num a, num b) {
  //   return divideDecStr(a.toString(), b.toString());
  // }

  /// 除 (精确相除,防止精度丢失).保留几位小数，返回String
  /// divide (without loosing precision).
  static String divideStrNum(num a, num b, int digits) {
    return Decimal.parse(
            divideDecStr((a).toString(), (b).toString()).toString())
        .toStringAsFixed(digits);
  }

  /// 除 (精确相除,防止精度丢失).保留几位小数，返回String
  /// divide (without loosing precision).
  static String divideStrStr(String a, String b, int digits) {
    return Decimal.parse(divideDecStr(a, b).toString()).toStringAsFixed(digits);
  }

  /// 四舍五入(精确相除,防止精度丢失).保留几位小数，返回String
  /// (without loosing precision).
  static String toStringAsFixed(num a, int digits) {
    return Decimal.parse((a).toString()).toStringAsFixed(digits);
  }

  /// 四舍五入(精确相除,防止精度丢失).保留几位小数，返回Double
  /// (without loosing precision).
  static num toDoubleAsFixed(num a, int digits) {
    return num.parse(Decimal.parse((a).toString()).toStringAsFixed(digits));
  }

  /// 四舍五入(精确相除,防止精度丢失).保留几位小数，返回String
  /// (without loosing precision).
  static String toStringAsFixedStr(String a, int digits) {
    return Decimal.parse(a).toStringAsFixed(digits);
  }

  /// 余数
  static Decimal remainder(num a, num b) {
    return remainderDecStr(a.toString(), b.toString());
  }

  /// Relational less than operator.
  static bool lessThan(num a, num b) {
    return lessThanDecStr(a.toString(), b.toString());
  }

  /// Relational less than or equal operator.
  static bool thanOrEqual(num a, num b) {
    return thanOrEqualDecStr(a.toString(), b.toString());
  }

  /// Relational greater than operator.
  static bool greaterThan(num a, num b) {
    return greaterThanDecStr(a.toString(), b.toString());
  }

  /// Relational greater than or equal operator.
  static bool greaterOrEqual(num a, num b) {
    return greaterOrEqualDecStr(a.toString(), b.toString());
  }

  /// 加
  static Decimal addDecStr(String a, String b) {
    return Decimal.parse(a) + Decimal.parse(b);
  }

  /// 减
  static Decimal subtractDecStr(String a, String b) {
    return Decimal.parse(a) - Decimal.parse(b);
  }

  /// 乘
  static Decimal multiplyDecStr(String a, String b) {
    return Decimal.parse(a) * Decimal.parse(b);
  }

  /// 除
  static Rational divideDecStr(String a, String b) {
    return (Decimal.parse(a)) / (Decimal.parse(b));
  }

  /// 余数
  static Decimal remainderDecStr(String a, String b) {
    return Decimal.parse(a) % Decimal.parse(b);
  }

  /// Relational less than operator.
  static bool lessThanDecStr(String a, String b) {
    return Decimal.parse(a) < Decimal.parse(b);
  }

  /// Relational less than or equal operator.
  static bool thanOrEqualDecStr(String a, String b) {
    return Decimal.parse(a) <= Decimal.parse(b);
  }

  /// Relational greater than operator.
  static bool greaterThanDecStr(String a, String b) {
    return Decimal.parse(a) > Decimal.parse(b);
  }

  /// Relational greater than or equal operator.
  static bool greaterOrEqualDecStr(String a, String b) {
    return Decimal.parse(a) >= Decimal.parse(b);
  }

  static String parseFeeNumber(num fee) {
    if (fee == null) {
      return "0";
    }
    //数字格式化
    NumberFormat format = NumberFormat.currency(
        locale: 'id_ID', name: 'IDR', symbol: 'Rp', decimalDigits: 0);
    return format.format(fee);
  }
}
