import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:tencentcloud_cos_sdk_plugin/fetch_credentials.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart';

class FetchScopeLimitCredentials implements IFetchScopeLimitCredentials {
  @override
  Future<SessionQCloudCredentials> fetchScopeLimitCredentials(
      List<STSCredentialScope?> stsCredentialScopes) async {
    // 首先从您的临时密钥服务器获取包含了密钥信息的响应，例如：
    var httpClient = HttpClient();
    try {
      // 临时密钥服务器 url，临时密钥生成服务请参考 https://cloud.tencent.com/document/product/436/14048
      // 范围限制的临时密钥服务请参考：https://cloud.tencent.com/document/product/436/31923
      var stsUrl = "https://i.shootz.tech/mgr-api/common/sts";
      var request = await httpClient.postUrl(Uri.parse(stsUrl));
      request.headers.contentType = ContentType.json;
      // 将范围实体列表转换为post body中的json
      final body = jsonifyScopes(stsCredentialScopes);
      if (kDebugMode) {
        print(body);
      }
      request.write(body);

      var response = await request.close();
      if (response.statusCode == HttpStatus.OK) {
        var json = await response.transform(utf8.decoder).join();
        if (kDebugMode) {
          print(json);
        }
        // 然后解析响应，获取临时密钥信息
        var data = jsonDecode(json);
        // 最后返回临时密钥信息对象
        return SessionQCloudCredentials(
            secretId: data['credentials']['tmpSecretId'],
            secretKey: data['credentials']['tmpSecretKey'],
            token: data['credentials']['sessionToken'],
            startTime: data['startTime'],
            expiredTime: data['expiredTime']);
      } else {
        throw ArgumentError();
      }
    } catch (exception) {
      if (kDebugMode) {
        print(exception);
      }
      throw ArgumentError();
    }
  }

  // 将范围实体列表转换为json
  String jsonifyScopes(List<STSCredentialScope?> scopes) {
    List<Map<String, String?>> scopeList = [];
    for (STSCredentialScope? scope in scopes) {
      if (scope != null) {
        Map<String, String?> scopeMap = {
          'action': scope.action,
          'bucket': scope.bucket,
          'prefix': scope.prefix,
          'region': scope.region,
        };
        scopeList.add(scopeMap);
      }
    }
    return jsonEncode(scopeList);
  }
}
