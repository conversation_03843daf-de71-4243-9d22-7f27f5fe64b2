import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get_utils/src/platform/platform.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

sealed class GetSharePath {
  String getPath();
  int get getShareType;
  bool get shareWx => false;
  bool get isTimeline => false;
}

class ShareVideoPageInvitation extends GetSharePath {
  @override
  bool isTimeline;
  @override
  bool shareWx = true;

  ShareVideoPageInvitation({required this.isTimeline});

  @override
  String getPath() => "/xxx";

  @override
  int get getShareType => 8;
}

///球队主页
class ShareTeamHome extends GetSharePath {
  final String teamId;
  ShareTeamHome({required this.teamId});
  @override
  String getPath() => "/teamHome?teamId=$teamId";

  @override
  int get getShareType => 7;
}
//https://idev.shootz.tech/shared-h5/myShoot?userId=269750&reportId=1&trainType=2

///半场报告
class ShareHalfShootingReport extends GetSharePath {
  final String userId;
  final String reportId;
  final String trainType;
  @override
  bool shareWx = true;
  ShareHalfShootingReport(
      {required this.userId, required this.reportId, required this.trainType});
  @override
  String getPath() =>
      "/myShoot?userId=$userId&reportId=$reportId&trainType=$trainType";

  @override
  int get getShareType => 9;
}

///球员报告
class SharePlayerReport extends GetSharePath {
  final String matchId;
  final String teamId;
  final String playerId;
  SharePlayerReport(
      {required this.matchId, required this.teamId, required this.playerId});
  @override
  String getPath() =>
      "/playerDetails?matchId=$matchId=&teamId=$teamId&playerId=$playerId";

  @override
  int get getShareType => 4;
}

///球队报告
class ShareTeamReport extends GetSharePath {
  final String matchId;
  final String teamId;
  ShareTeamReport({required this.matchId, required this.teamId});
  @override
  String getPath() => "/matchReport?$matchId=&teamId=$teamId";

  @override
  int get getShareType => 3;
}

///比赛概况
class ShareGameDetails extends GetSharePath {
  final String matchId;
  ShareGameDetails({required this.matchId});
  @override
  String getPath() => "/matchOverview?matchId=$matchId";

  @override
  int get getShareType => 2;
}

///球馆主页
class ShareArenaDetails extends GetSharePath {
  final String arenaId;
  ShareArenaDetails({required this.arenaId});
  @override
  String getPath() => "/arenaHomePage?arenaId=$arenaId";

  @override
  int get getShareType => 5;
}

//进球播放页
class ShareGoalVideos extends GetSharePath {
  final String fragmentId;
  final String sharedFrom;
  ShareGoalVideos({required this.fragmentId, required this.sharedFrom});
  @override
  String getPath() => "/goalPlay?fragmentId=$fragmentId&sharedFrom=$sharedFrom";

  @override
  int get getShareType => 6;
}

//集锦页
class ShareHighlights extends GetSharePath {
  final String highlightId;
  final String sharedFrom;
  final String type; //0普通集锦  1半场集锦
  ShareHighlights(
      {required this.highlightId,
      required this.sharedFrom,
      required this.type});
  @override
  String getPath() =>
      "/myHighlight?highlightId=$highlightId&sharedFrom=$sharedFrom&type=$type";

  @override
  int get getShareType => 1;
}

//精选视频
//type //0 精选视频  1 教学视频
//header //0 显示顶部打开app 1 不显示
class ShareVideosId extends GetSharePath {
  final String videosId;
  final String type;
  final String header;
  ShareVideosId(
      {required this.videosId, required this.header, required this.type});
  @override
  String getPath() => "/sharePlay?id=$videosId&type=$type&header=$header";

  @override
  int get getShareType => 1;
}

//精选视频
//type //0 精选视频  1 教学视频
//header //0 显示顶部打开app 1 不显示
class ShareVideosId2 extends GetSharePath {
  final String videosId;
  final String type;
  final String header;
  ShareVideosId2(
      {required this.videosId, required this.header, required this.type});
  @override
  String getPath() => "/sharePlay?id=$videosId&type=$type&header=$header";

  @override
  int get getShareType => 11;
}

//邀请页
class ShareInvite extends GetSharePath {
  final String code;
  ShareInvite({required this.code});
  @override
  String getPath() => "/?code=$code";

  @override
  int get getShareType => 0;
}

//abstract mixin class
class MyShareH5 {
// 分享微信小程序
  static void shareMiniProgram(
      InviteMiniPathModel inviteMiniPathModel, Uint8List imageBytes) async {
    // if (inviteMiniPathModel.image == "") {
    //   ByteData data = await rootBundle
    //       .load("assets/images/vip_dialog12.png"); //dialog_invitation
    //   imageBytes = data.buffer.asUint8List();
    // }
    var type = true;
    if (const String.fromEnvironment('env', defaultValue: 'dev') != 'pro') {
      type = false;
    } else {
      type = true;
    }
    WeChatShareMiniProgramModel model = WeChatShareMiniProgramModel(
        webPageUrl: "wwww.shootz.tech",
        userName: inviteMiniPathModel.appRawId ?? "gh_04b5087f070b", //原始id
        title: inviteMiniPathModel.title ?? "",
        description: inviteMiniPathModel.remark ?? "",
        thumbData: imageBytes,
        path: inviteMiniPathModel.path ?? "/pages/index/index", //
        miniProgramType:
            type ? WXMiniProgramType.release : WXMiniProgramType.preview
        // scene: WeChatScene.timeline,
        );
    FluwxUtils.instance.shareMiniPath(model).then((value) {
      if (value) {
        getShareTask(8);
      }
    });
    // final miniProgram = FluwxUtils.instance.WeChatMiniProgram(
    //   userName: "your_mini_program_user_name", // 小程序原始id
    //   path: "/pages/index/index?params=123", // 小程序页面路径
    //   withShareTicket: true,
    //   miniprogramType: fluwx.MiniProgramType.RELEASE, // 类型：RELEASE、TEST、PREVIEW
    // );

    // fluwx.shareToWeChat(fluwx.WeChatShareMiniProgramModel(
    //   miniProgram: miniProgram,
    //   title: "分享标题",
    //   description: "分享描述",
    //   thumb:
    //       fluwx.WeChatImage.network("https://example.com/thumb.jpg"), // 缩略图URL
    // ));
  }

  static void getShareH5(GetSharePath getSharePath) async {
    //shareType 分享类型 1:个人集锦分享、2:比赛概况、3:球队比赛报告、4:个人比赛报告分享、5:球馆主页分享 6单个进球 7球队主页 8合成视频邀请好友
    final url = '${WxEnv.instance.shareUrl}${getSharePath.getPath()}';
    if (!getSharePath.shareWx) {
      if (GetPlatform.isAndroid) {
        Share.share(url).then((onValue) {
          if (onValue.status == ShareResultStatus.success) {
            getShareTask(getSharePath.getShareType);
          }
        });
      } else {
        Share.shareUri(Uri.parse(url)).then((onValue) {
          if (onValue.status == ShareResultStatus.success) {
            getShareTask(getSharePath.getShareType);
          }
        });
      }
    } else {
      ByteData data =
          await rootBundle.load("assets/images/3.0x/ic_launcher.png");
      WeChatShareWebPageModel model = WeChatShareWebPageModel(url,
          thumbData: data.buffer.asUint8List(),
          scene: getSharePath.isTimeline
              ? WeChatScene.timeline
              : WeChatScene.session,
          description: "你的专属篮球AI摄影师\n记录你的篮球生涯",
          title:
              "好友${UserManager.instance.userInfo.value?.userName}邀请您获取精彩进球~");
      FluwxUtils.instance.share(model).then((value) {
        if (value) {
          getShareTask(getSharePath.getShareType);
        }
      });
    }
  }

  //分享任务
  static void getShareTask(int shareType) async {
    if (shareType == 0 ||
        shareType == 9 ||
        shareType == 10 ||
        shareType == 11) {
      return;
    }
    var param = {
      'shareType': shareType,
      'taskID': 2,
    };

    final res =
        await Api().post(ApiUrl.getShareTask, data: param, showError: false);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      log("Share111_接口：${res.data}");
      if ((res.data["data"] ?? 0) > 0) {
        getMyDialog(
            S.current.share_Successful,
            S.current.sure,
            () {
              AppPage.back();
            },
            btnText2: S.current.Go_points,
            onPressed2: () {
              AppPage.back();
              AppPage.to(Routes.pointsPage);
            },
            isShowClose: false,
            contentWidget: RichText(
              text: TextSpan(
                  text: "+",
                  style: TextStyle(
                      color: Colours.colorA44EFF,
                      fontSize: 22.sp,
                      fontWeight: FontWeight.bold),
                  children: <InlineSpan>[
                    TextSpan(
                        text: "${res.data["data"] ?? 0}\t",
                        style: TextStyle(
                            color: Colours.colorA44EFF,
                            fontSize: 26.sp,
                            fontWeight: FontWeight.bold)),
                    TextSpan(
                        text: S.current.integral,
                        style: TextStyle(
                            color: Colours.colorA44EFF,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.normal)),
                  ]),
            ));
      }
    } else {
      // WxLoading.showToast(res.message);
    }
  }
}
