import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utils_package/utils_package.dart';
// import 'package:xbr_gaode_navi_amap/location/xbr_location_service.dart';

class Position {
  final double latitude;
  final double longitude;

  Position(this.latitude, this.longitude);
}

class LocationUtils {
  Position? position;
  String? cityName;
  String? cityId;
  String? provinceName;
  var havePermission = false.obs;
  var isLocationServiceEnabled = false.obs;
  LocationUtils._privateConstructor();
  static final LocationUtils instance = LocationUtils._privateConstructor();

  late AMapFlutterLocation _location;
  late StreamSubscription<Map<String, Object>> _locationListener;

  setup() {
    if (Platform.isAndroid) {
      _location = AMapFlutterLocation();
      //私权政策是否弹窗展示告知用户
      AMapFlutterLocation.updatePrivacyShow(true, true);
// [hasAgree] 隐私权政策是否已经取得用户同意
      AMapFlutterLocation.updatePrivacyAgree(true);
      AMapFlutterLocation.setApiKey("335ee6ef58c385efc605ac0e12cb7b75",
          "b063fbfe754018b0aa215ec27e090771");
      // AMapLocationOption locationOption = AMapLocationOption();
      // locationOption.needAddress = false;
      // locationOption.onceLocation = true;
      // locationOption.distanceFilter = 100;
      // locationOption.desiredAccuracy = DesiredAccuracy.NearestTenMeters;
      // _location.setLocationOption(locationOption);
      ///注册定位结果监听
      _locationListener =
          _location.onLocationChanged().listen((Map<String, Object> result) {
        _location.stopLocation();
        final longitude = result["longitude"] as double?;
        final latitude = result["latitude"] as double?;
        if (longitude != null && latitude != null) {
          position = Position(latitude, longitude);
        }
        completer.complete(position);
      });
    }
  }

  Future<bool> checkPermission() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      isLocationServiceEnabled.value = false;
      havePermission.value = false;
      return false;
    }
    isLocationServiceEnabled.value = true;
    var permission = await Geolocator.checkPermission();
    havePermission.value = !(permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever);
    return havePermission.value;
  }

  Future<bool> requestPermission(BuildContext context, String msg) async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      isLocationServiceEnabled.value = false;
      havePermission.value = false;
      return havePermission.value;
    }
    isLocationServiceEnabled.value = true;

    if (Platform.isAndroid) {
      if (await Permission.locationWhenInUse.isDenied) {
        // 使用新的工具类显示提示
        PermissionToastUtils.showPermissionToast(
          context: context,
          message: msg,
        );
      }
    }

    final permission = await Geolocator.requestPermission();
    havePermission.value = !(permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever);
    return havePermission.value;
  }

  Future<bool> openSettings(String msg) async {
    bool havePermission = await requestPermission(Get.context!, msg);
    if (havePermission) {
      final position = await getCurrentPosition();
      if (position != null) {
        BusUtils.instance.fire(EventAction(key: EventBusKey.getLocation));
      }
      return true;
    }
    if (!isLocationServiceEnabled.value) {
      return await Geolocator.openLocationSettings();
    }
    return await Geolocator.openAppSettings();
  }

  var completer = Completer<Position?>();
  Future<Position?> getCurrentPosition() async {
    // final completer = Completer<Position?>();
    // XbrLocation.instance().execOnceLocation(callback: (locationInfo) {
    //   if(locationInfo.longitude != null && locationInfo.latitude != null) {
    //     position = Position(locationInfo.latitude!,locationInfo.longitude!);
    //   }
    //   completer.complete(position);
    //
    // });
    // return completer.future;

    if (Platform.isAndroid) {
      completer = Completer<Position?>();
      _location.startLocation();
      position = await completer.future;
    } else {
      try {
        final pos = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 5),
        ));
        position = Position(pos.latitude, pos.longitude);
      } on TimeoutException catch (e) {
        final pos = await Geolocator.getLastKnownPosition();
        if (pos != null) {
          position = Position(pos.latitude, pos.longitude);
        }
      }
      print('${position?.longitude} + ${position?.latitude}');
    }
    if (position != null) {
      Api().get(ApiUrl.getCity, queryParameters: {
        "longitude": position?.longitude,
        "latitude": position?.latitude,
      }).then((value) {
        if (value.isSuccessful()) {
          final data = value.data;
          if (data != null) {
            provinceName = data["name"];
            if (data["nodes"] != null) {
              List cityArr = data["nodes"];
              if (cityArr.isNotEmpty) {
                var cityDic = cityArr[0];
                cityName = cityDic['name'];
                cityId = cityDic['code'];
              }
            }
            BusUtils.instance.fire(EventAction(key: EventBusKey.getCity));
          }
        }
      });
    }
    return position;
  }
}
