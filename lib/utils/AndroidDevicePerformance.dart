import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

// 设备等级划分
// ========== 设备等级枚举 - 优化点 ==========
enum DeviceTier {
  lowEnd,
  midEnd,
  highEnd;

  // 最大点数
  int get maxPoints {
    switch (this) {
      case DeviceTier.lowEnd:
        return 300;
      case DeviceTier.midEnd:
        return 500;
      case DeviceTier.highEnd:
        return 1000;
    }
  }

  // 每帧加载点数
  int get pointLoad {
    switch (this) {
      case DeviceTier.lowEnd:
        return 10;
      case DeviceTier.midEnd:
        return 20;
      case DeviceTier.highEnd:
        return 40;
    }
  }

  // 点尺寸
  double get pointSize {
    switch (this) {
      case DeviceTier.lowEnd:
        return 4.0;
      case DeviceTier.midEnd:
        return 4.0;
      case DeviceTier.highEnd:
        return 4.0;
    }
  }
}

// 设备性能检测类
class AndroidDevicePerformance {
  static Future<DeviceTier> determineDeviceTier() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;

      // 获取关键性能指标
      final int ramMB = await _getTotalRAMInMB();
      final int coreCount = await _getCpuCoreCount();
      final double clockSpeedGHz = await _getMaxCpuClockSpeedGHz();

      debugPrint('设备信息: '
          '型号=${androidInfo.model}, '
          'Android=${androidInfo.version.sdkInt}, '
          'RAM=${ramMB}MB, '
          'CPU核心数=$coreCount, '
          '最大频率=${clockSpeedGHz}GHz');
      // return DeviceTier.midEnd;
      // 性能分级规则
      if (ramMB <= 4096) {
        // <= 2GB RAM
        return DeviceTier.lowEnd;
      } else if (ramMB <= 8192) {
        // 3-4GB RAM
        // 检查CPU性能
        if (coreCount <= 4 || clockSpeedGHz <= 2.4) {
          return DeviceTier.lowEnd;
        }
        return DeviceTier.midEnd;
      } else {
        // > 4GB RAM
        // 检查CPU性能
        if (coreCount >= 6 && clockSpeedGHz >= 2.4) {
          return DeviceTier.highEnd;
        } else if (coreCount >= 4 && clockSpeedGHz >= 2.4) {
          return DeviceTier.midEnd;
        }
        return DeviceTier.midEnd; // 默认中端
      }
    } catch (e) {
      debugPrint('确定设备等级失败: $e');
      return DeviceTier.midEnd; // 错误时返回中端
    }
  }

  // 获取RAM总量（MB）
  static Future<int> _getTotalRAMInMB() async {
    try {
      if (Platform.isAndroid) {
        const platform = MethodChannel('performance_info');
        final ramMB = await platform.invokeMethod('getTotalRAM');
        return int.parse(ramMB.toString());
      }
    } catch (e) {
      debugPrint('获取RAM失败: $e');
    }

    // 备选方案：根据设备模型估算
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final model = androidInfo.model.toLowerCase();

    // 已知低端设备型号模式
    final lowEndPatterns = [
      'm3',
      'm5',
      'm10',
      'lite',
      'a0',
      'a1',
      'a2',
      'redmi go',
      'c9',
      'e5',
      'sm-a0'
    ];

    // 已知高端设备型号模式
    final highEndPatterns = [
      's2',
      's20',
      's21',
      's22',
      'note10',
      'note20',
      'note30',
      'pixel 4',
      'pixel 5',
      'pixel 6',
      'oneplus 9',
      'mi 11',
      'mate 40'
    ];

    for (final pattern in lowEndPatterns) {
      if (model.contains(pattern)) return 2000;
    }

    for (final pattern in highEndPatterns) {
      if (model.contains(pattern)) return 8000;
    }

    // 默认估算
    final year = DateTime.now().year;
    final releaseYear =
        int.tryParse(androidInfo.version.release.split('.')[0]) ?? 2019;

    if (year - releaseYear >= 3) return 3000; // 老设备
    if (year - releaseYear <= 1) return 6000; // 新设备

    return 4000; // 默认中端
  }

  // 获取CPU核心数
  static Future<int> _getCpuCoreCount() async {
    try {
      if (Platform.isAndroid) {
        const platform = MethodChannel('performance_info');
        final cores = await platform.invokeMethod('getCpuCoreCount');
        return int.parse(cores.toString());
      }
    } catch (e) {
      debugPrint('获取CPU核心数失败: $e');
    }

    // 备选方案
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final model = androidInfo.model.toLowerCase();

    if (model.contains('mt67') || model.contains('helio p')) return 4;
    if (model.contains('snapdragon 8')) return 8;
    return 4; // 默认4核
  }

  // 获取最大CPU频率（GHz）
  static Future<double> _getMaxCpuClockSpeedGHz() async {
    try {
      if (Platform.isAndroid) {
        const platform = MethodChannel('performance_info');

        final speedMHz = await platform.invokeMethod('getMaxCpuClockSpeed');
        return double.parse(speedMHz.toString()) / 1000;
      }
    } catch (e) {
      debugPrint('获取CPU频率失败: $e');
    }

    // 备选方案
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final model = androidInfo.model.toLowerCase();

    if (model.contains('snapdragon 8')) return 2.8;
    if (model.contains('exynos 9')) return 2.7;
    return 1.8; // 默认1.8GHz
  }
}
