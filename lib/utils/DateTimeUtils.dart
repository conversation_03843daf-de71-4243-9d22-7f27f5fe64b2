// datetime_utils.dart
import 'package:intl/intl.dart';

class DateTimeUtils {
  // 格式化日期时间，包含毫秒
  static String formatDateTimeWithMilliseconds(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(dateTime);
  }

  static String formatDateTimeWithSeconds(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  // 格式化日期时间，包含毫秒（24小时制）
  static String formatDateTimeWithMilliseconds24(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(dateTime);
  }

  // 只显示时间部分，包含毫秒
  static String formatTimeWithMilliseconds(DateTime dateTime) {
    return DateFormat('HH:mm:ss.SSS').format(dateTime);
  }

  // 从时间戳创建 DateTime（支持毫秒）
  static DateTime fromMillisecondsSinceEpoch(int milliseconds) {
    return DateTime.fromMillisecondsSinceEpoch(milliseconds);
  }

  // 从字符串解析 DateTime（支持毫秒格式）
  static DateTime parseWithMilliseconds(String dateString) {
    try {
      // 尝试解析包含毫秒的格式
      return DateFormat('yyyy-MM-dd HH:mm:ss.SSS').parse(dateString);
    } catch (e) {
      // 如果失败，尝试普通解析
      return DateTime.parse(dateString);
    }
  }
}
