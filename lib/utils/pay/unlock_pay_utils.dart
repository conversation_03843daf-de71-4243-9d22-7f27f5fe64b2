import 'dart:developer';
import 'dart:io';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/utils/pay/pay_utils.dart';

import '../event_bus.dart';

class UnlockPayUtils {
  static void pay(String matchId, bool matchSet,
      {String? playerId,
      String? teamId,
      String? couponId,
      bool isBindPlayer = false}) async {
    WxLoading.show();
    final clientType = Platform.isAndroid ? 1 : 2;
    final res = await Api().post(ApiUrl.orderMatch, data: {
      'clientType': clientType.toString(),
      'matchId': matchId,
      'playerId': playerId ?? '0',
      'teamId': teamId ?? '0',
      'matchSet': matchSet,
      'couponId': couponId,
    });
    log("payinfo params=${{
      'clientType': clientType.toString(),
      'matchId': matchId,
      'playerId': playerId ?? '0',
      'teamId': teamId ?? '0',
      'matchSet': matchSet,
      'couponId': couponId,
    }}");
    log("payinfo res=${res.data}");
    if (!res.isSuccessful()) {
      WxLoading.dismiss();
      return;
    }
    log("payinfo res0=${res.data}");
    final orderId = res.data['orderId'];
    if (couponId != null) {
      final res = await Api().post(ApiUrl.pay, data: {
        'channel': 4,
        'orderId': orderId,
        if (isBindPlayer) "orderParams": "bindPlayer",
      });
      log("payinfo res1=${res.data}");
      WxLoading.dismiss();
      var result = false;
      if (res.isSuccessful()) {
        result = res.data['result'] == 1;
      }
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.unlockResult, action: result));
    } else if (Platform.isAndroid) {
      final res = await Api().post(ApiUrl.pay, data: {
        'orderId': orderId,
        'channel': 1,
        if (isBindPlayer) "orderParams": "bindPlayer",
      });
      WxLoading.dismiss();
      log("payinfo res2=${res.data}");
      if (res.isSuccessful()) {
        final channelPayParams = res.data['channelPayParams'];
        if (channelPayParams != null) {
          PayUtils.instance.wxPay(channelPayParams, unlock: true);
        } else {
          WxLoading.showToast('获取支付参数出错');
        }
      }
    } else {
      PayUtils.instance.applePay(res.data['appProductId'], orderId,
          unlock: true, isBindPlayer: isBindPlayer);
    }
  }
}
