// ignore_for_file: depend_on_referenced_packages

import 'dart:async';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart'
    show InAppPurchaseAndroidPlatformAddition, QueryPurchaseDetailsResponse;
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';

import '../event_bus.dart';

class PayUtils {
  static const orderKey = "order_key";
  static const isBindPlayerKey = "isBindPlayerKey";
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  // 创建一个存储实例
  final secureStorage = const FlutterSecureStorage();

  PayUtils._();

  static final instance = PayUtils._();

  void listen() {
    // 监听购买流
    _subscription = _inAppPurchase.purchaseStream.listen(
      (purchaseDetailsList) {
        _listenToPurchaseUpdated(purchaseDetailsList);
      },
      onDone: () => _subscription.cancel(),
      onError: (error) => print('Purchase Stream Error: $error'),
    );
    // queryPastPurchases();
  }

  Future<void> _listenToPurchaseUpdated(
      List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debug('purchaseDetails.status == ${purchaseDetails.status}');
      if (purchaseDetails.status == PurchaseStatus.pending) {
        /// 等待购买中
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        /// 取消订单
        completePurchase(purchaseDetails);
        WxLoading.dismiss();
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          /// 购买出错
          WxLoading.dismiss();
          WxLoading.showToast('购买出错');
          completePurchase(purchaseDetails);
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          // final String platform = purchaseDetails.verificationData.source; // "ios" 或 "google_play"
          /// 后台验单，发放vip
          final result = await verifyReceipt(
              purchaseDetails.verificationData.serverVerificationData);
          final isUnlock = purchaseDetails.productID.startsWith('unlock_');
          BusUtils.instance.fire(EventAction(
              key: isUnlock ? EventBusKey.unlockResult : EventBusKey.payResult,
              action: result));
          if (result) {
            if (!isUnlock) UserManager.instance.pullUserInfo();
            completePurchase(purchaseDetails);
          }
        }
      }
    }
  }

  void completePurchase(PurchaseDetails purchaseDetails) {
    secureStorage.delete(key: orderKey);
    secureStorage.delete(key: isBindPlayerKey);
    _inAppPurchase.completePurchase(purchaseDetails);
  }

  /// 苹果内购 (购买)
  Future<void> applePay(String productId, String orderId,
      {bool unlock = false, bool isBindPlayer = false}) async {
    /// _inAppPurchase是否有效
    final bool isAvailable = await _inAppPurchase.isAvailable();
    if (!isAvailable) {
      return;
    }
    WxLoading.show();

    /// 查询ProductId是否在苹果服务器上注册了
    final ProductDetailsResponse productDetailResponse =
        await _inAppPurchase.queryProductDetails({productId});

    /// 查询不到说明没注册
    if (productDetailResponse.error != null) {
      WxLoading.dismiss();
      WxLoading.showToast(S.current.Failed_to_obtain_product);
      return;
    }

    /// 查询不到商品详情说明没注册
    if (productDetailResponse.productDetails.isEmpty) {
      WxLoading.dismiss();
      await _inAppPurchase.queryProductDetails({productId});
      return;
    }

    /// 查询成功
    ProductDetails productDetails = productDetailResponse.productDetails.first;

    ///创单
    // 保存数据到 Keychain
    await secureStorage.write(key: orderKey, value: orderId);
    if (isBindPlayer) {
      await secureStorage.write(key: isBindPlayerKey, value: isBindPlayerKey);
    }

    late PurchaseParam purchaseParam;
    purchaseParam = PurchaseParam(productDetails: productDetails);

    /// 向苹果服务器发起支付请求
    try {
      if (unlock) {
        await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
      } else {
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      }
    } catch (e) {
      WxLoading.dismiss();
      WxLoading.showToast(e.toString());
    }
  }

  Future<bool> verifyReceipt(String receiptData) async {
    String? orderId = await secureStorage.read(key: orderKey);
    String? isBindPlayer = await secureStorage.read(key: isBindPlayerKey);
    if (orderId == null) {
      return false;
    }
    WxLoading.show();
    final res = await Api().post(ApiUrl.pay, data: {
      'orderId': orderId,
      'payParams': receiptData,
      'channel': 3,
      if (isBindPlayer != null) "orderParams": "bindPlayer",
    });
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      if (res.data['result'] == 1) {
        return true;
      } else {
        WxLoading.showToast(res.data['message']);
        return false;
      }
    } else {
      return false;
    }
  }

  // Future<bool> verifyReceiptWithDio(String receiptData) async {
  //   String? value = await secureStorage.read(key: orderKey);
  //   debug('orderKey = $value');
  //   final dio = Dio();
  //   // 苹果接口地址
  //   const String appleUrl =
  //       'https://buy.itunes.apple.com/verifyReceipt'; // 生产环境
  //   const String sandboxUrl =
  //       'https://sandbox.itunes.apple.com/verifyReceipt'; // 沙盒环境
  //
  //   // 请求体
  //   final Map<String, dynamic> payload = {
  //     'receipt-data': receiptData,
  //     'exclude-old-transactions': true,
  //   };
  //
  //   try {
  //     // 发起 POST 请求
  //     final response = await dio.post(
  //       appleUrl, // 或使用 sandboxUrl
  //       data: jsonEncode(payload),
  //       options: Options(
  //         headers: {'Content-Type': 'application/json'},
  //       ),
  //     );
  //
  //     // 处理响应
  //     if (response.statusCode == 200) {
  //       final responseData = response.data;
  //       print('Apple Response: $responseData');
  //       if (responseData['status'] == 0) {
  //         print('Verification successful');
  //         return true;
  //       } else {
  //         print('Verification failed with status: ${responseData['status']}');
  //         if (responseData['status'] == 21007) {
  //           // 切换到沙盒环境重新验证
  //           return await verifyReceiptWithSandboxDio(receiptData);
  //         }
  //       }
  //     } else {
  //       print('HTTP error: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     print('Error verifying receipt: $e');
  //   }
  //   return false;
  // }

// 沙盒环境验证
//   Future<bool> verifyReceiptWithSandboxDio(String receiptData) async {
//     final dio = Dio();
//     const String sandboxUrl = 'https://sandbox.itunes.apple.com/verifyReceipt';
//
//     final Map<String, dynamic> payload = {
//       'receipt-data': receiptData,
//       'exclude-old-transactions': true,
//     };
//
//     try {
//       final response = await dio.post(
//         sandboxUrl,
//         data: jsonEncode(payload),
//         options: Options(
//           headers: {'Content-Type': 'application/json'},
//         ),
//       );
//
//       if (response.statusCode == 200) {
//         final responseData = response.data;
//         print('Sandbox Apple Response: $responseData');
//         if (responseData['status'] == 0) {
//           print('Verification successful');
//           return true;
//         }
//       } else {
//         print('Sandbox HTTP error: ${response.statusCode}');
//       }
//     } catch (e) {
//       print('Error verifying receipt in sandbox: $e');
//     }
//     return false;
//   }

  ///google pay需要通过查询历史然后验单防止丢单 针对非消耗品
  ///非消耗品交易是自动完成无需调用inAppPurchase.completePurchase。消耗品交易不是自动完成的，如果没有调用inAppPurchase.completePurchase，就能通过androidAddition.queryPastPurchases查询出来
  // void queryPastPurchases() async {
  //   final InAppPurchaseAndroidPlatformAddition androidAddition = InAppPurchase
  //       .instance
  //       .getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();
  //
  //   final QueryPurchaseDetailsResponse response =
  //       await androidAddition.queryPastPurchases();
  //   for (var purchase in response.pastPurchases) {
  //     if (purchase.status == PurchaseStatus.purchased ||
  //         purchase.status == PurchaseStatus.restored) {
  //       // 验证购买并授予商品
  //       // await _processPurchase(purchase);
  //     }
  //   }
  // }

  ///针对消耗品
  // Future<void> queryUnconsumedPurchases() async {
  //   final androidAddition = InAppPurchase.instance
  //       .getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();
  //
  //   // 查询历史购买记录
  //   final QueryPurchaseDetailsResponse response =
  //       await androidAddition.queryPastPurchases();
  //
  //   // 遍历未完成交易
  //   for (var purchase in response.pastPurchases) {
  //     if (!purchase.billingClientPurchase.isAcknowledged) {
  //       print("Unfinished purchase found: ${purchase.productID}");
  //
  //       // 验证购买状态 授予商品
  //
  //       // 消耗购买
  //       await androidAddition.consumePurchase(purchase);
  //     }
  //   }
  // }

  // 1. Google Play 的交易行为
  //
  // 非消耗品和订阅
  //
  // •	特点：
  // •	交易自动完成。
  // •	用户购买后，Google Play 自动将交易标记为已确认（acknowledged）。
  // •	无需调用 completePurchase。
  // •	结果：
  // •	不会出现在 queryPastPurchases 的未完成交易列表中。
  // •	再次查询时会返回已完成状态。
  //
  // 消耗品
  //
  // •	特点：
  // •	必须调用 consumePurchase 或 completePurchase 来标记交易完成。
  // •	如果未完成交易（比如 App 被杀掉），购买记录会一直保留在未完成状态，直到被消耗。
  // •	结果：
  // •	未完成的交易会出现在 androidAddition.queryPastPurchases 的查询结果中。已完成的不会在
  // •	可通过查询重新处理这些交易。

  void wxPay(String channelPayParams, {bool unlock = false}) {
    FluwxUtils.instance.wxPay(channelPayParams, unlock: unlock);
  }
}
