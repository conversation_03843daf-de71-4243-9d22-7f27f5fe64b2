#!/bin/bash

# 渠道列表（包含正常版和所有其他渠道）
channels=(
    "universal"  # 正常版
    "lqc01"
    "xiaomi" "vivo" "oppo" "honor" "huawei" "yingyongbao"
)

# 清理构建目录
echo "Performing deep clean..."
flutter clean
rm -rf build/
rm -rf .gradle/
rm -rf android/.gradle/
rm -rf android/build/
rm -rf android/app/build/

# 创建输出目录
mkdir -p build/app/outputs/apk/

# 循环构建每个渠道
for channel in "${channels[@]}"
do
  echo "=============================================="
  echo "Building $channel APK..."
  echo "=============================================="

  # 构建命令
  flutter build apk --release \
    --flavor $channel \
    --dart-define=env=pro

  # 可能的APK文件路径
  possible_paths=(
    "build/app/outputs/apk/$channel/release/app-$channel-release.apk"
    "build/app/outputs/apk/release/app-$channel-release.apk"
    "build/app/outputs/apk/release/app-release.apk"
    "build/app/outputs/apk/$channel/release/app-release.apk"
  )

  # 目标路径
  dest_path="build/app/outputs/apk/app-$channel-release.apk"

  # 查找实际生成的APK文件
  found=false
  for path in "${possible_paths[@]}"
  do
    if [ -f "$path" ]; then
      echo "Found APK at: $path"
      mv "$path" "$dest_path"
      echo "Moved to: $dest_path"
      found=true
      break
    fi
  done

  if [ "$found" = false ]; then
    echo "Error: APK file for $channel not found in any expected locations"
    echo "Searching in build directory..."

    # 在整个build目录中搜索APK文件
    find_result=$(find build -name "*.apk" 2>/dev/null)

    if [ -n "$find_result" ]; then
      echo "Found APK files:"
      echo "$find_result"

      # 尝试移动第一个找到的APK文件
      first_apk=$(echo "$find_result" | head -n 1)
      mv "$first_apk" "$dest_path"
      echo "Moved $first_apk to $dest_path"
    else
      echo "No APK files found in build directory"
    fi
  fi
done

echo "=============================================="
echo "All channel APKs build process completed!"
echo "=============================================="

# 列出所有生成的APK
echo "Generated APKs:"
ls -l build/app/outputs/apk/app-*-release.apk 2>/dev/null || echo "No APK files found"

# 计算构建结果
success_count=$(ls build/app/outputs/apk/app-*-release.apk 2>/dev/null | wc -l)
total_count=${#channels[@]}

echo "Successfully built $success_count out of $total_count channels"