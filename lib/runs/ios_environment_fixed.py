#!/usr/bin/python
# -*- coding: utf-8 -*-

# iphone的 aliyun一键登录 需要添加 -ObjC OtherLinkFlag标记
# 并修复每次 pod install 后 OtherLinkFlag被重置的问题

# 修改Pods/Pods.xcodeproj 文件中的
# MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
# OTHER_LDFLAGS = (
# 	"$(inherited)",
# 	"-framework",
# 	Flutter,
# );

# 为

# MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
# OTHER_LDFLAGS = (
# 	"$(inherited)",
# 	"-framework",
# 	Flutter,
#     -ObjC,
# );

import re
import os
import sys
import io


def fixed():

    p_origin = """
				MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
"""

    p = """
				MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
				OTHER_LDFLAGS = \(
					"\$\(inherited\)",
					"-framework",
					Flutter,
				\);
"""

    holder = """
				MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
                    -ObjC,
				);
"""

    p2 = """
				MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = \(
					"\$\(inherited\)",
					"-framework",
					Flutter,
				\);
"""

    holder2 = """
				MODULEMAP_FILE = "Target Support Files/aliyun_face_plugin/aliyun_face_plugin.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
                    -ObjC,
				);
"""

    filePath = "../../ios/Pods/Pods.xcodeproj/project.pbxproj"
    paths = sys.argv
    if len(paths) > 1:
        filePath = paths[1]
        filePath = filePath.replace("srcroot=", "")
        filePath = filePath+"/Pods/Pods.xcodeproj/project.pbxproj"

    with io.open(filePath, "r", encoding="utf-8") as f:
        lines = f.readlines()

    content = "".join(lines)

    if content.count(p_origin) > 0:
        content = re.sub(p2, '\n{}\n'.format(holder2), content)
        content = re.sub(p, '\n{}\n'.format(holder), content)

        with io.open(filePath, "w", encoding="utf-8") as f:
            f.write(content)

    #


fixed()
