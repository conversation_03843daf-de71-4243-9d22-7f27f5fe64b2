python3 assets_images_auto.py
##!/bin/bash
#
#tmpdir="../../tmpimages"
#p1_0x="../../assets/images"
#p2_0x="../../assets/images/2.0x"
#p3_0x="../../assets/images/3.0x"
#
#function moveHdpiImages() {
#  tmpimages=$(ls "$tmpdir")
#
#  for p in $tmpimages; do
#    tmpfile="$tmpdir/$p"
#    if [[ ! -d "$tmpfile" ]]; then
#      continue
#    fi
#
#    inners=$(ls "$tmpfile")
#
#    for t in $inners; do
#      dest=""
#      if [[ "$t" == *"mipmap-xhdpi"* ]]; then
#        dest="$p1_0x"
#      elif [[ "$t" == *"mipmap-xxhdpi"* ]]; then
#        dest="$p2_0x"
#      elif [[ "$t" == *"mipmap-xxxhdpi"* ]]; then
#        dest="$p3_0x"
#      fi
#
#      if [[ "$dest" != "" ]]; then
#        src="$tmpfile/$t"
#        for f in $(ls "$src"); do
#          if [[ "$f" == *".png" ]]; then
#            cp -p "$src/$f" "$dest"
#          fi
#        done
#      fi
#    done
#  done
#}
#
#function moveIOSImages() {
#  tmpimages=$(ls "$tmpdir")
#
#  for p in $tmpimages; do
#    tmpfile="$tmpdir/$p"
#    if [[ ! -d "$tmpfile" ]]; then
#      continue
#    fi
#
#    inners=$(ls "$tmpfile")
#
#    for t in $inners; do
#      if [[ "$t" != *".png" ]]; then
#        continue
#      fi
#      dest=""
#      fname=""
#      if [[ "$t" == *"@2x"* ]]; then
#        dest="$p2_0x"
#      elif [[ "$t" == *"@3x"* ]]; then
#        dest="$p3_0x"
#      else
#        dest="$p1_0x"
#      fi
#      fname=$(echo "$t" | sed 's/@2x//g' | sed 's/@3x//g')
#      src="$tmpfile/$t"
#      cp -p "$src" "$dest/$fname"
#    done
#  done
#}
#
#moveHdpiImages
#moveIOSImages