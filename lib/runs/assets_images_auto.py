import os
import shutil
# 将图片下载存放到目录./tmpimages
# 修改图片名称为相同的名称
# 将图片自动拷贝到./assets/images ./assets/images/2.0x ./assets/images/3.0x

def moveHdpiImages():
    tmpdir="../../tmpimages"
    tmpimages=os.listdir(tmpdir)
    p1_0x="../../assets/images"
    p2_0x="../../assets/images/2.0x"
    p3_0x="../../assets/images/3.0x"

    for p in tmpimages :
        tmpfile=tmpdir+"/"+p
        if not os.path.isdir(tmpfile):
            continue
        inners=os.listdir(tmpfile)
      
        for t in inners:
            dest=None
            if t.count("mipmap-xhdpi")>0:
                dest=p1_0x
            elif t.count("mipmap-xxhdpi")>0:
                dest=p2_0x
            elif t.count("mipmap-xxxhdpi")>0:
                dest=p3_0x
            
            if dest != None:
                src=tmpfile+"/"+t
                for f in os.listdir(src):
                    if f.endswith("png"):
                        shutil.copy2(src+"/"+f,dest) 
    


def moveIOSImages():
    tmpdir="../../tmpimages"
    tmpimages=os.listdir(tmpdir)
    p1_0x="../../assets/images"
    p2_0x="../../assets/images/2.0x"
    p3_0x="../../assets/images/3.0x"

    for p in tmpimages :
        tmpfile=tmpdir+"/"+p
        if not os.path.isdir(tmpfile):
            continue
        inners=os.listdir(tmpfile)
      
        for t in inners:
            if not t.endswith("png"):
                continue
            dest=None
            fname=None
            if t.count("@2x")>0:
                dest=p2_0x
            elif t.count("@3x")>0:
                dest=p3_0x
            else:
                dest=p1_0x
            fname=t.replace("@2x","").replace("@3x","")
            src=tmpfile+"/"+t
            shutil.copyfile(src,dest+"/"+fname)
 
                    
moveHdpiImages()
moveIOSImages()