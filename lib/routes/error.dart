import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

class AppPageError extends StatelessWidget {
  const AppPageError({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.page_error),
      ),
      body: <PERSON><PERSON>(
        child: Column(
          children: [
            SizedBox(
              height: 160.w,
            ),
            // WxAssets.images.logoBanner.image(scale: 3),
            SizedBox(
              height: 28.w,
            ),
            Text(
              S.current.Route_error, //    "路由错误，app没有实现此页面",
              style: TextStyle(
                fontSize: 20.sp,
                color: Colours.text,
                fontWeight: AppFontWeight.medium(),
              ),
            ),
            SizedBox(
              height: 30.w,
            ),
            // WxButton(
            //   text: "返回",
            //   onPressed: () {
            //     AppPage.back();
            //   },
            // ),
          ],
        ),
      ),
    );
  }
}
