import 'package:get/get.dart';

import '../pages/login/user.dart';
import 'route.dart';

class AppPage {
  /// 跳转页面
  /// [page] 跳转页面
  /// [needLogin] 是否需要登录
  /// [arguments] 跳转参数
  /// [closePreviousPage] 是否关闭上一个页面
  static Future<dynamic> to(
    String page, {
    bool needLogin = false,
    dynamic arguments,
    bool closePreviousPage = false,
    bool preventDuplicates = false,
  }) async {
    bool isLogin = UserManager.instance.isLogin;
    if (needLogin && !isLogin) {
      return Get.toNamed(Routes.login)?.then((onValue) {
        if (onValue == true) {
          AppPage.to(page,
              needLogin: needLogin,
              arguments: arguments,
              closePreviousPage: closePreviousPage,
              preventDuplicates: preventDuplicates);
        }
      });
    } else {
      if (closePreviousPage) {
        return Get.offNamed(
          page,
          arguments: arguments,
          preventDuplicates: preventDuplicates,
        );
      } else {
        await Future.delayed(const Duration(milliseconds: 200));
        return Get.toNamed(
          page,
          arguments: arguments,
          preventDuplicates: preventDuplicates,
        );
      }
    }
  }

  /// 跳转页面
  /// [page] 跳转页面
  /// [needLogin] 是否需要登录
  /// [arguments] 跳转参数
  /// [closePreviousPage] 是否关闭上一个页面
  static Future<dynamic> toPage(
    dynamic page, {
    bool needLogin = true,
    dynamic arguments,
    String? routeName,
    bool closePreviousPage = false,
    Transition? transition = Transition.rightToLeft,
    bool preventDuplicates = true,
  }) async {
    var isLogin = true;
    if (!isLogin) {
      // Get.toNamed(Routes.login, arguments: arguments);
    } else {
      if (closePreviousPage) {
        return Get.off(
          page,
          arguments: arguments,
          routeName: routeName,
          preventDuplicates: preventDuplicates,
        );
      } else {
        return Get.to(
          page,
          arguments: arguments,
          routeName: routeName,
          preventDuplicates: preventDuplicates,
          transition: transition,
        );
      }
    }
  }

  /// 返回
  /// [page] 返回到指定页面
  static void back({String? page, dynamic result}) {
    if (page == null) {
      Get.back(result: result);
    } else {
      Get.until((route) => route.settings.name == page);
    }
  }

  /// 重置根页面
  static void resetRootPageName(String page) {
    Get.offAllNamed(page);
  }

  /// 返回根页面
  static void returnRoot() {
    /// 如果当前页面是tab 页面，则不处理
    if (currentRoute == Routes.tab) {
      return;
    }
    Get.until((route) => route.settings.name == Routes.tab);
  }

  /// 当前视图的routeName
  static String? get currentRoute => Get.currentRoute;
}
