import 'dart:async';

import 'package:floor/floor.dart';
import 'package:shoot_z/converter/OptionGoalModelCamerasConverter.dart';
import 'package:shoot_z/converter/OptionGoalModelOtherVideosListConverter.dart';
import 'package:shoot_z/dao/OptionGoalDao.dart';
import 'package:shoot_z/entities/option_goal_model.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
part 'app_database.g.dart';

@Database(version: 1, entities: [OptionGoalModel]) //, views: [OptionGoalDao]
abstract class AppDatabase extends FloorDatabase {
  OptionGoalDao get optionGoalDao;
}
