import 'dart:async';

import 'package:floor/floor.dart';
import 'package:shoot_z/converter/OptionGoalModelCamerasConverter.dart';
import 'package:shoot_z/converter/OptionGoalModelOtherVideosListConverter.dart';
import 'package:shoot_z/dao/OptionGoalDao.dart';
import 'package:shoot_z/dao/SelfieShotDao.dart';
import 'package:shoot_z/network/model/option_goal_model.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
part 'app_database.g.dart';

@Database(
    version: 2, //原有线上数据库版本 1
    entities: [OptionGoalModel, ShotRecordModel]) //, views: [OptionGoalDao]
abstract class AppDatabase extends FloorDatabase {
  OptionGoalDao get optionGoalDao;
  SelfieShotDao get selfieShotDao;
  // 添加迁移回调
  // @override
  // Migration get migration => Migration(1, 2, (database) async {
  //       // 创建新表
  //       await database.execute('''
  //     CREATE TABLE ShotRecordModel (
  //       id INTEGER PRIMARY KEY AUTOINCREMENT,
  //       trainingId TEXT NOT NULL,
  //       vid TEXT NOT NULL,
  //       userId TEXT NOT NULL,
  //       recordDate TEXT NOT NULL,
  //       videoPath TEXT,
  //       score INTEGER NOT NULL,
  //       remarks TEXT
  //     )
  //   ''');

  //       //     // 如果需要，可以在这里添加索引
  //       //     await database.execute('''
  //       //   CREATE INDEX idx_shotrecord_user ON ShotRecordModel (userId)
  //       // ''');

  //       //     await database.execute('''
  //       //   CREATE INDEX idx_shotrecord_training ON ShotRecordModel (trainingId)
  //       // ''');
  //     });
}
