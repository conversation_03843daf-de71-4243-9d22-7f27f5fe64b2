// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $AppDatabaseBuilderContract {
  /// Adds migrations to the builder.
  $AppDatabaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $AppDatabaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<AppDatabase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorAppDatabase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDatabaseBuilderContract databaseBuilder(String name) =>
      _$AppDatabaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDatabaseBuilderContract inMemoryDatabaseBuilder() =>
      _$AppDatabaseBuilder(null);
}

class _$AppDatabaseBuilder implements $AppDatabaseBuilderContract {
  _$AppDatabaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $AppDatabaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $AppDatabaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<AppDatabase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$AppDatabase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$AppDatabase extends AppDatabase {
  _$AppDatabase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  OptionGoalDao? _optionGoalDaoInstance;

  SelfieShotDao? _selfieShotDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 2,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `OptionGoalModel` (`id` TEXT, `videoDateTimeStr` INTEGER, `arenaID` TEXT, `videoPath` TEXT, `videoTime` TEXT, `videoDate` TEXT, `userId` TEXT, `videoDateTime` TEXT, `cameraIndex` INTEGER, `selected` INTEGER, `used` INTEGER, `otherVideos` TEXT, `duration` INTEGER, `cameras` TEXT, PRIMARY KEY (`id`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `ShotRecordModel` (`player_confidence` REAL, `file_path` TEXT, `newwork_file_path` TEXT, `start_time` TEXT, `shoot_time` REAL, `training_id` TEXT, `user_id` TEXT, `id` TEXT, `is_goal` INTEGER, `player_image_path` TEXT, `goal_time` REAL, `shoot_coord` TEXT, `img_load_ok` TEXT, `video_load_ok` TEXT, `created_at` REAL, `type` TEXT, `is_check` TEXT, PRIMARY KEY (`id`))');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  OptionGoalDao get optionGoalDao {
    return _optionGoalDaoInstance ??= _$OptionGoalDao(database, changeListener);
  }

  @override
  SelfieShotDao get selfieShotDao {
    return _selfieShotDaoInstance ??= _$SelfieShotDao(database, changeListener);
  }
}

class _$OptionGoalDao extends OptionGoalDao {
  _$OptionGoalDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _optionGoalModelInsertionAdapter = InsertionAdapter(
            database,
            'OptionGoalModel',
            (OptionGoalModel item) => <String, Object?>{
                  'id': item.id,
                  'videoDateTimeStr': item.videoDateTimeStr,
                  'arenaID': item.arenaID,
                  'videoPath': item.videoPath,
                  'videoTime': item.videoTime,
                  'videoDate': item.videoDate,
                  'userId': item.userId,
                  'videoDateTime': item.videoDateTime,
                  'cameraIndex': item.cameraIndex,
                  'selected':
                      item.selected == null ? null : (item.selected! ? 1 : 0),
                  'used': item.used == null ? null : (item.used! ? 1 : 0),
                  'otherVideos': _optionGoalModelOtherVideosListConverter
                      .encode(item.otherVideos),
                  'duration': item.duration,
                  'cameras':
                      _optionGoalModelCamerasConverter.encode(item.cameras)
                }),
        _optionGoalModelUpdateAdapter = UpdateAdapter(
            database,
            'OptionGoalModel',
            ['id'],
            (OptionGoalModel item) => <String, Object?>{
                  'id': item.id,
                  'videoDateTimeStr': item.videoDateTimeStr,
                  'arenaID': item.arenaID,
                  'videoPath': item.videoPath,
                  'videoTime': item.videoTime,
                  'videoDate': item.videoDate,
                  'userId': item.userId,
                  'videoDateTime': item.videoDateTime,
                  'cameraIndex': item.cameraIndex,
                  'selected':
                      item.selected == null ? null : (item.selected! ? 1 : 0),
                  'used': item.used == null ? null : (item.used! ? 1 : 0),
                  'otherVideos': _optionGoalModelOtherVideosListConverter
                      .encode(item.otherVideos),
                  'duration': item.duration,
                  'cameras':
                      _optionGoalModelCamerasConverter.encode(item.cameras)
                }),
        _optionGoalModelDeletionAdapter = DeletionAdapter(
            database,
            'OptionGoalModel',
            ['id'],
            (OptionGoalModel item) => <String, Object?>{
                  'id': item.id,
                  'videoDateTimeStr': item.videoDateTimeStr,
                  'arenaID': item.arenaID,
                  'videoPath': item.videoPath,
                  'videoTime': item.videoTime,
                  'videoDate': item.videoDate,
                  'userId': item.userId,
                  'videoDateTime': item.videoDateTime,
                  'cameraIndex': item.cameraIndex,
                  'selected':
                      item.selected == null ? null : (item.selected! ? 1 : 0),
                  'used': item.used == null ? null : (item.used! ? 1 : 0),
                  'otherVideos': _optionGoalModelOtherVideosListConverter
                      .encode(item.otherVideos),
                  'duration': item.duration,
                  'cameras':
                      _optionGoalModelCamerasConverter.encode(item.cameras)
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<OptionGoalModel> _optionGoalModelInsertionAdapter;

  final UpdateAdapter<OptionGoalModel> _optionGoalModelUpdateAdapter;

  final DeletionAdapter<OptionGoalModel> _optionGoalModelDeletionAdapter;

  @override
  Future<List<OptionGoalModel>> findAllGoal(
    String arenaID,
    String userId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM OptionGoalModel WHERE arenaID = ?1 AND userId = ?2 ORDER BY videoDateTimeStr ASC',
        mapper: (Map<String, Object?> row) => OptionGoalModel(id: row['id'] as String?, videoDateTimeStr: row['videoDateTimeStr'] as int?, videoPath: row['videoPath'] as String?, videoTime: row['videoTime'] as String?, videoDate: row['videoDate'] as String?, videoDateTime: row['videoDateTime'] as String?, cameraIndex: row['cameraIndex'] as int?, selected: row['selected'] == null ? null : (row['selected'] as int) != 0, otherVideos: _optionGoalModelOtherVideosListConverter.decode(row['otherVideos'] as String), duration: row['duration'] as int?, userId: row['userId'] as String?, arenaID: row['arenaID'] as String?, cameras: _optionGoalModelCamerasConverter.decode(row['cameras'] as String), used: row['used'] == null ? null : (row['used'] as int) != 0),
        arguments: [arenaID, userId]);
  }

  @override
  Future<void> deleteGoal1(
    String arenaID,
    String userId,
    String id,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM OptionGoalModel WHERE arenaID = ?1 AND userId = ?2 AND id = ?3',
        arguments: [arenaID, userId, id]);
  }

  @override
  Future<void> deleteAll(
    String arenaID,
    String userId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM OptionGoalModel WHERE arenaID = ?1 AND userId = ?2',
        arguments: [arenaID, userId]);
  }

  @override
  Future<void> deleteAllUserId(String userId) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM OptionGoalModel WHERE userId = ?1',
        arguments: [userId]);
  }

  @override
  Future<void> insertGoal2(OptionGoalModel optionGoalModel) async {
    await _optionGoalModelInsertionAdapter.insert(
        optionGoalModel, OnConflictStrategy.replace);
  }

  @override
  Future<void> updateGoal2(OptionGoalModel optionGoalModel) async {
    await _optionGoalModelUpdateAdapter.update(
        optionGoalModel, OnConflictStrategy.abort);
  }

  @override
  Future<void> deleteGoal2(OptionGoalModel optionGoalModel) async {
    await _optionGoalModelDeletionAdapter.delete(optionGoalModel);
  }
}

class _$SelfieShotDao extends SelfieShotDao {
  _$SelfieShotDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _shotRecordModelInsertionAdapter = InsertionAdapter(
            database,
            'ShotRecordModel',
            (ShotRecordModel item) => <String, Object?>{
                  'player_confidence': item.playerConfidence,
                  'file_path': item.filePath,
                  'newwork_file_path': item.newworkFilePath,
                  'start_time': item.startTime,
                  'shoot_time': item.shootTime,
                  'training_id': item.trainingId,
                  'user_id': item.userId,
                  'id': item.eventId,
                  'is_goal':
                      item.isGoal == null ? null : (item.isGoal! ? 1 : 0),
                  'player_image_path': item.playerImagePath,
                  'goal_time': item.goalTime,
                  'shoot_coord': _doubleListConverter.encode(item.shootCoord),
                  'img_load_ok': item.imgLoadOK,
                  'video_load_ok': item.videoLoadOK,
                  'created_at': item.createdAt,
                  'type': item.type,
                  'is_check': item.isCheck
                }),
        _shotRecordModelUpdateAdapter = UpdateAdapter(
            database,
            'ShotRecordModel',
            ['id'],
            (ShotRecordModel item) => <String, Object?>{
                  'player_confidence': item.playerConfidence,
                  'file_path': item.filePath,
                  'newwork_file_path': item.newworkFilePath,
                  'start_time': item.startTime,
                  'shoot_time': item.shootTime,
                  'training_id': item.trainingId,
                  'user_id': item.userId,
                  'id': item.eventId,
                  'is_goal':
                      item.isGoal == null ? null : (item.isGoal! ? 1 : 0),
                  'player_image_path': item.playerImagePath,
                  'goal_time': item.goalTime,
                  'shoot_coord': _doubleListConverter.encode(item.shootCoord),
                  'img_load_ok': item.imgLoadOK,
                  'video_load_ok': item.videoLoadOK,
                  'created_at': item.createdAt,
                  'type': item.type,
                  'is_check': item.isCheck
                }),
        _shotRecordModelDeletionAdapter = DeletionAdapter(
            database,
            'ShotRecordModel',
            ['id'],
            (ShotRecordModel item) => <String, Object?>{
                  'player_confidence': item.playerConfidence,
                  'file_path': item.filePath,
                  'newwork_file_path': item.newworkFilePath,
                  'start_time': item.startTime,
                  'shoot_time': item.shootTime,
                  'training_id': item.trainingId,
                  'user_id': item.userId,
                  'id': item.eventId,
                  'is_goal':
                      item.isGoal == null ? null : (item.isGoal! ? 1 : 0),
                  'player_image_path': item.playerImagePath,
                  'goal_time': item.goalTime,
                  'shoot_coord': _doubleListConverter.encode(item.shootCoord),
                  'img_load_ok': item.imgLoadOK,
                  'video_load_ok': item.videoLoadOK,
                  'created_at': item.createdAt,
                  'type': item.type,
                  'is_check': item.isCheck
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<ShotRecordModel> _shotRecordModelInsertionAdapter;

  final UpdateAdapter<ShotRecordModel> _shotRecordModelUpdateAdapter;

  final DeletionAdapter<ShotRecordModel> _shotRecordModelDeletionAdapter;

  @override
  Future<List<ShotRecordModel>> findAllShot(
    String trainingId,
    String userId,
    String type,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM ShotRecordModel WHERE training_id = ?1 AND user_id = ?2 AND type = ?3 ORDER BY created_at ASC',
        mapper: (Map<String, Object?> row) => ShotRecordModel(playerConfidence: row['player_confidence'] as double?, filePath: row['file_path'] as String?, startTime: row['start_time'] as String?, shootTime: row['shoot_time'] as double?, trainingId: row['training_id'] as String?, eventId: row['id'] as String?, isGoal: row['is_goal'] == null ? null : (row['is_goal'] as int) != 0, type: row['type'] as String?, playerImagePath: row['player_image_path'] as String?, goalTime: row['goal_time'] as double?, shootCoord: _doubleListConverter.decode(row['shoot_coord'] as String?), imgLoadOK: row['img_load_ok'] as String?, videoLoadOK: row['video_load_ok'] as String?, newworkFilePath: row['newwork_file_path'] as String?, createdAt: row['created_at'] as double?, isCheck: row['is_check'] as String?, userId: row['user_id'] as String?),
        arguments: [trainingId, userId, type]);
  }

  @override
  Future<List<ShotRecordModel>> findPagedTrainingRecords(
    String userId,
    String type,
    int limit,
    int pageSize,
    int page,
  ) async {
    return _queryAdapter.queryList(
        'WITH TrainingGroups AS (       SELECT training_id       FROM ShotRecordModel       WHERE user_id = ?1       AND (\'\' = ?2 OR type = ?2)       GROUP BY training_id       ORDER BY MAX(created_at) DESC       LIMIT ?4 OFFSET ?5     ),     RankedRecords AS (       SELECT          s.*,         ROW_NUMBER() OVER (           PARTITION BY s.training_id            ORDER BY s.created_at DESC, s.id DESC         ) AS row_num       FROM ShotRecordModel s       JOIN TrainingGroups tg ON s.training_id = tg.training_id       WHERE s.user_id = ?1     )     SELECT * FROM RankedRecords      WHERE row_num <= ?3',
        mapper: (Map<String, Object?> row) => ShotRecordModel(playerConfidence: row['player_confidence'] as double?, filePath: row['file_path'] as String?, startTime: row['start_time'] as String?, shootTime: row['shoot_time'] as double?, trainingId: row['training_id'] as String?, eventId: row['id'] as String?, isGoal: row['is_goal'] == null ? null : (row['is_goal'] as int) != 0, type: row['type'] as String?, playerImagePath: row['player_image_path'] as String?, goalTime: row['goal_time'] as double?, shootCoord: _doubleListConverter.decode(row['shoot_coord'] as String?), imgLoadOK: row['img_load_ok'] as String?, videoLoadOK: row['video_load_ok'] as String?, newworkFilePath: row['newwork_file_path'] as String?, createdAt: row['created_at'] as double?, isCheck: row['is_check'] as String?, userId: row['user_id'] as String?),
        arguments: [userId, type, limit, pageSize, page]);
  }

  @override
  Future<void> deleteAll(
    String trainingId,
    String userId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM ShotRecordModel WHERE training_id = ?1 AND user_id = ?2',
        arguments: [trainingId, userId]);
  }

  @override
  Future<void> deleteAllUserId(String userId) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM ShotRecordModel WHERE user_id = ?1',
        arguments: [userId]);
  }

  @override
  Future<void> deleteShot1(
    String trainingId,
    String userId,
    String eventId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM ShotRecordModel WHERE training_id = ?1 AND user_id = ?2 AND id = ?3',
        arguments: [trainingId, userId, eventId]);
  }

  @override
  Future<void> insertShot2(ShotRecordModel shotRecordModel) async {
    await _shotRecordModelInsertionAdapter.insert(
        shotRecordModel, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertRecord(ShotRecordModel record) async {
    await _shotRecordModelInsertionAdapter.insert(
        record, OnConflictStrategy.replace);
  }

  @override
  Future<void> updateRecord(ShotRecordModel record) async {
    await _shotRecordModelUpdateAdapter.update(
        record, OnConflictStrategy.abort);
  }

  @override
  Future<void> deleteShot2(ShotRecordModel shotRecordModel) async {
    await _shotRecordModelDeletionAdapter.delete(shotRecordModel);
  }
}

// ignore_for_file: unused_element
final _optionGoalModelOtherVideosListConverter =
    OptionGoalModelOtherVideosListConverter();
final _optionGoalModelCamerasConverter = OptionGoalModelCamerasConverter();
final _doubleListConverter = DoubleListConverter();
