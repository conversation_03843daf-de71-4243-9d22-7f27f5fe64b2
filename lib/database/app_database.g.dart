// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $AppDatabaseBuilderContract {
  /// Adds migrations to the builder.
  $AppDatabaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $AppDatabaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<AppDatabase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorAppDatabase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDatabaseBuilderContract databaseBuilder(String name) =>
      _$AppDatabaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDatabaseBuilderContract inMemoryDatabaseBuilder() =>
      _$AppDatabaseBuilder(null);
}

class _$AppDatabaseBuilder implements $AppDatabaseBuilderContract {
  _$AppDatabaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $AppDatabaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $AppDatabaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<AppDatabase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$AppDatabase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$AppDatabase extends AppDatabase {
  _$AppDatabase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  OptionGoalDao? _optionGoalDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 1,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `OptionGoalModel` (`id` TEXT, `videoDateTimeStr` INTEGER, `arenaID` TEXT, `videoPath` TEXT, `videoTime` TEXT, `videoDate` TEXT, `userId` TEXT, `videoDateTime` TEXT, `cameraIndex` INTEGER, `selected` INTEGER, `used` INTEGER, `otherVideos` TEXT, `duration` INTEGER, `cameras` TEXT, PRIMARY KEY (`id`))');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  OptionGoalDao get optionGoalDao {
    return _optionGoalDaoInstance ??= _$OptionGoalDao(database, changeListener);
  }
}

class _$OptionGoalDao extends OptionGoalDao {
  _$OptionGoalDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _optionGoalModelInsertionAdapter = InsertionAdapter(
            database,
            'OptionGoalModel',
            (OptionGoalModel item) => <String, Object?>{
                  'id': item.id,
                  'videoDateTimeStr': item.videoDateTimeStr,
                  'arenaID': item.arenaID,
                  'videoPath': item.videoPath,
                  'videoTime': item.videoTime,
                  'videoDate': item.videoDate,
                  'userId': item.userId,
                  'videoDateTime': item.videoDateTime,
                  'cameraIndex': item.cameraIndex,
                  'selected':
                      item.selected == null ? null : (item.selected! ? 1 : 0),
                  'used': item.used == null ? null : (item.used! ? 1 : 0),
                  'otherVideos': _optionGoalModelOtherVideosListConverter
                      .encode(item.otherVideos),
                  'duration': item.duration,
                  'cameras':
                      _optionGoalModelCamerasConverter.encode(item.cameras)
                },
            changeListener),
        _optionGoalModelUpdateAdapter = UpdateAdapter(
            database,
            'OptionGoalModel',
            ['id'],
            (OptionGoalModel item) => <String, Object?>{
                  'id': item.id,
                  'videoDateTimeStr': item.videoDateTimeStr,
                  'arenaID': item.arenaID,
                  'videoPath': item.videoPath,
                  'videoTime': item.videoTime,
                  'videoDate': item.videoDate,
                  'userId': item.userId,
                  'videoDateTime': item.videoDateTime,
                  'cameraIndex': item.cameraIndex,
                  'selected':
                      item.selected == null ? null : (item.selected! ? 1 : 0),
                  'used': item.used == null ? null : (item.used! ? 1 : 0),
                  'otherVideos': _optionGoalModelOtherVideosListConverter
                      .encode(item.otherVideos),
                  'duration': item.duration,
                  'cameras':
                      _optionGoalModelCamerasConverter.encode(item.cameras)
                },
            changeListener),
        _optionGoalModelDeletionAdapter = DeletionAdapter(
            database,
            'OptionGoalModel',
            ['id'],
            (OptionGoalModel item) => <String, Object?>{
                  'id': item.id,
                  'videoDateTimeStr': item.videoDateTimeStr,
                  'arenaID': item.arenaID,
                  'videoPath': item.videoPath,
                  'videoTime': item.videoTime,
                  'videoDate': item.videoDate,
                  'userId': item.userId,
                  'videoDateTime': item.videoDateTime,
                  'cameraIndex': item.cameraIndex,
                  'selected':
                      item.selected == null ? null : (item.selected! ? 1 : 0),
                  'used': item.used == null ? null : (item.used! ? 1 : 0),
                  'otherVideos': _optionGoalModelOtherVideosListConverter
                      .encode(item.otherVideos),
                  'duration': item.duration,
                  'cameras':
                      _optionGoalModelCamerasConverter.encode(item.cameras)
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<OptionGoalModel> _optionGoalModelInsertionAdapter;

  final UpdateAdapter<OptionGoalModel> _optionGoalModelUpdateAdapter;

  final DeletionAdapter<OptionGoalModel> _optionGoalModelDeletionAdapter;

  @override
  Future<List<OptionGoalModel>> findAllGoal(
    String arenaID,
    String userId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM OptionGoalModel WHERE arenaID = ?1 AND userId = ?2 ORDER BY videoDateTimeStr ASC',
        mapper: (Map<String, Object?> row) => OptionGoalModel(id: row['id'] as String?, videoDateTimeStr: row['videoDateTimeStr'] as int?, videoPath: row['videoPath'] as String?, videoTime: row['videoTime'] as String?, videoDate: row['videoDate'] as String?, videoDateTime: row['videoDateTime'] as String?, cameraIndex: row['cameraIndex'] as int?, selected: row['selected'] == null ? null : (row['selected'] as int) != 0, otherVideos: _optionGoalModelOtherVideosListConverter.decode(row['otherVideos'] as String), duration: row['duration'] as int?, userId: row['userId'] as String?, arenaID: row['arenaID'] as String?, cameras: _optionGoalModelCamerasConverter.decode(row['cameras'] as String), used: row['used'] == null ? null : (row['used'] as int) != 0),
        arguments: [arenaID, userId]);
  }

  @override
  Stream<OptionGoalModel?> findGoalById(
    String id,
    String arenaID,
    String userId,
  ) {
    return _queryAdapter.queryStream(
        'SELECT * FROM OptionGoalModel WHERE id = ?1 AND arenaID = ?2 AND userId = ?3',
        mapper: (Map<String, Object?> row) => OptionGoalModel(
            id: row['id'] as String?,
            videoDateTimeStr: row['videoDateTimeStr'] as int?,
            videoPath: row['videoPath'] as String?,
            videoTime: row['videoTime'] as String?,
            videoDate: row['videoDate'] as String?,
            videoDateTime: row['videoDateTime'] as String?,
            cameraIndex: row['cameraIndex'] as int?,
            selected:
                row['selected'] == null ? null : (row['selected'] as int) != 0,
            otherVideos: _optionGoalModelOtherVideosListConverter
                .decode(row['otherVideos'] as String),
            duration: row['duration'] as int?,
            userId: row['userId'] as String?,
            arenaID: row['arenaID'] as String?,
            cameras: _optionGoalModelCamerasConverter
                .decode(row['cameras'] as String),
            used: row['used'] == null ? null : (row['used'] as int) != 0),
        arguments: [id, arenaID, userId],
        queryableName: 'OptionGoalModel',
        isView: false);
  }

  @override
  Future<void> deleteGoal1(
    String arenaID,
    String userId,
    String id,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM OptionGoalModel WHERE arenaID = ?1 AND userId = ?2 AND id = ?3',
        arguments: [arenaID, userId, id]);
  }

  @override
  Future<void> deleteAll(
    String arenaID,
    String userId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM OptionGoalModel WHERE arenaID = ?1 AND userId = ?2',
        arguments: [arenaID, userId]);
  }

  @override
  Future<void> deleteAllUserId(String userId) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM OptionGoalModel WHERE userId = ?1',
        arguments: [userId]);
  }

  @override
  Future<void> insertGoal2(OptionGoalModel optionGoalModel) async {
    await _optionGoalModelInsertionAdapter.insert(
        optionGoalModel, OnConflictStrategy.replace);
  }

  @override
  Future<void> updateGoal2(OptionGoalModel optionGoalModel) async {
    await _optionGoalModelUpdateAdapter.update(
        optionGoalModel, OnConflictStrategy.abort);
  }

  @override
  Future<void> deleteGoal2(OptionGoalModel optionGoalModel) async {
    await _optionGoalModelDeletionAdapter.delete(optionGoalModel);
  }
}

// ignore_for_file: unused_element
final _optionGoalModelOtherVideosListConverter =
    OptionGoalModelOtherVideosListConverter();
final _optionGoalModelCamerasConverter = OptionGoalModelCamerasConverter();
