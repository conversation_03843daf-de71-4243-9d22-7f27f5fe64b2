import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:ui_packages/ui_packages.dart';

import '../generated/l10n.dart';
import '../inappwebview/router.dart';
import '../network/api_url.dart';
import '../routes/app.dart';
import '../routes/route.dart';

class PrivacyDialog extends StatelessWidget {
  const PrivacyDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        width: 305.w,
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 35.w,
            ),
            Text(
              S.current.user_notice,
              style: TextStyles.semiBold.copyWith(fontSize: 18.sp),
            ),
            SizedBox(
              height: 25.w,
            ),
            Text.rich(TextSpan(children: [
              TextSpan(
                  text: S.current.before_use,
                  style: TextStyles.display14
                      .copyWith(color: Colours.color9393A5)),
              TextSpan(
                text: "《${S.current.user_policy_line}》",
                style: TextStyles.display14.copyWith(
                  color: Colors.white,
                ),
                recognizer: TapGestureRecognizer()..onTap = didUserPolicy,
              ),
              TextSpan(
                  text: " ",
                  style: TextStyles.display14
                      .copyWith(color: Colours.color9393A5)),
              TextSpan(
                text: "《${S.current.privacy_policy}》",
                style: TextStyles.display14.copyWith(
                  color: Colors.white,
                ),
                recognizer: TapGestureRecognizer()..onTap = didPrivacyPolicy,
              ),
              TextSpan(
                  text: S.current.user_notice_content,
                  style: TextStyles.display14
                      .copyWith(color: Colours.color9393A5)),
            ])),
            SizedBox(
              height: 30.w,
            ),
            WxButton(
              text: S.current.agree,
              textStyle: TextStyles.regular.copyWith(fontSize: 15.sp),
              // width: 266.w,
              height: 46.w,
              borderRadius: BorderRadius.circular(23.w),
              linearGradient: GradientUtils.mainGradient,
              onPressed: () {
                AppPage.back();
                LocationUtils.instance.setup();
                WxStorage.instance.setBool('PrivacyDialog', true);
                UserManager.instance.showPrivacyDialog.value = false;
              },
            ),
            SizedBox(
              height: 20.w,
            ),
            GestureDetector(
                onTap: () => SystemNavigator.pop(),
                child: Text(
                  S.current.disagree,
                  style:
                      TextStyles.regular.copyWith(color: Colours.colorA44EFF),
                )),
            SizedBox(
              height: 30.w,
            )
          ],
        ),
      ),
    );
  }

  void didUserPolicy() async {
    WebviewRouter router = WebviewRouter(
        url: ApiUrl.userPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.user_policy_line);
    AppPage.to(Routes.webview, arguments: router);
  }

  void didPrivacyPolicy() {
    WebviewRouter router = WebviewRouter(
        url: ApiUrl.privacyPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.privacy_policy);
    AppPage.to(Routes.webview, arguments: router);
  }
}
