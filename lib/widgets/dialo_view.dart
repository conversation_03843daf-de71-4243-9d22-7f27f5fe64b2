import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

//需要vip的弹窗
void getNeedVipDialog(String sureText, String sureText2,
    void Function()? onPressed, void Function()? onPressed2) {
  Get.dialog(
    Padding(
      padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
      child: Material(
        type: MaterialType.transparency,
        color: Colors.transparent,
        child: Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  color: Colors.transparent,
                  child: Column(
                    children: <Widget>[
                      //upload_top_img
                      SizedBox(
                        height: 60.w,
                      ),

                      Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          MyImage(
                            "vipdialog1.png",
                            width: 283.w,
                            height: 180.w,
                            isAssetImage: true,
                            fit: BoxFit.fill,
                            bgColor: Colors.transparent,
                            radius: 12.r,
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 45.w),
                            alignment: Alignment.topLeft,
                            constraints: BoxConstraints(
                              maxHeight: 365.w,
                              minHeight: 235.w,
                            ),
                            decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius: BorderRadius.all(
                                Radius.circular(25.r),
                              ),
                            ),
                            width: double.infinity,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                SizedBox(
                                  height: 20.w,
                                ),
                                WxAssets.images.vipdialog.image(
                                    width: 236.w,
                                    height: 47.w,
                                    fit: BoxFit.fill),
                                SizedBox(
                                  height: 25.w,
                                ),
                                WxAssets.images.vipdialog2.image(
                                    width: 269.w,
                                    height: 88.w,
                                    fit: BoxFit.fill),
                                SizedBox(
                                  height: 35.w,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      sureText,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed2,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color22222D,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      sureText2,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 15.sp,
                                          color: Colours.color9393A5),
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      SizedBox(
                        height: 25.w,
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.back();
                        },
                        child: WxAssets.images.icCloseDialog
                            .image(width: 30.w, height: 30.w),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    barrierColor: Colors.black.withOpacity(0.85),
  );
}

//领取vip的弹窗
void getReciverVipDialog(String sureText, String sureText2,
    void Function()? onPressed, void Function()? onPressed2) {
  Get.dialog(
    Padding(
      padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
      child: Material(
        type: MaterialType.transparency,
        color: Colors.transparent,
        child: Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  color: Colors.transparent,
                  child: Column(
                    children: <Widget>[
                      Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          MyImage(
                            "Receive_vip.png",
                            width: double.infinity,
                            height: 483.w,
                            isAssetImage: true,
                            fit: BoxFit.fill,
                            bgColor: Colors.transparent,
                            radius: 12.r,
                          ),
                          Container(
                            alignment: Alignment.topLeft,
                            constraints: BoxConstraints(
                              maxHeight: 300.w,
                              minHeight: 235.w,
                            ),
                            margin: EdgeInsets.all(5.w),
                            decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius: BorderRadius.all(
                                Radius.circular(25.r),
                              ),
                            ),
                            width: double.infinity,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                SizedBox(
                                  height: 20.w,
                                ),
                                WxAssets.images.vipdialog.image(
                                    width: 236.w,
                                    height: 44.w,
                                    fit: BoxFit.fill),
                                SizedBox(
                                  height: 20.w,
                                ),
                                WxAssets.images.vipdialog2.image(
                                    width: 269.w,
                                    height: 88.w,
                                    fit: BoxFit.fill),
                                SizedBox(
                                  height: 20.w,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      left: 25.w,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      sureText,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed2,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                        right: 25.w, left: 25.w, top: 5.w),
                                    child: Text(
                                      sureText2,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 15.sp,
                                          color: Colours.color9393A5),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    barrierColor: Colors.black.withOpacity(0.85),
  );
}
