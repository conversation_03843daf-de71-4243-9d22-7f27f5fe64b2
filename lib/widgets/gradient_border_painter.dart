
import 'package:flutter/cupertino.dart';
import 'package:ui_packages/ui_packages.dart';

class GradientBorderPainter extends CustomPainter {
  double strokeWidth;
  LinearGradient gradient;
  double radius;

  GradientBorderPainter({this.strokeWidth = 1,this.radius = 16,LinearGradient? gradient}) : gradient = gradient ?? GradientUtils.mainGradient;

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final roundedRect = RRect.fromRectAndRadius(rect, Radius.circular(radius));
    canvas.drawRRect(roundedRect, paint);
  }

  @override
  bool shouldRepaint(covariant GradientBorderPainter oldDelegate) => true;
}