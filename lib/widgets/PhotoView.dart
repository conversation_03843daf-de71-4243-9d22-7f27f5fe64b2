// ignore_for_file: use_super_parameters

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

//首页朋友圈图片
class PhotoView extends StatefulWidget {
  final List<String?> images;
  final int index;
  final String? tag;
  final double flag;
  final bool isUrl;
  const PhotoView({
    Key? key,
    this.images = const [],
    this.index = 0,
    this.tag,
    this.flag = 0.0,
    this.isUrl = false,
  }) : super(key: key);

  @override
  _PhotoViewState createState() => _PhotoViewState();
}

class _PhotoViewState extends State<PhotoView> {
  int currentIndex = 0;
  PageController? controller;
  var imgUrl = ''; //https://gkyimg.oss-cn-shenzhen.aliyuncs.com/

  @override
  void initState() {
    super.initState();
    print('$imgUrl${widget.images[widget.index]}');
    print(
        '\n\n==========================图片地址================================\n\n');
    currentIndex = widget.index;
    controller = PageController(initialPage: widget.index);
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.light,
        statusBarColor: Colors.transparent,
      ),
      child: Scaffold(
        backgroundColor: const Color(0xff191919),
        appBar: MyAppBar(
          title: Text("查看图片${currentIndex + 1}/${widget.images.length}"),
        ),
        body: GestureDetector(
          onTap: () {
            //  AppPage.back();
          },
          child: Stack(
            children: <Widget>[
              Positioned(
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                child: NotificationListener<OverscrollIndicatorNotification>(
                  onNotification: handleGlowNotification,
                  child: PhotoViewGallery.builder(
                    builder: (BuildContext context, int index) {
                      return PhotoViewGalleryPageOptions(
                        heroAttributes: PhotoViewHeroAttributes(
                          tag: (widget.images.length - 1 < widget.index
                                  ? ''
                                  : widget.images[widget.index]) ??
                              "",
                          //  widget.images[widget.index] ??
                          //     "${widget.tag ?? ''}${Random().nextInt(100000000)}",
                        ),
                        maxScale: 5.0,
                        filterQuality: FilterQuality.high,
                        imageProvider: CachedNetworkImageProvider(
                          widget.isUrl
                              ? '${widget.images[index]}'
                              : '$imgUrl${widget.images[index]}',
                        ),
                      );
                    },
                    itemCount: widget.images.length,
                    backgroundDecoration: const BoxDecoration(
                      color: Colors.transparent,
                    ),
                    pageController: controller,
                    enableRotation: true,
                    onPageChanged: (index) {
                      setState(() {
                        currentIndex = index;
                      });
                    },
                  ),
                ),
              ),
              // Positioned(
              //   left: 0,
              //   right: 0,
              //   child: Container(
              //     decoration: const BoxDecoration(
              //       gradient: LinearGradient(
              //         colors: [
              //           Color(0x50000000),
              //           Color(0x00000000),
              //         ],
              //         begin: Alignment.topCenter,
              //         end: Alignment.bottomCenter,
              //       ),
              //     ),
              //     child: Column(
              //       children: <Widget>[
              //         SizedBox(height: padd(context).top),
              //         Container(
              //           height: 72,
              //           padding: EdgeInsets.symmetric(horizontal: 16),
              //           child: Row(
              //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //             children: <Widget>[
              //               Opacity(
              //                 opacity: widget.flag,
              //                 child: IconButton(
              //                   icon: Icon(Icons.delete_outline),
              //                   color: Colors.white,
              //                   onPressed: () {
              //                     if (widget.flag == 1.0)
              //                       // ignore: curly_braces_in_flow_control_structures
              //                       showTc(
              //                         context,
              //                         title: '确定删除这张照片吗？',
              //                         isClose: false,
              //                         okColor: Theme.of(context).primaryColor,
              //                         onPressed: () async {
              //                           if (widget.images.length == 1) {
              //                             widget.images.removeAt(currentIndex);
              //                           } else {
              //                             widget.images.removeAt(currentIndex);
              //                             setState(() {});
              //                           }
              //                           pop(context, 123);
              //                         },
              //                       );
              //                   },
              //                 ),
              //               ),
              //               MyText(
              //                 "${currentIndex + 1}/${widget.images.length}",
              //                 size: 16,
              //                 color: Colors.white,
              //               ),
              //               IconButton(
              //                 icon: Icon(Icons.close),
              //                 color: Colors.white,
              //                 onPressed: () => pop(context),
              //               ),
              //             ],
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
              // Positioned(
              //   left: 10.w,
              //   bottom: 20.w,
              //   child: Container(
              //     width: ScreenUtil().screenWidth - 20.w,
              //     height: 50.w,
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //       children: [
              //         btnWidget(" 批量下载", "diy_copy.png", width1: 140.w, () {
              //           if (mineLogic.isVIP.value) {
              //             logic.imagesDownload(context, widget.images);
              //           } else {
              //             MyToast.show("请开通会员后再下载");
              //             Get.toNamed(Routes.vipPage)?.then((value) async {
              //               if (value == "1") {
              //                 mineLogic.isVIP.value = UserUtil.getIsVIP();
              //                 mineLogic.isVIP.refresh();
              //               }
              //             });
              //           }
              //         }),
              //         btnWidget(" 单张下载", "diy_copy.png", width1: 140.w, () {
              //           if (mineLogic.isVIP.value) {
              //             logic.imageDownload(
              //                 context, widget.images[currentIndex].toString());
              //           } else {
              //             MyToast.show("请开通会员后再下载");
              //             Get.toNamed(Routes.vipPage)?.then((value) async {
              //               if (value == "1") {
              //                 mineLogic.isVIP.value = UserUtil.getIsVIP();
              //                 mineLogic.isVIP.refresh();
              //               }
              //             });
              //           }
              //         }),
              //       ],
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Container btnWidget(String title, String image, Function()? onTap,
      {double width1 = 110,
      double height1 = 45,
      double imgwidth1 = 18,
      double imgheight1 = 18,
      double radius = 15}) {
    return Container(
      width: width1.w,
      height: height1.w,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        gradient: const LinearGradient(colors: [
          Color(0xffFFC0D0),
          Color(0xffFD94AC),
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        borderRadius: BorderRadius.circular(radius.r),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius.r),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            child: SizedBox(
              width: width1.w,
              height: height1.w,
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  image == ''
                      ? Container()
                      : MyImage(
                          image,
                          isAssetImage: true,
                          width: imgwidth1.w,
                          height: imgheight1.w,
                          fit: BoxFit.contain,
                        ),
                  if (title != "")
                    MyText(
                      title,
                      size: 15.sp,
                      color: Colours.white,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
