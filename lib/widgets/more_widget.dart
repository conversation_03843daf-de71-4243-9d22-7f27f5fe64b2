import 'package:flutter/cupertino.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:ui_packages/ui_packages.dart';

class MoreWidget extends StatelessWidget {
  const MoreWidget(this.itemCount, this.hasMore, this.pageSize, {super.key});

  final int itemCount;
  final bool hasMore;
  final int pageSize;

  @override
  Widget build(BuildContext context) {
    const TextStyle style = TextStyle(color: Colours.dark_text);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (hasMore) const CupertinoActivityIndicator(),
          if (hasMore) Gaps.hGap5,

          /// 只有一页的时候，就不显示FooterView了
          Text(
              hasMore
                  ? S.current.loading
                  : (itemCount < pageSize ? '' : S.current.no_more),
              style: style),
        ],
      ),
    );
  }
}
