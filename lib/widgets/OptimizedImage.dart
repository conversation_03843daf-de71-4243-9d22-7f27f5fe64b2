import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class OptimizedImage extends StatefulWidget {
  final String imageUrl;
  final double width;
  final double height;
  final BoxFit fit;
  final Widget? placeholder;
  final String? cacheKey;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final bool useMemoryOptimizer;
  final double radius; // 新增圆角参数

  const OptimizedImage({
    Key? key,
    required this.imageUrl,
    required this.width,
    required this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.cacheKey,
    this.memCacheWidth,
    this.memCacheHeight,
    this.useMemoryOptimizer = true,
    this.radius = 0.0, // 默认无圆角
  }) : super(key: key);

  @override
  _OptimizedImageState createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage>
    with WidgetsBindingObserver {
  final GlobalKey _imageKey = GlobalKey();
  bool _canCalculateCacheSize = false;
  double? _devicePixelRatio;
  int? _calculatedMemCacheWidth;
  int? _calculatedMemCacheHeight;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 初始化不依赖 context 的状态
    if (widget.cacheKey != null) {
      _calculatedMemCacheWidth = widget.memCacheWidth;
      _calculatedMemCacheHeight = widget.memCacheHeight;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 检查是否准备好计算缓存尺寸
    final mediaQuery = MediaQuery.maybeOf(context);
    if (mediaQuery != null) {
      _devicePixelRatio = mediaQuery.devicePixelRatio;
      _canCalculateCacheSize = true;

      // 只有在可以使用时才计算缓存尺寸
      if (widget.useMemoryOptimizer && widget.cacheKey == null) {
        _calculateCacheSizes();
      }
    }
  }

  // 安全计算缓存尺寸
  void _calculateCacheSizes() {
    if (!_canCalculateCacheSize || _devicePixelRatio == null) {
      return;
    }

    // 计算内存缓存尺寸
    _calculatedMemCacheWidth = (widget.memCacheWidth ??
            (widget.width * _devicePixelRatio!).clamp(0, 400))
        .toInt();
    _calculatedMemCacheHeight = (widget.memCacheHeight ??
            (widget.height * _devicePixelRatio!).clamp(0, 400))
        .toInt();

    // 限制缓存尺寸最大值
    const maxSize = 2048;
    if (_calculatedMemCacheWidth! > maxSize ||
        _calculatedMemCacheHeight! > maxSize) {
      final ratio = widget.width / widget.height;
      if (_calculatedMemCacheWidth! > maxSize) {
        _calculatedMemCacheWidth = maxSize;
        _calculatedMemCacheHeight = (maxSize / ratio).toInt();
      } else {
        _calculatedMemCacheHeight = maxSize;
        _calculatedMemCacheWidth = (maxSize * ratio).toInt();
      }
    }
  }

  // 构建占位符
  Widget _buildPlaceholder() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.radius),
      child: widget.placeholder ??
          Image.asset(
            'assets/images/error_image.png',
            fit: BoxFit.cover,
            width: widget.width,
            height: widget.height,
          ),
    );
  }

  // 构建图像 (核心构建方法)
  Widget _buildImage() {
    if (widget.imageUrl.isEmpty) {
      return _buildPlaceholder();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.radius),
      child: CachedNetworkImage(
        key: _imageKey,
        imageUrl: widget.imageUrl,
        cacheKey: widget.cacheKey,
        memCacheWidth: _calculatedMemCacheWidth,
        memCacheHeight: _calculatedMemCacheHeight,
        fadeInDuration: const Duration(milliseconds: 200),
        fadeOutDuration: const Duration(milliseconds: 100),
        fit: widget.fit,
        width: widget.width,
        height: widget.height,
        placeholder: (context, url) => _buildPlaceholder(),
        errorWidget: (context, url, error) => _buildPlaceholder(),
      ),
    );
  }

  // 优化的圆角渲染（可选高级实现）
  Widget _buildImageWithRRect() {
    if (widget.imageUrl.isEmpty) {
      return _buildPlaceholder();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.radius),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Stack(
            fit: StackFit.expand,
            children: [
              Positioned.fill(
                child: CachedNetworkImage(
                  key: _imageKey,
                  imageUrl: widget.imageUrl,
                  cacheKey: widget.cacheKey,
                  memCacheWidth: _calculatedMemCacheWidth,
                  memCacheHeight: _calculatedMemCacheHeight,
                  fadeInDuration: const Duration(milliseconds: 200),
                  fadeOutDuration: const Duration(milliseconds: 100),
                  fit: widget.fit,
                  placeholder: (context, url) => _buildPlaceholder(),
                  errorWidget: (context, url, error) => _buildPlaceholder(),
                ),
              ),
              // 添加边框（可选）
              if (widget.radius > 0)
                IgnorePointer(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(widget.radius),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果第一次构建时没有 MediaQuery，现在尝试获取
    if (!_canCalculateCacheSize && MediaQuery.maybeOf(context) != null) {
      _devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
      _canCalculateCacheSize = true;

      if (widget.useMemoryOptimizer && widget.cacheKey == null) {
        _calculateCacheSizes();
      }
    }

    return _buildImage(); // 使用基础版或高级版都可
  }
}
