// ignore_for_file: use_build_context_synchronously, avoid_print, prefer_interpolation_to_compose_strings, unrelated_type_equality_checks
import 'dart:developer';
import 'dart:io';
import 'package:android_package_installer/android_package_installer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ota_update/ota_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/version_model.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:utils_package/utils_package.dart';

import '../generated/l10n.dart';

//更新版本
class UpdateVersion {
  static ProgressDialog? progressDialog;
  static String donwLoadUrl = "";
  //获得基础数据
  static VersionModel? versionModel;
  static String? versionname;

  ///获取版本
  static getCanVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    log("versioncode2=${Platform.isAndroid ? packageInfo.buildNumber : packageInfo.buildNumber}");
    //Loading.show("检测版本中...");
    var param = {
      'clientType': GetPlatform.isAndroid ? "Android" : "iOS",
    };
    final res = await Api().get(ApiUrl.commonRelease, queryParameters: param);
    if (res.isSuccessful()) {
      versionModel = VersionModel.fromJson(res.data);
    } else {
      WxLoading.showToast(res.message);
    }
    String version = packageInfo.version; //app版本信息
    if (versionModel?.versionCode == "") {
      return false;
    }
    String ver = versionModel?.versionCode ?? "1.0.1";
    version = version.replaceAll('.', '');
    ver = ver.replaceAll('.', '');
    ver = ver.replaceAll('V', '');
    ver = ver.replaceAll('v', '');
    int aa = int.parse(version);
    int bb = int.parse(ver);
    if (aa >= bb) {
      return false;
    }
    return true;
  }

  ///获取版本
  static getVersion(BuildContext context, {int type = 0}) async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    progressDialog ??= ProgressDialog(
      context,
      type: ProgressDialogType.download,
    );
    // progressDialog?.update(progress: double.parse("0.81"), message: "下载中，请稍后…");
    // progressDialog?.show();
    log("versioncode2=${Platform.isAndroid ? packageInfo.buildNumber : packageInfo.buildNumber}");
    //Loading.show("检测版本中...");
    var param = {
      'clientType': GetPlatform.isAndroid ? "Android" : "iOS",
    };
    if (type != 0) WxLoading.show();
    final res = await Api().get(ApiUrl.commonRelease, queryParameters: param);

    log("versioncode22=${res.data}");
    if (type != 0) WxLoading.dismiss();
    if (res.isSuccessful()) {
      versionModel = VersionModel.fromJson(res.data);
    } else {
      WxLoading.showToast(res.message);
    }
    String version = packageInfo.version; //app版本信息
    if (versionModel?.versionCode == "") {
      if (type == 1) {
        WxLoading.showToast(S.current.is_latest_version);
      }
      return;
    }
    String ver = versionModel?.versionCode ?? "1.0.1";
    version = version.replaceAll('.', '');
    ver = ver.replaceAll('.', '');
    ver = ver.replaceAll('V', '');
    ver = ver.replaceAll('v', '');
    int aa = int.parse(version);
    int bb = int.parse(ver);
    log("versioncode222=${aa}--${bb}");
    if (aa >= bb) {
      if (type == 1) {
        WxLoading.showToast(S.current.is_latest_version);
      }
      return;
    }
    versionname = ver;
    donwLoadUrl = versionModel?.downloadUrl ?? '';
    _checkVersionCode(
        versionModel?.updateType ?? 0, // updateType  0 普通升级；1 强制升级
        versionModel?.versionName,
        versionModel?.releaseNote,
        context);
  }

//检查版本更新的版本号
  static _checkVersionCode(int? va, String? versionName, String? upgradeInfo,
      BuildContext context) async {
    showVersionAppDialog(va ?? 0, versionName, upgradeInfo, context);
  }

//弹出"版本更新"对话框
  static showVersionAppDialog(int? va, String? versionName, String? upgradeInfo,
      BuildContext context) async {
    return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              print('返回了');
              return false;
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
              child: Material(
                type: MaterialType.transparency,
                color: Colors.transparent,
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          color: Colors.transparent,
                          child: Column(
                            children: <Widget>[
                              //upload_top_img
                              Container(
                                height: 136.w,
                                width: double.infinity,
                                margin: EdgeInsets.only(top: 20.w),
                                padding: EdgeInsets.only(left: 25.w),
                                decoration: BoxDecoration(
                                    color: Colours.color191921,
                                    borderRadius: BorderRadius.circular(24.w)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 30.w,
                                        ),
                                        MyImage(
                                          "updateversiontitle.png",
                                          width: 135.w,
                                          height: 45.w,
                                          isAssetImage: true,
                                          fit: BoxFit.fitWidth,
                                          bgColor: Colors.transparent,
                                        ),
                                        Transform.translate(
                                          offset: Offset(0, -10.w),
                                          child: Text(
                                            "V$versionName",
                                            style: TextStyle(
                                                fontSize: 18.sp,
                                                color: Colours.colorA44EFF,
                                                fontWeight: FontWeight.w700),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        )
                                      ],
                                    ),
                                    Transform.translate(
                                      offset: Offset(0, -20.w),
                                      child: MyImage(
                                        "updateversion1.png",
                                        width: 114.w,
                                        height: 136.w,
                                        isAssetImage: true,
                                        fit: BoxFit.fitWidth,
                                        bgColor: Colors.transparent,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              Transform.translate(
                                offset: const Offset(0, -30),
                                child: Container(
                                  alignment: Alignment.topLeft,
                                  constraints: BoxConstraints(
                                    maxHeight: va == 0 ? 330.w : 260.w,
                                    minHeight: 235.w,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colours.color191921,
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(25.r),
                                      bottomRight: Radius.circular(25.r),
                                    ),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 18, vertical: 2),
                                  width: double.infinity,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: <Widget>[
                                      // const SizedBox(
                                      //   height: 22,
                                      // ),
                                      // Text(
                                      //   "更新内容:",
                                      //   style: TextStyle(
                                      //       fontSize: 14.sp,
                                      //       color: const Color(0xff333333),
                                      //       fontWeight: FontWeight.w600),
                                      //   maxLines: 2,
                                      //   overflow: TextOverflow.ellipsis,
                                      // ),
                                      const SizedBox(
                                        height: 14,
                                      ),
                                      Container(
                                        height: 70.w,
                                        constraints: BoxConstraints(
                                          maxHeight: 200.w,
                                          minHeight: 150.w,
                                        ),
                                        child: ListView(
                                          children: [
                                            Text(
                                              (upgradeInfo ??
                                                  ""), //"1.修复了一些BUG;\n2.更新部分内容",//
                                              //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                              style: TextStyles.regular
                                                  .copyWith(
                                                      color:
                                                          Colours.color9393A5,
                                                      fontSize: 14.sp),
                                              maxLines: 20,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 21,
                                      ),
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          _launch();
                                          if (va == 0) {
                                            Navigator.of(context).pop();
                                          }
                                        },
                                        child: Container(
                                          height: 46.w,
                                          width: double.infinity,
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.only(right: 10.w),
                                          padding: EdgeInsets.only(
                                              left: 5.w,
                                              right: 5.w,
                                              top: 3.w,
                                              bottom: 3.w),
                                          decoration: BoxDecoration(
                                            color: Colours.color282735,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(28.r)),
                                            gradient: const LinearGradient(
                                              colors: [
                                                Colours.color7732ED,
                                                Colours.colorA555EF
                                              ],
                                              begin: Alignment.bottomLeft,
                                              end: Alignment.bottomRight,
                                            ),
                                          ),
                                          child: Text(
                                            S.current.Upgrade_now,
                                            style: TextStyles.titleMedium18
                                                .copyWith(fontSize: 15.sp),
                                          ),
                                        ),
                                      ),
                                      if (va == 0)
                                        GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: () {
                                            Navigator.of(context).pop();
                                          },
                                          child: Container(
                                            height: 46.w,
                                            width: double.infinity,
                                            alignment: Alignment.center,
                                            margin: EdgeInsets.only(
                                                right: 10.w, top: 15.w),
                                            padding: EdgeInsets.only(
                                                left: 5.w,
                                                right: 5.w,
                                                top: 3.w,
                                                bottom: 3.w),
                                            decoration: BoxDecoration(
                                              color: Colours.color22222D,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(28.r)),
                                            ),
                                            child: Text(
                                              S.current.No_upgrade,
                                              style: TextStyles.titleMedium18
                                                  .copyWith(
                                                      fontSize: 15.sp,
                                                      color:
                                                          Colours.color9393A5),
                                            ),
                                          ),
                                        ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  static _launch() async {
    if (Platform.isIOS) {
      String url =
          "https://itunes.apple.com/cn/app/id6739193119"; // id 后面的数字换成自己的应用
      // ignore: deprecated_member_use
      if (await canLaunch(url)) {
        // ignore: deprecated_member_use
        await launch(url, forceSafariVC: true);
      } else {
        throw 'Could not launch $url';
      }
    } else {
      checkPermission();
    }
  }

  static Future checkPermission() async {
    // 申请权限
    final storage = await WxPermissionUtils.storage();
    if (storage) {
//             MyToast.show("权限申请通过");
      // donwLoadUrl='https://img.inengjian.com/2020-08-07/1-20200807180436.mp4';
      executeDownload();
    } else {
      if (await canLaunchUrl(Uri(path: donwLoadUrl))) {
        // ignore: deprecated_member_use
        await launch(donwLoadUrl, forceSafariVC: true);
      } else {
        WxLoading.showToast("获取权限失败,请到商店完成更新");
      }
    }
  }

  // 获取安装地址
  static Future<String> get _apkLocalPath async {
    final directory = await getExternalStorageDirectory();
    return directory?.path ?? "";

    // Directory storageDir = await getExternalStorageDirectory();
    // String storagePath = storageDir.path;
    // File file = File('$storagePath/jsrmt.apk');
  }

//下载
  static Future<void> executeDownload() async {
    // WxLoading.showToast("开始下载，请稍后...");
    // WxLoading.show();
    final path = (await _apkLocalPath) + '/Download';
    final savedDir = Directory(path);
    // 判断下载路径是否存在
    bool hasExisted = await savedDir.exists();
    // 不存在就新建路径
    if (!hasExisted) {
      savedDir.create();
    }
//    //下载
//    final taskId = await FlutterDownloader.enqueue(
//        url: donwLoadUrl,
//        savedDir: path,
//        showNotification: true,
//        openFileFromNotification: true);
//    print('taskId = $taskId');

    // 获取APP安装路径
    Directory appDocDir = await getApplicationDocumentsDirectory();
    String appDocPath = appDocDir.path;
    print('获取到的路径==' + appDocPath);

    try {
      OtaUpdate()
          .execute(donwLoadUrl,
              destinationFilename: 'shootz_for_android$versionname.apk')
          .listen(
        (OtaEvent event) {
          print('urllll=' + donwLoadUrl);
          //print('status:${event.status},value:${event.value}');

          switch (event.status) {
            case OtaStatus.DOWNLOADING: // 下载中
              print('OtaEvent下载中');
              progressDialog?.update(
                  progress: double.parse(event.value ?? "0.1"),
                  message: "下载中，请稍后…");
              progressDialog?.show();
              break;
            case OtaStatus.INSTALLING: //安装中
              if (progressDialog != null && progressDialog!.isShowing())
                progressDialog?.hide();
              print('-----安装中----');
              // 打开安装文件
              //这里的这个Apk文件名可以写，也可以不写
              //不写的话会出现让你选择用浏览器打开，点击取消就好了，写了后会直接打开当前目录下的Apk文件，名字随便定就可以
              // OpenFile.open("${appDocPath}/.apk");

              //_installApk("${appDocPath}/shootz_for_android.apk");
              break;
            case OtaStatus.PERMISSION_NOT_GRANTED_ERROR: // 权限错误
              print('更新失败，请稍后再试');
              if (progressDialog!.isShowing()) progressDialog?.hide();

              break;
            default: // 其他问题
              print('OtaEvent其他问题');
              if (progressDialog!.isShowing()) progressDialog?.hide();
              break;
          }
        },
      );
    } catch (e) {
      print('更新失败，请稍后再试');
      WxLoading.showToast("更新失败，请稍后再试");
    }
  }

// 安装
  static Future<Null> _installApk(String path) async {
    print('_installApk路径===' + path);
    // InstallPlugin.installApk(path, 'com.shootZ.app.shoot_z').then((result) {
    //   print('install apk $result');
    // }).catchError((error) {
    //   print('install apk error: $error');
    // });

    int? statusCode =
        await AndroidPackageInstaller.installApk(apkFilePath: path);
    print('_installApk路径2===${statusCode}');
    if (statusCode != null) {
      PackageInstallerStatus installationStatus =
          PackageInstallerStatus.byCode(statusCode);
      print(installationStatus.name);
    }
  }
}
