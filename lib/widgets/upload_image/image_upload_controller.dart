// controllers/image_upload_controller.dart
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:dio/dio.dart' as dio;
import 'package:shoot_z/network/api_url.dart';

class ImageUploadController extends GetxController {
  final RxList<String> selectedImagesUrl;
  final int maxImages;

  final ImagePicker _picker = ImagePicker();

  ImageUploadController({required this.selectedImagesUrl, this.maxImages = 3});

  // 获取剩余可上传图片数量
  int get remainingSlots => maxImages - selectedImagesUrl.length;

  // 是否可以继续上传
  bool get canUploadMore => selectedImagesUrl.length < maxImages;

  // 选择图片
  Future<void> pickImage(ImageSource source) async {
    try {
      if (!canUploadMore) {
        Get.snackbar('提示', '最多只能上传$maxImages张图片');
        return;
      }

      final XFile? image = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        WxLoading.show();
        File file = File(image.path);
        String path = file.path;
        var fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        WxLoading.dismiss();
        if (res.isSuccessful()) {
          selectedImagesUrl.add(res.data['path']);
        }
      }
    } catch (e) {
      WxLoading.showToast('图片上传失败');
    }
  }

  // 删除图片
  void removeImage(int index) {
    if (index >= 0 && index < selectedImagesUrl.length) {
      selectedImagesUrl.removeAt(index);
    }
  }

  // 清空所有图片
  void clearAllImages() {
    selectedImagesUrl.clear();
  }

  // 显示选择来源对话框
  void showImageSourceDialog() {
    Get.bottomSheet(
      SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('从相册选择'),
              onTap: () {
                Get.back();
                pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('拍照'),
              onTap: () {
                Get.back();
                pickImage(ImageSource.camera);
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
