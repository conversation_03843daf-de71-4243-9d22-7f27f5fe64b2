// widgets/image_upload_widget.dart
import 'dart:developer';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/widgets/PhotoView.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_controller.dart';
import 'package:ui_packages/ui_packages.dart';

class ImageUploadWidget extends StatelessWidget {
  final RxList<String> selectedImagesUrl;
  final ValueChanged<List<String>>? onImagesChanged;
  final int maxImages;
  final String? tag;

  ImageUploadWidget({
    super.key,
    required this.selectedImagesUrl,
    this.onImagesChanged,
    this.maxImages = 3,
    this.tag,
  });

  late final ImageUploadController controller = Get.put(
      ImageUploadController(
          selectedImagesUrl: selectedImagesUrl, maxImages: maxImages),
      tag: tag);
  @override
  Widget build(BuildContext context) {
    // 监听图片变化
    ever(controller.selectedImagesUrl, (List<String> images) {
      onImagesChanged?.call(images);
    });

    return Obx(() {
      // 计算需要显示的总项目数（图片 + 添加按钮）
      int totalItems = controller.selectedImagesUrl.length;
      if (controller.canUploadMore) {
        totalItems += 1;
      }

      return GridView.builder(
        shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // 每行两个 item
          crossAxisSpacing: 15.w,
          mainAxisSpacing: 15.w,
          childAspectRatio: 150 / 84, // 控制每个 item 的宽高比例
        ),
        itemCount: totalItems,
        padding: EdgeInsets.zero,
        itemBuilder: (context, position) {
          return Obx(() {
            return (position == controller.selectedImagesUrl.length &&
                    controller.canUploadMore)
                ? GestureDetector(
                    onTap: () {
                      showDateDialog(context);
                    },
                    child: WxAssets.images.logoSelectIcon.image(),
                  )
                : _buildImagePreview(controller, position);
          });
        },
      );
    });
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              SizedBox(
                height: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      Get.back();
                      controller.pickImage(ImageSource.camera);
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "拍照",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                      controller.pickImage(ImageSource.gallery);
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "相册",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      alignment: Alignment.center,
                      child: Text(
                        "取消",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 47.w,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildImagePreview(ImageUploadController controller, int index) {
    return GestureDetector(
      onTap: () {
        log("message!!!!${controller.selectedImagesUrl}$index");
        Get.to(
          PhotoView(
            images: controller.selectedImagesUrl,
            index: index,
            flag: 1,
          ),
        );
      },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            width: double.infinity,
            height: 84.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              image: DecorationImage(
                image: CachedNetworkImageProvider(
                    controller.selectedImagesUrl[index]),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: -8.w,
            right: -8.w,
            child: GestureDetector(
              onTap: () => controller.removeImage(index),
              child: WxAssets.images.imageDeleteIcon.image(),
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePreview(String imagePath) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.file(
                  File(imagePath),
                  fit: BoxFit.contain,
                ),
              ),
            ),
            Positioned(
              top: 40.w,
              right: 20.w,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24.w,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
