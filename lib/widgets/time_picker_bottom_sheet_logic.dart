import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TimePickerBottomSheetLogic extends GetxController {
  FixedExtentScrollController hourController = FixedExtentScrollController();
  FixedExtentScrollController minuteController = FixedExtentScrollController();

  var hours = <String>[
    for (int i = 1; i <= 24; i++) '${i.toString().padLeft(2, '0')}时'
  ].obs;
  var minutes = <String>[
    for (int i = 0; i <= 59; i++) '${i.toString().padLeft(2, '0')}分'
  ].obs;
  var selectedHourIndex = 0.obs;
  var selectedMinuteIndex = 0.obs;
  var saveSelectHourIndex = 0.obs;
  var saveSelectMinuteIndex = 0.obs;
  var selectedHour = '10时'.obs;
  var selectedMinute = '02分'.obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void dispose() {
    hourController.dispose();
    minuteController.dispose();
    super.dispose();
  }
}
