import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';

/// 带有右上角图标的文本组件
class TextWithIcon extends StatelessWidget {
  /// 文本内容
  final String title;

  /// 文本样式
  final TextStyle? textStyle;

  /// 右上角图标
  final Widget? icon;

  /// 图标相对于文本的偏移量
  final Offset iconOffset;

  /// 图标大小
  final Size? iconSize;

  /// 文本对齐方式
  final TextAlign? textAlign;

  /// 最大行数
  final int? maxLines;

  /// 文本溢出处理
  final TextOverflow? overflow;

  const TextWithIcon({
    super.key,
    required this.title,
    this.icon,
    this.textStyle,
    this.iconOffset = const Offset(-6, 3),
    this.iconSize,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // 右上角图标
        Positioned(
          top: iconOffset.dy,
          right: iconOffset.dx,
          child: iconSize != null
              ? SizedBox(
                  width: iconSize!.width,
                  height: iconSize!.height,
                  child: icon,
                )
              : icon != null
                  ? icon!
                  : WxAssets.images.indicatorIcon.image(),
        ),
        // 主文本
        Text(
          title,
          style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        ),
      ],
    );
  }
}

/// 预设样式的文本图标组件
class TextWithBadge extends StatelessWidget {
  /// 文本内容
  final String title;

  /// 文本样式
  final TextStyle? textStyle;

  /// 徽章图标（通常是小圆点或数字）
  final Widget badge;

  /// 徽章相对于文本的偏移量
  final Offset badgeOffset;

  const TextWithBadge({
    super.key,
    required this.title,
    required this.badge,
    this.textStyle,
    this.badgeOffset = const Offset(-2, -2),
  });

  @override
  Widget build(BuildContext context) {
    return TextWithIcon(
      title: title,
      icon: badge,
      textStyle: textStyle,
      iconOffset: badgeOffset,
    );
  }
}

/// 带有数字徽章的文本组件
class TextWithNumberBadge extends StatelessWidget {
  /// 文本内容
  final String title;

  /// 文本样式
  final TextStyle? textStyle;

  /// 徽章数字
  final int number;

  /// 徽章背景颜色
  final Color badgeColor;

  /// 徽章文字颜色
  final Color badgeTextColor;

  /// 徽章大小
  final double badgeSize;

  /// 徽章相对于文本的偏移量
  final Offset badgeOffset;

  const TextWithNumberBadge({
    super.key,
    required this.title,
    required this.number,
    this.textStyle,
    this.badgeColor = Colors.red,
    this.badgeTextColor = Colors.white,
    this.badgeSize = 16.0,
    this.badgeOffset = const Offset(-2, -2),
  });

  @override
  Widget build(BuildContext context) {
    return TextWithIcon(
      title: title,
      textStyle: textStyle,
      iconOffset: badgeOffset,
      icon: Container(
        width: badgeSize,
        height: badgeSize,
        decoration: BoxDecoration(
          color: badgeColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            number > 99 ? '99+' : number.toString(),
            style: TextStyle(
              color: badgeTextColor,
              fontSize: (badgeSize * 0.6).sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}

/// 带有红点徽章的文本组件
class TextWithDotBadge extends StatelessWidget {
  /// 文本内容
  final String title;

  /// 文本样式
  final TextStyle? textStyle;

  /// 红点颜色
  final Color dotColor;

  /// 红点大小
  final double dotSize;

  /// 红点相对于文本的偏移量
  final Offset dotOffset;

  const TextWithDotBadge({
    super.key,
    required this.title,
    this.textStyle,
    this.dotColor = Colors.red,
    this.dotSize = 8.0,
    this.dotOffset = const Offset(-2, -2),
  });

  @override
  Widget build(BuildContext context) {
    return TextWithIcon(
      title: title,
      textStyle: textStyle,
      iconOffset: dotOffset,
      icon: Container(
        width: dotSize,
        height: dotSize,
        decoration: BoxDecoration(
          color: dotColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
