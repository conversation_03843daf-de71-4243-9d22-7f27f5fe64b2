import 'dart:async';
import 'package:flutter/material.dart';

class GlobalFloatingButton extends StatefulWidget {
  const GlobalFloatingButton({super.key});

  @override
  State<GlobalFloatingButton> createState() => _GlobalFloatingButtonState();
}

class _GlobalFloatingButtonState extends State<GlobalFloatingButton>
    with SingleTickerProviderStateMixin {
  // 按钮位置
  Offset _position = Offset.zero;

  // 按钮尺寸
  final double _buttonSize = 60.0;

  // 状态标志
  bool _isDocked = false;
  bool _isFullyVisible = true;

  // 未操作计时器
  Timer? _inactivityTimer;

  // 动画控制器
  late AnimationController _animationController;
  late Animation<Offset> _positionAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // 重置计时器
    _resetInactivityTimer();

    // 延迟设置初始位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final screenSize = MediaQuery.of(context).size;
        setState(() {
          _position = Offset(
            screenSize.width - _buttonSize - 16,
            screenSize.height - _buttonSize - 16,
          );
        });
      }
    });
  }

  @override
  void dispose() {
    _inactivityTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  // 重置未操作计时器
  void _resetInactivityTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(const Duration(seconds: 10), () {
      if (!mounted) return;
      if (_isFullyVisible) {
        _dockToCorner();
      }
    });
  }

  // 吸附到边角
  void _dockToCorner() {
    if (!mounted) return;

    final screenSize = MediaQuery.of(context).size;
    final corner = _findNearestCorner(screenSize);

    // 创建动画
    _positionAnimation = Tween<Offset>(
      begin: _position,
      end: corner,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _animationController.reset();
    _animationController.forward().then((_) {
      if (!mounted) return;
      setState(() {
        _isDocked = true;
        _isFullyVisible = false;
      });
    });
  }

  // 查找最近的边角
  Offset _findNearestCorner(Size screenSize) {
    final corners = [
      const Offset(0, 0),
      Offset(screenSize.width - _buttonSize / 2, 0),
      Offset(0, screenSize.height - _buttonSize / 2),
      Offset(screenSize.width - _buttonSize / 2,
          screenSize.height - _buttonSize / 2),
    ];

    Offset nearest = corners.first;
    double minDistance = double.infinity;

    for (final corner in corners) {
      final distance = (_position - corner).distance;
      if (distance < minDistance) {
        minDistance = distance;
        nearest = corner;
      }
    }

    return nearest;
  }

  // 完全显示按钮
  void _showFully() {
    if (!mounted) return;

    final screenSize = MediaQuery.of(context).size;
    final targetPosition = Offset(
      screenSize.width - _buttonSize - 16,
      screenSize.height - _buttonSize - 16,
    );

    // 创建动画
    _positionAnimation = Tween<Offset>(
      begin: _position,
      end: targetPosition,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _animationController.reset();
    _animationController.forward().then((_) {
      if (!mounted) return;
      setState(() {
        _isFullyVisible = true;
        _isDocked = false;
      });
    });

    // 重置计时器
    _resetInactivityTimer();
  }

  // 处理拖动
  void _handleDragUpdate(DragUpdateDetails details) {
    if (!mounted) return;

    final screenSize = MediaQuery.of(context).size;

    setState(() {
      _position = Offset(
        (_position.dx + details.delta.dx)
            .clamp(0.0, screenSize.width - _buttonSize),
        (_position.dy + details.delta.dy)
            .clamp(0.0, screenSize.height - _buttonSize),
      );
    });

    _resetInactivityTimer();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final currentPosition = _animationController.isAnimating
            ? _positionAnimation.value
            : _position;

        return Positioned(
          left: currentPosition.dx,
          top: currentPosition.dy,
          child: GestureDetector(
            onPanUpdate: _handleDragUpdate,
            onTap: () {
              if (_isDocked) {
                _showFully();
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('全局按钮被点击')),
                );
                _resetInactivityTimer();
              }
            },
            child: Container(
              width: _buttonSize,
              height: _buttonSize,
              decoration: BoxDecoration(
                color: _isDocked ? Colors.blue[700] : Colors.blue,
                borderRadius: BorderRadius.circular(_buttonSize / 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                _isDocked ? Icons.arrow_forward : Icons.touch_app,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        );
      },
    );
  }
}
