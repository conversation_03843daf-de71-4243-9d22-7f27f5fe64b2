import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:ui_packages/ui_packages.dart';

import '../gen/assets.gen.dart';
import '../generated/l10n.dart';
import '../routes/app.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionDialog extends StatelessWidget {
  final String text;
  final String contentDes;
  final String icon;
  final void Function()? onPressed;
  const PermissionDialog(
      {super.key,
      required this.text,
      required this.contentDes,
      required this.icon,
      this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: IntrinsicHeight(
        child: SizedBox(
          width: 315.w,
          child: Stack(children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 53.w,
                ),
                Container(
                  padding: EdgeInsets.only(
                      left: 20.w, right: 20.w, top: 65.w, bottom: 30.w),
                  decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        text,
                        style: TextStyles.semiBold.copyWith(fontSize: 18.sp),
                      ),
                      SizedBox(
                        height: 20.w,
                      ),
                      Padding(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          child: Text(
                            contentDes,
                            style: TextStyles.regular.copyWith(
                                fontSize: 14.sp,
                                color: Colours.color9393A5,
                                height: 1.5),
                            textAlign: TextAlign.center,
                          )),
                      SizedBox(
                        height: 35.w,
                      ),
                      WxButton(
                        text: S.current.open_now,
                        textStyle: TextStyles.regular.copyWith(fontSize: 15.sp),
                        height: 46.w,
                        borderRadius: BorderRadius.circular(23.w),
                        onPressed: () {
                          AppPage.back();
                          if (onPressed == null) {
                            openAppSettings();
                          } else {
                            onPressed!();
                          }
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 30.w,
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    AppPage.back();
                  },
                  child: WxAssets.images.icCloseDialog
                      .image(width: 28.w, height: 28.w, fit: BoxFit.fill),
                ),
              ],
            ),
            Align(
                alignment: Alignment.topCenter,
                child: AssetGenImage('assets/images/$icon.png')
                    .image(width: 100.w, fit: BoxFit.fill)),
          ]),
        ),
      ),
    );
  }
}

void showLocationDialog() {
  Get.dialog(PermissionDialog(
    text: S.current.enable_location_access,
    contentDes: S.current.location_dialog_tips,
    icon: 'dialoglocation',
    onPressed: () =>
        LocationUtils.instance.openSettings("我们需要您的位置信息以提供位置，查看附近球馆信息和个性化服务"),
  ));
}

void showDownloadDialog() {
  Get.dialog(PermissionDialog(
      text: S.current.enable_permissions,
      contentDes: S.current.add_permission_prompt,
      icon: 'ic_photo_permission'));
}
