// ignore_for_file: must_be_immutable

import 'dart:ui' as ui;
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PreloadedImageDotPainter extends FlDotPainter {
  static final Map<String, ui.Image> _imageCache = {};
  static const Size defaultSize = Size(12, 12);

  final String assetPath;
  final double sizeScale;
  final Color fallbackColor;

  late ui.Image _image;
  late Size _imageSize;

  PreloadedImageDotPainter({
    required this.assetPath,
    this.sizeScale = 1.0,
    this.fallbackColor = Colors.blue,
  }) {
    if (_imageCache.containsKey(assetPath)) {
      _image = _imageCache[assetPath]!;
      _imageSize = Size(
        _image.width.toDouble() * sizeScale,
        _image.height.toDouble() * sizeScale,
      );
    } else {
      // 如果没有预加载，使用默认值
      _imageSize = defaultSize;
    }
  }

  /// 预加载图像资源 - 在图表构建前调用
  static Future<void> preloadImage(
    String assetPath, {
    BuildContext? context,
  }) async {
    if (_imageCache.containsKey(assetPath)) return;

    try {
      // 获取图像字节数据
      final byteData = await (context != null
          ? DefaultAssetBundle.of(context).load(assetPath)
          : rootBundle.load(assetPath));

      // 解码图像
      final codec = await ui.instantiateImageCodec(
        byteData.buffer.asUint8List(),
        targetWidth: 24, // 优化图像尺寸
        targetHeight: 24,
      );

      final frame = await codec.getNextFrame();
      _imageCache[assetPath] = frame.image;
    } catch (e) {
      debugPrint('Image preload error for $assetPath: $e');
      throw Exception('Failed to preload image: $assetPath');
    }
  }

  @override
  void draw(Canvas canvas, FlSpot spot, Offset offset) {
    if (_imageCache.containsKey(assetPath)) {
      _image = _imageCache[assetPath]!;

      // 计算缩放比例
      if (sizeScale != 1.0) {
        _imageSize = Size(
          _image.width.toDouble() * sizeScale,
          _image.height.toDouble() * sizeScale,
        );
      } else {
        _imageSize = Size(
          _image.width.toDouble(),
          _image.height.toDouble(),
        );
      }

      // 绘制图像
      final double halfWidth = _imageSize.width / 2;
      final double halfHeight = _imageSize.height / 2;

      canvas.save();
      canvas.translate(offset.dx - halfWidth, offset.dy - halfHeight);

      final paint = Paint()..filterQuality = FilterQuality.high;
      canvas.drawImageRect(
        _image,
        Rect.fromLTWH(0, 0, _image.width.toDouble(), _image.height.toDouble()),
        Rect.fromLTWH(0, 0, _imageSize.width, _imageSize.height),
        paint,
      );

      canvas.restore();
    } else {
      // 如果图像未加载，绘制备用圆形
      canvas.drawCircle(
        offset,
        6, // defaultSize.width / 2 * sizeScale,
        Paint()..color = fallbackColor,
      );
    }
  }

  @override
  Size getSize(FlSpot spot) => _imageSize;

  @override
  PreloadedImageDotPainter lerp(FlDotPainter a, FlDotPainter b, double t) {
    // 简单的线性插值实现
    return b is PreloadedImageDotPainter ? b : this;
  }

  @override
  Color get mainColor => fallbackColor;

  @override
  List<Object?> get props => [assetPath, sizeScale, fallbackColor];

  /// 清除缓存，用于内存优化
  static void clearCache() => _imageCache.clear();
}
