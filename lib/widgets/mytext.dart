import 'package:flutter/material.dart';
import 'package:shoot_z/generated/l10n.dart';

class MyText extends StatefulWidget {
  final String? text;
  final Color? color;
  final bool isBold;
  final double? size;
  final int? maxLines;
  final bool isOverflow;
  final TextAlign? textAlign;
  final List<InlineSpan> children;
  final FontWeight? fontWeight;
  final TextDecoration? decoration;
  final double? letterSpacing;
  final double? wordSpacing;

  const MyText(
    this.text, {
    Key? key,
    this.color = Colors.black87,
    this.isBold = false,
    this.size,
    this.maxLines,
    this.isOverflow = true,
    this.textAlign,
    this.decoration,
    this.children = const [],
    this.fontWeight,
    this.letterSpacing,
    this.wordSpacing,
  }) : super(key: key);
  @override
  // ignore: library_private_types_in_public_api
  _MyTextState createState() => _MyTextState();

  ///textspan的构建
  static TextSpan ts(
    String text, {
    Color? color,
    double? size,
    double? wordSpacing,
    double? letterSpacing,
    bool isBold = false,
    TextDecoration? decoration,
  }) {
    return TextSpan(
      text: text, // ?? '暂无'.
      style: TextStyle(
        color: color,
        fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
        fontSize: size,
        decoration: decoration,
        letterSpacing: letterSpacing,
        wordSpacing: wordSpacing,
      ),
    );
  }
}

class _MyTextState extends State<MyText> {
  @override
  Widget build(BuildContext context) {
    return widget.children.isEmpty ? buildText() : buildTextRich();
  }

  ///纯文本组件
  Text buildText() {
    return Text(
      widget.text ?? S.current.None1,
      maxLines: widget.maxLines,
      overflow: widget.isOverflow ? TextOverflow.ellipsis : null,
      textAlign: widget.textAlign,
      style: TextStyle(
        color: widget.color,
        decoration: widget.decoration ?? TextDecoration.none,
        fontWeight:
            widget.fontWeight ?? (widget.isBold ? FontWeight.bold : null),
        fontSize: widget.size,
        letterSpacing: widget.letterSpacing,
        wordSpacing: widget.wordSpacing,
      ),
    );
  }

  ///TextRich组件
  Text buildTextRich() {
    return Text.rich(
      TextSpan(text: widget.text ?? S.current.None1, children: widget.children),
      maxLines: widget.maxLines,
      overflow: widget.isOverflow ? TextOverflow.ellipsis : null,
      textAlign: widget.textAlign,
      style: TextStyle(
        color: widget.color,
        decoration: widget.decoration ?? TextDecoration.none,
        fontWeight:
            widget.fontWeight ?? (widget.isBold ? FontWeight.bold : null),
        fontSize: widget.size,
        letterSpacing: widget.letterSpacing,
        wordSpacing: widget.wordSpacing,
      ),
    );
  }
}
