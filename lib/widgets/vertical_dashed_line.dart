import 'package:flutter/material.dart';

class VerticalDashedLine extends StatelessWidget {
  final double height; // 虚线总高度
  final double dashHeight; // 单个虚线段高度
  final double dashSpacing; // 虚线段之间的间距
  final Color color; // 虚线颜色

  const VerticalDashedLine({
    Key? key,
    required this.height,
    this.dashHeight = 5.0,
    this.dashSpacing = 3.0,
    this.color = Colors.black,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: CustomPaint(
        size: Size(1, height), // 宽度固定为 1，高度为传入的值
        painter: _DashedLinePainter(
          dashHeight: dashHeight,
          dashSpacing: dashSpacing,
          color: color,
        ),
      ),
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  final double dashHeight; // 单个虚线段高度
  final double dashSpacing; // 虚线段之间的间距
  final Color color; // 虚线颜色

  _DashedLinePainter({
    required this.dashHeight,
    required this.dashSpacing,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = size.width
      ..strokeCap = StrokeCap.round;

    double startY = 0.0; // 开始绘制的 Y 坐标
    while (startY < size.height) {
      final endY = (startY + dashHeight).clamp(0.0, size.height); // 确保不会超出边界
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, endY),
        paint,
      );
      startY += dashHeight + dashSpacing; // 更新到下一个虚线段
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // 因为虚线的属性不会动态更新，所以无需重绘
  }
}