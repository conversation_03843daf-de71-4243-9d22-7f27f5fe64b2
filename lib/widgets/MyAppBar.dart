import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/routes/app.dart';

class MyAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title; // 国际化 key，例如 S.current.home_title
  final Widget? leading;
  final List<Widget>? actions;
  final double? leadingWidth;

  const MyAppBar({
    Key? key,
    this.title,
    this.leading,
    this.actions,
    this.leadingWidth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: leading ??
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              size: 20.w,
            ),
            onPressed: () {
              AppPage.back();
            },
          ),
      title: title,
      leadingWidth: leadingWidth,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
