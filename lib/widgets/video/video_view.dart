import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/main.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:video_player/video_player.dart';

class VideoView extends StatefulWidget {
  final VideoController controller;
  const VideoView({super.key, required this.controller});

  @override
  State<VideoView> createState() => _VideoViewState();
}

class _VideoViewState extends State<VideoView> with RouteAware {
  @override
  Widget build(BuildContext context) {
    debug('_VideoViewState build');
    subscribe(context);
    return Obx(
      () {
        final videoPath = widget.controller.networkUrl.value;
        if (videoPath.isEmpty) return const SizedBox();
        return widget.controller.videoCover == null
            ? _noVideoCoverWidget()
            : _videoWidget();
      },
    );
  }

  Widget _noVideoCoverWidget() {
    return Container(
      color: Colors.black,
      child: widget.controller.chewieController.value == null
          ? (() {
              if (!widget.controller.isInitializing) {
                widget.controller.initVideo();
              }
              return const Center(
                key: ValueKey('loading'),
                child: CircularProgressIndicator(),
              );
            })()
          : widget.controller.highlightVideoId == null
              ? Chewie(
                  key: const ValueKey('video'),
                  controller: widget.controller.chewieController.value!,
                )
              : Stack(children: [
                  Chewie(
                    controller: widget.controller.chewieController.value!,
                  ),
                  Positioned(
                    left: 0,
                    bottom: 42.w,
                    child: _videoIdWidget(),
                  )
                ]),
    );
  }

  Widget _videoWidget() {
    return widget.controller.chewieController.value == null
        ? (() {
            if (!widget.controller.isInitializing) {
              widget.controller.initVideo();
            }

            return Stack(children: [
              Positioned.fill(
                child: widget.controller.videoCover!.startsWith('http')
                    ? CachedNetworkImage(
                        imageUrl: widget.controller.videoCover!,
                        fit: BoxFit.cover,
                      )
                    : AssetGenImage(
                            'assets/images/${widget.controller.videoCover!}.png')
                        .image(fit: BoxFit.cover),
              ),
              const Center(child: CircularProgressIndicator()),
            ]);
          })()
        : Stack(children: [
            Chewie(
              controller: widget.controller.chewieController.value!,
            ),
            if (widget.controller.highlightVideoId != null)
              Positioned(
                left: 0,
                bottom: 42.w,
                child: _videoIdWidget(),
              ),
            ValueListenableBuilder<VideoPlayerValue>(
              valueListenable: widget.controller.videoPlayerController!,
              builder: (context, VideoPlayerValue value, child) {
                return (value.isBuffering || value.position == Duration.zero)
                    ? Stack(children: [
                        Positioned.fill(
                          child: widget.controller.videoCover!
                                  .startsWith('http')
                              ? CachedNetworkImage(
                                  imageUrl: widget.controller.videoCover!,
                                  fit: BoxFit.cover,
                                )
                              : AssetGenImage(
                                      'assets/images/${widget.controller.videoCover!}.png')
                                  .image(fit: BoxFit.cover),
                        ),
                        const Center(child: CircularProgressIndicator()),
                      ])
                    : const SizedBox.shrink();
              },
            ),
          ]);
  }

  Widget _videoIdWidget() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: Colours.color5E000000,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(2.w),
              bottomRight: Radius.circular(2.w))),
      child: Text(
        '视频ID：${widget.controller.highlightVideoId ?? ''}',
        style: TextStyles.semiBold.copyWith(fontSize: 10.sp),
      ),
    );
  }

  var isSubscribe = false;
  void subscribe(BuildContext context) {
    if (isSubscribe) return;
    final ModalRoute? route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
      isSubscribe = true;
    }
  }

  @override
  void didPushNext() {
    debug('didPushNext');
    if (widget.controller.pushDisposeOnAndroid && Platform.isAndroid) {
      if (!widget.controller.toFullScreen) {
        widget.controller.dispose(savePosition: true);
      }
    }
    if (!widget.controller.toFullScreen) {
      widget.controller.pause();
    } else {
      widget.controller.fromFullScreen = true;
    }
    super.didPushNext();
  }

  @override
  void didPopNext() {
    if (widget.controller.pushDisposeOnAndroid && Platform.isAndroid) {
      if (!widget.controller.fromFullScreen) {
        widget.controller.reinitialize();
      }
    }

    if (!widget.controller.fromFullScreen) {
      widget.controller.play();
    } else {
      widget.controller.fromFullScreen = false;
    }
    super.didPopNext();
  }

  @override
  void dispose() {
    // 确保在销毁时清理资源
    if (mounted) {
      widget.controller.pause();
    }
    routeObserver.unsubscribe(this);
    super.dispose();
  }
}
