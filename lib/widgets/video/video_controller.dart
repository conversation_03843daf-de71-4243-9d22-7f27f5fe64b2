import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:video_player/video_player.dart';

class VideoController {
  final chewieController = Rx<ChewieController?>(null);
  VideoPlayerController? videoPlayerController;
  final bool autoPlay;
  final bool looping;
  final RxString networkUrl;
  String? videoCover;
  bool watermark;
  bool pushDisposeOnAndroid;//安卓设备上push到新页面时是否销毁播放器

  var fromFullScreen = false;//用于didpop判断是否从全屏页返回

  bool get toFullScreen {//用于didPushNext判断是否是去全屏页
    return chewieController.value?.isFullScreen ?? false;
  }

  String? get dataSource {
    return chewieController.value?.videoPlayerController.dataSource;
  }

  VideoController(
      {String videoPath = '',
      this.autoPlay = true,
      this.looping = true,
      this.videoCover,
      this.watermark = false,
      this.pushDisposeOnAndroid = false})
      : networkUrl = RxString(videoPath);

  void setData({required String videoPath, String? videoCover,bool? showWatermark}) {
    networkUrl.value = videoPath;
    this.videoCover = videoCover;
    if(showWatermark != null) {
      watermark = showWatermark;
    }
    dispose();
  }

  void showWatermark(bool show) async {
    if(networkUrl.isEmpty) {
      watermark = show;
      return;
    }
    if (show != watermark) {
        watermark = show;
        final position = videoPlayerController?.value.position;
        if(_isInitializing) {//reinitialize方法正在初始化initVideo
          return;
        }
        dispose();
        // 添加标记，防止 VideoView 重复初始化
        _isInitializing = true;
        try {
            await initVideo(startAt: position);
        } finally {
            _isInitializing = false;
        }
    }
  }

  bool _isInitializing = false;
  bool get isInitializing => _isInitializing;
  Duration? position;
  void dispose({bool savePosition = false}) {
    debug('dispose---');
    if(savePosition) {
      position = videoPlayerController?.value.position;
      _isInitializing = true;
    }
    videoPlayerController?.pause();
    videoPlayerController?.dispose();
    chewieController.value?.dispose();
    chewieController.value = null;
    videoPlayerController = null;
  }

  void reinitialize() async {
  if (networkUrl.value.isNotEmpty) {
    await initVideo(startAt: position);
    _isInitializing = false;
  }
}

  Future<void> initVideo({Duration? startAt}) async {
    if(isPause) {
      debug('not initVideo isPause');
      return;
    }
    try {
        final videoPath = networkUrl.value;
        if (videoPath.isEmpty) return;

        if (chewieController.value != null &&
            chewieController.value?.videoPlayerController.dataSource == videoPath) {
          return;
        }
        debug(videoPath);
        dispose();
        videoPlayerController =
            VideoPlayerController.networkUrl(Uri.parse(videoPath));
        await videoPlayerController?.initialize();
        debug('videoPlayerController?.initialize()');
        final chewieControllerValue = ChewieController(
          videoPlayerController: videoPlayerController!,
          autoPlay: !isPause,
          looping: true,
          startAt: startAt,  // 动态设置开始位置
          aspectRatio: videoPlayerController!.value.aspectRatio,
          allowedScreenSleep: false,
          showControlsOnInitialize: false,
          overlay: watermark
              ? WxAssets.images.watermark.image(
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.fill,
                )
              : null,
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Text(
                errorMessage,
                style: const TextStyle(color: Colors.white),
              ),
            );
          },
        );
        if(isPause) {
          debug('videoPlayerController?.initialize() isPause');
          videoPlayerController?.pause();
        }
        chewieController.value = chewieControllerValue;
    } catch (e) {
      debug('Video initialization error: $e');
        dispose();
    }
  }
  var isPause = false;
  void pause() {
    isPause = true;
    videoPlayerController?.pause();
  }

  void play() {
    isPause = false;
    videoPlayerController?.play();
  }
}
