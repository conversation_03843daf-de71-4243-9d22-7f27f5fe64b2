import 'dart:developer' as cc;
import 'package:amap_flutter_base/amap_flutter_base.dart';
import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/const_config.dart';
import 'package:shoot_z/widgets/select_location/select_location_logic.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///更多比赛
class SelectLocationPage extends StatelessWidget {
  SelectLocationPage({super.key});

  final logic = Get.put(SelectLocationLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SizedBox(
          height: double.infinity,
          child: Obx(() {
            return Stack(
              children: [
                SizedBox(
                  height: MediaQuery.of(Get.context!).size.height * 0.65,
                  width: ScreenUtil().screenWidth,
                  child: AMapWidget(
                    privacyStatement: ConstConfig.amapPrivacyStatement,
                    apiKey: ConstConfig.amapApiKeys,
                    onMapCreated: onMapCreated,
                    mapType: MapType.night,
                    initialCameraPosition: CameraPosition(
                        target: logic.currentPosition.value ??
                            const LatLng(39.909187, 116.397451),
                        zoom: 18),
                    onLocationChanged: (location) {
                      cc.log('onLocationChanged!!!22222$location');
                    },
                    markers: logic.selectPosision.value != null
                        ? {
                            Marker(
                              position: logic.selectPosision.value!,
                              icon: logic.customIcon ??
                                  BitmapDescriptor.fromIconPath(
                                    'BitmapDescriptor.hueBlue',
                                  ),
                            ),
                          }
                        : {},
                  ),
                ),
                Positioned(
                    right: 10,
                    bottom: ScreenUtil().screenHeight * 0.35 + 40,
                    child: Container(
                      color: Colors.transparent,
                      alignment: Alignment.centerLeft,
                      child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [...logic.approvalNumberWidget]),
                    )),
                Positioned(
                    left: 15,
                    bottom: ScreenUtil().screenHeight * 0.35 + 40,
                    child: InkWell(
                      onTap: () {
                        if (logic.currentPosition.value != null) {
                          logic.moveToCurrentLocation(
                              logic.currentPosition.value!);
                        }
                      },
                      child: Image.asset('assets/images/position_btn.png'),
                    )),
                _topBarWidget(context),
                _searchListWidget(context),
              ],
            );
          })),
    );
  }

  void onMapCreated(AMapController controller) {
    logic.updateMapController(controller);
    getApprovalNumber(controller);
  }

  /// 获取审图号
  void getApprovalNumber(AMapController controller) async {
    //普通地图审图号
    String? mapContentApprovalNumber =
        await controller.getMapContentApprovalNumber();
    //卫星地图审图号
    String? satelliteImageApprovalNumber =
        await controller.getSatelliteImageApprovalNumber();
    if (null != mapContentApprovalNumber) {
      logic.approvalNumberWidget.add(Text(mapContentApprovalNumber));
    }
    if (null != satelliteImageApprovalNumber) {
      logic.approvalNumberWidget.add(Text(satelliteImageApprovalNumber));
    }
    cc.log('地图审图号（普通地图）: $mapContentApprovalNumber');
    cc.log('地图审图号（卫星地图): $satelliteImageApprovalNumber');
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 8.w, right: 10.w, top: 6.w),
            child: IconButton(
                onPressed: () {
                  AppPage.back();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                )),
          ),
          InkWell(
            onTap: () {
              // Navigator.pop(context,
              //         logic.places[logic.selectedIndex.value]);
              cc.log("back!!!!${logic.places[logic.selectedIndex.value]}");
              AppPage.back(result: logic.places[logic.selectedIndex.value]);
            },
            child: Container(
              width: 70.w,
              height: 30.w,
              margin: EdgeInsets.only(right: 20.w, top: 6.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(15.r)),
              child: Text(
                '确定',
                style: TextStyles.semiBold14.copyWith(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _searchListWidget(BuildContext context) {
    final topPosition = logic.isSearchFocused.value
        ? MediaQuery.of(Get.context!).size.height * 0.65 -
            20 -
            MediaQuery.of(context).viewInsets.bottom
        : MediaQuery.of(Get.context!).size.height * 0.65 - 20;
    return Positioned(
        left: 0,
        right: 0,
        top: topPosition,
        bottom: 0,
        child: Container(
          decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r))),
          child: Column(
            children: [
              _topSearchWidget(),
              Expanded(
                child:
                    //   RefreshIndicator(
                    // onRefresh: logic.getNearbyPlaces,
                    // child:
                    Container(
                  child: logic.init.value
                      ? (logic.places.isEmpty
                          ? _emptyView(context)
                          : _listView(context))
                      : buildLoad(),
                ),
                // )
              )
            ],
          ),
        ));
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 6.w),
        itemCount: logic.places.length,
        itemBuilder: (context, index) {
          final item = logic.places[index];
          // 列表项
          return _buildListItem(item, index);
        });
  }

  /// 构建列表项
  Widget _buildListItem(Place item, int index) {
    return InkWell(
        onTap: () {
          logic.selectedIndex.value = index;
          LatLng latLng = item.location;
          logic.selectPosision.value = item.location;
          logic.moveToCurrentLocation(latLng);
        },
        child: Container(
          height: 70.w,
          margin: const EdgeInsets.only(left: 15, right: 15),
          decoration: BoxDecoration(
              border: Border(
                  bottom: BorderSide(
                      color: const Color(0x1A5C5C6E), width: 0.5.w))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: ScreenUtil().screenWidth - 60,
                    child: Text(item.name,
                        style: TextStyles.semiBold14,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1),
                  ),
                  const SizedBox(
                    width: 7,
                  ),
                  SizedBox(
                      width: ScreenUtil().screenWidth - 60,
                      child: Text(item.address,
                          style: const TextStyle(
                              color: Colours.color5C5C6E, fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1)),
                ],
              ),
              Obx(() {
                return logic.selectedIndex.value == index
                    ? const Icon(
                        Icons.check,
                        color: Color(0xFF7732ED),
                        size: 20,
                      )
                    : const SizedBox();
              })
            ],
          ),
        ));
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double topDistance = (constraints.maxHeight -
                ScreenUtil().statusBarHeight -
                ScreenUtil().bottomBarHeight -
                250) /
            2;
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: Padding(
                  padding: EdgeInsets.only(top: topDistance),
                  child: myNoDataView(
                    context,
                    msg: '暂无地址',
                  )),
            ));
      },
    );
  }

  Widget _topSearchWidget() {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: const EdgeInsets.fromLTRB(15, 20, 15, 5),
      padding: const EdgeInsets.only(left: 20, right: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF0F0F16), // 设置背景颜色
        borderRadius: BorderRadius.circular(25), // 设置圆角
      ),
      child: Row(
        children: [
          Image.asset('assets/images/ic_search.png'),
          const SizedBox(
            width: 12,
          ),
          Expanded(
              child: TextField(
            controller: logic.txtController1,
            focusNode: logic.focusNode,
            onChanged: (value) {
              logic.searchKeyword.value = value;
            },
            onSubmitted: (value) {
              logic.searchPlaces();
            },
            style: TextStyles.regular,
            decoration: InputDecoration(
              hintText: '搜索地点',
              hintStyle:
                  TextStyles.regular.copyWith(color: Colours.color5C5C6E),
              contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
              //让文字垂直居中,
              border: InputBorder.none,
            ),
            keyboardType: TextInputType.name,
          ))
        ],
      ),
    );
  }
}
