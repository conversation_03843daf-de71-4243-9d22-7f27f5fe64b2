import 'dart:developer' as cc;

class LocationModel {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final String? address;
  final String? description;
  final String? locTime;
  final String? province;
  final String? city;
  final String? streetNumber;
  final String? district;
  final String? street;
  final String? cityCode;
  // final double? accuracy;
  // final DateTime timestamp;

  LocationModel({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.address,
    this.description,
    this.locTime,
    this.province,
    this.city,
    this.streetNumber,
    this.district,
    this.street,
    this.cityCode,
    // this.accuracy,
    // required this.timestamp,
  });

  factory LocationModel.fromMap(Map<String, dynamic> map) {
    String latitudeStr = map['latitude'];
    String longitudeStr = map['longitude'];
    return LocationModel(
      latitude: double.parse(latitudeStr),
      longitude: double.parse(longitudeStr),
      accuracy: map['accuracy'] as double?,
      address: map['address'] as String?,
      description: map['description'] as String?,
      locTime: map['locTime'] as String?,
      province: map['province'] as String?,
      city: map['city'] as String?,
      streetNumber: map['streetNumber'] as String?,
      district: map['district'] as String?,
      street: map['street'] as String?,
      cityCode: map['cityCode'] as String?,
      // accuracy: map['accuracy'] as double?,
      // timestamp: DateTime.fromMillisecondsSinceEpoch(
      //   (map['timestamp'] as int?) ?? DateTime.now().millisecondsSinceEpoch,
      // ),
    );
  }

  @override
  String toString() {
    return 'LocationModel(latitude: $latitude, longitude: $longitude,address:$address,description:$description,street:$street,district:$district ,streetNumber:$streetNumber,city:$city ,province:$province)';
  }
}
