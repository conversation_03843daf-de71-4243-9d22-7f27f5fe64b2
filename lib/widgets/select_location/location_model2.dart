///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class LocationModel2 {
/*
{
  "callbackTime": "2025-08-29 13:50:32",
  "locationTime": "2025-08-29 13:50:23",
  "locationType": 1,
  "latitude": 28.23038055293505,
  "longitude": 112.88536555430132,
  "accuracy": 30,
  "altitude": 0,
  "bearing": 0,
  "speed": 0,
  "country": "",
  "province": "",
  "city": "",
  "district": "",
  "street": "",
  "streetNumber": "",
  "cityCode": "",
  "adCode": "",
  "address": "",
  "description": ""
} 
*/

  String? callbackTime;
  String? locationTime;
  int? locationType;
  double? latitude;
  double? longitude;
  int? accuracy;
  int? altitude;
  int? bearing;
  int? speed;
  String? country;
  String? province;
  String? city;
  String? district;
  String? street;
  String? streetNumber;
  String? cityCode;
  String? adCode;
  String? address;
  String? description;

  LocationModel2({
    this.callbackTime,
    this.locationTime,
    this.locationType,
    this.latitude,
    this.longitude,
    this.accuracy,
    this.altitude,
    this.bearing,
    this.speed,
    this.country,
    this.province,
    this.city,
    this.district,
    this.street,
    this.streetNumber,
    this.cityCode,
    this.adCode,
    this.address,
    this.description,
  });
  LocationModel2.fromJson(Map<String, dynamic> json) {
    callbackTime = json['callbackTime']?.toString();
    locationTime = json['locationTime']?.toString();
    locationType = json['locationType']?.toInt();
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    accuracy = json['accuracy']?.toInt();
    altitude = json['altitude']?.toInt();
    bearing = json['bearing']?.toInt();
    speed = json['speed']?.toInt();
    country = json['country']?.toString();
    province = json['province']?.toString();
    city = json['city']?.toString();
    district = json['district']?.toString();
    street = json['street']?.toString();
    streetNumber = json['streetNumber']?.toString();
    cityCode = json['cityCode']?.toString();
    adCode = json['adCode']?.toString();
    address = json['address']?.toString();
    description = json['description']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['callbackTime'] = callbackTime;
    data['locationTime'] = locationTime;
    data['locationType'] = locationType;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['accuracy'] = accuracy;
    data['altitude'] = altitude;
    data['bearing'] = bearing;
    data['speed'] = speed;
    data['country'] = country;
    data['province'] = province;
    data['city'] = city;
    data['district'] = district;
    data['street'] = street;
    data['streetNumber'] = streetNumber;
    data['cityCode'] = cityCode;
    data['adCode'] = adCode;
    data['address'] = address;
    data['description'] = description;
    return data;
  }
}
