import 'dart:async';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:amap_flutter_base/amap_flutter_base.dart';
import 'package:dio/dio.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:developer' as cc;

import 'package:shoot_z/widgets/select_location/location_model.dart';

class SelectLocationLogic extends GetxController {
  TextEditingController txtController1 = TextEditingController();
  StreamSubscription<Map<String, Object>>? locationListener;
  RxList<Widget> approvalNumberWidget = <Widget>[].obs;
  AMapFlutterLocation locationPlugin = AMapFlutterLocation();
  AMapController? _aMapController;
  Rx<LatLng?> currentPosition = Rx<LatLng?>(null);
  Rx<LatLng?> selectPosision = Rx<LatLng?>(null);
  BitmapDescriptor? customIcon;
  final focusNode = FocusNode();
  final isSearchFocused = false.obs;
  final searchKeyword = ''.obs;
  var retryCount = 0;
  final int maxRetryCount = 3;
  // 周围地点列表
  var places = <Place>[].obs;
  Place? currentPlace;
  var isLoading = false.obs;
  var init = false.obs;
  var selectedIndex = 0.obs;
  @override
  void onInit() {
    super.onInit();
    focusNode.addListener(_handleFocusChange);
    AMapFlutterLocation.updatePrivacyShow(true, true);
    AMapFlutterLocation.updatePrivacyAgree(true);
    requestPermission();
    AMapFlutterLocation.setApiKey(
        "8fb8c0695ce76a8a3b1f307aad2e68a8", "8fb8c0695ce76a8a3b1f307aad2e68a8");
    // if (Platform.isIOS) {
    //   requestAccuracyAuthorization();
    // }
    _initLocation();
    _loadCustomIcon();
  }

  void _handleFocusChange() {
    isSearchFocused.value = focusNode.hasFocus;
  }

  Future<String> _getAddressFromLocation(LatLng location) async {
    try {
      final response = await Dio().get(
        'https://restapi.amap.com/v3/geocode/regeo',
        queryParameters: {
          'key': 'b46a951e8cd35c21cb2419643b37e238',
          'location': '${location.longitude},${location.latitude}',
          'radius': '1000',
          'roadlevel': '1',
          'poitype': '商务写字楼',
          'extensions': 'all',
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        cc.log('getAddressFromLocation$location');
        cc.log('getAddressFromLocation$data');
        final formattedAddress =
            data['regeocode']?['formatted_address'] as String?;
        // cc.log('getAddressFromLocation!!!!!!!!$formattedAddress');
        return formattedAddress ?? '未知位置';
      }
    } catch (e) {
      print('反向地理编码失败: $e');
    }
    return '未知位置';
  }

  // 初始化定位
  void _initLocation() {
    WxLoading.show();
    locationPlugin.setLocationOption(_getLocationOption());

    locationPlugin.onLocationChanged().listen((event) {
      WxLoading.dismiss();
      if (!event.containsKey('errorCode')) {
        final location = LocationModel.fromMap(event.cast<String, dynamic>());
        currentPosition.value = LatLng(location.latitude, location.longitude);
        selectPosision.value = LatLng(location.latitude, location.longitude);
        currentPlace = Place(
          citycode: location.cityCode ?? '',
          name: _extractLandmarkName(location),
          address: location.address ?? '${location.province}${location.street}',
          location: LatLng(location.latitude, location.longitude),
        );
        moveToCurrentLocation(currentPosition.value!);
        getNearbyPlaces();
        // _getAddressFromLocation( LatLng(location.latitude, location.longitude));
      } else {
        //定位失败，重新定位
        // 如果重试次数未达到上限，则重新定位
        if (retryCount < maxRetryCount) {
          retryCount++;
          Get.snackbar('定位失败', '正在尝试重新定位 ($retryCount/$maxRetryCount)...');
          Future.delayed(const Duration(seconds: 2), () {
            _retryLocation();
          });
        } else {
          Get.snackbar('定位失败', '已达到最大重试次数');
        }
      }
      cc.log("_initLocation$event");
    });

    locationPlugin.startLocation();
  }

// 重新定位方法
  void _retryLocation() {
    if (retryCount >= maxRetryCount) return;

    locationPlugin.stopLocation();
    Future.delayed(const Duration(milliseconds: 500), () {
      locationPlugin.startLocation();
    });
  }

  // 获取附近地点
  Future<void> getNearbyPlaces() async {
    isLoading.value = true;

    try {
      // 这里使用高德地图的POI搜索API
      // 实际项目中需要替换为你的高德API key
      final response = await Dio().get(
        'https://restapi.amap.com/v3/place/around',
        queryParameters: {
          'key': 'b46a951e8cd35c21cb2419643b37e238',
          'location':
              '${currentPosition.value?.longitude},${currentPosition.value?.latitude}',
          'radius': '1000',
          'types': '',
          'offset': '10',
          'extensions': 'all'
        },
      );
      cc.log('message!!response${response}');
      if (response.statusCode == 200) {
        init.value = true;
        isLoading.value = false;
        final data = response.data as Map<String, dynamic>;
        final pois = data['pois'] as List<dynamic>?;
        // 创建包含当前位置的地点列表
        List<Place> nearbyPlaces = [];

        // 首先添加当前位置
        if (currentPlace != null) {
          nearbyPlaces.add(currentPlace!);
        }
        if (pois != null) {
          nearbyPlaces.addAll(pois.map((poi) => Place.fromJson(poi)));
        }
        places.value = nearbyPlaces;
        cc.log('message!!places${places}');
      }
    } catch (e) {
      init.value = true;
      isLoading.value = false;
      Get.snackbar('错误', '获取附近地点失败: $e');
    } finally {
      init.value = true;
      isLoading.value = false;
    }
  }

// 搜索地点
  void searchPlaces() async {
    if (searchKeyword.value.isEmpty) {
      return;
    }

    WxLoading.show();

    try {
      final response = await Dio().get(
        'https://restapi.amap.com/v3/place/text',
        queryParameters: {
          'key': 'b46a951e8cd35c21cb2419643b37e238',
          'keywords': searchKeyword.value,
          'location':
              '${currentPosition.value?.longitude},${currentPosition.value?.latitude}',
          'radius': '1000',
          'offset': '10',
          'extensions': 'all'
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final pois = data['pois'] as List<dynamic>?;
        cc.log('searchResult$pois');
        places.value = pois?.map((poi) => Place.fromJson(poi)).toList() ?? [];
        selectedIndex.value = 0;
        if (places.isNotEmpty) {
          selectPosision.value = places[selectedIndex.value].location;
          moveToCurrentLocation(selectPosision.value!);
        }
      }
    } catch (e) {
      cc.log('searchResultFailed$e');
      Get.snackbar('错误', '搜索地点失败: $e');
    } finally {
      WxLoading.dismiss();
    }
  }

  // 地标名称提取逻辑
  static String _extractLandmarkName(LocationModel location) {
    // 1. 从description中提取"靠近XX"的地标
    final desc = location.description ?? '';
    const marker = '靠近';
    if (desc.contains(marker)) {
      return desc.split(marker).last.trim();
    }

    // 2. 使用街道+门牌号（如"青山路733号"）
    if (location.street != null && location.streetNumber != null) {
      return '${location.street}${location.streetNumber}';
    }

    // 3. 退回使用区级地名
    return location.district ?? location.city ?? location.province ?? '我的位置';
  }

  // 获取定位配置
  AMapLocationOption _getLocationOption() {
    return AMapLocationOption(
      needAddress: true,
      onceLocation: true,
      locationMode: AMapLocationMode.Device_Sensors,
    );
  }

  Future<void> _loadCustomIcon() async {
    customIcon = await getBitmapDescriptor();
  }

  Future<BitmapDescriptor> getBitmapDescriptor() async {
    // 模拟异步操作，例如从网络加载图片
    return BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(26, 48)),
        'assets/images/marker_icon.png');
  }

  // 更新地图控制器
  void updateMapController(AMapController controller) {
    _aMapController = controller;
    update();
  }

  // 移动到指定位置（需要先获取位置）
  void moveToCurrentLocation(LatLng latLng) {
    // // 这里添加获取当前位置的逻辑
    // // 例如使用 amap_flutter_location 插件获取当前位置
    _aMapController?.moveCamera(
      CameraUpdate.newLatLngZoom(latLng, 18),
    );
  }

  // 停止监听
  void stopListening() {
    locationListener?.cancel();
    locationListener = null;
  }

  // // 示例：移动地图到指定位置
  // void moveToPosition(LatLng position) {
  //   _aMapController?.moveCamera(
  //     CameraUpdate.newLatLng(position),
  //   );
  //   update(); // 通知监听者
  // }
  ///获取iOS native的accuracyAuthorization类型
  void requestAccuracyAuthorization() async {
    AMapAccuracyAuthorization currentAccuracyAuthorization =
        await locationPlugin.getSystemAccuracyAuthorization();
    if (currentAccuracyAuthorization ==
        AMapAccuracyAuthorization.AMapAccuracyAuthorizationFullAccuracy) {
      cc.log("精确定位类型");
    } else if (currentAccuracyAuthorization ==
        AMapAccuracyAuthorization.AMapAccuracyAuthorizationReducedAccuracy) {
      cc.log("模糊定位类型");
    } else {
      cc.log("未知定位类型");
    }
  }

  /// 动态申请定位权限
  void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      cc.log("定位权限申请通过");
    } else {
      cc.log("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    focusNode.removeListener(_handleFocusChange);
    focusNode.dispose();
    stopListening();
    locationPlugin.stopLocation();
    _aMapController?.disponse();
    super.onClose();
  }
}

class Place {
  final String citycode;
  final String name;
  final String address;
  final LatLng location;
  // final double distance;

  Place({
    required this.citycode,
    required this.name,
    required this.address,
    required this.location,
    // required this.distance,
  });

  factory Place.fromJson(Map<String, dynamic> json) {
    // 处理可能为List或String的字段
    final locationStr = (json['location'] is List)
        ? (json['location'] as List).join(',')
        : json['location']?.toString() ?? '';
    final locationParts = locationStr.split(',');

    return Place(
      citycode: json['citycode'] as String? ?? '',
      name: json['name'] as String? ?? '',
      address: json['address'] as String? ?? '',
      location: LatLng(
        double.tryParse(locationParts[1]) ?? 0,
        double.tryParse(locationParts[0]) ?? 0,
      ),
      // distance: double.tryParse(json['distance'] as String? ?? '0') ?? 0,
    );
  }
}
