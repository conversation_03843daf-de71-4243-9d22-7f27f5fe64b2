// ignore_for_file: avoid_print

import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_common/wx_env.dart';
import '../pages/login/user.dart';

//// QueuedInterceptor序列拦截器
//// 同一个dio对象如果有多个并发请求，则在进入拦截器之前将请求添加到队列中。每次只有一个请求进入拦截器，在该请求被拦截器处理后，下一个请求将进入拦截器。不调用InterceptorHandler的方法时,其它请求会一直等待.
////只是在拦截的onRequest、但是如果是拦截onresponse、onError对应方法里面加锁，不会同时执行，但是如果是拦截onresponse、onError，其余请求的onRequest已经执行了，所以就算刷新了token，其余请求也已经返回401，还是得重新请求
class TokenInterceptor extends QueuedInterceptor {
  Dio? _tokenDio;
  // @override
  // void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
  //   // TODO: implement onRequest
  //   print('----------- onRequest ${options.path}------------');
  //   super.onRequest(options, handler);
  // }

  // @override
  // Future<void> onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) async {
  //   //401代表token过期
  //   if (response.statusCode == 401) {
  //    final success = await UserManager.instance.refreshToken();
  //
  //     if (success) {
  //       // 重新请求失败接口
  //       final RequestOptions request = response.requestOptions;
  //       request.headers["token"] = WxEnv.instance.currentToken;
  //
  //       final Options options = Options(
  //         headers: request.headers,
  //         method: request.method,
  //           responseType: ResponseType.json
  //       );
  //
  //       try {
  //         print('----------- 重新请求接口 ------------');
  //         /// 避免重复执行拦截器，使用tokenDio
  //         _tokenDio ??= Dio();
  //         final Response<dynamic> response = await _tokenDio!.request<dynamic>(request.path,
  //           data: request.data,
  //           queryParameters: request.queryParameters,
  //           cancelToken: request.cancelToken,
  //           options: options,
  //           onReceiveProgress: request.onReceiveProgress,
  //         );
  //         return handler.next(response);
  //       } on DioException catch (e) {
  //         return handler.reject(e);
  //       }
  //     }
  //   }
  //   return handler.next(response);
  // }

  // @override
  // void onError(DioException err, ErrorInterceptorHandler handler) async {
  //   // TODO: implement onError
  //   if (err.response?.statusCode == 401) {
  //     print('----------- onError 401 ------------');
  //     final success = await UserManager.instance.refreshToken();
  //     if (success) {
  //       // 重新请求失败接口
  //       err.requestOptions.headers["token"] = WxEnv.instance.currentToken;
  //
  //       try {
  //         print('----------- 重新请求接口 ------------');
  //         /// 避免重复执行拦截器，使用tokenDio
  //         _tokenDio ??= Dio();
  //         final Response<dynamic> response = await _tokenDio!.fetch(err.requestOptions);
  //         return handler.resolve(response);
  //       } on DioException catch (e) {
  //         return handler.reject(e);
  //       }
  //     }
  //   }
  //   return handler.next(err);
  // }

  bool isRefreshing = false;
  final List<void Function(bool)> requestQueue = []; // 存储等待的请求
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      print('----------- onError 401 ------------');
      if (!isRefreshing) {
        isRefreshing = true; // 开始刷新 Token
        var result = false;
        try {
          print('----------- isRefreshing ------------');
          result = await UserManager.instance.refreshToken(); // 调用刷新 Token 方法
          if (result) {
            /// 避免重复执行拦截器，使用tokenDio
            _tokenDio ??= Dio();
            err.requestOptions.headers["Authorization"] =
                'Bearer ${WxEnv.instance.currentToken}';
            final Response<dynamic> response =
                await _tokenDio!.fetch(err.requestOptions);
            handler.resolve(response);
          } else {
            handler.reject(err);
          }
        } catch (e) {
          print('----------- 如果刷新失败，传递原始错误 ------------');
          handler.reject(err); // 如果刷新失败，传递原始错误
        } finally {
          isRefreshing = false;
          print('----------- isRefreshing = false ------------');
          // 刷新完成后，执行队列中的请求
          for (var callback in requestQueue) {
            callback(result); // 执行等待中的请求
          }
          requestQueue.clear(); // 清空队列
        }
      } else {
        print('----------- 如果正在刷新 Token，将当前请求加入队列 ------------');
        // 如果正在刷新 Token，将当前请求加入队列
        final completer = Completer<Response>();
        requestQueue.add((bool result) async {
          print('----------- requestQueue.add() ------------');
          try {
            if (result) {
              /// 避免重复执行拦截器，使用tokenDio
              _tokenDio ??= Dio();
              err.requestOptions.headers["Authorization"] =
                  'Bearer ${WxEnv.instance.currentToken}';
              final Response<dynamic> response =
                  await _tokenDio!.fetch(err.requestOptions);
              print('----------- 请求成功 complete------------');
              completer.complete(response); // 请求成功
            } else {
              completer.completeError(err);
            }
          } catch (e) {
            print('----------- 请求失败 completeError------------');
            completer.completeError(err); // 请求失败
          }
        });
        // 等待刷新完成后继续请求
        try {
          print('----------- 等待刷新完成后继续请求------------');
          final response = await completer.future;
          print('----------- 等待刷新完成后返回成功响应------------');
          return handler.resolve(response); // 返回成功响应
        } catch (e) {
          print('----------- 等待刷新完成后返回失败响应------------');
          return handler.reject(e as DioException); // 返回失败响应
        }
      }
    } else {
      print('----------- 非 401 错误直接传递------------');
      //  print(err.response);
      handler.next(err); // 非 401 错误直接传递
    }
  }
}
