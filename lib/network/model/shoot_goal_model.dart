///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ShootGoalModel {
/*
{
  "id": "9",
  "videoPath": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/persistent/algo_prod/match/2152/2024-10-24/19-00-00/video/match-highlight_2152_2024-10-24_19-05-49_0_1.mp4",
  "time": "10:09:00",
  "shootType": 1,
  "hit": true,
  "checked": true
} 
*/

  String? id;
  String? cover;
  String? videoPath;
  String? time;
  int? shootType;
  bool? hit;
  bool? checked;

  ShootGoalModel({
    this.id,
    this.videoPath,
    this.time,
    this.shootType,
    this.hit,
    this.checked,
  });
  ShootGoalModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    cover = json['cover']?.toString();
    videoPath = json['videoPath']?.toString();
    time = json['time']?.toString();
    shootType = json['shootType']?.toInt();
    hit = json['hit'];
    checked = json['checked'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['cover'] = cover;
    data['videoPath'] = videoPath;
    data['time'] = time;
    data['shootType'] = shootType;
    data['hit'] = hit;
    data['checked'] = checked;
    return data;
  }
}
