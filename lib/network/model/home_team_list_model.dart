///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HomeTeamListModel {
/*
{
  "hotScore": 0,
  "levelIcon": "string",
  "levelName": "string",
  "levelScore": 0,
  "matchNum": 0,
  "teamId": 0,
  "teamLogo": "string",
  "teamName": "string",
  "teamNum": 0,
  "teamPhoto": "string"
} 
*/

  int? hotScore;
  String? levelIcon;
  String? levelName;
  int? levelScore;
  int? matchNum;
  int? teamId;
  String? teamLogo;
  String? teamName;
  int? teamNum;
  String? teamPhoto;

  HomeTeamListModel({
    this.hotScore,
    this.levelIcon,
    this.levelName,
    this.levelScore,
    this.matchNum,
    this.teamId,
    this.teamLogo,
    this.teamName,
    this.teamNum,
    this.teamPhoto,
  });
  HomeTeamListModel.fromJson(Map<String, dynamic> json) {
    hotScore = json['hotScore']?.toInt();
    levelIcon = json['levelIcon']?.toString();
    levelName = json['levelName']?.toString();
    levelScore = json['levelScore']?.toInt();
    matchNum = json['matchNum']?.toInt();
    teamId = json['teamId']?.toInt();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    teamNum = json['teamNum']?.toInt();
    teamPhoto = json['teamPhoto']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['hotScore'] = hotScore;
    data['levelIcon'] = levelIcon;
    data['levelName'] = levelName;
    data['levelScore'] = levelScore;
    data['matchNum'] = matchNum;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    data['teamNum'] = teamNum;
    data['teamPhoto'] = teamPhoto;
    return data;
  }
}
