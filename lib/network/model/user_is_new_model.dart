///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class UserIsNewModelIsFirstRegister {
/*
{
  "isNew": true,
  "receivedVip": false,
  "ReceivableActivityId": "0"
} 
*/

  bool? isNew;
  bool? receivedVip;
  String? receivableActivityId;

  UserIsNewModelIsFirstRegister({
    this.isNew,
    this.receivedVip,
    this.receivableActivityId,
  });
  UserIsNewModelIsFirstRegister.fromJson(Map<String, dynamic> json) {
    isNew = json['isNew'];
    receivedVip = json['receivedVip'];
    receivableActivityId = json['receivableActivityId']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['isNew'] = isNew;
    data['receivedVip'] = receivedVip;
    data['receivableActivityId'] = receivableActivityId;
    return data;
  }
}

class UserIsNewModel {
/*
{
  "isFirstRegister": {
    "isNew": true,
    "receivedVip": false,
    "ReceivableActivityId": "0"
  }
} 
*/

  UserIsNewModelIsFirstRegister? isFirstRegister;

  UserIsNewModel({
    this.isFirstRegister,
  });
  UserIsNewModel.fromJson(Map<String, dynamic> json) {
    isFirstRegister = (json['isFirstRegister'] != null)
        ? UserIsNewModelIsFirstRegister.fromJson(json['isFirstRegister'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (isFirstRegister != null) {
      data['isFirstRegister'] = isFirstRegister!.toJson();
    }
    return data;
  }
}
