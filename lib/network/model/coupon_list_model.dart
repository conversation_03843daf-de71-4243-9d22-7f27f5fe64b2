///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CouponListModel {
/*
{
  "id": "4",
  "code": "MP0001000100010001",
  "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/images/index/career.png",
  "name": "赛事个人解锁券",
  "description": "赛事个人解锁，仅能单人解锁",
  "type": 1,
  "bizType": 1000,
  "price": "0",
  "discount": "1",
  "effectiveTime": "2025-01-18 16:34:50",
  "expireTime": "2025-03-18 16:34:56",
  "remainingDays": 0,
  "verifiedOff": false,
  "expired": false
} 
*/

  String? id;
  String? code;
  String? logo;
  String? name;
  String? description;
  int? type;
  int? bizType;
  String? price;
  String? discount;
  String? effectiveTime;
  String? expireTime;
  int? remainingDays;
  bool? verifiedOff;
  bool? expired;

  CouponListModel({
    this.id,
    this.code,
    this.logo,
    this.name,
    this.description,
    this.type,
    this.bizType,
    this.price,
    this.discount,
    this.effectiveTime,
    this.expireTime,
    this.remainingDays,
    this.verifiedOff,
    this.expired,
  });
  CouponListModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    code = json['code']?.toString();
    logo = json['logo']?.toString();
    name = json['name']?.toString();
    description = json['description']?.toString();
    type = json['type']?.toInt();
    bizType = json['bizType']?.toInt();
    price = json['price']?.toString();
    discount = json['discount']?.toString();
    effectiveTime = json['effectiveTime']?.toString();
    expireTime = json['expireTime']?.toString();
    remainingDays = json['remainingDays']?.toInt();
    verifiedOff = json['verifiedOff'];
    expired = json['expired'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['code'] = code;
    data['logo'] = logo;
    data['name'] = name;
    data['description'] = description;
    data['type'] = type;
    data['bizType'] = bizType;
    data['price'] = price;
    data['discount'] = discount;
    data['effectiveTime'] = effectiveTime;
    data['expireTime'] = expireTime;
    data['remainingDays'] = remainingDays;
    data['verifiedOff'] = verifiedOff;
    data['expired'] = expired;
    return data;
  }
}
