///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamMemberModel {
/*
{
  "userId": "269705",
  "userName": "用户56321",
  "userPhoto": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/images/shootzAvatar/a.png",
  "memberId": "10061",
  "height": "",
  "weight": "",
  "position": "0",
  "me": true,
  "isSelect": false
} 
*/

  String? userId;
  String? userName;
  String? userPhoto;
  String? memberId;
  String? height;
  String? weight;
  String? position;
  bool? me;

  TeamMemberModel({
    this.userId,
    this.userName,
    this.userPhoto,
    this.memberId,
    this.height,
    this.weight,
    this.position,
    this.me,
  });
  TeamMemberModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId']?.toString();
    userName = json['userName']?.toString();
    userPhoto = json['userPhoto']?.toString();
    memberId = json['memberId']?.toString();
    height = json['height']?.toString();
    weight = json['weight']?.toString();
    position = json['position']?.toString();
    me = json['me'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['userId'] = userId;
    data['userName'] = userName;
    data['userPhoto'] = userPhoto;
    data['memberId'] = memberId;
    data['height'] = height;
    data['weight'] = weight;
    data['position'] = position;
    data['me'] = me;
    return data;
  }
}
