///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VideosUsedShotsModel {
/*
{
  "availableMergeTimesToday": 0,
  "couponCount": 0,
  "couponExpireSoonCount": 0,
  "couponExpireTime": "string",
  "mergeTips": "string",
  "remainingCount": 0,
  "shareRights": 0,
  "shotCount": 0,
  "shots": [
    0
  ],
  "todayMergeCount": 0,
  "vip": true,
  "vipExpired": true
} 
*/

  int? availableMergeTimesToday;
  int? couponCount;
  int? couponExpireSoonCount;
  String? couponExpireTime;
  String? mergeTips;
  int? remainingCount;
  int? shareRights;
  int? shotCount;
  List<int?>? shots;
  int? todayMergeCount;
  bool? vip;
  bool? vipExpired;

  VideosUsedShotsModel({
    this.availableMergeTimesToday,
    this.couponCount,
    this.couponExpireSoonCount,
    this.couponExpireTime,
    this.mergeTips,
    this.remainingCount,
    this.shareRights,
    this.shotCount,
    this.shots,
    this.todayMergeCount,
    this.vip,
    this.vipExpired,
  });
  VideosUsedShotsModel.fromJson(Map<String, dynamic> json) {
    availableMergeTimesToday = json['availableMergeTimesToday']?.toInt();
    couponCount = json['couponCount']?.toInt();
    couponExpireSoonCount = json['couponExpireSoonCount']?.toInt();
    couponExpireTime = json['couponExpireTime']?.toString();
    mergeTips = json['mergeTips']?.toString();
    remainingCount = json['remainingCount']?.toInt();
    shareRights = json['shareRights']?.toInt();
    shotCount = json['shotCount']?.toInt();
    if (json['shots'] != null) {
      final v = json['shots'];
      final arr0 = <int>[];
      v.forEach((v) {
        arr0.add(v.toInt());
      });
      shots = arr0;
    }
    todayMergeCount = json['todayMergeCount']?.toInt();
    vip = json['vip'];
    vipExpired = json['vipExpired'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['availableMergeTimesToday'] = availableMergeTimesToday;
    data['couponCount'] = couponCount;
    data['couponExpireSoonCount'] = couponExpireSoonCount;
    data['couponExpireTime'] = couponExpireTime;
    data['mergeTips'] = mergeTips;
    data['remainingCount'] = remainingCount;
    data['shareRights'] = shareRights;
    data['shotCount'] = shotCount;
    if (shots != null) {
      final v = shots;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['shots'] = arr0;
    }
    data['todayMergeCount'] = todayMergeCount;
    data['vip'] = vip;
    data['vipExpired'] = vipExpired;
    return data;
  }
}
