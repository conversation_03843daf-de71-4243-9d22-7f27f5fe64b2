///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamInfoModel {
/*
{
  "description": "string",
  "id": "0",
  "logo": "string",
  "name": "string",
  "teamPhoto": "string"
} 
*/

  String? description;
  String? id;
  String? logo;
  String? name;
  String? teamPhoto;

  TeamInfoModel({
    this.description,
    this.id,
    this.logo,
    this.name,
    this.teamPhoto,
  });
  TeamInfoModel.fromJson(Map<String, dynamic> json) {
    description = json['description']?.toString();
    id = json['id']?.toString();
    logo = json['logo']?.toString();
    name = json['name']?.toString();
    teamPhoto = json['teamPhoto']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['description'] = description;
    data['id'] = id;
    data['logo'] = logo;
    data['name'] = name;
    data['teamPhoto'] = teamPhoto;
    return data;
  }
}
