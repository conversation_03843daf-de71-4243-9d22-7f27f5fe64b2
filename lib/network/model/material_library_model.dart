import 'package:shoot_z/network/model/option_goal_model.dart';

class MaterialLibraryModel {
  DateTime? videoTime;
  String? videoTimeStr;
  bool? selected;
  int? selecteCount;
  String? videoDateStr;
  List<OptionGoalModel?>? otherVideos;

  MaterialLibraryModel(
      {this.videoTime, this.videoTimeStr, this.otherVideos, this.selected});
  MaterialLibraryModel.fromJson(Map<String, dynamic> json) {
    videoTime = json['videoTime'];
    videoTimeStr = json['videoTimeStr']?.toString();
    selected = json['selected'];
    selecteCount = json['selecteCount'].toInt();
    videoDateStr = json['videoDateStr']?.toString();
    if (json['otherVideos'] != null) {
      final v = json['otherVideos'];
      final arr0 = <OptionGoalModel>[];
      v.forEach((v) {
        arr0.add(OptionGoalModel.fromJson(v));
      });
      otherVideos = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['videoTime'] = videoTime;
    data['videoTimeStr'] = videoTimeStr;
    data['selected'] = selected;
    data['selecteCount'] = selecteCount;
    data['videoDateStr'] = videoDateStr;

    if (otherVideos != null) {
      final v = otherVideos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['otherVideos'] = arr0;
    }
    return data;
  }
}
