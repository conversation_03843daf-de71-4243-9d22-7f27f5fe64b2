///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class SiteGoalModelVideosCameras {
/*
{
  "cameraIndex": 0,
  "cameraName": "侧面"
} 
*/

  int? cameraIndex;
  String? cameraName;

  SiteGoalModelVideosCameras({
    this.cameraIndex,
    this.cameraName,
  });
  SiteGoalModelVideosCameras.fromJson(Map<String, dynamic> json) {
    cameraIndex = json['cameraIndex']?.toInt();
    cameraName = json['cameraName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['cameraIndex'] = cameraIndex;
    data['cameraName'] = cameraName;
    return data;
  }
}

class SiteGoalModelVideosOtherVideos {
/*
{
  "pid": "634325525",
  "id": "634325525",
  "hashCode": "\"c4192c73998e5f4af192b1226c8b7c68\"",
  "videoPath": "https://cdn.shootz.tech/202505281517/9f48f82332b121cfe80c4792b083fcc7/algo_prod/match/124/2025-05-27/15-00-00/video/match-highlight_124_2025-05-27_15-12-25_0_1.mp4",
  "time": "15:12:25",
  "cameraIndex": 0,
  "selected": false,
  "collect": false,
  "mainCamera": true,
  "duration": 10,
  "shootType": 0,
  "hit": true
} 
*/

  String? pid;
  String? id;
  String? hashCode2;
  String? videoPath;
  String? time;
  int? cameraIndex;
  bool? selected;
  bool? collect;
  bool? mainCamera;
  int? duration;
  int? shootType;
  bool? hit;

  SiteGoalModelVideosOtherVideos({
    this.pid,
    this.id,
    this.hashCode2,
    this.videoPath,
    this.time,
    this.cameraIndex,
    this.selected,
    this.collect,
    this.mainCamera,
    this.duration,
    this.shootType,
    this.hit,
  });
  SiteGoalModelVideosOtherVideos.fromJson(Map<String, dynamic> json) {
    pid = json['pid']?.toString();
    id = json['id']?.toString();
    hashCode2 = json['hashCode']?.toString();
    videoPath = json['videoPath']?.toString();
    time = json['time']?.toString();
    cameraIndex = json['cameraIndex']?.toInt();
    selected = json['selected'];
    collect = json['collect'];
    mainCamera = json['mainCamera'];
    duration = json['duration']?.toInt();
    shootType = json['shootType']?.toInt();
    hit = json['hit'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['pid'] = pid;
    data['id'] = id;
    data['hashCode'] = hashCode2;
    data['videoPath'] = videoPath;
    data['time'] = time;
    data['cameraIndex'] = cameraIndex;
    data['selected'] = selected;
    data['collect'] = collect;
    data['mainCamera'] = mainCamera;
    data['duration'] = duration;
    data['shootType'] = shootType;
    data['hit'] = hit;
    return data;
  }
}

class SiteGoalModelVideos {
/*
{
  "id": "634325525",
  "hashCode": "c4192c73998e5f4af192b1226c8b7c68",
  "videoPath": "https://cdn.shootz.tech/202505281517/9f48f82332b121cfe80c4792b083fcc7/algo_prod/match/124/2025-05-27/15-00-00/video/match-highlight_124_2025-05-27_15-12-25_0_1.mp4",
  "time": "15:12:25",
  "cameraIndex": 0,
  "selected": false,
  "collect": false,
  "otherVideos": [
    {
      "pid": "634325525",
      "id": "634325525",
      "hashCode": "\"c4192c73998e5f4af192b1226c8b7c68\"",
      "videoPath": "https://cdn.shootz.tech/202505281517/9f48f82332b121cfe80c4792b083fcc7/algo_prod/match/124/2025-05-27/15-00-00/video/match-highlight_124_2025-05-27_15-12-25_0_1.mp4",
      "time": "15:12:25",
      "cameraIndex": 0,
      "selected": false,
      "collect": false,
      "mainCamera": true,
      "duration": 10,
      "shootType": 0,
      "hit": true
    }
  ],
  "checked": false,
  "duration": 10,
  "cameras": [
    {
      "cameraIndex": 0,
      "cameraName": "侧面"
    }
  ],
  "shootType": 0,
  "hit": true,
  "recommended": false
} 
*/

  String? id;
  String? hashCode2;
  String? videoPath;
  String? time;
  int? cameraIndex;
  // bool? selected;
  bool? collect;
  List<SiteGoalModelVideosOtherVideos?>? otherVideos;
  bool? checked;
  int? duration;
  List<SiteGoalModelVideosCameras?>? cameras;
  int? shootType;
  bool? hit;
  bool? recommended;

  SiteGoalModelVideos({
    this.id,
    this.hashCode2,
    this.videoPath,
    this.time,
    this.cameraIndex,
    //  this.selected,
    this.collect,
    this.otherVideos,
    this.checked,
    this.duration,
    this.cameras,
    this.shootType,
    this.hit,
    this.recommended,
  });
  SiteGoalModelVideos.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    hashCode2 = json['hashCode']?.toString();
    videoPath = json['videoPath']?.toString();
    time = json['time']?.toString();
    cameraIndex = json['cameraIndex']?.toInt();
    // selected = json['selected'];
    collect = json['collect'];
    if (json['otherVideos'] != null) {
      final v = json['otherVideos'];
      final arr0 = <SiteGoalModelVideosOtherVideos>[];
      v.forEach((v) {
        arr0.add(SiteGoalModelVideosOtherVideos.fromJson(v));
      });
      otherVideos = arr0;
    }
    //checked = json['checked'];
    checked = json['selected'];
    duration = json['duration']?.toInt();
    if (json['cameras'] != null) {
      final v = json['cameras'];
      final arr0 = <SiteGoalModelVideosCameras>[];
      v.forEach((v) {
        arr0.add(SiteGoalModelVideosCameras.fromJson(v));
      });
      cameras = arr0;
    }
    shootType = json['shootType']?.toInt();
    hit = json['hit'];
    recommended = json['recommended'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['hashCode'] = hashCode2;
    data['videoPath'] = videoPath;
    data['time'] = time;
    data['cameraIndex'] = cameraIndex;
    // data['selected'] = selected;
    data['collect'] = collect;
    if (otherVideos != null) {
      final v = otherVideos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['otherVideos'] = arr0;
    }
    data['selected'] = checked;
    data['duration'] = duration;
    if (cameras != null) {
      final v = cameras;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['cameras'] = arr0;
    }
    data['shootType'] = shootType;
    data['hit'] = hit;
    data['recommended'] = recommended;
    return data;
  }
}

class SiteGoalModel {
/*
{
  "arenaName": "译星体育(星耀馆)",
  "arenaId": 30,
  "reportDate": "05-27",
  "reportStartTime": "15:02",
  "reportEndTime": "16:02",
  "courtId": "124",
  "courtName": "1-1",
  "cameraSet": [
    ""
  ],
  "videos": [
    {
      "id": "634325525",
      "hashCode": "c4192c73998e5f4af192b1226c8b7c68",
      "videoPath": "https://cdn.shootz.tech/202505281517/9f48f82332b121cfe80c4792b083fcc7/algo_prod/match/124/2025-05-27/15-00-00/video/match-highlight_124_2025-05-27_15-12-25_0_1.mp4",
      "time": "15:12:25",
      "cameraIndex": 0,
      "selected": false,
      "collect": false,
      "otherVideos": [
        {
          "pid": "634325525",
          "id": "634325525",
          "hashCode": "\"c4192c73998e5f4af192b1226c8b7c68\"",
          "videoPath": "https://cdn.shootz.tech/202505281517/9f48f82332b121cfe80c4792b083fcc7/algo_prod/match/124/2025-05-27/15-00-00/video/match-highlight_124_2025-05-27_15-12-25_0_1.mp4",
          "time": "15:12:25",
          "cameraIndex": 0,
          "selected": false,
          "collect": false,
          "mainCamera": true,
          "duration": 10,
          "shootType": 0,
          "hit": true
        }
      ],
      "checked": false,
      "duration": 10,
      "cameras": [
        {
          "cameraIndex": 0,
          "cameraName": "侧面"
        }
      ],
      "shootType": 0,
      "hit": true,
      "recommended": false
    }
  ],
  "totalCount": 0
} 
*/

  String? arenaName;
  int? arenaId;
  String? reportDate;
  String? reportStartTime;
  String? reportEndTime;
  String? courtId;
  String? courtName;
  List<String?>? cameraSet;
  //List<SiteGoalModelVideos?>? videos;
  int? totalCount;

  SiteGoalModel({
    this.arenaName,
    this.arenaId,
    this.reportDate,
    this.reportStartTime,
    this.reportEndTime,
    this.courtId,
    this.courtName,
    this.cameraSet,
    // this.videos,
    this.totalCount,
  });
  SiteGoalModel.fromJson(Map<String, dynamic> json) {
    arenaName = json['arenaName']?.toString();
    arenaId = json['arenaId']?.toInt();
    reportDate = json['reportDate']?.toString();
    reportStartTime = json['reportStartTime']?.toString();
    reportEndTime = json['reportEndTime']?.toString();
    courtId = json['courtId']?.toString();
    courtName = json['courtName']?.toString();
    if (json['cameraSet'] != null) {
      final v = json['cameraSet'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      cameraSet = arr0;
    }
    // if (json['videos'] != null) {
    //   final v = json['videos'];
    //   final arr0 = <SiteGoalModelVideos>[];
    //   v.forEach((v) {
    //     arr0.add(SiteGoalModelVideos.fromJson(v));
    //   });
    //   videos = arr0;
    // }
    totalCount = json['totalCount']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaName'] = arenaName;
    data['arenaId'] = arenaId;
    data['reportDate'] = reportDate;
    data['reportStartTime'] = reportStartTime;
    data['reportEndTime'] = reportEndTime;
    data['courtId'] = courtId;
    data['courtName'] = courtName;
    if (cameraSet != null) {
      final v = cameraSet;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['cameraSet'] = arr0;
    }
    // if (videos != null) {
    //   final v = videos;
    //   final arr0 = [];
    //   v!.forEach((v) {
    //     arr0.add(v!.toJson());
    //   });
    //   data['videos'] = arr0;
    // }
    data['totalCount'] = totalCount;
    return data;
  }
}
