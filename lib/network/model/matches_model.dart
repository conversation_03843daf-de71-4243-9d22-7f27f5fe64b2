///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MatchesModel {
/*
{
  "matchTime": "2024-12-22 18:40:00",
  "arenaName": "滨江篮球馆",
  "arenaAliasName": "滨江篮球馆",
  "courts": [
    "3-1"
  ],
  "matchId": "2793",
  "arenaId": "36",
  "leftTeamName": "滨江酒蒙子",
  "leftTeamId": "2061",
  "LeftWin": true,
  "leftTeamLogo": "",
  "rightTeamName": "flow",
  "rightTeamId": "1199",
  "rightWin": false,
  "rightTeamLogo": "",
  "leftTeamScore": 120,
  "rightTeamScore": 118,
  "win": true,
  "status": 2,
  "markStatus": 2,
  "matchDateStr": "12-22",
  "matchWeekStr": "周日",
  "matchTimeStr": "18:40"
} 
*/

  String? matchTime;
  String? arenaName;
  String? arenaAliasName;
  List<String?>? courts;
  String? matchId;
  String? arenaId;
  String? leftTeamName;
  String? leftTeamId;
  bool? LeftWin;
  String? leftTeamLogo;
  String? rightTeamName;
  String? rightTeamId;
  bool? rightWin;
  String? rightTeamLogo;
  int? leftTeamScore;
  int? leftScore;
  int? rightTeamScore;
  int? rightScore;
  bool? win;
  int? status;
  int? markStatus;
  String? matchDateStr;
  String? matchWeekStr;
  String? matchTimeStr;

  MatchesModel({
    this.matchTime,
    this.arenaName,
    this.arenaAliasName,
    this.courts,
    this.matchId,
    this.arenaId,
    this.leftTeamName,
    this.leftTeamId,
    this.LeftWin,
    this.leftTeamLogo,
    this.rightTeamName,
    this.rightTeamId,
    this.rightWin,
    this.rightTeamLogo,
    this.leftTeamScore,
    this.rightTeamScore,
    this.win,
    this.status,
    this.markStatus,
    this.matchDateStr,
    this.matchWeekStr,
    this.matchTimeStr,
  });
  MatchesModel.fromJson(Map<String, dynamic> json) {
    matchTime = json['matchTime']?.toString();
    arenaName = json['arenaName']?.toString();
    arenaAliasName = json['arenaAliasName']?.toString();
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      courts = arr0;
    }
    matchId = json['matchId']?.toString();
    arenaId = json['arenaId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    LeftWin = json['LeftWin'];
    leftTeamLogo = json['leftTeamLogo']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    rightWin = json['rightWin'];
    rightTeamLogo = json['rightTeamLogo']?.toString();
    leftTeamScore = json['leftTeamScore']?.toInt();
    leftScore = json['leftScore']?.toInt();
    rightTeamScore = json['rightTeamScore']?.toInt();
    rightScore = json['rightScore']?.toInt();
    win = json['win'];
    status = json['status']?.toInt();
    markStatus = json['markStatus']?.toInt();
    matchDateStr = json['matchDateStr']?.toString();
    matchWeekStr = json['matchWeekStr']?.toString();
    matchTimeStr = json['matchTimeStr']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['matchTime'] = matchTime;
    data['arenaName'] = arenaName;
    data['arenaAliasName'] = arenaAliasName;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['courts'] = arr0;
    }
    data['matchId'] = matchId;
    data['arenaId'] = arenaId;
    data['leftTeamName'] = leftTeamName;
    data['leftTeamId'] = leftTeamId;
    data['LeftWin'] = LeftWin;
    data['leftTeamLogo'] = leftTeamLogo;
    data['rightTeamName'] = rightTeamName;
    data['rightTeamId'] = rightTeamId;
    data['rightWin'] = rightWin;
    data['rightTeamLogo'] = rightTeamLogo;
    data['leftTeamScore'] = leftTeamScore;
    data['leftScore'] = leftScore;
    data['rightTeamScore'] = rightTeamScore;
    data['rightScore'] = rightScore;
    data['win'] = win;
    data['status'] = status;
    data['markStatus'] = markStatus;
    data['matchDateStr'] = matchDateStr;
    data['matchWeekStr'] = matchWeekStr;
    data['matchTimeStr'] = matchTimeStr;
    return data;
  }
}
