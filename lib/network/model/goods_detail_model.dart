///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class GoodsDetailModelSpecListAttrValueItem {
/*
{
  "attrId": 1,
  "attrName": "尺码",
  "attrValueId": 9,
  "attrValueName": "xxl"
} 
*/

  int? attrId;
  String? attrName;
  int? attrValueId;
  String? attrValueName;

  GoodsDetailModelSpecListAttrValueItem({
    this.attrId,
    this.attrName,
    this.attrValueId,
    this.attrValueName,
  });
  GoodsDetailModelSpecListAttrValueItem.fromJson(Map<String, dynamic> json) {
    attrId = json['attrId']?.toInt();
    attrName = json['attrName']?.toString();
    attrValueId = json['attrValueId']?.toInt();
    attrValueName = json['attrValueName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['attrId'] = attrId;
    data['attrName'] = attrName;
    data['attrValueId'] = attrValueId;
    data['attrValueName'] = attrValueName;
    return data;
  }
}

class GoodsDetailModelSpecList {
/*
{
  "skuId": 153,
  "imageUrl": "",
  "pointPrice": 1,
  "stock": 9,
  "attrValueItem": [
    {
      "attrId": 1,
      "attrName": "尺码",
      "attrValueId": 9,
      "attrValueName": "xxl"
    }
  ]
} 
*/

  int? skuId;
  String? imageUrl;
  int? pointPrice;
  int? stock;
  List<GoodsDetailModelSpecListAttrValueItem?>? attrValueItem;

  GoodsDetailModelSpecList({
    this.skuId,
    this.imageUrl,
    this.pointPrice,
    this.stock,
    this.attrValueItem,
  });
  GoodsDetailModelSpecList.fromJson(Map<String, dynamic> json) {
    skuId = json['skuId']?.toInt();
    imageUrl = json['imageUrl']?.toString();
    pointPrice = json['pointPrice']?.toInt();
    stock = json['stock']?.toInt();
    if (json['attrValueItem'] != null) {
      final v = json['attrValueItem'];
      final arr0 = <GoodsDetailModelSpecListAttrValueItem>[];
      v.forEach((v) {
        arr0.add(GoodsDetailModelSpecListAttrValueItem.fromJson(v));
      });
      attrValueItem = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['skuId'] = skuId;
    data['imageUrl'] = imageUrl;
    data['pointPrice'] = pointPrice;
    data['stock'] = stock;
    if (attrValueItem != null) {
      final v = attrValueItem;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['attrValueItem'] = arr0;
    }
    return data;
  }
}

class GoodsDetailModel {
/*
{
  "goodsName": "柯基22",
  "goodsDese": "",
  "imageUrl": [
    "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/images/arena/logo/1.png"
  ],
  "thumbUrl": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/product/videoNew1753252169325.jpg",
  "bannerUrl": [
    "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/images/arena/logo/2.png"
  ],
  "goodType": 2,
  "vipDay": 0,
  "pointPrice": 500,
  "specList": [
    {
      "skuId": 153,
      "imageUrl": "",
      "pointPrice": 1,
      "stock": 9,
      "attrValueItem": [
        {
          "attrId": 1,
          "attrName": "尺码",
          "attrValueId": 9,
          "attrValueName": "xxl"
        }
      ]
    }
  ]
} 
*/

  String? goodsName;
  String? goodsDese;
  List<String?>? imageUrl;
  String? thumbUrl;
  List<String?>? bannerUrl;
  int? goodType;
  int? vipDay;
  int? pointPrice;
  List<GoodsDetailModelSpecList?>? specList;

  GoodsDetailModel({
    this.goodsName,
    this.goodsDese,
    this.imageUrl,
    this.thumbUrl,
    this.bannerUrl,
    this.goodType,
    this.vipDay,
    this.pointPrice,
    this.specList,
  });
  GoodsDetailModel.fromJson(Map<String, dynamic> json) {
    goodsName = json['goodsName']?.toString();
    goodsDese = json['goodsDese']?.toString();
    if (json['imageUrl'] != null) {
      final v = json['imageUrl'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      imageUrl = arr0;
    }
    thumbUrl = json['thumbUrl']?.toString();
    if (json['bannerUrl'] != null) {
      final v = json['bannerUrl'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      bannerUrl = arr0;
    }
    goodType = json['goodType']?.toInt();
    vipDay = json['vipDay']?.toInt();
    pointPrice = json['pointPrice']?.toInt();
    if (json['specList'] != null) {
      final v = json['specList'];
      final arr0 = <GoodsDetailModelSpecList>[];
      v.forEach((v) {
        arr0.add(GoodsDetailModelSpecList.fromJson(v));
      });
      specList = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['goodsName'] = goodsName;
    data['goodsDese'] = goodsDese;
    if (imageUrl != null) {
      final v = imageUrl;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['imageUrl'] = arr0;
    }
    data['thumbUrl'] = thumbUrl;
    if (bannerUrl != null) {
      final v = bannerUrl;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['bannerUrl'] = arr0;
    }
    data['goodType'] = goodType;
    data['vipDay'] = vipDay;
    data['pointPrice'] = pointPrice;
    if (specList != null) {
      final v = specList;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['specList'] = arr0;
    }
    return data;
  }
}
