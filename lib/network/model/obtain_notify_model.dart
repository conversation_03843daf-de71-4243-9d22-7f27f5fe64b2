///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ObtainNotifyModel {
/*
{
  "count": 0,
  "couponCode": "string",
  "couponId": "0",
  "expireTime": "string",
  "expiringSoon": true,
  "notify": true,
  "notifyId": 0
} 
*/

  int? count;
  String? couponCode;
  String? couponId;
  String? expireTime;
  bool? expiringSoon;
  bool? notify;
  int? notifyId;

  ObtainNotifyModel({
    this.count,
    this.couponCode,
    this.couponId,
    this.expireTime,
    this.expiringSoon,
    this.notify,
    this.notifyId,
  });
  ObtainNotifyModel.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    couponCode = json['couponCode']?.toString();
    couponId = json['couponId']?.toString();
    expireTime = json['expireTime']?.toString();
    expiringSoon = json['expiringSoon'];
    notify = json['notify'];
    notifyId = json['notifyId']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['couponCode'] = couponCode;
    data['couponId'] = couponId;
    data['expireTime'] = expireTime;
    data['expiringSoon'] = expiringSoon;
    data['notify'] = notify;
    data['notifyId'] = notifyId;
    return data;
  }
}
