///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CDKeyModel {
/*
{
  "Type": 4,
  "day": 0,
  "ArenaName": "",
  "arenaId": 0,
  "matchCouponType": "",
  "discount": "6.6折扣"
} 
*/

  int? type;
  int? day;
  String? arenaName;
  int? arenaId;
  String? matchCouponType;
  String? discount;

  CDKeyModel({
    this.type,
    this.day,
    this.arenaName,
    this.arenaId,
    this.matchCouponType,
    this.discount,
  });
  CDKeyModel.fromJson(Map<String, dynamic> json) {
    type = json['Type']?.toInt();
    day = json['day']?.toInt();
    arenaName = json['ArenaName']?.toString();
    arenaId = json['arenaId']?.toInt();
    matchCouponType = json['matchCouponType']?.toString();
    discount = json['discount']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['Type'] = type;
    data['day'] = day;
    data['ArenaName'] = arenaName;
    data['arenaId'] = arenaId;
    data['matchCouponType'] = matchCouponType;
    data['discount'] = discount;
    return data;
  }
}
