///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class InviteMiniPathModel {
/*
{
  "appRawId": "gh_04b5087f070b",
  "appId": "wx4a1876ae0bb5f000",
  "path": "pages/index/index?inviteCode=10",
  "image": "",
  "icon": "",
  "title": "好友用户53333邀请您获取精彩进球~",
  "remark": "你的专属篮球AI摄影师记录你的篮球生涯"
} 
*/

  String? appRawId;
  String? appId;
  String? path;
  String? image;
  String? icon;
  String? title;
  String? remark;

  InviteMiniPathModel({
    this.appRawId,
    this.appId,
    this.path,
    this.image,
    this.icon,
    this.title,
    this.remark,
  });
  InviteMiniPathModel.fromJson(Map<String, dynamic> json) {
    appRawId = json['appRawId']?.toString();
    appId = json['appId']?.toString();
    path = json['path']?.toString();
    image = json['image']?.toString();
    icon = json['icon']?.toString();
    title = json['title']?.toString();
    remark = json['remark']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['appRawId'] = appRawId;
    data['appId'] = appId;
    data['path'] = path;
    data['image'] = image;
    data['icon'] = icon;
    data['title'] = title;
    data['remark'] = remark;
    return data;
  }
}
