///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class SiteMapModel {
/*
{
  "id": 703883,
  "name": "球秀体育",
  "imageUrl": "",
  "address": "湖南省长沙市岳麓区中电软件园二期D6栋",
  "longitude": 112.885497,
  "latitude": 28.230369,
  "distance": 0.014668655617907214,
  "typeFlag": 1
} 
*/

  int? id;
  String? name;
  String? imageUrl;
  String? address;
  double? longitude;
  double? latitude;
  double? distance;
  int? typeFlag;

  SiteMapModel({
    this.id,
    this.name,
    this.imageUrl,
    this.address,
    this.longitude,
    this.latitude,
    this.distance,
    this.typeFlag,
  });
  SiteMapModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    name = json['name']?.toString();
    imageUrl = json['imageUrl']?.toString();
    address = json['address']?.toString();
    longitude = json['longitude']?.toDouble();
    latitude = json['latitude']?.toDouble();
    distance = json['distance']?.toDouble();
    typeFlag = json['typeFlag']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['imageUrl'] = imageUrl;
    data['address'] = address;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['distance'] = distance;
    data['typeFlag'] = typeFlag;
    return data;
  }
}
