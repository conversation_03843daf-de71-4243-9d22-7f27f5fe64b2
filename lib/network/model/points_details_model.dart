///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PointsDetailsModel {
/*
{
  "name": "签到",
  "date": "2025-04-02 14:15:31",
  "point": 2,
  "source": 0
} 
*/

  String? name;
  String? date;
  int? point;
  int? source;

  PointsDetailsModel({
    this.name,
    this.date,
    this.point,
    this.source,
  });
  PointsDetailsModel.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    date = json['date']?.toString();
    point = json['point']?.toInt();
    source = json['source']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['date'] = date;
    data['point'] = point;
    data['source'] = source;
    return data;
  }
}
