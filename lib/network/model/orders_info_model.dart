///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class OrdersInfoModel {
/*
{
  "orderId": "44303",
  "matchId": "4101",
  "teamId": "2846",
  "playerId": "58376",
  "orderStatus": 1,
  "orderNo": "M202501231426970560487",
  "leftTeamId": "564",
  "rightTeamId": "2846",
  "leftTeamName": "保利堂悦",
  "rightTeamName": "新宇宙",
  "leftTeamLogo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app_test/teamLogo/1722052332_tmp_f8112d46c523e32901d5bbe96e603921.jpg",
  "rightTeamLogo": "",
  "matchTime": "2025-01-21 19:05",
  "orderTime": "2025-01-23 14:47",
  "arenaName": "城市荣耀篮球中心",
  "arenaAliasName": "城市荣耀",
  "courts": [
    "1-1"
  ],
  "amount": "99.90",
  "orderDesc": "球员报告解锁",
  "unlockType": 1,
  "unlockBy": "球员 23号"
} 
*/

  String? orderId;
  String? matchId;
  String? teamId;
  String? playerId;
  int? orderStatus;
  String? orderNo;
  String? leftTeamId;
  String? rightTeamId;
  String? leftTeamName;
  String? rightTeamName;
  String? leftTeamLogo;
  String? rightTeamLogo;
  String? matchTime;
  String? orderTime;
  String? arenaName;
  String? arenaAliasName;
  List<String?>? courts;
  String? amount;
  String? orderDesc;
  int? unlockType;
  String? unlockBy;

  OrdersInfoModel({
    this.orderId,
    this.matchId,
    this.teamId,
    this.playerId,
    this.orderStatus,
    this.orderNo,
    this.leftTeamId,
    this.rightTeamId,
    this.leftTeamName,
    this.rightTeamName,
    this.leftTeamLogo,
    this.rightTeamLogo,
    this.matchTime,
    this.orderTime,
    this.arenaName,
    this.arenaAliasName,
    this.courts,
    this.amount,
    this.orderDesc,
    this.unlockType,
    this.unlockBy,
  });
  OrdersInfoModel.fromJson(Map<String, dynamic> json) {
    orderId = json['orderId']?.toString();
    matchId = json['matchId']?.toString();
    teamId = json['teamId']?.toString();
    playerId = json['playerId']?.toString();
    orderStatus = json['orderStatus']?.toInt();
    orderNo = json['orderNo']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    leftTeamLogo = json['leftTeamLogo']?.toString();
    rightTeamLogo = json['rightTeamLogo']?.toString();
    matchTime = json['matchTime']?.toString();
    orderTime = json['orderTime']?.toString();
    arenaName = json['arenaName']?.toString();
    arenaAliasName = json['arenaAliasName']?.toString();
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      courts = arr0;
    }
    amount = json['amount']?.toString();
    orderDesc = json['orderDesc']?.toString();
    unlockType = json['unlockType']?.toInt();
    unlockBy = json['unlockBy']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['orderId'] = orderId;
    data['matchId'] = matchId;
    data['teamId'] = teamId;
    data['playerId'] = playerId;
    data['orderStatus'] = orderStatus;
    data['orderNo'] = orderNo;
    data['leftTeamId'] = leftTeamId;
    data['rightTeamId'] = rightTeamId;
    data['leftTeamName'] = leftTeamName;
    data['rightTeamName'] = rightTeamName;
    data['leftTeamLogo'] = leftTeamLogo;
    data['rightTeamLogo'] = rightTeamLogo;
    data['matchTime'] = matchTime;
    data['orderTime'] = orderTime;
    data['arenaName'] = arenaName;
    data['arenaAliasName'] = arenaAliasName;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['courts'] = arr0;
    }
    data['amount'] = amount;
    data['orderDesc'] = orderDesc;
    data['unlockType'] = unlockType;
    data['unlockBy'] = unlockBy;
    return data;
  }
}
