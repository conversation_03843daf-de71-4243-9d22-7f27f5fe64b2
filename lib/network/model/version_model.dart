///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VersionModel {
/*
{
  "versionName": "1.0.2",
  "versionCode": "1.0.2",
  "releaseNote": "测试下升级",
  "releaseTime": "2024-12-24 13:38:22",
  "downloadUrl": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app-release/apk/app-release.apk",
  "clientType": "Android",
  "updateType": 1
} 
*/

  String? versionName;
  String? versionCode;
  String? releaseNote;
  String? releaseTime;
  String? downloadUrl;
  String? clientType;
  int? updateType;

  VersionModel({
    this.versionName,
    this.versionCode,
    this.releaseNote,
    this.releaseTime,
    this.downloadUrl,
    this.clientType,
    this.updateType,
  });
  VersionModel.fromJson(Map<String, dynamic> json) {
    versionName = json['versionName']?.toString();
    versionCode = json['versionCode']?.toString();
    releaseNote = json['releaseNote']?.toString();
    releaseTime = json['releaseTime']?.toString();
    downloadUrl = json['downloadUrl']?.toString();
    clientType = json['clientType']?.toString();
    updateType = json['updateType']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['versionName'] = versionName;
    data['versionCode'] = versionCode;
    data['releaseNote'] = releaseNote;
    data['releaseTime'] = releaseTime;
    data['downloadUrl'] = downloadUrl;
    data['clientType'] = clientType;
    data['updateType'] = updateType;
    return data;
  }
}
