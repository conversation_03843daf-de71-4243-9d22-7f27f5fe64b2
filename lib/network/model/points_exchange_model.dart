///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PointsExchangeModel {
/*
{
  "exchangeId": 3300,
  "goodId": 130,
  "name": "好东西",
  "date": "2025-07-24 15:32:14",
  "type": 2,
  "goodAttrs": "蓝色、M",
  "address": "改33",
  "phone": "17680156321",
  "pointCost": 1
} 
*/

  int? exchangeId;
  int? goodId;
  String? name;
  String? date;
  int? type;
  String? goodAttrs;
  String? address;
  String? phone;
  int? pointCost;

  PointsExchangeModel({
    this.exchangeId,
    this.goodId,
    this.name,
    this.date,
    this.type,
    this.goodAttrs,
    this.address,
    this.phone,
    this.pointCost,
  });
  PointsExchangeModel.fromJson(Map<String, dynamic> json) {
    exchangeId = json['exchangeId']?.toInt();
    goodId = json['goodId']?.toInt();
    name = json['name']?.toString();
    date = json['date']?.toString();
    type = json['type']?.toInt();
    goodAttrs = json['goodAttrs']?.toString();
    address = json['address']?.toString();
    phone = json['phone']?.toString();
    pointCost = json['pointCost']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['exchangeId'] = exchangeId;
    data['goodId'] = goodId;
    data['name'] = name;
    data['date'] = date;
    data['type'] = type;
    data['goodAttrs'] = goodAttrs;
    data['address'] = address;
    data['phone'] = phone;
    data['pointCost'] = pointCost;
    return data;
  }
}
