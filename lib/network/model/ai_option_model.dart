///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AiOptionModel {
/*
{
  "picture": "https://cdn.shootz.tech/202412261603/3d227472cabd45cbacd8ab16ce1d820f/algo_test/high_end/238/2024-12-24/16-00-00/goal-video_238_2024-12-18_20-02-37_2.jpg",
  "id": 4,
  "isSelect": false
} 
*/

  String? picture;
  int? id;
  bool? isSelect;

  AiOptionModel({
    this.picture,
    this.id,
    this.isSelect,
  });
  AiOptionModel.fromJson(Map<String, dynamic> json) {
    picture = json['picture']?.toString();
    id = json['id']?.toInt();
    isSelect = json['isSelect'] ?? false;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['picture'] = picture;
    data['id'] = id;
    data['isSelect'] = isSelect;
    return data;
  }
}
