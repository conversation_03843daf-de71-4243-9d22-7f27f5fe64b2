///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ShootingVideosModel {
/*
{
  "id": "2",
  "videoId": "2",
  "videoPath": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/merged/video/2023-08-09/oCGAR4_-nHggRTlP_mtFhqSSUTqA/db4d6f23-05d9-4ab4-95d9-3fcbd2b4e648.mp4",
  "cover": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/high_end/238/2024-12-13/20-00-00/match-highlight_238_2024-12-13_20-06-32_0_1.jpg",
  "status": 3,
  "completedTime": "2025-05-13 17:33:53"
} 
*/

  String? id;
  String? videoId;
  String? videoPath;
  String? cover;
  int? status;
  String? completedTime;
  String? name;
  ShootingVideosModel(
      {this.id,
      this.videoId,
      this.videoPath,
      this.cover,
      this.status,
      this.completedTime,
      this.name});
  ShootingVideosModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    videoId = json['videoId']?.toString();
    videoPath = json['videoPath']?.toString();
    cover = json['cover']?.toString();
    status = json['status']?.toInt();
    completedTime = json['completedTime']?.toString();
    name = json['name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['videoId'] = videoId;
    data['videoPath'] = videoPath;
    data['cover'] = cover;
    data['status'] = status;
    data['completedTime'] = completedTime;
    data['name'] = name;
    return data;
  }
}
