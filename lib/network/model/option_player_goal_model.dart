///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class OptionPlayerGoalModelVideosOtherVideos {
/*
{
  "pid": "0",
  "id": "379855502",
  "videoPath": "https://cdn.shootz.tech/202412181611/c58d61d0f92c799c756abed983a02560/algo_prod/highlights/200/2024-12-15/21-30-00/goal-video_200_2024-12-15_21-54-15_0.mp4",
  "videoTime": "21:54:15",
  "videoDate": "2024-12-15T00:00:00+08:00",
  "videoDateTime": "2024-12-15T00:00:00+08:00 21:54:15",
  "cameraIndex": 0,
  "selected": false,
  "duration": 10
} 
*/

  String? pid;
  String? id;
  String? videoPath;
  String? videoTime;
  String? videoDate;
  String? videoDateTime;
  int? cameraIndex;
  bool? selected;
  int? duration;

  OptionPlayerGoalModelVideosOtherVideos({
    this.pid,
    this.id,
    this.videoPath,
    this.videoTime,
    this.videoDate,
    this.videoDateTime,
    this.cameraIndex,
    this.selected,
    this.duration,
  });
  OptionPlayerGoalModelVideosOtherVideos.fromJson(Map<String, dynamic> json) {
    pid = json['pid']?.toString();
    id = json['id']?.toString();
    videoPath = json['videoPath']?.toString();
    videoTime = json['videoTime']?.toString();
    videoDate = json['videoDate']?.toString();
    videoDateTime = json['videoDateTime']?.toString();
    cameraIndex = json['cameraIndex']?.toInt();
    selected = json['selected'];
    duration = json['duration']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['pid'] = pid;
    data['id'] = id;
    data['videoPath'] = videoPath;
    data['videoTime'] = videoTime;
    data['videoDate'] = videoDate;
    data['videoDateTime'] = videoDateTime;
    data['cameraIndex'] = cameraIndex;
    data['selected'] = selected;
    data['duration'] = duration;
    return data;
  }
}

class OptionPlayerGoalModelVideos {
/*
{
  "id": "416407852",
  "videoPath": "https://cdn.shootz.tech/202504231806/89b1c10e52114fab232810412dfff986/algo_prod/match/124/2025-01-14/18-30-00/video/match-highlight_124_2025-01-14_18-41-04_0_2.mp4",
  "time": "18:41:04",
  "cameraIndex": 0,
  "selected": false,
  "collect": false,
  "otherVideos": [
    {
      "pid": "0",
      "id": "379855502",
      "videoPath": "https://cdn.shootz.tech/202412181611/c58d61d0f92c799c756abed983a02560/algo_prod/highlights/200/2024-12-15/21-30-00/goal-video_200_2024-12-15_21-54-15_0.mp4",
      "videoTime": "21:54:15",
      "videoDate": "2024-12-15T00:00:00+08:00",
      "videoDateTime": "2024-12-15T00:00:00+08:00 21:54:15",
      "cameraIndex": 0,
      "selected": false,
      "duration": 10
    }
  ],
  "checked": false,
  "duration": 10,
  "shootType": 4,
  "shootResult": 2,
  "teamId": "3423",
  "playerId": "53408"
} 
*/

  String? id;
  String? videoPath;
  String? time;
  int? cameraIndex;
  bool? selected;
  bool? collect;
  List<OptionPlayerGoalModelVideosOtherVideos?>? otherVideos;
  bool? checked;
  int? duration;
  int? shootType;
  int? shootResult;
  String? teamId;
  String? playerId;

  OptionPlayerGoalModelVideos({
    this.id,
    this.videoPath,
    this.time,
    this.cameraIndex,
    this.selected,
    this.collect,
    this.otherVideos,
    this.checked,
    this.duration,
    this.shootType,
    this.shootResult,
    this.teamId,
    this.playerId,
  });
  OptionPlayerGoalModelVideos.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    videoPath = json['videoPath']?.toString();
    time = json['time']?.toString();
    cameraIndex = json['cameraIndex']?.toInt();
    selected = json['selected'];
    collect = json['collect'];
    if (json['otherVideos'] != null) {
      final v = json['otherVideos'];
      final arr0 = <OptionPlayerGoalModelVideosOtherVideos>[];
      v.forEach((v) {
        arr0.add(OptionPlayerGoalModelVideosOtherVideos.fromJson(v));
      });
      otherVideos = arr0;
    }
    checked = json['checked'];
    duration = json['duration']?.toInt();
    shootType = json['shootType']?.toInt();
    shootResult = json['shootResult']?.toInt();
    teamId = json['teamId']?.toString();
    playerId = json['playerId']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['videoPath'] = videoPath;
    data['time'] = time;
    data['cameraIndex'] = cameraIndex;
    data['selected'] = selected;
    data['collect'] = collect;
    if (otherVideos != null) {
      final v = otherVideos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['otherVideos'] = arr0;
    }
    data['checked'] = checked;
    data['duration'] = duration;
    data['shootType'] = shootType;
    data['shootResult'] = shootResult;
    data['teamId'] = teamId;
    data['playerId'] = playerId;
    return data;
  }
}

class OptionPlayerGoalModel {
/*
{
  "matchId": "3811",
  "arenaId": "30",
  "currentScore": {
    "416407852": {
      "leftTeamScore": 0,
      "rightTeamScore": 0,
      "leftScoreIncr": 0,
      "rightScoreIncr": 0
    }
  },
  "cameraSet": [
    0
  ],
  "leftTeamId": "3423",
  "rightTeamId": "275",
  "leftTeamName": "本立科技",
  "rightTeamName": "华诺星空",
  "leftTeamScore": 99,
  "rightTeamScore": 83,
  "videos": [
    {
      "id": "416407852",
      "videoPath": "https://cdn.shootz.tech/202504231806/89b1c10e52114fab232810412dfff986/algo_prod/match/124/2025-01-14/18-30-00/video/match-highlight_124_2025-01-14_18-41-04_0_2.mp4",
      "time": "18:41:04",
      "cameraIndex": 0,
      "selected": false,
      "collect": false,
      "otherVideos": [
        {
          "pid": "0",
          "id": "379855502",
          "videoPath": "https://cdn.shootz.tech/202412181611/c58d61d0f92c799c756abed983a02560/algo_prod/highlights/200/2024-12-15/21-30-00/goal-video_200_2024-12-15_21-54-15_0.mp4",
          "videoTime": "21:54:15",
          "videoDate": "2024-12-15T00:00:00+08:00",
          "videoDateTime": "2024-12-15T00:00:00+08:00 21:54:15",
          "cameraIndex": 0,
          "selected": false,
          "duration": 10
        }
      ],
      "checked": false,
      "duration": 10,
      "shootType": 4,
      "shootResult": 2,
      "teamId": "3423",
      "playerId": "53408"
    }
  ],
  "matchStartTime": "2025-01-14 18:40:00",
  "leftTeamTotalScore": 0,
  "rightTeamTotalScore": 0
} 
*/

  String? matchId;
  String? arenaId;
  Map<String, dynamic>? currentScore;
  List<int?>? cameraSet;
  String? leftTeamId;
  String? rightTeamId;
  String? leftTeamName;
  String? rightTeamName;
  int? leftTeamScore;
  int? rightTeamScore;
  List<OptionPlayerGoalModelVideos?>? videos;
  String? matchStartTime;
  int? leftTeamTotalScore;
  int? rightTeamTotalScore;

  OptionPlayerGoalModel({
    this.matchId,
    this.arenaId,
    this.currentScore,
    this.cameraSet,
    this.leftTeamId,
    this.rightTeamId,
    this.leftTeamName,
    this.rightTeamName,
    this.leftTeamScore,
    this.rightTeamScore,
    this.videos,
    this.matchStartTime,
    this.leftTeamTotalScore,
    this.rightTeamTotalScore,
  });
  OptionPlayerGoalModel.fromJson(Map<String, dynamic> json) {
    matchId = json['matchId']?.toString();
    arenaId = json['arenaId']?.toString();
    currentScore = (json['currentScore'] != null) ? json['currentScore'] : null;
    if (json['cameraSet'] != null) {
      final v = json['cameraSet'];
      final arr0 = <int>[];
      v.forEach((v) {
        arr0.add(v.toInt());
      });
      cameraSet = arr0;
    }
    leftTeamId = json['leftTeamId']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    leftTeamScore = json['leftTeamScore']?.toInt();
    rightTeamScore = json['rightTeamScore']?.toInt();
    if (json['videos'] != null) {
      final v = json['videos'];
      final arr0 = <OptionPlayerGoalModelVideos>[];
      v.forEach((v) {
        arr0.add(OptionPlayerGoalModelVideos.fromJson(v));
      });
      videos = arr0;
    }
    matchStartTime = json['matchStartTime']?.toString();
    leftTeamTotalScore = json['leftTeamTotalScore']?.toInt();
    rightTeamTotalScore = json['rightTeamTotalScore']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['matchId'] = matchId;
    data['arenaId'] = arenaId;
    if (currentScore != null) {
      data['currentScore'] = currentScore!;
    }
    if (cameraSet != null) {
      final v = cameraSet;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['cameraSet'] = arr0;
    }
    data['leftTeamId'] = leftTeamId;
    data['rightTeamId'] = rightTeamId;
    data['leftTeamName'] = leftTeamName;
    data['rightTeamName'] = rightTeamName;
    data['leftTeamScore'] = leftTeamScore;
    data['rightTeamScore'] = rightTeamScore;
    if (videos != null) {
      final v = videos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['videos'] = arr0;
    }
    data['matchStartTime'] = matchStartTime;
    data['leftTeamTotalScore'] = leftTeamTotalScore;
    data['rightTeamTotalScore'] = rightTeamTotalScore;
    return data;
  }
}
