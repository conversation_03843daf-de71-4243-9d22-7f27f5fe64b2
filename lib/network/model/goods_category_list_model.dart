///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class GoodsCategoryListModel {
/*
{
  "categoryId": 0,
  "name": "string",
  "weight": 0
} 
*/

  int? categoryId;
  String? name;
  int? weight;

  GoodsCategoryListModel({
    this.categoryId,
    this.name,
    this.weight,
  });
  GoodsCategoryListModel.fromJson(Map<String, dynamic> json) {
    categoryId = json['categoryId']?.toInt();
    name = json['name']?.toString();
    weight = json['weight']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['categoryId'] = categoryId;
    data['name'] = name;
    data['weight'] = weight;
    return data;
  }
}
