///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamPlayers2Model {
/*
{
  "height": "string",
  "leader": 0,
  "me": true,
  "memberId": "0",
  "position": "string",
  "userId": "0",
  "userName": "string",
  "userPhoto": "string",
  "weight": "string"
} 
*/

  String? height;
  int? leader;
  bool? me;
  String? memberId;
  String? position;
  String? userId;
  String? userName;
  String? userPhoto;
  String? weight;

  TeamPlayers2Model({
    this.height,
    this.leader,
    this.me,
    this.memberId,
    this.position,
    this.userId,
    this.userName,
    this.userPhoto,
    this.weight,
  });
  TeamPlayers2Model.fromJson(Map<String, dynamic> json) {
    height = json['height']?.toString();
    leader = json['leader']?.toInt();
    me = json['me'];
    memberId = json['memberId']?.toString();
    position = json['position']?.toString();
    userId = json['userId']?.toString();
    userName = json['userName']?.toString();
    userPhoto = json['userPhoto']?.toString();
    weight = json['weight']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['height'] = height;
    data['leader'] = leader;
    data['me'] = me;
    data['memberId'] = memberId;
    data['position'] = position;
    data['userId'] = userId;
    data['userName'] = userName;
    data['userPhoto'] = userPhoto;
    data['weight'] = weight;
    return data;
  }
}
