///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AuditApplyTeamModel {
/*
{
  "id": 48,
  "userName": "泽宽",
  "userId": 25,
  "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83epGAIq2gmng5wHGr13Kv016EGqTg4VtibxgibO76MFOySc9YfLFox9DtZwic1ibE349v1czzh5EbAGK6Q/132",
  "createdTime": "2025-07-03 15:52:05"
} 
*/

  int? id;
  String? userName;
  int? userId;
  String? avatar;
  String? createdTime;

  AuditApplyTeamModel({
    this.id,
    this.userName,
    this.userId,
    this.avatar,
    this.createdTime,
  });
  AuditApplyTeamModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    userName = json['userName']?.toString();
    userId = json['userId']?.toInt();
    avatar = json['avatar']?.toString();
    createdTime = json['createdTime']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['userName'] = userName;
    data['userId'] = userId;
    data['avatar'] = avatar;
    data['createdTime'] = createdTime;
    return data;
  }
}
