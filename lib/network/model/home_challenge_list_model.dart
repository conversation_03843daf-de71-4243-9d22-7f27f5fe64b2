///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HomeChallengeListModel {
/*
{
  "arenaId": "0",
  "arenaName": "string",
  "avatar": "string",
  "challengeCost": 0,
  "challengeFormat": 0,
  "challengeStrength": 0,
  "challengeTitle": "string",
  "challengeType": 0,
  "createTime": "string",
  "createdBy": 0,
  "createdName": "string",
  "distance": 0,
  "id": "0",
  "matchTime": "string",
  "phone": "string",
  "status": 0,
  "teamId": "0",
  "teamLogo": "string",
  "teamName": "string",
  "weekDate": "string"
} 
*/

  String? arenaId;
  String? arenaName;
  String? avatar;
  int? challengeCost;
  int? challengeFormat;
  int? challengeStrength;
  String? challengeTitle;
  int? challengeType;
  String? createTime;
  int? createdBy;
  String? createdName;
  double? distance;
  String? id;
  String? matchTime;
  String? phone;
  int? status;
  String? teamId;
  String? teamLogo;
  String? teamName;
  String? weekDate;

  HomeChallengeListModel({
    this.arenaId,
    this.arenaName,
    this.avatar,
    this.challengeCost,
    this.challengeFormat,
    this.challengeStrength,
    this.challengeTitle,
    this.challengeType,
    this.createTime,
    this.createdBy,
    this.createdName,
    this.distance,
    this.id,
    this.matchTime,
    this.phone,
    this.status,
    this.teamId,
    this.teamLogo,
    this.teamName,
    this.weekDate,
  });
  HomeChallengeListModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    arenaName = json['arenaName']?.toString();
    avatar = json['avatar']?.toString();
    challengeCost = json['challengeCost']?.toInt();
    challengeFormat = json['challengeFormat']?.toInt();
    challengeStrength = json['challengeStrength']?.toInt();
    challengeTitle = json['challengeTitle']?.toString();
    challengeType = json['challengeType']?.toInt();
    createTime = json['createTime']?.toString();
    createdBy = json['createdBy']?.toInt();
    createdName = json['createdName']?.toString();
    distance = json['distance']?.toDouble();
    id = json['id']?.toString();
    matchTime = json['matchTime']?.toString();
    phone = json['phone']?.toString();
    status = json['status']?.toInt();
    teamId = json['teamId']?.toString();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    weekDate = json['weekDate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['arenaName'] = arenaName;
    data['avatar'] = avatar;
    data['challengeCost'] = challengeCost;
    data['challengeFormat'] = challengeFormat;
    data['challengeStrength'] = challengeStrength;
    data['challengeTitle'] = challengeTitle;
    data['challengeType'] = challengeType;
    data['createTime'] = createTime;
    data['createdBy'] = createdBy;
    data['createdName'] = createdName;
    data['distance'] = distance;
    data['id'] = id;
    data['matchTime'] = matchTime;
    data['phone'] = phone;
    data['status'] = status;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    data['weekDate'] = weekDate;
    return data;
  }
}
