///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HalfShootingRecordsModelResult {
/*
{
  "trainingId": 2,
  "id": "2",
  "startTime": "2025-04-21T14:52:33+08:00",
  "duration": "00:20:00",
  "shotCount": 100,
  "hitCount": 20,
  "rate": 10
} 
*/

  int? trainingId;
  String? id;
  String? startTime;
  String? duration;
  int? shotCount;
  int? hitCount;
  int? rate;

  HalfShootingRecordsModelResult({
    this.trainingId,
    this.id,
    this.startTime,
    this.duration,
    this.shotCount,
    this.hitCount,
    this.rate,
  });
  HalfShootingRecordsModelResult.fromJson(Map<String, dynamic> json) {
    trainingId = json['trainingId']?.toInt();
    id = json['id']?.toString();
    startTime = json['startTime']?.toString();
    duration = json['duration']?.toString();
    shotCount = json['shotCount']?.toInt();
    hitCount = json['hitCount']?.toInt();
    rate = json['rate']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['trainingId'] = trainingId;
    data['id'] = id;
    data['startTime'] = startTime;
    data['duration'] = duration;
    data['shotCount'] = shotCount;
    data['hitCount'] = hitCount;
    data['rate'] = rate;
    return data;
  }
}

class HalfShootingRecordsModel {
/*
{
  "currentPage": 1,
  "totalPages": 1,
  "totalRows": 0,
  "totalCount": 1,
  "isEnd": true,
  "result": [
    {
      "trainingId": 2,
      "id": "2",
      "startTime": "2025-04-21T14:52:33+08:00",
      "duration": "00:20:00",
      "shotCount": 100,
      "hitCount": 20,
      "rate": 10
    }
  ]
} 
*/

  int? currentPage;
  int? totalPages;
  int? totalRows;
  int? totalCount;
  bool? isEnd;
  List<HalfShootingRecordsModelResult?>? result;

  HalfShootingRecordsModel({
    this.currentPage,
    this.totalPages,
    this.totalRows,
    this.totalCount,
    this.isEnd,
    this.result,
  });
  HalfShootingRecordsModel.fromJson(Map<String, dynamic> json) {
    currentPage = json['currentPage']?.toInt();
    totalPages = json['totalPages']?.toInt();
    totalRows = json['totalRows']?.toInt();
    totalCount = json['totalCount']?.toInt();
    isEnd = json['isEnd'];
    if (json['result'] != null) {
      final v = json['result'];
      final arr0 = <HalfShootingRecordsModelResult>[];
      v.forEach((v) {
        arr0.add(HalfShootingRecordsModelResult.fromJson(v));
      });
      result = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['currentPage'] = currentPage;
    data['totalPages'] = totalPages;
    data['totalRows'] = totalRows;
    data['totalCount'] = totalCount;
    data['isEnd'] = isEnd;
    if (result != null) {
      final v = result;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['result'] = arr0;
    }
    return data;
  }
}
