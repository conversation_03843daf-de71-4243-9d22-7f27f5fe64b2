///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamListModel {
/*
{
  "id": "3148",
  "name": "传奇",
  "logo": "",
  "winCount": 1,
  "loseCount": 0,
  "matchCount": 1,
  "playerCount": 4,
  "playedCount": 1,
  "leader": false,
  "defaultTeam": false
} 
*/

  String? id;
  String? name;
  String? logo;
  int? winCount;
  int? loseCount;
  int? matchCount;
  int? playerCount;
  int? playedCount;
  int? leaderUserId;
  String? leaderUserName;
  int? rankScore;
  String? imagePath;
  bool? leader;
  bool? defaultTeam;

  TeamListModel({
    this.id,
    this.name,
    this.logo,
    this.winCount,
    this.loseCount,
    this.matchCount,
    this.playerCount,
    this.playedCount,
    this.leaderUserId,
    this.leaderUserName,
    this.rankScore,
    this.imagePath,
    this.leader,
    this.defaultTeam,
  });
  TeamListModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name']?.toString();
    logo = json['logo']?.toString();
    winCount = json['winCount']?.toInt();
    loseCount = json['loseCount']?.toInt();
    matchCount = json['matchCount']?.toInt();
    playerCount = json['playerCount']?.toInt();
    playedCount = json['playedCount']?.toInt();
    leaderUserId = json['leaderUserId']?.toInt();
    leaderUserName = json['leaderUserName']?.toString();
    rankScore = json['rankScore']?.toInt();
    winCount = json['winCount']?.toInt();
    leader = json['leader'];
    defaultTeam = json['defaultTeam'];
    final rankScoreInt = rankScore ?? 0;
    imagePath = rankScoreInt >= 3000
        ? 'assets/images/five_stars_icon.png'
        : rankScoreInt >= 2000
            ? 'assets/images/four_stars_icon.png'
            : rankScoreInt >= 1500
                ? 'assets/images/three_stars_icon.png'
                : rankScoreInt >= 1200
                    ? 'assets/images/two_stars_icon.png'
                    : 'assets/images/one_stars_icon.png';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['logo'] = logo;
    data['winCount'] = winCount;
    data['loseCount'] = loseCount;
    data['matchCount'] = matchCount;
    data['playerCount'] = playerCount;
    data['playedCount'] = playedCount;
    data['leaderUserId'] = leaderUserId;
    data['leaderUserName'] = leaderUserName;
    data['rankScore'] = rankScore;
    data['defaultTeam'] = defaultTeam;
    data['imagePath'] = imagePath;
    return data;
  }
}
