///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class BannerModel {
/*
{
  "id": "19",
  "action": 0,
  "subPath": "",
  "name": "\"banner名称没给我\"",
  "path": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app-release/app-banner/prize0430.png"
} 
*/

  String? id;
  int? action;
  String? subPath;
  String? name;
  String? path;

  BannerModel({
    this.id,
    this.action,
    this.subPath,
    this.name,
    this.path,
  });
  BannerModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    action = json['action']?.toInt();
    subPath = json['subPath']?.toString();
    name = json['name']?.toString();
    path = json['path']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['action'] = action;
    data['subPath'] = subPath;
    data['name'] = name;
    data['path'] = path;
    return data;
  }
}
