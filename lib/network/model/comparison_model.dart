///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ComparisonModelTeamsReboundKing {
/*
{
  "playerId": "0",
  "playerName": "string",
  "playerNumber": "string",
  "playerPhoto": "string",
  "score": 0
} 
*/

  String? playerId;
  String? playerName;
  String? playerNumber;
  String? playerPhoto;
  int? score;

  ComparisonModelTeamsReboundKing({
    this.playerId,
    this.playerName,
    this.playerNumber,
    this.playerPhoto,
    this.score,
  });
  ComparisonModelTeamsReboundKing.fromJson(Map<String, dynamic> json) {
    playerId = json['playerId']?.toString();
    playerName = json['playerName']?.toString();
    playerNumber = json['playerNumber']?.toString();
    playerPhoto = json['playerPhoto']?.toString();
    score = json['score']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['playerId'] = playerId;
    data['playerName'] = playerName;
    data['playerNumber'] = playerNumber;
    data['playerPhoto'] = playerPhoto;
    data['score'] = score;
    return data;
  }
}

class ComparisonModelTeamsAssistKing {
/*
{
  "playerId": "0",
  "playerName": "string",
  "playerNumber": "string",
  "playerPhoto": "string",
  "score": 0
} 
*/

  String? playerId;
  String? playerName;
  String? playerNumber;
  String? playerPhoto;
  int? score;

  ComparisonModelTeamsAssistKing({
    this.playerId,
    this.playerName,
    this.playerNumber,
    this.playerPhoto,
    this.score,
  });
  ComparisonModelTeamsAssistKing.fromJson(Map<String, dynamic> json) {
    playerId = json['playerId']?.toString();
    playerName = json['playerName']?.toString();
    playerNumber = json['playerNumber']?.toString();
    playerPhoto = json['playerPhoto']?.toString();
    score = json['score']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['playerId'] = playerId;
    data['playerName'] = playerName;
    data['playerNumber'] = playerNumber;
    data['playerPhoto'] = playerPhoto;
    data['score'] = score;
    return data;
  }
}

class ComparisonModelTeamsScoreKing {
/*
{
  "playerId": "0",
  "playerName": "string",
  "playerNumber": "string",
  "playerPhoto": "string",
  "score": 0
} 
*/

  String? playerId;
  String? playerName;
  String? playerNumber;
  String? playerPhoto;
  int? score;

  ComparisonModelTeamsScoreKing({
    this.playerId,
    this.playerName,
    this.playerNumber,
    this.playerPhoto,
    this.score,
  });
  ComparisonModelTeamsScoreKing.fromJson(Map<String, dynamic> json) {
    playerId = json['playerId']?.toString();
    playerName = json['playerName']?.toString();
    playerNumber = json['playerNumber']?.toString();
    playerPhoto = json['playerPhoto']?.toString();
    score = json['score']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['playerId'] = playerId;
    data['playerName'] = playerName;
    data['playerNumber'] = playerNumber;
    data['playerPhoto'] = playerPhoto;
    data['score'] = score;
    return data;
  }
}

class ComparisonModelTeams {
/*
{
  "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app/match/default_team_logo.jpeg",
  "teamId": "419",
  "teamName": "球秀热火",
  "matchCount": 4,
  "winRate": "0.40",
  "winRateInt": 40,
  "rebound": "61.25",
  "reboundInt": 6125,
  "assist": "13.25",
  "assistInt": 1325,
  "shotRate": "0.46",
  "shotRateInt": 45,
  "threeRate": "0.17",
  "threeRateInt": 17,
  "locked": false,
  "scoreKing": {
    "playerId": "0",
    "playerName": "string",
    "playerNumber": "string",
    "playerPhoto": "string",
    "score": 0
  },
  "assistKing": {
    "playerId": "0",
    "playerName": "string",
    "playerNumber": "string",
    "playerPhoto": "string",
    "score": 0
  },
  "reboundKing": {
    "playerId": "0",
    "playerName": "string",
    "playerNumber": "string",
    "playerPhoto": "string",
    "score": 0
  },
  "videoPath": "",
  "VideoCover": ""
} 
*/

  String? logo;
  String? teamId;
  String? teamName;
  int? matchCount;
  String? winRate;
  int? winRateInt;
  String? rebound;
  int? reboundInt;
  String? assist;
  int? assistInt;
  String? shotRate;
  int? shotRateInt;
  String? threeRate;
  int? threeRateInt;
  bool? locked;
  ComparisonModelTeamsScoreKing? scoreKing;
  ComparisonModelTeamsScoreKing? assistKing;
  ComparisonModelTeamsScoreKing? reboundKing;
  String? videoPath;
  String? VideoCover;

  ComparisonModelTeams({
    this.logo,
    this.teamId,
    this.teamName,
    this.matchCount,
    this.winRate,
    this.winRateInt,
    this.rebound,
    this.reboundInt,
    this.assist,
    this.assistInt,
    this.shotRate,
    this.shotRateInt,
    this.threeRate,
    this.threeRateInt,
    this.locked,
    this.scoreKing,
    this.assistKing,
    this.reboundKing,
    this.videoPath,
    this.VideoCover,
  });
  ComparisonModelTeams.fromJson(Map<String, dynamic> json) {
    logo = json['logo']?.toString();
    teamId = json['teamId']?.toString();
    teamName = json['teamName']?.toString();
    matchCount = json['matchCount']?.toInt();
    winRate = json['winRate']?.toString();
    winRateInt = json['winRateInt']?.toInt();
    rebound = json['rebound']?.toString();
    reboundInt = json['reboundInt']?.toInt();
    assist = json['assist']?.toString();
    assistInt = json['assistInt']?.toInt();
    shotRate = json['shotRate']?.toString();
    shotRateInt = json['shotRateInt']?.toInt();
    threeRate = json['threeRate']?.toString();
    threeRateInt = json['threeRateInt']?.toInt();
    locked = json['locked'];
    scoreKing = (json['scoreKing'] != null)
        ? ComparisonModelTeamsScoreKing.fromJson(json['scoreKing'])
        : null;
    assistKing = (json['assistKing'] != null)
        ? ComparisonModelTeamsScoreKing.fromJson(json['assistKing'])
        : null;
    reboundKing = (json['reboundKing'] != null)
        ? ComparisonModelTeamsScoreKing.fromJson(json['reboundKing'])
        : null;
    videoPath = json['videoPath']?.toString();
    VideoCover = json['VideoCover']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['logo'] = logo;
    data['teamId'] = teamId;
    data['teamName'] = teamName;
    data['matchCount'] = matchCount;
    data['winRate'] = winRate;
    data['winRateInt'] = winRateInt;
    data['rebound'] = rebound;
    data['reboundInt'] = reboundInt;
    data['assist'] = assist;
    data['assistInt'] = assistInt;
    data['shotRate'] = shotRate;
    data['shotRateInt'] = shotRateInt;
    data['threeRate'] = threeRate;
    data['threeRateInt'] = threeRateInt;
    data['locked'] = locked;
    if (scoreKing != null) {
      data['scoreKing'] = scoreKing!.toJson();
    }
    if (assistKing != null) {
      data['assistKing'] = assistKing!.toJson();
    }
    if (reboundKing != null) {
      data['reboundKing'] = reboundKing!.toJson();
    }
    data['videoPath'] = videoPath;
    data['VideoCover'] = VideoCover;
    return data;
  }
}

class ComparisonModel {
/*
{
  "teams": [
    {
      "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app/match/default_team_logo.jpeg",
      "teamId": "419",
      "teamName": "球秀热火",
      "matchCount": 4,
      "winRate": "0.40",
      "winRateInt": 40,
      "rebound": "61.25",
      "reboundInt": 6125,
      "assist": "13.25",
      "assistInt": 1325,
      "shotRate": "0.46",
      "shotRateInt": 45,
      "threeRate": "0.17",
      "threeRateInt": 17,
      "locked": false,
      "scoreKing": {
        "playerId": "0",
        "playerName": "string",
        "playerNumber": "string",
        "playerPhoto": "string",
        "score": 0
      },
      "assistKing": {
        "playerId": "0",
        "playerName": "string",
        "playerNumber": "string",
        "playerPhoto": "string",
        "score": 0
      },
      "reboundKing": {
        "playerId": "0",
        "playerName": "string",
        "playerNumber": "string",
        "playerPhoto": "string",
        "score": 0
      },
      "videoPath": "",
      "VideoCover": ""
    }
  ],
  "subscribed": false,
  "locked": true
} 
*/

  List<ComparisonModelTeams?>? teams;
  bool? subscribed;
  bool? locked;

  ComparisonModel({
    this.teams,
    this.subscribed,
    this.locked,
  });
  ComparisonModel.fromJson(Map<String, dynamic> json) {
    if (json['teams'] != null) {
      final v = json['teams'];
      final arr0 = <ComparisonModelTeams>[];
      v.forEach((v) {
        arr0.add(ComparisonModelTeams.fromJson(v));
      });
      teams = arr0;
    }
    subscribed = json['subscribed'];
    locked = json['locked'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (teams != null) {
      final v = teams;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['teams'] = arr0;
    }
    data['subscribed'] = subscribed;
    data['locked'] = locked;
    return data;
  }
}
