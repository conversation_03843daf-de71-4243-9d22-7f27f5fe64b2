///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MessageHasUnreadModel {
/*
{
  "hasUnread": true,
  "officialUnreadNum": 0,
  "sysUnreadNum": 0
} 
*/

  bool? hasUnread;
  int? officialUnreadNum;
  int? sysUnreadNum;

  MessageHasUnreadModel({
    this.hasUnread,
    this.officialUnreadNum,
    this.sysUnreadNum,
  });
  MessageHasUnreadModel.fromJson(Map<String, dynamic> json) {
    hasUnread = json['hasUnread'];
    officialUnreadNum = json['officialUnreadNum']?.toInt();
    sysUnreadNum = json['sysUnreadNum']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['hasUnread'] = hasUnread;
    data['officialUnreadNum'] = officialUnreadNum;
    data['sysUnreadNum'] = sysUnreadNum;
    return data;
  }
}
