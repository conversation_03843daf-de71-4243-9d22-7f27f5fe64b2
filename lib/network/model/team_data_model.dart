///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamDataModelTeamDataItemByRecentTen {
/*
{
  "avgScore": 0,
  "freeHitRate": "string",
  "freeNum": 0,
  "hitNumRate": "string",
  "maxScore": 0,
  "shotNum": 0,
  "threeHitRate": "string",
  "threeNum": 0,
  "win": 0,
  "winRate": "string",
  "description": "string"
} 
*/

  int? avgScore;
  String? freeHitRate;
  int? freeNum;
  String? hitNumRate;
  int? maxScore;
  int? shotNum;
  String? threeHitRate;
  int? threeNum;
  int? win;
  String? winRate;
  String? description;

  TeamDataModelTeamDataItemByRecentTen({
    this.avgScore,
    this.freeHitRate,
    this.freeNum,
    this.hitNumRate,
    this.maxScore,
    this.shotNum,
    this.threeHitRate,
    this.threeNum,
    this.win,
    this.winRate,
    this.description,
  });
  TeamDataModelTeamDataItemByRecentTen.fromJson(Map<String, dynamic> json) {
    avgScore = json['avgScore']?.toInt();
    freeHitRate = json['freeHitRate']?.toString();
    freeNum = json['freeNum']?.toInt();
    hitNumRate = json['hitNumRate']?.toString();
    maxScore = json['maxScore']?.toInt();
    shotNum = json['shotNum']?.toInt();
    threeHitRate = json['threeHitRate']?.toString();
    threeNum = json['threeNum']?.toInt();
    win = json['win']?.toInt();
    winRate = json['winRate']?.toString();
    description = json['description']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['avgScore'] = avgScore;
    data['freeHitRate'] = freeHitRate;
    data['freeNum'] = freeNum;
    data['hitNumRate'] = hitNumRate;
    data['maxScore'] = maxScore;
    data['shotNum'] = shotNum;
    data['threeHitRate'] = threeHitRate;
    data['threeNum'] = threeNum;
    data['win'] = win;
    data['winRate'] = winRate;
    data['description'] = description;
    return data;
  }
}

class TeamDataModel {
/*
{
  "matchCount": 0,
  "teamDataItemByRecentTen": {
    "avgScore": 0,
    "freeHitRate": "string",
    "freeNum": 0,
    "hitNumRate": "string",
    "maxScore": 0,
    "shotNum": 0,
    "threeHitRate": "string",
    "threeNum": 0,
    "win": 0,
    "winRate": "string",
    "description": "string"
  },
  "totalAssists": 0,
  "totalRebound": 0,
  "totalScore": 0,
  "winCount": 0,
  "winRate": "string"
} 
*/

  int? matchCount;
  TeamDataModelTeamDataItemByRecentTen? teamDataItemByRecentTen;
  int? totalAssists;
  int? totalRebound;
  int? totalScore;
  int? winCount;
  String? winRate;

  TeamDataModel({
    this.matchCount,
    this.teamDataItemByRecentTen,
    this.totalAssists,
    this.totalRebound,
    this.totalScore,
    this.winCount,
    this.winRate,
  });
  TeamDataModel.fromJson(Map<String, dynamic> json) {
    matchCount = json['matchCount']?.toInt();
    teamDataItemByRecentTen = (json['teamDataItemByRecentTen'] != null)
        ? TeamDataModelTeamDataItemByRecentTen.fromJson(
            json['teamDataItemByRecentTen'])
        : null;
    totalAssists = json['totalAssists']?.toInt();
    totalRebound = json['totalRebound']?.toInt();
    totalScore = json['totalScore']?.toInt();
    winCount = json['winCount']?.toInt();
    winRate = json['winRate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['matchCount'] = matchCount;
    if (teamDataItemByRecentTen != null) {
      data['teamDataItemByRecentTen'] = teamDataItemByRecentTen!.toJson();
    }
    data['totalAssists'] = totalAssists;
    data['totalRebound'] = totalRebound;
    data['totalScore'] = totalScore;
    data['winCount'] = winCount;
    data['winRate'] = winRate;
    return data;
  }
}
