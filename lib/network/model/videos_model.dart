///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VideosModel {
/*
{
  "arenaId": "30",
  "arenaName": "译星体育(星耀馆)",
  "arenaAliasName": "星耀",
  "arenaLogo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/images/arena/logo/30.png",
  "matchTime": "18:40",
  "matchDate": "2025-01-14",
  "matchWeek": "周二",
  "leftTeamId": "275",
  "rightTeamId": "3423",
  "leftTeamName": "华诺星空",
  "rightTeamName": "本立科技",
  "leftTeamLogo": "",
  "rightTeamLogo": "",
  "leftScore": 83,
  "rightScore": 99,
  "shotType": 4,
  "shotResult": 2,
  "assist": false,
  "rebound": false,
  "videoPath": "https://cdn.shootz.tech/202501181053/43cfa67d2e120014a8875d59edf37927/algo_prod/match/124/2025-01-14/18-30-00/video/match-highlight_124_2025-01-14_18-41-04_0_2.mp4",
  "videoCover": "https://cdn.shootz.tech/202501181053/0e455cc5dec40042e7b4e238f7a61842/algo_prod/match/124/2025-01-14/18-30-00/video/match-highlight_124_2025-01-14_18-41-04_0_2.jpg"
} 
*/

  String? arenaId;
  String? arenaName;
  String? arenaAliasName;
  String? arenaLogo;
  String? matchTime;
  String? matchDate;
  String? matchWeek;
  String? leftTeamId;
  String? rightTeamId;
  String? leftTeamName;
  String? rightTeamName;
  String? leftTeamLogo;
  String? rightTeamLogo;
  int? leftScore;
  int? rightScore;
  int? shotType;
  int? shotResult;
  bool? assist;
  bool? rebound;
  String? videoPath;
  String? videoCover;

  VideosModel({
    this.arenaId,
    this.arenaName,
    this.arenaAliasName,
    this.arenaLogo,
    this.matchTime,
    this.matchDate,
    this.matchWeek,
    this.leftTeamId,
    this.rightTeamId,
    this.leftTeamName,
    this.rightTeamName,
    this.leftTeamLogo,
    this.rightTeamLogo,
    this.leftScore,
    this.rightScore,
    this.shotType,
    this.shotResult,
    this.assist,
    this.rebound,
    this.videoPath,
    this.videoCover,
  });
  VideosModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    arenaName = json['arenaName']?.toString();
    arenaAliasName = json['arenaAliasName']?.toString();
    arenaLogo = json['arenaLogo']?.toString();
    matchTime = json['matchTime']?.toString();
    matchDate = json['matchDate']?.toString();
    matchWeek = json['matchWeek']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    leftTeamLogo = json['leftTeamLogo']?.toString();
    rightTeamLogo = json['rightTeamLogo']?.toString();
    leftScore = json['leftScore']?.toInt();
    rightScore = json['rightScore']?.toInt();
    shotType = json['shotType']?.toInt();
    shotResult = json['shotResult']?.toInt();
    assist = json['assist'];
    rebound = json['rebound'];
    videoPath = json['videoPath']?.toString();
    videoCover = json['videoCover']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['arenaName'] = arenaName;
    data['arenaAliasName'] = arenaAliasName;
    data['arenaLogo'] = arenaLogo;
    data['matchTime'] = matchTime;
    data['matchDate'] = matchDate;
    data['matchWeek'] = matchWeek;
    data['leftTeamId'] = leftTeamId;
    data['rightTeamId'] = rightTeamId;
    data['leftTeamName'] = leftTeamName;
    data['rightTeamName'] = rightTeamName;
    data['leftTeamLogo'] = leftTeamLogo;
    data['rightTeamLogo'] = rightTeamLogo;
    data['leftScore'] = leftScore;
    data['rightScore'] = rightScore;
    data['shotType'] = shotType;
    data['shotResult'] = shotResult;
    data['assist'] = assist;
    data['rebound'] = rebound;
    data['videoPath'] = videoPath;
    data['videoCover'] = videoCover;
    return data;
  }
}
