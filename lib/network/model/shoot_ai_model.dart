///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ShootAiModel {
/*
{
  "id": "0",
  "isSelect": false,
  "photo": "string"
} 
*/

  String? id;
  bool? isSelect;
  String? photo;

  ShootAiModel({
    this.id,
    this.isSelect,
    this.photo,
  });
  ShootAiModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    isSelect = json['isSelect'];
    photo = json['photo']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['isSelect'] = isSelect;
    data['photo'] = photo;
    return data;
  }
}
