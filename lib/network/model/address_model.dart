///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AddressModel {
/*
{
  "id": 0,
  "address": "string",
  "city": "string",
  "cityName": "string",
  "district": "string",
  "districtName": "string",
  "isDefault": 0,
  "name": "string",
  "phone": "string",
  "province": "string",
  "provinceName": "string"
} 
*/

  int? id;
  String? address;
  String? city;
  String? cityName;
  String? district;
  String? districtName;
  int? isDefault;
  String? name;
  String? phone;
  String? province;
  String? provinceName;

  AddressModel({
    this.id,
    this.address,
    this.city,
    this.cityName,
    this.district,
    this.districtName,
    this.isDefault,
    this.name,
    this.phone,
    this.province,
    this.provinceName,
  });
  AddressModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    address = json['address']?.toString();
    city = json['city']?.toString();
    cityName = json['cityName']?.toString();
    district = json['district']?.toString();
    districtName = json['districtName']?.toString();
    isDefault = json['isDefault']?.toInt();
    name = json['name']?.toString();
    phone = json['phone']?.toString();
    province = json['province']?.toString();
    provinceName = json['provinceName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['address'] = address;
    data['city'] = city;
    data['cityName'] = cityName;
    data['district'] = district;
    data['districtName'] = districtName;
    data['isDefault'] = isDefault;
    data['name'] = name;
    data['phone'] = phone;
    data['province'] = province;
    data['provinceName'] = provinceName;
    return data;
  }
}
