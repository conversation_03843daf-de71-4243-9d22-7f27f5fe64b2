///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MessageListModel {
/*
{
  "businessId": 0,
  "content": "string",
  "coverImg": "string",
  "isRead": true,
  "linkUrl": "string",
  "messageId": 0,
  "sendLogo": "string",
  "sendName": "string",
  "sendTime": "string",
  "subType": 0,
  "title": "string",
  "type": 0
} 
*/

  int? businessId;
  String? content;
  String? coverImg;
  bool? isRead;
  String? linkUrl;
  int? messageId;
  String? sendLogo;
  String? sendName;
  String? sendTime;
  int? subType;
  String? title;
  int? type;

  MessageListModel({
    this.businessId,
    this.content,
    this.coverImg,
    this.isRead,
    this.linkUrl,
    this.messageId,
    this.sendLogo,
    this.sendName,
    this.sendTime,
    this.subType,
    this.title,
    this.type,
  });
  MessageListModel.fromJson(Map<String, dynamic> json) {
    businessId = json['businessId']?.toInt();
    content = json['content']?.toString();
    coverImg = json['coverImg']?.toString();
    isRead = json['isRead'];
    linkUrl = json['linkUrl']?.toString();
    messageId = json['messageId']?.toInt();
    sendLogo = json['sendLogo']?.toString();
    sendName = json['sendName']?.toString();
    sendTime = json['sendTime']?.toString();
    subType = json['subType']?.toInt();
    title = json['title']?.toString();
    type = json['type']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['businessId'] = businessId;
    data['content'] = content;
    data['coverImg'] = coverImg;
    data['isRead'] = isRead;
    data['linkUrl'] = linkUrl;
    data['messageId'] = messageId;
    data['sendLogo'] = sendLogo;
    data['sendName'] = sendName;
    data['sendTime'] = sendTime;
    data['subType'] = subType;
    data['title'] = title;
    data['type'] = type;
    return data;
  }
}
