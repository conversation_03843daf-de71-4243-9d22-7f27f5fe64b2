///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamPlayerRankModel {
/*
{
  "userId": "269705",
  "memberId": "10125",
  "userName": "用户56321",
  "avatar": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app/avatar/1736926448_1000028144.jpg",
  "score": 1,
  "bindMatchNum": 1
} 
*/

  String? userId;
  String? memberId;
  String? userName;
  String? avatar;
  int? score;
  int? bindMatchNum;

  TeamPlayerRankModel({
    this.userId,
    this.memberId,
    this.userName,
    this.avatar,
    this.score,
    this.bindMatchNum,
  });
  TeamPlayerRankModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId']?.toString();
    memberId = json['memberId']?.toString();
    userName = json['userName']?.toString();
    avatar = json['avatar']?.toString();
    score = json['score']?.toInt();
    bindMatchNum = json['bindMatchNum']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['userId'] = userId;
    data['memberId'] = memberId;
    data['userName'] = userName;
    data['avatar'] = avatar;
    data['score'] = score;
    data['bindMatchNum'] = bindMatchNum;
    return data;
  }
}
