///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class GameCouponsModel {
/*
{
  "id": "4",
  "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/images/index/career.png",
  "name": "赛事个人解锁券",
  "code": "MP0001000100010001",
  "rangeType": 0,
  "startTime": "2025-01-18 16:34:50",
  "endTime": "2025-03-18 16:34:56",
  "limitUse": 1,
  "type": 1,
  "remainingDays": 1,
  "price": "0",
  "discount": "1",
  "description": "赛事个人解锁，仅能单人解锁",
  "disabledReason": "赛事个人解锁，仅能单人解锁"
} 
*/

  String? id;
  String? logo;
  String? name;
  String? code;
  int? rangeType;
  String? startTime;
  String? endTime;
  int? limitUse;
  int? type;
  int? remainingDays;
  String? price;
  String? discount;
  String? description;
  String? disabledReason;

  GameCouponsModel({
    this.id,
    this.logo,
    this.name,
    this.code,
    this.rangeType,
    this.startTime,
    this.endTime,
    this.limitUse,
    this.type,
    this.remainingDays,
    this.price,
    this.discount,
    this.description,
    this.disabledReason,
  });
  GameCouponsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    logo = json['logo']?.toString();
    name = json['name']?.toString();
    code = json['code']?.toString();
    rangeType = json['rangeType']?.toInt();
    startTime = json['startTime']?.toString();
    endTime = json['endTime']?.toString();
    limitUse = json['limitUse']?.toInt();
    type = json['type']?.toInt();
    remainingDays = json['remainingDays']?.toInt();
    price = json['price']?.toString();
    discount = json['discount']?.toString();
    description = json['description']?.toString();
    disabledReason = json['disabledReason']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['logo'] = logo;
    data['name'] = name;
    data['code'] = code;
    data['rangeType'] = rangeType;
    data['startTime'] = startTime;
    data['endTime'] = endTime;
    data['limitUse'] = limitUse;
    data['type'] = type;
    data['remainingDays'] = remainingDays;
    data['price'] = price;
    data['discount'] = discount;
    data['description'] = description;
    data['disabledReason'] = disabledReason;
    return data;
  }
}
