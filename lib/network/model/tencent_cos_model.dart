///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TencentCosModel {
/*
{
  "tmpSecretId": "AKIDVSAqfsYgVAxFH5GaUTj1_ejqaRaoaf-da6UAM1zaYM7EcwQ24p7VSVRh30omVXax",
  "tmpSecretKey": "EpI98fOJrqzuoDs3ndbGDZctQ2X1WyCE0mwPi5Fd/uM=",
  "sessionToken": "1IwVfG6hLADAACrVf0A0xYreq2eaShaaca874afeb374a5c71d21e6e092fb485bcbR9I4grEwSR93i8qndvIZsMZeQOnCnrFVexvPpacGT7-nXadNAWeioThViAwzlsLxN-sMzip5PfBKE4pNcS79r6Og1USjhWSmp-E6MvGjDCPVCDlgu3jYowtSWTBGKdsgvmo3qEub-7xuM-I7xCy6kI6AbGrA9YXsboQt2OMpJfCbd7uyv06a7pNwOLIT3QHiK3QdEAwV_BkdXHigVkBw6PkU9Qe-uMOTQBRgGwomG8wtGLREWaGVLjBjH2jHyQdguYIIg29_ya5Oso48Lh0GeThOp7h_lClkYOoxJAGliiH6xS-66dsGz0ER1ZxvhCy2TnC0DxHe3_IHu4WpezsbTTXhpz8IYvx2xkmzwFeGClIftRMDki251NaTXdO0deYlXpSr7EUfpVKR5FjNbWEdZpu643eQC380KEQb0EogkRLRl57tv-lFRQp25-TnJWLOtiU5XEakKfnyxGsz_-4MmOM9sqVgAf79NWaeDuyEsAmYjcGVFcwAbg_1f4UqrLZPh41Z-OT1Dl5ikKbEL4nE_DtdfJSAbtit4KryoGWtytKz7wIgtraDsYTD7ysjsQ",
  "region": "ap-guangzhou",
  "bucketName": "uni-shootz-1308047407",
  "endpoint": "myqcloud.com",
  "expiredTime": 1747105930,
  "startTime": 1747104130
} 
*/

  String? tmpSecretId;
  String? tmpSecretKey;
  String? sessionToken;
  String? region;
  String? bucketName;
  String? endpoint;
  int? expiredTime;
  int? startTime;

  TencentCosModel({
    this.tmpSecretId,
    this.tmpSecretKey,
    this.sessionToken,
    this.region,
    this.bucketName,
    this.endpoint,
    this.expiredTime,
    this.startTime,
  });
  TencentCosModel.fromJson(Map<String, dynamic> json) {
    tmpSecretId = json['tmpSecretId']?.toString();
    tmpSecretKey = json['tmpSecretKey']?.toString();
    sessionToken = json['sessionToken']?.toString();
    region = json['region']?.toString();
    bucketName = json['bucketName']?.toString();
    endpoint = json['endpoint']?.toString();
    expiredTime = json['expiredTime']?.toInt();
    startTime = json['startTime']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['tmpSecretId'] = tmpSecretId;
    data['tmpSecretKey'] = tmpSecretKey;
    data['sessionToken'] = sessionToken;
    data['region'] = region;
    data['bucketName'] = bucketName;
    data['endpoint'] = endpoint;
    data['expiredTime'] = expiredTime;
    data['startTime'] = startTime;
    return data;
  }
}
