///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MessageTypeListModel {
/*
{
  "latestTitle": "string",
  "totalNum": 0,
  "typeId": 0,
  "typeName": "string",
  "unreadNum": 0
} 
*/

  String? latestTitle;
  int? totalNum;
  int? typeId;
  String? typeName;
  int? unreadNum;

  MessageTypeListModel({
    this.latestTitle,
    this.totalNum,
    this.typeId,
    this.typeName,
    this.unreadNum,
  });
  MessageTypeListModel.fromJson(Map<String, dynamic> json) {
    latestTitle = json['latestTitle']?.toString();
    totalNum = json['totalNum']?.toInt();
    typeId = json['typeId']?.toInt();
    typeName = json['typeName']?.toString();
    unreadNum = json['unreadNum']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['latestTitle'] = latestTitle;
    data['totalNum'] = totalNum;
    data['typeId'] = typeId;
    data['typeName'] = typeName;
    data['unreadNum'] = unreadNum;
    return data;
  }
}
