import 'package:shoot_z/network/model/matches_model.dart';

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
//
///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamCompetitionsModel {
/*
{
  "ShootRate": "string",
  "averageScore": 0,
  "competitionId": 0,
  "competitionName": "string",
  "endTime": "string",
  "outcomeRate": "string",
  "startTime": "string"
} 
*/

  String? ShootRate;
  int? averageScore;
  int? competitionId;
  String? competitionName;
  String? endTime;
  String? outcomeRate;
  String? startTime;

  TeamCompetitionsModel({
    this.ShootRate,
    this.averageScore,
    this.competitionId,
    this.competitionName,
    this.endTime,
    this.outcomeRate,
    this.startTime,
  });
  TeamCompetitionsModel.fromJson(Map<String, dynamic> json) {
    ShootRate = json['ShootRate']?.toString();
    averageScore = json['averageScore']?.toInt();
    competitionId = json['competitionId']?.toInt();
    competitionName = json['competitionName']?.toString();
    endTime = json['endTime']?.toString();
    outcomeRate = json['outcomeRate']?.toString();
    startTime = json['startTime']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['ShootRate'] = ShootRate;
    data['averageScore'] = averageScore;
    data['competitionId'] = competitionId;
    data['competitionName'] = competitionName;
    data['endTime'] = endTime;
    data['outcomeRate'] = outcomeRate;
    data['startTime'] = startTime;
    return data;
  }
}

class HomeHotRecordModelCompetitions {
/*
{
  "arenLongitude": 0,
  "arenaAddress": "string",
  "arenaCount": 0,
  "arenaId": "0",
  "arenaImageUrl": "string",
  "arenaLatitude": 0,
  "arenaName": "string",
  "competitionId": 0,
  "competitionName": "string",
  "endTime": "string",
  "nextMatchTime": "string",
  "registrationDeadline": "string",
  "startTime": "string",
  "status": 0
} 
*/

  int? arenLongitude;
  String? arenaAddress;
  int? arenaCount;
  String? arenaId;
  String? arenaImageUrl;
  int? arenaLatitude;
  String? arenaName;
  int? competitionId;
  String? competitionName;
  String? endTime;
  String? nextMatchTime;
  String? registrationDeadline;
  String? startTime;
  int? status;

  HomeHotRecordModelCompetitions({
    this.arenLongitude,
    this.arenaAddress,
    this.arenaCount,
    this.arenaId,
    this.arenaImageUrl,
    this.arenaLatitude,
    this.arenaName,
    this.competitionId,
    this.competitionName,
    this.endTime,
    this.nextMatchTime,
    this.registrationDeadline,
    this.startTime,
    this.status,
  });
  HomeHotRecordModelCompetitions.fromJson(Map<String, dynamic> json) {
    arenLongitude = json['arenLongitude']?.toInt();
    arenaAddress = json['arenaAddress']?.toString();
    arenaCount = json['arenaCount']?.toInt();
    arenaId = json['arenaId']?.toString();
    arenaImageUrl = json['arenaImageUrl']?.toString();
    arenaLatitude = json['arenaLatitude']?.toInt();
    arenaName = json['arenaName']?.toString();
    competitionId = json['competitionId']?.toInt();
    competitionName = json['competitionName']?.toString();
    endTime = json['endTime']?.toString();
    nextMatchTime = json['nextMatchTime']?.toString();
    registrationDeadline = json['registrationDeadline']?.toString();
    startTime = json['startTime']?.toString();
    status = json['status']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenLongitude'] = arenLongitude;
    data['arenaAddress'] = arenaAddress;
    data['arenaCount'] = arenaCount;
    data['arenaId'] = arenaId;
    data['arenaImageUrl'] = arenaImageUrl;
    data['arenaLatitude'] = arenaLatitude;
    data['arenaName'] = arenaName;
    data['competitionId'] = competitionId;
    data['competitionName'] = competitionName;
    data['endTime'] = endTime;
    data['nextMatchTime'] = nextMatchTime;
    data['registrationDeadline'] = registrationDeadline;
    data['startTime'] = startTime;
    data['status'] = status;
    return data;
  }
}

class HomeHotRecordModel {
/*
{
  "competitions": [
    {
      "arenLongitude": 0,
      "arenaAddress": "string",
      "arenaCount": 0,
      "arenaId": "0",
      "arenaImageUrl": "string",
      "arenaLatitude": 0,
      "arenaName": "string",
      "competitionId": 0,
      "competitionName": "string",
      "endTime": "string",
      "nextMatchTime": "string",
      "registrationDeadline": "string",
      "startTime": "string",
      "status": 0
    }
  ],
  "matches": [
    {
      "leftRankScore": 0,
      "leftScore": 0,
      "leftTeamId": "0",
      "leftTeamLogo": "string",
      "leftTeamName": "string",
      "markStatus": 0,
      "matchDate": "string",
      "matchDateStr": "string",
      "matchDateWeek": "string",
      "matchDateYtt": "string",
      "matchId": "0",
      "matchTimeStr": "string",
      "rightRankScore": 0,
      "rightScore": 0,
      "rightTeamId": "0",
      "rightTeamLogo": "string",
      "rightTeamName": "string",
      "status": 0
    }
  ]
} 
*/

  List<HomeHotRecordModelCompetitions?>? competitions;
  List<MatchesModel?>? matches;

  HomeHotRecordModel({
    this.competitions,
    this.matches,
  });
  HomeHotRecordModel.fromJson(Map<String, dynamic> json) {
    if (json['competitions'] != null) {
      final v = json['competitions'];
      final arr0 = <HomeHotRecordModelCompetitions>[];
      v.forEach((v) {
        arr0.add(HomeHotRecordModelCompetitions.fromJson(v));
      });
      competitions = arr0;
    }
    if (json['matches'] != null) {
      final v = json['matches'];
      final arr0 = <MatchesModel>[];
      v.forEach((v) {
        arr0.add(MatchesModel.fromJson(v));
      });
      matches = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (competitions != null) {
      final v = competitions;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['competitions'] = arr0;
    }
    if (matches != null) {
      final v = matches;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['matches'] = arr0;
    }
    return data;
  }
}
