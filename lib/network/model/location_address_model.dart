///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class LocationAddressModelNodesNodes {
/*
{
  "name": "岳麓区",
  "code": "430104",
  "nodes": null
} 
*/

  String? name;
  String? code;
  String? nodes;

  LocationAddressModelNodesNodes({
    this.name,
    this.code,
    this.nodes,
  });
  LocationAddressModelNodesNodes.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    code = json['code']?.toString();
    nodes = json['nodes']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['code'] = code;
    data['nodes'] = nodes;
    return data;
  }
}

class LocationAddressModelNodes {
/*
{
  "name": "长沙市",
  "code": "430100",
  "nodes": [
    {
      "name": "岳麓区",
      "code": "430104",
      "nodes": null
    }
  ]
} 
*/

  String? name;
  String? code;
  List<LocationAddressModelNodesNodes?>? nodes;

  LocationAddressModelNodes({
    this.name,
    this.code,
    this.nodes,
  });
  LocationAddressModelNodes.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    code = json['code']?.toString();
    if (json['nodes'] != null) {
      final v = json['nodes'];
      final arr0 = <LocationAddressModelNodesNodes>[];
      v.forEach((v) {
        arr0.add(LocationAddressModelNodesNodes.fromJson(v));
      });
      nodes = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['code'] = code;
    if (nodes != null) {
      final v = nodes;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['nodes'] = arr0;
    }
    return data;
  }
}

class LocationAddressModel {
/*
{
  "name": "湖南省",
  "code": "430000",
  "nodes": [
    {
      "name": "长沙市",
      "code": "430100",
      "nodes": [
        {
          "name": "岳麓区",
          "code": "430104",
          "nodes": null
        }
      ]
    }
  ]
} 
*/

  String? name;
  String? code;
  List<LocationAddressModelNodes?>? nodes;

  LocationAddressModel({
    this.name,
    this.code,
    this.nodes,
  });
  LocationAddressModel.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    code = json['code']?.toString();
    if (json['nodes'] != null) {
      final v = json['nodes'];
      final arr0 = <LocationAddressModelNodes>[];
      v.forEach((v) {
        arr0.add(LocationAddressModelNodes.fromJson(v));
      });
      nodes = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['code'] = code;
    if (nodes != null) {
      final v = nodes;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['nodes'] = arr0;
    }
    return data;
  }
}
