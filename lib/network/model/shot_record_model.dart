///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ShotRecordModel {
/*
{
  "player_confidence": 0.6875,
  "file_path": "file:///private/var/mobile/Containers/Data/Application/97011915-C6B7-499C-B3E6-00F9028AC085/tmp/cache_20250514_140954.mp4",
  "start_time": "2025-05-14 14:09:43",
  "shoot_time": 1747202999.8294492,
  "training_id": "5",
  "event_id": "5",
  "is_goal": true,
  "player_image_path": "55CB9EC6-8ABE-420E-9B70-F356DACE8DED.jpg",
  "goal_time": 1747203001.192927,
  "shoot_coord": [
    -39.64937687647534
  ],
  "imgLoadOK": "0",
  "videoLoadOK": "0",
  "created_at": 768895802.796386
} 
*/

  double? playerConfidence;
  String? filePath;
  String? startTime;
  double? shootTime;
  String? trainingId;
  String? eventId;
  bool? isGoal;
  String? playerImagePath;
  double? goalTime;
  List<double?>? shootCoord;
  String? imgLoadOK;
  String? videoLoadOK;
  double? createdAt;

  ShotRecordModel({
    this.playerConfidence,
    this.filePath,
    this.startTime,
    this.shootTime,
    this.trainingId,
    this.eventId,
    this.isGoal,
    this.playerImagePath,
    this.goalTime,
    this.shootCoord,
    this.imgLoadOK,
    this.videoLoadOK,
    this.createdAt,
  });
  ShotRecordModel.fromJson(Map<String, dynamic> json) {
    playerConfidence = json['player_confidence']?.toDouble();
    filePath = json['file_path']?.toString();
    startTime = json['start_time']?.toString();
    shootTime = json['shoot_time']?.toDouble();
    trainingId = json['training_id']?.toString();
    eventId = json['id']?.toString();
    isGoal = json['is_goal'];
    playerImagePath = json['player_image_path']?.toString();
    goalTime = json['goal_time']?.toDouble();
    if (json['shoot_coord'] != null) {
      final v = json['shoot_coord'];
      final arr0 = <double>[];
      v.forEach((v) {
        arr0.add(v.toDouble());
      });
      shootCoord = arr0;
    }
    imgLoadOK = json['imgLoadOK']?.toString();
    videoLoadOK = json['videoLoadOK']?.toString();
    createdAt = json['created_at']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['player_confidence'] = playerConfidence;
    data['file_path'] = filePath;
    data['start_time'] = startTime;
    data['shoot_time'] = shootTime;
    data['training_id'] = trainingId;
    data['id'] = eventId;
    data['is_goal'] = isGoal;
    data['player_image_path'] = playerImagePath;
    data['goal_time'] = goalTime;
    if (shootCoord != null) {
      final v = shootCoord;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['shoot_coord'] = arr0;
    }
    data['imgLoadOK'] = imgLoadOK;
    data['videoLoadOK'] = videoLoadOK;
    data['created_at'] = createdAt;
    return data;
  }
}
