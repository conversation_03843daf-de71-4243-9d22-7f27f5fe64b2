///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class GoodsListModel {
/*
{
  "spuId": 21,
  "skuCode": "",
  "goodsName": "好东西",
  "goodsDese": "",
  "imageUrl": "",
  "categoryId": 2,
  "pointPrice": 100,
  "goodType": 0,
  "vipDay": 0,
  "stock": 0,
  "sort": 1,
  "status": 0,
  "skuStatue": 0
} 
*/

  int? spuId;
  String? skuCode;
  String? goodsName;
  String? goodsDese;
  String? imageUrl;
  int? categoryId;
  int? pointPrice;
  int? goodType;
  int? vipDay;
  int? stock;
  int? sort;
  int? status;
  int? skuStatue;

  GoodsListModel({
    this.spuId,
    this.skuCode,
    this.goodsName,
    this.goodsDese,
    this.imageUrl,
    this.categoryId,
    this.pointPrice,
    this.goodType,
    this.vipDay,
    this.stock,
    this.sort,
    this.status,
    this.skuStatue,
  });
  GoodsListModel.fromJson(Map<String, dynamic> json) {
    spuId = json['spuId']?.toInt();
    skuCode = json['skuCode']?.toString();
    goodsName = json['goodsName']?.toString();
    goodsDese = json['goodsDese']?.toString();
    imageUrl = json['imageUrl']?.toString();
    categoryId = json['categoryId']?.toInt();
    pointPrice = json['pointPrice']?.toInt();
    goodType = json['goodType']?.toInt();
    vipDay = json['vipDay']?.toInt();
    stock = json['stock']?.toInt();
    sort = json['sort']?.toInt();
    status = json['status']?.toInt();
    skuStatue = json['skuStatue']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['spuId'] = spuId;
    data['skuCode'] = skuCode;
    data['goodsName'] = goodsName;
    data['goodsDese'] = goodsDese;
    data['imageUrl'] = imageUrl;
    data['categoryId'] = categoryId;
    data['pointPrice'] = pointPrice;
    data['goodType'] = goodType;
    data['vipDay'] = vipDay;
    data['stock'] = stock;
    data['sort'] = sort;
    data['status'] = status;
    data['skuStatue'] = skuStatue;
    return data;
  }
}
