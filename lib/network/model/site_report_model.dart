///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class SiteReportModelPoints {
/*
{
  "id": "248378120",
  "x": 765,
  "y": 618,
  "hit": true
} 
*/

  String? id;
  double? x;
  double? y;
  bool? hit;

  SiteReportModelPoints({
    this.id,
    this.x,
    this.y,
    this.hit,
  });
  SiteReportModelPoints.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    x = json['x']?.toDouble();
    y = json['y']?.toDouble();
    hit = json['hit'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['x'] = x;
    data['y'] = y;
    data['hit'] = hit;
    return data;
  }
}

class SiteReportModelFragments {
/*
{
  "cover": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/match/124/2025-05-27/15-30-00/video/match-highlight_124_2025-05-27_15-31-26_0_1.jpg",
  "videoPath": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/match/124/2025-05-27/15-30-00/video/match-highlight_124_2025-05-27_15-31-26_0_1.mp4",
  "shootType": 1,
  "hit": true,
  "duration": 0
} 
*/

  String? cover;
  String? videoPath;
  int? shootType;
  bool? hit;
  int? duration;
  String? videoTime;
  SiteReportModelFragments(
      {this.cover,
      this.videoPath,
      this.shootType,
      this.hit,
      this.duration,
      this.videoTime});
  SiteReportModelFragments.fromJson(Map<String, dynamic> json) {
    cover = json['cover']?.toString();
    videoPath = json['videoPath']?.toString();
    shootType = json['shootType']?.toInt();
    videoTime = json['videoTime']?.toString();
    hit = json['hit'];
    duration = json['duration']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['cover'] = cover;
    data['videoPath'] = videoPath;
    data['shootType'] = shootType;
    data['videoTime'] = videoTime;
    data['hit'] = hit;
    data['duration'] = duration;
    return data;
  }
}

class SiteReportModel {
/*
{
  "id": "8",
  "photo": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/high_end/shooter_images/match-highlight_124_2025-05-27_15-31-26_shooter_img.jpg",
  "title": "篮网已张开双臂，等待你的完美弧线",
  "labels": [
    "三分之神"
  ],
  "shootCount": 206,
  "hitCount": 86,
  "shootRate": "41.7",
  "threeShootCount": 134,
  "threeHitCount": 55,
  "twoShootCount": 72,
  "twoHitCount": 31,
  "fragments": [
    {
      "cover": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/match/124/2025-05-27/15-30-00/video/match-highlight_124_2025-05-27_15-31-26_0_1.jpg",
      "videoPath": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/match/124/2025-05-27/15-30-00/video/match-highlight_124_2025-05-27_15-31-26_0_1.mp4",
      "shootType": 1,
      "hit": true,
      "duration": 0
    }
  ],
  "points": [
    {
      "id": "248378120",
      "x": 765,
      "y": 618,
      "hit": true
    }
  ]
} 
*/

  String? id;
  String? photo;
  String? title;
  List<String?>? labels;
  int? shootCount;
  int? hitCount;
  String? shootRate;
  int? threeShootCount;
  int? threeHitCount;
  int? twoShootCount;
  int? twoHitCount;
  List<SiteReportModelFragments?>? fragments;
  List<SiteReportModelPoints?>? points;

  SiteReportModel({
    this.id,
    this.photo,
    this.title,
    this.labels,
    this.shootCount,
    this.hitCount,
    this.shootRate,
    this.threeShootCount,
    this.threeHitCount,
    this.twoShootCount,
    this.twoHitCount,
    this.fragments,
    this.points,
  });
  SiteReportModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    photo = json['photo']?.toString();
    title = json['title']?.toString();
    if (json['labels'] != null) {
      final v = json['labels'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      labels = arr0;
    }
    shootCount = json['shootCount']?.toInt();
    hitCount = json['hitCount']?.toInt();
    shootRate = json['shootRate']?.toString();
    threeShootCount = json['threeShootCount']?.toInt();
    threeHitCount = json['threeHitCount']?.toInt();
    twoShootCount = json['twoShootCount']?.toInt();
    twoHitCount = json['twoHitCount']?.toInt();
    if (json['fragments'] != null) {
      final v = json['fragments'];
      final arr0 = <SiteReportModelFragments>[];
      v.forEach((v) {
        arr0.add(SiteReportModelFragments.fromJson(v));
      });
      fragments = arr0;
    }
    if (json['points'] != null) {
      final v = json['points'];
      final arr0 = <SiteReportModelPoints>[];
      v.forEach((v) {
        arr0.add(SiteReportModelPoints.fromJson(v));
      });
      points = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['photo'] = photo;
    data['title'] = title;
    if (labels != null) {
      final v = labels;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['labels'] = arr0;
    }
    data['shootCount'] = shootCount;
    data['hitCount'] = hitCount;
    data['shootRate'] = shootRate;
    data['threeShootCount'] = threeShootCount;
    data['threeHitCount'] = threeHitCount;
    data['twoShootCount'] = twoShootCount;
    data['twoHitCount'] = twoHitCount;
    if (fragments != null) {
      final v = fragments;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['fragments'] = arr0;
    }
    if (points != null) {
      final v = points;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['points'] = arr0;
    }
    return data;
  }
}
