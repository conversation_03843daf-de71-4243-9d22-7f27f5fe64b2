///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamMemberInfoModel {
/*
{
  "completed": true,
  "height": "string",
  "id": "0",
  "number": "string",
  "photo": "string",
  "position": 0,
  "realName": "string",
  "weight": "string"
} 
*/

  bool? completed;
  String? height;
  String? id;
  String? number;
  String? photo;
  int? position;
  String? realName;
  String? weight;

  TeamMemberInfoModel({
    this.completed,
    this.height,
    this.id,
    this.number,
    this.photo,
    this.position,
    this.realName,
    this.weight,
  });
  TeamMemberInfoModel.fromJson(Map<String, dynamic> json) {
    completed = json['completed'];
    height = json['height']?.toString();
    id = json['id']?.toString();
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    position = json['position']?.toInt();
    realName = json['realName']?.toString();
    weight = json['weight']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['completed'] = completed;
    data['height'] = height;
    data['id'] = id;
    data['number'] = number;
    data['photo'] = photo;
    data['position'] = position;
    data['realName'] = realName;
    data['weight'] = weight;
    return data;
  }
}
