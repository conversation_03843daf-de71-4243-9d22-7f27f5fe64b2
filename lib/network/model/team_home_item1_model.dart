///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamHomeItem1Model {
/*
{
  "matchTime": "2025-01-06 20:40:00",
  "arenaName": "摘星运动馆",
  "arenaAliasName": "花道",
  "courts": [
    "2-1"
  ],
  "matchId": "3421",
  "arenaId": "38",
  "leftTeamName": "硬扎",
  "leftTeamId": "3159",
  "LeftWin": false,
  "rightTeamName": "传奇",
  "rightTeamId": "3148",
  "rightWin": true,
  "leftTeamScore": 112,
  "rightTeamScore": 120,
  "win": true,
  "status": 2,
  "markStatus": 2
} 
*/

  String? matchTime;
  String? arenaName;
  String? arenaAliasName;
  List<String?>? courts;
  String? matchId;
  String? arenaId;
  String? leftTeamName;
  String? leftTeamId;
  bool? LeftWin;
  String? rightTeamName;
  String? rightTeamId;
  bool? rightWin;
  int? leftTeamScore;
  int? rightTeamScore;
  bool? win;
  int? status;
  int? markStatus;

  TeamHomeItem1Model({
    this.matchTime,
    this.arenaName,
    this.arenaAliasName,
    this.courts,
    this.matchId,
    this.arenaId,
    this.leftTeamName,
    this.leftTeamId,
    this.LeftWin,
    this.rightTeamName,
    this.rightTeamId,
    this.rightWin,
    this.leftTeamScore,
    this.rightTeamScore,
    this.win,
    this.status,
    this.markStatus,
  });
  TeamHomeItem1Model.fromJson(Map<String, dynamic> json) {
    matchTime = json['matchTime']?.toString();
    arenaName = json['arenaName']?.toString();
    arenaAliasName = json['arenaAliasName']?.toString();
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      courts = arr0;
    }
    matchId = json['matchId']?.toString();
    arenaId = json['arenaId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    LeftWin = json['LeftWin'];
    rightTeamName = json['rightTeamName']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    rightWin = json['rightWin'];
    leftTeamScore = json['leftTeamScore']?.toInt();
    rightTeamScore = json['rightTeamScore']?.toInt();
    win = json['win'];
    status = json['status']?.toInt();
    markStatus = json['markStatus']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['matchTime'] = matchTime;
    data['arenaName'] = arenaName;
    data['arenaAliasName'] = arenaAliasName;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['courts'] = arr0;
    }
    data['matchId'] = matchId;
    data['arenaId'] = arenaId;
    data['leftTeamName'] = leftTeamName;
    data['leftTeamId'] = leftTeamId;
    data['LeftWin'] = LeftWin;
    data['rightTeamName'] = rightTeamName;
    data['rightTeamId'] = rightTeamId;
    data['rightWin'] = rightWin;
    data['leftTeamScore'] = leftTeamScore;
    data['rightTeamScore'] = rightTeamScore;
    data['win'] = win;
    data['status'] = status;
    data['markStatus'] = markStatus;
    return data;
  }
}
