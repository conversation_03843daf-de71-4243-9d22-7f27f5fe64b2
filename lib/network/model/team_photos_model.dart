///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamPhotosModel {
/*
{
  "auditStatus": 0,
  "id": 0,
  "isCover": 0,
  "ischeck": false,
  "url": "string"
} 
*/

  int? auditStatus;
  int? id;
  int? isCover;
  bool? ischeck;
  String? url;

  TeamPhotosModel({
    this.auditStatus,
    this.id,
    this.isCover,
    this.ischeck,
    this.url,
  });
  TeamPhotosModel.fromJson(Map<String, dynamic> json) {
    auditStatus = json['auditStatus']?.toInt();
    id = json['id']?.toInt();
    isCover = json['isCover']?.toInt();
    ischeck = json['ischeck'];
    url = json['url']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['auditStatus'] = auditStatus;
    data['id'] = id;
    data['isCover'] = isCover;
    data['ischeck'] = ischeck;
    data['url'] = url;
    return data;
  }
}
