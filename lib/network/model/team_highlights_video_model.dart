///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamHighlightsVideoModel {
/*
{
  "videoId": "664383",
  "title": "「集锦」飞鹰45-28海底小纵队,飞鹰全场比赛球队集锦",
  "videoCover": "https://cdn.shootz.tech/202501151700/1d9b8566c51499c04e1c21bd0e125434/algo_test/merged_video_cover/2025-01-15/0/707233.jpg",
  "videoPath": "https://cdn.shootz.tech/202501151700/edf079fb0013e3c7e5598126caf3610c/algo_test/merged_video/2025-01-15/0/707233.mp4",
  "duration": 148,
  "matchId": "3519",
  "leftTeamId": "2304",
  "rightTeamId": "2303",
  "matchTime": "2024-12-11 19:00:00",
  "arenaId": "27",
  "leftTeamName": "飞鹰",
  "rightTeamName": "海底小纵队"
} 
*/

  String? videoId;
  String? title;
  String? videoCover;
  String? videoPath;
  int? duration;
  String? matchId;
  String? leftTeamId;
  String? rightTeamId;
  String? matchTime;
  String? arenaId;
  String? leftTeamName;
  String? rightTeamName;

  TeamHighlightsVideoModel({
    this.videoId,
    this.title,
    this.videoCover,
    this.videoPath,
    this.duration,
    this.matchId,
    this.leftTeamId,
    this.rightTeamId,
    this.matchTime,
    this.arenaId,
    this.leftTeamName,
    this.rightTeamName,
  });
  TeamHighlightsVideoModel.fromJson(Map<String, dynamic> json) {
    videoId = json['videoId']?.toString();
    title = json['title']?.toString();
    videoCover = json['videoCover']?.toString();
    videoPath = json['videoPath']?.toString();
    duration = json['duration']?.toInt();
    matchId = json['matchId']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    matchTime = json['matchTime']?.toString();
    arenaId = json['arenaId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['videoId'] = videoId;
    data['title'] = title;
    data['videoCover'] = videoCover;
    data['videoPath'] = videoPath;
    data['duration'] = duration;
    data['matchId'] = matchId;
    data['leftTeamId'] = leftTeamId;
    data['rightTeamId'] = rightTeamId;
    data['matchTime'] = matchTime;
    data['arenaId'] = arenaId;
    data['leftTeamName'] = leftTeamName;
    data['rightTeamName'] = rightTeamName;
    return data;
  }
}
