///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ShootingInfoModelPoints {
/*
{
  "shootX": 797,
  "shootY": 250,
  "hit": true
} 
*/

  int? shootX;
  int? shootY;
  bool? hit;

  ShootingInfoModelPoints({
    this.shootX,
    this.shootY,
    this.hit,
  });
  ShootingInfoModelPoints.fromJson(Map<String, dynamic> json) {
    shootX = json['shootX']?.toInt();
    shootY = json['shootY']?.toInt();
    hit = json['hit'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['shootX'] = shootX;
    data['shootY'] = shootY;
    data['hit'] = hit;
    return data;
  }
}

class ShootingInfoModelTrainingReports {
/*
{
  "reportId": 21,
  "title": "dd"
} 
*/

  int? reportId;
  String? title;

  ShootingInfoModelTrainingReports({
    this.reportId,
    this.title,
  });
  ShootingInfoModelTrainingReports.fromJson(Map<String, dynamic> json) {
    reportId = json['reportId']?.toInt();
    title = json['title']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['reportId'] = reportId;
    data['title'] = title;
    return data;
  }
}

class ShootingInfoModel {
/*
{
  "id": "0",
  "title": "沙雕1号",
  "photo": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/algo_prod/high_end/123/2025-04-22/20-30-00/match-highlight_123_2025-04-22_20-30-49_0_2_shooter.jpg",
  "userId": "269705",
  "duration": "20",
  "shotCount": 100,
  "hitCount": 10,
  "rate": "10.0",
  "trainingReports": [
    {
      "reportId": 21,
      "title": "dd"
    }
  ],
  "points": [
    {
      "shootX": 797,
      "shootY": 250,
      "hit": true
    }
  ]
} 
*/

  String? id;
  String? title;
  String? photo;
  String? userId;
  String? duration;
  int? shotCount;
  int? hitCount;
  String? rate;
  List<ShootingInfoModelTrainingReports?>? trainingReports;
  List<ShootingInfoModelPoints?>? points;

  ShootingInfoModel({
    this.id,
    this.title,
    this.photo,
    this.userId,
    this.duration,
    this.shotCount,
    this.hitCount,
    this.rate,
    this.trainingReports,
    this.points,
  });
  ShootingInfoModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    title = json['title']?.toString();
    photo = json['photo']?.toString();
    userId = json['userId']?.toString();
    duration = json['duration']?.toString();
    shotCount = json['shotCount']?.toInt();
    hitCount = json['hitCount']?.toInt();
    rate = json['rate']?.toString();
    if (json['trainingReports'] != null) {
      final v = json['trainingReports'];
      final arr0 = <ShootingInfoModelTrainingReports>[];
      v.forEach((v) {
        arr0.add(ShootingInfoModelTrainingReports.fromJson(v));
      });
      trainingReports = arr0;
    }
    if (json['points'] != null) {
      final v = json['points'];
      final arr0 = <ShootingInfoModelPoints>[];
      v.forEach((v) {
        arr0.add(ShootingInfoModelPoints.fromJson(v));
      });
      points = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['photo'] = photo;
    data['userId'] = userId;
    data['duration'] = duration;
    data['shotCount'] = shotCount;
    data['hitCount'] = hitCount;
    data['rate'] = rate;
    if (trainingReports != null) {
      final v = trainingReports;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['trainingReports'] = arr0;
    }
    if (points != null) {
      final v = points;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['points'] = arr0;
    }
    return data;
  }
}
