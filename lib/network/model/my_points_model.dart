///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MyPointsModelTaskList {
/*
{
  "id": 9,
  "taskName": "下载球秀APP并登录",
  "taskDesc": "首次下载球秀APP井注册成为球秀用户即可获得",
  "point": "1000",
  "taskType": 3,
  "count": 0,
  "totalCount": 1,
  "isCompleted": true
} 
*/

  int? id;
  String? taskName;
  String? taskDesc;
  String? point;
  int? taskType;
  int? count;
  int? totalCount;
  bool? isCompleted;

  MyPointsModelTaskList({
    this.id,
    this.taskName,
    this.taskDesc,
    this.point,
    this.taskType,
    this.count,
    this.totalCount,
    this.isCompleted,
  });
  MyPointsModelTaskList.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    taskName = json['taskName']?.toString();
    taskDesc = json['taskDesc']?.toString();
    point = json['point']?.toString();
    taskType = json['taskType']?.toInt();
    count = json['count']?.toInt();
    totalCount = json['totalCount']?.toInt();
    isCompleted = json['isCompleted'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['taskName'] = taskName;
    data['taskDesc'] = taskDesc;
    data['point'] = point;
    data['taskType'] = taskType;
    data['count'] = count;
    data['totalCount'] = totalCount;
    data['isCompleted'] = isCompleted;
    return data;
  }
}

class MyPointsModel {
/*
{
  "todayPoint": 0,
  "point": 0,
  "continuousDays": 0,
  "nextDayPoints ": 1,
  "isSignToday": false,
  "taskList": [
    {
      "id": 9,
      "taskName": "下载球秀APP并登录",
      "taskDesc": "首次下载球秀APP井注册成为球秀用户即可获得",
      "point": "1000",
      "taskType": 3,
      "sort": 1,
      "status": 1,
      "count": 0,
      "totalCount": 1
    }
  ]
} 
*/

  int? todayPoint;
  int? point;
  int? continuousDays;
  int? nextDayPoints;
  bool? isSignToday;
  List<MyPointsModelTaskList?>? taskList;

  MyPointsModel({
    this.todayPoint,
    this.point,
    this.continuousDays,
    this.nextDayPoints,
    this.isSignToday,
    this.taskList,
  });
  MyPointsModel.fromJson(Map<String, dynamic> json) {
    todayPoint = json['todayPoint']?.toInt();
    point = json['point']?.toInt();
    continuousDays = json['continuousDays']?.toInt();
    nextDayPoints = json['nextDayPoints']?.toInt();
    isSignToday = json['isSignToday'];
    if (json['taskList'] != null) {
      final v = json['taskList'];
      final arr0 = <MyPointsModelTaskList>[];
      v.forEach((v) {
        arr0.add(MyPointsModelTaskList.fromJson(v));
      });
      taskList = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['todayPoint'] = todayPoint;
    data['point'] = point;
    data['continuousDays'] = continuousDays;
    data['nextDayPoints '] = nextDayPoints;
    data['isSignToday'] = isSignToday;
    if (taskList != null) {
      final v = taskList;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['taskList'] = arr0;
    }
    return data;
  }
}
