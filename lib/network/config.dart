import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

import '../pages/login/user.dart';
import '../utils/event_bus.dart';

class ConfigUtils {
  ConfigUtils._privateConstructor() {
    BusUtils.instance.on((p0) {
      if (p0.key == EventBusKey.networkOk) {
        setup();
      }
    });
  }

  static final ConfigUtils _instance = ConfigUtils._privateConstructor();

  static ConfigUtils get instance {
    return _instance;
  }

  Future<void> setup() async {
    if (!UserManager.instance.isLogin) {
      FlutterNativeSplash.remove();
    } else {
      try {
        // await UserManager.instance.refreshToken();
        final res = await UserManager.instance.pullUserInfo();
        if (!res) {
          UserManager.instance.logout();
        }
        BusUtils.instance.fire(EventAction(key: EventBusKey.toHome));
      } catch (err) {
        // LucaBusUtils.instance.fire(EventAction(key: EventBusKey.networkErr));
        UserManager.instance.logout(isKickOut: false);
        BusUtils.instance.fire(EventAction(key: EventBusKey.toHome));
      }
    }
  }
}
