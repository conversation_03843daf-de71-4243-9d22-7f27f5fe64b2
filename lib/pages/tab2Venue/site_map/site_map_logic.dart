// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:async';
import 'dart:convert';
import 'dart:developer' as ccc;
import 'dart:math';

import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/model/site_map_model.dart';
import '../../../network/api_url.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/location_utils.dart';
import 'package:amap_flutter_base/amap_flutter_base.dart';
import '../../../widgets/permission_dialog.dart';

class SiteMapLogic extends GetxController {
  RefreshController controller = RefreshController(initialRefresh: false);
  TextEditingController nameController = TextEditingController();
  final PageController pageController = PageController(
    initialPage: 0, // 初始页面索引
    viewportFraction: 0.82, // 页面宽度占屏幕宽度的比例
  );
  // 地图控制器
  AMapController? mapController;

  // 选中的点位
  var selectedPoint = Rx<SiteMapModel?>(null);
  var userPosition = const LatLng(39.909187, 116.397451).obs;

  // 人物中心点图标路径
  final Rx<String?> personIconPath = "assets/icons/person.png".obs;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 200,
  }.obs;
  var currentPage = 0.obs;
  var isShowBottom = true.obs;
  //数据列表
  var dataList = <SiteMapModel>[].obs;
  StreamSubscription? locationSubscription;
  var mapChangdiCheck;
  var mapChangdi;
  var mapSiteCheck;
  var mapSite;
  var userIcon;
  var checkSiteMapModel = SiteMapModel().obs;
  var searchText = ''.obs; //搜索框文本状态
  var position22 = Position(0.0, 0.0);
  @override
  void onInit() {
    super.onInit();
    // 添加搜索框文本监听器
    // nameController.addListener(() {
    //   searchText.value = nameController.text;
    // });
    getImage();
    locationSubscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        getdataList(isLoad: false, isChange: true);
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    mapController!.disponse();
    locationSubscription?.cancel();
  }

  getImage() async {
    // mapChangdiCheck = await BitmapDescriptor.fromAssetImage(
    //   ImageConfiguration(
    //       size: const Size(20, 20),
    //       platform:
    //           Platform.isAndroid ? TargetPlatform.android : TargetPlatform.iOS),
    //   'assets/images/map_changdi_check.png',
    // );
    // mapChangdi = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(
    //         size: Size(20, 20),
    //         platform: Platform.isAndroid
    //             ? TargetPlatform.android
    //             : TargetPlatform.iOS),
    //     'assets/images/map_changdi.png');
    // mapSiteCheck = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(
    //         size: Size(20, 20),
    //         platform: Platform.isAndroid
    //             ? TargetPlatform.android
    //             : TargetPlatform.iOS),
    //     'assets/images/map_site_check.png');
    // mapSite = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(
    //         size: Size(20, 20),
    //         platform: Platform.isAndroid
    //             ? TargetPlatform.android
    //             : TargetPlatform.iOS),
    //     'assets/images/map_site.png');
    // userIcon = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(
    //         size: Size(20, 20),
    //         platform: Platform.isAndroid
    //             ? TargetPlatform.android
    //             : TargetPlatform.iOS),
    //     'assets/images/map_my_address.png');
    mapChangdiCheck =
        BitmapDescriptor.fromIconPath('assets/images/map_changdi_check.png');
    mapChangdi = BitmapDescriptor.fromIconPath('assets/images/map_changdi.png');
    mapSiteCheck =
        BitmapDescriptor.fromIconPath('assets/images/map_site_check.png');
    mapSite = BitmapDescriptor.fromIconPath('assets/images/map_site.png');
    userIcon =
        BitmapDescriptor.fromIconPath('assets/images/map_my_address.png');
    await Future.delayed(const Duration(milliseconds: 100));
    getdataList(isLoad: false, isChange: true);
  }

  getMyLocation() async {
    final location = await LocationUtils.instance.checkPermission();
    if (location) {
      await LocationUtils.instance.getCurrentPosition();
    }
    final position = LocationUtils.instance.position;
    if (position == null) {
      if (!location) {
        showLocationDialog();
      }
      return;
    }
    userPosition.value = LatLng(position.latitude, position.longitude);
    // 将地图移动到当前位置
    mapController?.moveCamera(
      CameraUpdate.newLatLngZoom(
        LatLng(position.latitude, position.longitude),
        15,
      ),
    );
    var distance = calculateDistance(
        LatLng(position.latitude, position.longitude),
        LatLng(position22.latitude, position22.longitude));
    ccc.log("getCameraMoveEnd3-${distance}");
    if (distance > 50000) {
      getdataList(
          isLoad: false,
          isChange: false,
          position2: Position(position.latitude, position.longitude));
    }
  }

  //获得最新列表
  getdataList({isLoad = true, isChange = false, Position? position2}) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }
    final location = await LocationUtils.instance.checkPermission();
    if (!isLoad && location) {
      await LocationUtils.instance.getCurrentPosition();
    }
    final position = LocationUtils.instance.position;
    if (position == null) {
      // dataList.clear();
      // WxLoading.showToast(S.current.failed_location);
      if (!location) {
        showLocationDialog();
      }
      return;
    }
    if (isChange) {
      userPosition.value = LatLng(position.latitude, position.longitude);
      // 将地图移动到当前位置
      mapController?.moveCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(position.latitude, position.longitude),
          15,
        ),
      );
    }
    position22 = position2 ?? position;
    var param = {
      'latitude':
          '${position2 != null ? position2.latitude : position.latitude}',
      'longitude':
          '${position2 != null ? position2.longitude : position.longitude}',
      'limit': dataFag["pageSize"] ?? 100,
      'page': dataFag["page"] ?? 1,
      'name': nameController.text.trim(),
      'radius': "200",
      'unit': "km", //m 米 km 千米
    };
    ccc.log("zzzzzz12removeAt-${param}");
    var res = await Api().get(ApiUrl.searchMapList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["result"] ?? [];
      List<SiteMapModel> modelList =
          list.map((e) => SiteMapModel.fromJson(e)).toList();

      ccc.log("zzzzzz12removeAt1-${jsonEncode(res.data)}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  // 设置点位图标
  // void setPointIcon(String pointId, String iconPath) {
  //   final index = dataList.indexWhere((point) => point.id == pointId);
  //   if (index != -1) {
  //     dataList[index] = dataList[index].copyWith(iconPath: iconPath);
  //   }
  // }

  // 选择点位
  void selectPoint(SiteMapModel siteMapModel) {
    checkSiteMapModel.value = siteMapModel;

    // // 重置所有点位的选中状态
    for (var i = 0; i < dataList.length; i++) {
      if (dataList[i].id == siteMapModel.id) {
        if (isShowBottom.value == false) {
          isShowBottom.value = true;
          isShowBottom.refresh();
          Future.delayed(const Duration(milliseconds: 100)).then((onValue) {
            pageController.jumpToPage(i);
          });
        } else {
          pageController.jumpToPage(i);
        }
      }
    }

    // 将地图中心移动到选中的点位
    final point =
        dataList.firstWhereOrNull((p) => (p.id ?? 0) == siteMapModel.id);
    if (point != null) {
      mapController?.moveCamera(
        CameraUpdate.newLatLngZoom(
            LatLng(point.latitude ?? 0.0, point.longitude ?? 0.0), 15),
      );
    }
  }

  void getCameraMoveEnd(CameraPosition argument) {
    final position = LocationUtils.instance.position;
    ccc.log("getCameraMoveEnd2-${position}");
    if (position != null) {
      var distance = calculateDistance(
          LatLng(position.latitude, position.longitude),
          LatLng(argument.target.latitude, argument.target.longitude));
      ccc.log("getCameraMoveEnd3-${distance}");
      if (distance > 50000) {
        getdataList(
            isLoad: false,
            isChange: false,
            position2:
                Position(argument.target.latitude, argument.target.longitude));
      }
    }
  }

  double calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // 地球半径（米）

    final double lat1 = point1.latitude * pi / 180;
    final double lon1 = point1.longitude * pi / 180;
    final double lat2 = point2.latitude * pi / 180;
    final double lon2 = point2.longitude * pi / 180;

    final double dLat = lat2 - lat1;
    final double dLon = lon2 - lon1;

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }
}
