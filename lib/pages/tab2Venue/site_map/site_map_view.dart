// ignore_for_file: unused_local_variable

import 'dart:convert';
import 'dart:developer';

import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/network/model/site_map_model.dart';
import 'package:shoot_z/pages/tab2Venue/site_map/site_map_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:amap_flutter_base/amap_flutter_base.dart';

class SiteMapPage extends StatefulWidget {
  const SiteMapPage({super.key});

  @override
  State<SiteMapPage> createState() => _PlacePageState();
}

class _PlacePageState extends State<SiteMapPage> {
  final logic = Get.put(SiteMapLogic());
  static const AMapPrivacyStatement amapPrivacyStatement =
      AMapPrivacyStatement(hasContains: true, hasShow: true, hasAgree: true);

  ///先将申请的Android端可以和iOS端key设置给AMapApiKey
  static const AMapApiKey amapApiKeys = AMapApiKey(
      androidKey: '1dbf56e2e8a4d0e4cdc2df9efd36bc71',
      iosKey: 'dfb64c0463cb53927914364b5c09aba0');

  void onMapCreated(AMapController controller) {
    setState(() {
      logic.mapController = controller;
      getApprovalNumber();
    });
  }

  /// 获取审图号
  void getApprovalNumber() async {
    //普通地图审图号
    String? mapContentApprovalNumber =
        await logic.mapController?.getMapContentApprovalNumber();
    //卫星地图审图号
    String? satelliteImageApprovalNumber =
        await logic.mapController?.getSatelliteImageApprovalNumber();
  }

  @override
  void initState() {
    super.initState();
    //AMapUtil.init(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: const MyAppBar(
          title: Text("球场地图"),
        ),
        body: Obx(() {
          return (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : Stack(
                  children: [
                    Positioned.fill(
                        child: Container(
                      color: Colours.color000000,
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      child: // 高德地图
                          Obx(() => AMapWidget(
                                apiKey: amapApiKeys,
                                onMapCreated: (c) => onMapCreated(c),
                                markers: _buildMarkers(),

                                ///必须正确设置的合规隐私声明，否则SDK不会工作，会造成地图白屏等问题。
                                privacyStatement: amapPrivacyStatement,
                                // myLocationStyleOptions: MyLocationStyleOptions(
                                //   true,
                                //   // 使用自定义图标
                                //   icon: logic.userIcon,
                                // ),
                                onCameraMoveEnd: (argument) {
                                  log("getCameraMoveEnd1=$argument");
                                  logic.getCameraMoveEnd(argument);
                                },
                                initialCameraPosition: CameraPosition(
                                  target: logic.userPosition.value,
                                  zoom: 15,
                                ),
                              )),
                    )),
                    Positioned(
                      top: 15.w,
                      left: 15.w,
                      right: 15.w,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        height: 44.w,
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                          color: Colours.color191921,
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            WxAssets.images.icSearch.image(),
                            SizedBox(
                              width: 10.w,
                            ),
                            Expanded(
                                child: TextField(
                              controller: logic.nameController,
                              textInputAction: TextInputAction.search,
                              keyboardType: TextInputType.text,
                              style: TextStyles.regular
                                  .copyWith(color: Colours.white),
                              autocorrect: false,
                              onChanged: (value) =>
                                  logic.searchText.value = value,
                              decoration: InputDecoration(
                                // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                                contentPadding:
                                    EdgeInsets.only(top: 0.w, bottom: 3.w),
                                border: InputBorder.none,
                                hintText: "请输入要查找的球场名称",
                                hintStyle: TextStyles.regular
                                    .copyWith(color: Colours.color5C5C6E),
                              ),
                              onSubmitted: (value) {
                                if (logic.position22.latitude <= 0) {
                                  logic.getdataList(
                                      isLoad: false, isChange: false);
                                } else {
                                  logic.getdataList(
                                      isLoad: false,
                                      position2: logic.position22,
                                      isChange: false);
                                }
                              },
                            )),
                            GestureDetector(
                                onTap: () {
                                  logic.nameController.text = '';
                                  logic.searchText.value = '';
                                  if (logic.position22.latitude <= 0) {
                                    logic.getdataList(
                                        isLoad: false, isChange: false);
                                  } else {
                                    logic.getdataList(
                                        isLoad: false,
                                        position2: logic.position22,
                                        isChange: false);
                                  }
                                },
                                child: Obx(() => Visibility(
                                    visible: logic.searchText.value.isNotEmpty,
                                    child: WxAssets.images.icSearchDelete
                                        .image()))),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                        right: 15.w,
                        bottom: 180.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.getMyLocation();
                          },
                          child: Container(
                            width: 40.w,
                            height: 40.w,
                            decoration: BoxDecoration(
                                color: Colours.color0F0F16,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.r))),
                            child: WxAssets.images.mapLocation
                                .image(width: 24.w, height: 24.w),
                          ),
                        )),
                    if (logic.isShowBottom.value)
                      Positioned(
                        bottom: 34.w,
                        width: ScreenUtil().screenWidth,
                        height: 110.w,
                        child: PageView.builder(
                          controller: logic.pageController,
                          itemCount: logic.dataList.length,
                          onPageChanged: (index) {
                            setState(() {
                              logic.currentPage.value = index;
                            });
                            logic.checkSiteMapModel.value =
                                logic.dataList[index];
                            logic.mapController?.moveCamera(
                              CameraUpdate.newLatLngZoom(
                                  LatLng(logic.dataList[index].latitude ?? 0.0,
                                      logic.dataList[index].longitude ?? 0.0),
                                  15),
                            );
                          },
                          itemBuilder: (context, index) {
                            return _buildPage(logic.dataList[index]);
                          },
                        ),
                      )
                  ],
                );
        }));
  }

  // 构建地图标记
  Set<Marker> _buildMarkers() {
    final markers = <Marker>{};
    final userPos = logic.userPosition.value;

    // 所有点位标记
    for (final point in logic.dataList) {
      markers.add(Marker(
        position: LatLng(point.latitude ?? 0.0, point.longitude ?? 0.0),
        //typeFlag 1场馆 2场地
        icon: logic.checkSiteMapModel.value.id == point.id
            ? point.typeFlag == 1
                ? logic.mapChangdiCheck
                : logic.mapSiteCheck
            : point.typeFlag == 1
                ? logic.mapChangdi
                : logic.mapSite, // 实际应用中使用自定义图标
        onTap: (argument) {
          log("argument22=$argument-${jsonEncode(point)}");
          logic.selectPoint(point);
        },
      ));
    }
    // 用户位置标记
    markers.add(Marker(
      position: userPos,
      icon: logic.userIcon, // BitmapDescriptor.defaultMarker, // 实际应用中使用自定义图标
    ));
    return markers;
  }

  Widget _buildPage(SiteMapModel siteMapModel) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 7.5.w),
      decoration: BoxDecoration(
        color: Colours.color191921,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.all(15.w),
            child: MyImage(
              siteMapModel.imageUrl ?? '',
              width: 80.w,
              height: 80.w,
              radius: 0.r,
              bgColor: Colours.color191921,
              fit: BoxFit.fill,
              errorImage: "error_img_site.png",
              placeholderImage: "error_img_site.png",
            ),
          ),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(top: 15.w),
                        child: Text(
                          siteMapModel.name ?? "",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: TextStyles.regular.copyWith(
                              fontSize: 14.sp,
                              color: Colours.white,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.isShowBottom.value = false;
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                            top: 10.w, right: 10.w, left: 10.w, bottom: 5.w),
                        child: WxAssets.images.icCloseDialog
                            .image(width: 16.w, height: 16.w),
                      ),
                    )
                  ],
                ),
                Row(
                  children: [
                    WxAssets.images.mapAddress.image(width: 14.w, height: 14.w),
                    Text(
                      (siteMapModel.distance ?? 0) > 1
                          ? "距离您${(siteMapModel.distance ?? 0.0).toStringAsFixed(2)}km"
                          : "距离您${((siteMapModel.distance ?? 0.0) * 1000).toStringAsFixed(0)}m",
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp, color: Colours.color5C5C6E),
                    ),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    WxButton(
                      text: "立即创作",
                      width: 80.w,
                      height: 30.w,
                      margin: EdgeInsets.only(bottom: 15.w),
                      textStyle: TextStyles.regular.copyWith(
                          fontSize: 12.sp, fontWeight: FontWeight.w600),
                      borderRadius: BorderRadius.circular(15.r),
                      onPressed: () {},
                    ),
                    const Spacer(),
                    //typeFlag 1场馆 2场地
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.w),
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(colors: [
                            Colours.color7732ED,
                            Colours.colorA555EF
                          ]),
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8.r),
                              bottomRight: Radius.circular(8.r))),
                      child: Text(
                        siteMapModel.typeFlag == 1 ? "场馆" : "场地",
                        style: TextStyles.regular
                            .copyWith(fontSize: 10.sp, color: Colours.white),
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
