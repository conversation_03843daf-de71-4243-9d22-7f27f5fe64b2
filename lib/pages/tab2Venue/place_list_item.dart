import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import '../../gen/assets.gen.dart';
import 'package:ui_packages/ui_packages.dart';

// import '../../generated/l10n.dart';

class PlaceListItem extends StatelessWidget {
  final PlaceModel model;
  const PlaceListItem({super.key, required this.model});
  // static final desList = [
  //   "#${S.current.video_free}",
  //   S.current.match_reports_available
  // ];
  @override
  Widget build(BuildContext context) {
    // List<String> des = [];
    // if (model.isBuy) {
    //   des.add(desList.first);
    // }
    // if (model.isReport) {
    //   des.add(desList.last);
    // }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => AppPage.to(Routes.arenaDetailsPage, arguments: {
        "id": model.arenaID,
      }),
      child: Padding(
        padding: EdgeInsets.only(bottom: 15.w, left: 15.w, right: 15.w),
        child: Stack(children: [
          Container(
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: CachedNetworkImage(
                        imageUrl: model.logo ?? '',
                        width: 96.w,
                        height: 96.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 15.w),
                        child: SizedBox(
                          height: 96.w,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                      child: Text(
                                    maxLines: 2,
                                    model.arenaName ?? '',
                                    style: TextStyles.semiBold14
                                        .copyWith(height: 1.71),
                                    overflow: TextOverflow.ellipsis,
                                  )),
                                  if (model.isPremium == 1)
                                    WxAssets.images.advancedArena.image()
                                ],
                              ),
                              Wrap(
                                spacing: 6.w,
                                runSpacing: 6.w,
                                children: _getTagsList(model.tags ?? '')
                                    .map((e) => Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8.w, vertical: 5.w),
                                          decoration: BoxDecoration(
                                            color: Colours.color2E1575,
                                            border: Border.all(
                                              width: 1,
                                              color: Colours.color6435E9,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10.r),
                                          ),
                                          child: Text(
                                            textAlign: TextAlign.center,
                                            '$e ',
                                            style: TextStyles.medium.copyWith(
                                                fontSize: 10.sp,
                                                color: Colours.white),
                                          ),
                                        ))
                                    .toList(),
                              ),
                              Row(
                                children: [
                                  WxAssets.images.icLocation
                                      .image(width: 14, height: 14),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text(
                                    "距离您${model.distance}km",
                                    style: TextStyles.display12,
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 15.w,
                ),
                Row(
                  children: [
                    Text(
                      '当前场馆人流量：',
                      style: TextStyles.display12,
                    ),
                    Container(
                      width: 10.w,
                      height: 10.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getFlowColor(
                            model.pedestrianFlowStatus ?? 0)['color'],
                      ),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(
                        _getFlowColor(model.pedestrianFlowStatus ?? 0)['title'],
                        style: TextStyles.display12.copyWith(
                            color: _getFlowColor(
                                model.pedestrianFlowStatus ?? 0)['color'])),
                  ],
                )
              ],
            ),
          ),
        ]),
      ),
    );
  }

  String removeDotIfLast(String input) {
    if (input.endsWith('·')) {
      return input.substring(0, input.length - 1);
    }
    return input;
  }

  Map<String, dynamic> _getFlowColor(int status) {
    switch (status) {
      case 0:
        return {'title': '未知', 'color': Colours.color5C5C6E};
      case 1:
        return {'title': '空闲', 'color': Colours.color15B200};
      case 2:
        return {'title': '稀疏', 'color': Colours.color15B200};
      case 3:
        return {'title': '适中', 'color': Colours.color82B200};
      case 4:
        return {'title': '拥挤', 'color': Colours.colorFB8E00};
      case 5:
        return {'title': '爆满', 'color': Colours.colorFF3F3F};
      default:
        return {'title': '未知', 'color': Colours.color5C5C6E};
    }
  }

  List<String> _getTagsList(String tags) {
    var result = <String>[];
    final tagList = tags.split(',');
    if (tagList.contains('1')) {
      result.add('高光时刻');
    }
    if (tagList.contains('2')) {
      result.add('数据报告');
    }
    if (tagList.contains('3')) {
      result.add('赛事直播');
    }
    return result;
  }
}
