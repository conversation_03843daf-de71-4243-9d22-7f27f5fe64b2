import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/arena_details_model.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/record_model.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/utils/myShareH5.dart';

class SiteDetailLogic extends GetxController {
  var arenaDetailsModel = ArenaDetailsModel().obs;
  var arenaID = 0.obs;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var videoList = <RecordModel>[].obs;
  var init = false.obs;
  @override
  void onInit() {
    super.onInit();
    arenaID.value = Get.arguments["id"]; //39 ??
    getDataInfo(true);
  }

//获得数据
  Future<void> getDataInfo(bool isRefresh) async {
    if (isRefresh) {
      if (await LocationUtils.instance.checkPermission()) {
        await LocationUtils.instance.getCurrentPosition();
      }
    }

    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    // WxLoading.show();
    var param = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
      'arenaID': arenaID.value,
    };

    final res = await Api().get(ApiUrl.arenasDetail, queryParameters: param);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      arenaDetailsModel.value = ArenaDetailsModel.fromJson(res.data);
      getdataList(arenaDetailsModel.value.arenaID ?? 0);
      arenaDetailsModel.refresh();

      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

//获得最新列表
  Future<void> getdataList(int arenaID) async {
    Map<String, dynamic> param = {
      'pageIndex': 1,
      'pageSize': 4,
      'arenaId': arenaID,
      'lastRecordId': '0'
    };
    log('message$param');
    var res =
        await Api().get(ApiUrl.arenaHighlightsList, queryParameters: param);
    init.value = true;
    if (res.isSuccessful()) {
      final list = (res.data['result'] as List)
          .map((e) => RecordModel.fromJson(e))
          .toList();
      videoList.value = list;
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void share() async {
    // switch (type.value) {
    //   case "0": //普通半场
    //   case "1": //半场集锦
    //     MyShareH5.getShareH5(
    //       ShareVideosId(
    //           videosId: (featuredListModel.value.id ?? "").toString(),
    //           header: "0",
    //           type: "0"),
    //     ); // //0普通集锦  1半场集锦
    //     break;
    //   case "2": //教学分享
    //     MyShareH5.getShareH5(
    //       ShareVideosId2(
    //           videosId: (featuredListModel.value.id ?? "").toString(),
    //           header: "0",
    //           type: "1"),
    //     ); // //0普通集锦  1半场集锦
    //     break;
    // }
  }
  @override
  void onReady() {
    super.onReady();
  }
}
