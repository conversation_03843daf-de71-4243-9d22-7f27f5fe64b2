import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/site_details/site_detail_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class SiteDetailPage extends StatelessWidget {
  SiteDetailPage({super.key});
  final logic = Get.put(SiteDetailLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: const Text('中电软件园1期篮球场'),
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              logic.share();
            },
            child: WxAssets.images.icShare.image(),
          ).marginOnly(right: 15.w),
        ],
      ),
      body: Obx(() {
        return logic.arenaDetailsModel.value.arenaID == null
            ? buildLoad()
            : Column(
                children: [
                  CachedNetworkImage(
                      width: ScreenUtil().screenWidth,
                      height: 210.w,
                      fit: BoxFit.fitWidth,
                      imageUrl: logic.arenaDetailsModel.value.logo ?? ''),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: 20.w, horizontal: 15.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            logic.arenaDetailsModel.value.arenaName ?? '',
                            style: TextStyles.titleSemiBold16,
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  (logic.arenaDetailsModel.value
                                              .floorCondition ??
                                          [])
                                      .whereType<String>()
                                      .join(' | '),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyles.display12.copyWith(
                                    color: Colours.color5C5C6E,
                                  ),
                                ),
                              ),
                              Text(
                                '创建人：兰亭月',
                                style: TextStyles.display12,
                              )
                            ],
                          ),
                          SizedBox(
                            height: 20.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const TextWithIcon(title: '精彩视频'),
                              GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    AppPage.to(Routes.moreHighlightsPage,
                                        arguments: {
                                          "arenaId": logic.arenaID.value,
                                          "arenaName": logic.arenaDetailsModel
                                                  .value.arenaName ??
                                              ""
                                        });
                                  },
                                  child: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 16.sp,
                                    color: Colours.white,
                                  ))
                            ],
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                          Expanded(
                            child: logic.init.value
                                ? (logic.videoList.isEmpty
                                    ? _buildEmptyView(context)
                                    : _list(context))
                                : buildLoad(),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              );
      }),
      bottomNavigationBar: InkWell(
        onTap: () {
          // logic.subscribeAIReport();
        },
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '查找视频片段',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无生涯视频，快去创作吧',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => // 分组内容
          GridView.builder(
        scrollDirection: Axis.vertical,
        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
        shrinkWrap: true,
        // physics:
        //     const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 15.w,
          mainAxisSpacing: 15.w,
          childAspectRatio: 165 / 131,
        ),
        padding: EdgeInsets.only(bottom: 20.w),
        itemCount: logic.videoList.length,
        itemBuilder: (context, itemIndex) {
          final itemModel = logic.videoList[itemIndex];
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              AppPage.to(Routes.highlightsVideo, arguments: {
                'record': itemModel,
                'arenaName': logic.arenaDetailsModel.value.arenaName
              });
            },
            child: Column(
              children: [
                Expanded(
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.w),
                        topRight: Radius.circular(8.w)),
                    child: Stack(
                      children: [
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 0,
                            child: CachedNetworkImage(
                              imageUrl: itemModel.cover ?? '',
                              fit: BoxFit.cover,
                              height: 93.w,
                            )),
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              height: 24.w,
                              alignment: Alignment.centerLeft,
                              color: Colours.color0F0F16.withOpacity(0.6),
                              child: Text(
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                itemModel.name ?? '',
                                style: TextStyles.display12
                                    .copyWith(color: Colors.white),
                              ),
                            )),
                        Positioned(
                            left: 0,
                            right: 0,
                            top: 32.w,
                            child: WxAssets.images.selfieShotPlay
                                .image(width: 28.w, height: 28.w)),
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 38.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8.w),
                          bottomRight: Radius.circular(8.w))),
                  child: Row(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(9.w),
                          child: CachedNetworkImage(
                            imageUrl: itemModel.userAvatar ?? '',
                            fit: BoxFit.cover,
                            height: 18.w,
                            width: 18.w,
                          )),
                      SizedBox(
                        width: 6.w,
                      ),
                      Expanded(
                          child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        itemModel.userName ?? '',
                        style: TextStyles.display12,
                      ))
                    ],
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
