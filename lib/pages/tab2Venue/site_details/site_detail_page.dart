import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/site_details/site_detail_logic.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class SiteDetailPage extends StatelessWidget {
  SiteDetailPage({super.key});
  final logic = Get.put(SiteDetailLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Obx(() => Text(logic.venueModel.value.name ?? '')),
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              logic.share();
            },
            child: WxAssets.images.icShare.image(),
          ).marginOnly(right: 15.w),
        ],
      ),
      body: Obx(() {
        return logic.venueModel.value.id == null
            ? buildLoad()
            : Column(
                children: [
                  CachedNetworkImage(
                      width: ScreenUtil().screenWidth,
                      height: 210.w,
                      fit: BoxFit.fitWidth,
                      imageUrl: logic.venueModel.value.coverUrl ?? ''),
                  Expanded(
                      child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: 20.w, horizontal: 15.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            logic.venueModel.value.name ?? '',
                            style: TextStyles.titleSemiBold16,
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  _getTagsStr(logic.venueModel.value)
                                      .whereType<String>()
                                      .join(' | '),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyles.display12.copyWith(
                                    color: Colours.color5C5C6E,
                                  ),
                                ),
                              ),
                              Text(
                                '创建人：${logic.venueModel.value.createdByName}',
                                style: TextStyles.display12,
                              )
                            ],
                          ),
                          Container(
                            width: ScreenUtil().screenWidth - 30.w,
                            height: 70.w,
                            padding: EdgeInsets.all(15.w),
                            margin: EdgeInsets.only(top: 15.w, bottom: 20.w),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.r)),
                                color: Colours.color1F1A2F,
                                image: DecorationImage(
                                    image: WxAssets.images.arenaDetailAddressBg
                                        .provider(),
                                    fit: BoxFit.fill)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                        width: 220.w,
                                        child: Text(
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                          logic.venueModel.value.address ?? "",
                                          style: TextStyles.semiBold14,
                                        )),
                                    const Spacer(),
                                    Text(
                                      S.current.distance_you(
                                          logic.venueModel.value.distance ??
                                              ""),
                                      style: TextStyles.display12,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const TextWithIcon(title: '精彩视频'),
                              GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    AppPage.to(Routes.venueMoreHighlightsPage,
                                        arguments: {
                                          "venueId": logic.venueId,
                                          "arenaName":
                                              logic.venueModel.value.name ?? ""
                                        });
                                  },
                                  child: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 16.sp,
                                    color: Colours.white,
                                  ))
                            ],
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                          Expanded(
                            child: logic.init.value
                                ? (logic.videoList.isEmpty
                                    ? _buildEmptyView(context)
                                    : _list(context))
                                : buildLoad(),
                          ),
                        ],
                      ),
                    ),
                  ))
                ],
              );
      }),
      bottomNavigationBar: InkWell(
        onTap: () {
          // logic.subscribeAIReport();
          AppPage.to(Routes.selectHalfCourtPage, arguments: {
            "halfCourtPics": logic.venueModel.value.halves,
            "courtName": logic.venueModel.value.name ?? ""
          });
        },
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '查找视频片段',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无生涯视频，快去创作吧',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => // 分组内容
          GridView.builder(
        scrollDirection: Axis.vertical,
        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
        shrinkWrap: true,
        // physics:
        //     const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 15.w,
          mainAxisSpacing: 15.w,
          childAspectRatio: 165 / 131,
        ),
        padding: EdgeInsets.only(bottom: 20.w),
        itemCount: logic.videoList.length,
        itemBuilder: (context, itemIndex) {
          final itemModel = logic.videoList[itemIndex];
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              AppPage.to(Routes.highlightsVideo, arguments: {
                'record': itemModel,
                'arenaName': logic.venueModel.value.name
              });
            },
            child: Column(
              children: [
                Expanded(
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.w),
                        topRight: Radius.circular(8.w)),
                    child: Stack(
                      children: [
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 0,
                            child: CachedNetworkImage(
                              imageUrl: itemModel.coverPath ?? '',
                              fit: BoxFit.cover,
                              height: 93.w,
                            )),
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              height: 24.w,
                              alignment: Alignment.centerLeft,
                              color: Colours.color0F0F16.withOpacity(0.6),
                              child: Text(
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                itemModel.title ?? '',
                                style: TextStyles.display12
                                    .copyWith(color: Colors.white),
                              ),
                            )),
                        Positioned(
                            left: 0,
                            right: 0,
                            top: 32.w,
                            child: WxAssets.images.selfieShotPlay
                                .image(width: 28.w, height: 28.w)),
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 38.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8.w),
                          bottomRight: Radius.circular(8.w))),
                  child: Row(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(9.w),
                          child: CachedNetworkImage(
                            imageUrl: itemModel.userAvatar ?? '',
                            fit: BoxFit.cover,
                            height: 18.w,
                            width: 18.w,
                          )),
                      SizedBox(
                        width: 6.w,
                      ),
                      Expanded(
                          child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        itemModel.userName ?? '',
                        style: TextStyles.display12,
                      ))
                    ],
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  List<String> _getTagsStr(VenueModel model) {
    final floorMaterial = ['木地板', '塑胶', '悬浮拼接地板', '水泥地'];
    final hasLight = ['有灯光', '无灯光'];
    // final isFree = ['免费', '收费'];
    final openTime = ['不对外开放', '全天开放', '白天开放', '晚上开放'];
    final type = ['室内', '室外'];
    var result = <String>[];
    // final tagList = tags.split(',');
    result.add(type[(model.type ?? 1) - 1]);
    result.add(openTime[(model.openTime ?? 1) - 1]);
    result.add(floorMaterial[(model.floorMaterial ?? 1) - 1]);
    result.add(hasLight[(model.hasLight ?? 1) - 1]);

    return result;
  }
}
