import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/record_model.dart';

class MoreHighlightsLogic extends GetxController {
  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  bool isEnd = false;
  var arenaId = 0;
  var arenaName = '';
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  //数据列表
  var dataList = <RecordModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('arenaId')) {
      arenaId = Get.arguments['arenaId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('arenaName')) {
      arenaName = Get.arguments['arenaName'];
    }
    onRefresh();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await getdataList(false);
  }

  bool hasMore() {
    return isEnd == false;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await getdataList(true);
    // init.value = true;
  }

  //获得最新列表
  Future<void> getdataList(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': pageSize,
      'arenaId': arenaId,
      'lastRecordId': isRefresh ? '0' : dataList.last.id
    };
    var res =
        await Api().get(ApiUrl.arenaHighlightsList, queryParameters: param);
    _isLoading = false;
    init.value = true;
    cc.log("result%%%%%%%%%%${param}${res.data}");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      // cc.log("result${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => RecordModel.fromJson(e))
          .toList();
      isEnd = res.data["isEnd"];
      // totalCount = res.data["totalCount"];
      if (isRefresh) {
        dataList.value = list;
      } else {
        dataList.addAll(list);
      }
    } else {
      if (isRefresh) {
        dataList.value = [];
        isEnd = false;
      }
      WxLoading.showToast(res.message);
    }
  }
}
