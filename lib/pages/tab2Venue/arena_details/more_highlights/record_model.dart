///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class RecordModel {
/*
{
  "arenaId": "0",
  "completedTime": "string",
  "cover": "string",
  "createdTime": "string",
  "duration": "string",
  "id": "0",
  "name": "string",
  "playCount": 0,
  "status": 0,
  "userAvatar": "string",
  "userId": "0",
  "userName": "string",
  "videoPath": "string",
  "vipLevel": 0
} 
*/

  String? arenaId;
  String? completedTime;
  String? cover;
  String? createdTime;
  String? duration;
  String? id;
  String? name;
  int? playCount;
  int? status;
  String? userAvatar;
  String? userId;
  String? userName;
  String? videoPath;
  int? vipLevel;

  RecordModel({
    this.arenaId,
    this.completedTime,
    this.cover,
    this.createdTime,
    this.duration,
    this.id,
    this.name,
    this.playCount,
    this.status,
    this.userAvatar,
    this.userId,
    this.userName,
    this.videoPath,
    this.vipLevel,
  });
  RecordModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    completedTime = json['completedTime']?.toString();
    cover = json['cover']?.toString();
    createdTime = json['createdTime']?.toString();
    duration = json['duration']?.toString();
    id = json['id']?.toString();
    name = json['name']?.toString();
    playCount = json['playCount']?.toInt();
    status = json['status']?.toInt();
    userAvatar = json['userAvatar']?.toString();
    userId = json['userId']?.toString();
    userName = json['userName']?.toString();
    videoPath = json['videoPath']?.toString();
    vipLevel = json['vipLevel']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['completedTime'] = completedTime;
    data['cover'] = cover;
    data['createdTime'] = createdTime;
    data['duration'] = duration;
    data['id'] = id;
    data['name'] = name;
    data['playCount'] = playCount;
    data['status'] = status;
    data['userAvatar'] = userAvatar;
    data['userId'] = userId;
    data['userName'] = userName;
    data['videoPath'] = videoPath;
    data['vipLevel'] = vipLevel;
    return data;
  }
}
