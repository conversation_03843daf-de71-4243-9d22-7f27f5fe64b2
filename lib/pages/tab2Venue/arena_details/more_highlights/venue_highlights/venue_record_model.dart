///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VenueRecordModel {
/*
{
  "coverPath": "string",
  "id": "0",
  "title": "string",
  "userAvatar": "string",
  "userId": 0,
  "userName": "string",
  "venueId": "0",
  "venueName": "string",
  "videoPath": "string"
} 
*/

  String? coverPath;
  String? id;
  String? title;
  String? userAvatar;
  int? userId;
  String? userName;
  String? venueId;
  String? venueName;
  String? videoPath;

  VenueRecordModel({
    this.coverPath,
    this.id,
    this.title,
    this.userAvatar,
    this.userId,
    this.userName,
    this.venueId,
    this.venueName,
    this.videoPath,
  });
  VenueRecordModel.fromJson(Map<String, dynamic> json) {
    coverPath = json['coverPath']?.toString();
    id = json['id']?.toString();
    title = json['title']?.toString();
    userAvatar = json['userAvatar']?.toString();
    userId = json['userId']?.toInt();
    userName = json['userName']?.toString();
    venueId = json['venueId']?.toString();
    venueName = json['venueName']?.toString();
    videoPath = json['videoPath']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['coverPath'] = coverPath;
    data['id'] = id;
    data['title'] = title;
    data['userAvatar'] = userAvatar;
    data['userId'] = userId;
    data['userName'] = userName;
    data['venueId'] = venueId;
    data['venueName'] = venueName;
    data['videoPath'] = videoPath;
    return data;
  }
}
