import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_model.dart';

class ArenaCompetitionListLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  var tabList = [
    {'title': '全部', 'id': '0'},
    {'title': '报名中', 'id': '1'},
    {'title': '待开赛', 'id': '2'},
    {'title': '进行中', 'id': '3'},
    {'title': '已结束', 'id': '4'},
  ];

  var searchText = ''.obs; //搜索框文本状态
  TextEditingController searchController = TextEditingController(); //搜索框
  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var areaId;
  var pageSize = 10;
  var totalRows = 0;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  //数据列表
  var dataList = <CompetitionModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    areaId = Get.arguments;
    onRefresh();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await getdataList(false);
  }

  bool hasMore() {
    return dataList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await getdataList(true);
    // init.value = true;
  }

  //获得最新列表
  Future<void> getdataList(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': pageSize,
      'arenaId': areaId,
    };
    if (searchText.isNotEmpty) {
      param['competitionName'] = searchText;
    }
    cc.log("getdataList!!!!!!!!$param");
    var res = await Api().get(ApiUrl.competitionsList, queryParameters: param);
    _isLoading = false;
    init.value = true;
    cc.log("result${res.data}");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      cc.log("result${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => CompetitionModel.fromJson(e))
          .toList();
      totalRows = res.data["totalCount"];
      if (isRefresh) {
        dataList.value = list;
      } else {
        dataList.addAll(list);
      }
    } else {
      if (isRefresh) {
        dataList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
