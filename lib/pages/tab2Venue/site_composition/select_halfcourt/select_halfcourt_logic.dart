import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';

class SelectHalfcourtLogic extends GetxController {
  List<VenueModelHalves> halfCourtPics = [];
  var selectHalfCourt = <VenueModelHalves>[].obs;
  var courtName = ''.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      halfCourtPics = Get.arguments['halfCourtPics'];
      courtName.value = Get.arguments['courtName'];
    }
  }

  @override
  void onReady() {
    super.onReady();
  }
}
