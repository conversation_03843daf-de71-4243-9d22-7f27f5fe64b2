import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_match_mdel.dart';

class CompetitionListLogic extends GetxController {
  var myBattleModelList = <CompetitionMatchModel>[].obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;

  int competitionId = 0;
  setCompetitionId(int competitionId) {
    this.competitionId = competitionId;
    onRefresh();
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> loadMore() async {
    cc.log("loadMore!!!!!!!!");
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await myMatches(false);
  }

  bool hasMore() {
    return myBattleModelList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await myMatches(true);
    // init.value = true;
  }

  //我的约战
  Future<void> myMatches(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> request = {'pageSize': pageSize, 'pageIndex': page};
    var res = await Api()
        .get(ApiUrl.competitionList(competitionId), queryParameters: request);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      cc.log("result${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => CompetitionMatchModel.fromJson(e))
          .toList();
      totalRows = res.data["totalCount"];
      if (isRefresh) {
        myBattleModelList.value = list;
      } else {
        myBattleModelList.addAll(list);
      }
    } else {
      if (isRefresh) {
        myBattleModelList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
