import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/team_ranking_model.dart';

class RankingPlayerInfoLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final int competitionId;
  RankingPlayerInfoLogic(this.competitionId);
  var playerRankingList = <PlayerRankingModel>[].obs;
  final requestError = false.obs;
  var isInit = false;
  TabController? tabController;
  var tabNameList = [
    S.current.team_tag4,
    S.current.team_tag5,
    S.current.team_tag6,
  ].obs;
  var tabbarIndex = 0.obs;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabNameList.length, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
        getPlayerData();
      },
    );
    getPlayerData();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /// Maps tab index to sortBy value for player data
  /// index 0 (总得分): sortBy = 4
  /// index 1 (总篮板): sortBy = 3
  /// index 2 (总助攻): sortBy = 2
  /// index 3 (场次): sortBy = 1
  int _getPlayerSortBy(int index) {
    switch (index) {
      case 0:
        return 4; // 总得分
      case 1:
        return 3; // 总篮板
      case 2:
        return 2; // 总助攻
      case 3:
        return 1; // 场次
      default:
        return index + 1; // fallback to original logic
    }
  }

  Future<void> getPlayerData() async {
    WxLoading.show();
    requestError.value = false;
    final url = ApiUrl.playerRankData(competitionId);
    var params = {
      'sortBy': _getPlayerSortBy(tabbarIndex.value),
    };
    log("message!!!!!!@222$params$competitionId");
    final response = await Api.instance.get(url, queryParameters: params);
    isInit = true;
    log("message333~~~~~~${response.data}");
    WxLoading.dismiss();
    if (response.isSuccessful()) {
      requestError.value = false;
      playerRankingList.value = (response.data as List)
          .map((e) => PlayerRankingModel.fromJson(e))
          .toList();
      // if (state.rankingModel.value.billboard?.isEmpty ?? true) {
      //   rankingsLogic.state.noData.value = true;
      // } else {
      //   rankingsLogic.state.noData.value = false;
      // }
      // if (UserManager.instance.isLogin) {
      //   rankingsLogic.state.numbers[index] = state.rankingModel.value.myIndex!;
      //   rankingsLogic.state.myRankData[index] =
      //       state.rankingModel.value.myRankData ?? '0';
      // }
    } else {
      requestError.value = true;
    }
  }
}
