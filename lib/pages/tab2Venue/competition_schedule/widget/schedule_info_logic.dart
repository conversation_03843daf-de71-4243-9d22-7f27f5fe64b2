import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_model.dart';

class ScheduleInfoLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  var competitionDataModel = CompetitionDataModel().obs;
  int competitionId = 0;
  TabController? tabController;
  var tabNameList = [S.current.team, S.current.players].obs;
  var tabbarIndex = 0.obs;
  setCompetitionId(int competitionId) {
    this.competitionId = competitionId;
    scheduleInfo();
  }

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabNameList.length, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
  }

  void switchTab(index) {
    tabbarIndex.value = index;
    tabController?.animateTo(index);
  }

  //赛程数据
  Future<void> scheduleInfo() async {
    WxLoading.show();
    var res = await Api()
        .get(ApiUrl.competitionData(competitionId), queryParameters: {});
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      competitionDataModel.value = CompetitionDataModel.fromJson(res.data);
      cc.log('scheduleInfo!!!!!${res.data}');
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
