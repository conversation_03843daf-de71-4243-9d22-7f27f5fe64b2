import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/team_ranking_model.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/ranking_player_info_logic.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class RankingPlayerInfoPage extends StatelessWidget {
  final int competitionId;
  RankingPlayerInfoPage({super.key, required this.competitionId});
  // 声明 logic 变量但不立即初始化
  late final logic = Get.put(RankingPlayerInfoLogic(competitionId));

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.all(Radius.circular(8.r))),
        padding:
            EdgeInsets.only(top: 20.w, bottom: 20.w, left: 15.w, right: 15.w),
        margin: EdgeInsets.only(top: 15.w),
        child: Column(
          children: [
            PreferredSize(
              preferredSize: Size.fromHeight(48.w), // 固定高度30
              child: Container(
                width: double.infinity,
                alignment: Alignment.topLeft,
                padding: EdgeInsets.only(bottom: 5.w),
                child: TabBar(
                    controller: logic.tabController,
                    unselectedLabelColor: Colours.color5C5C6E,
                    unselectedLabelStyle: TextStyle(
                        fontSize: 18.sp,
                        color: Colours.color5C5C6E,
                        fontWeight: FontWeight.w600),
                    labelColor: Colours.white,
                    labelStyle: TextStyle(
                        fontSize: 20.sp,
                        color: Colours.white,
                        fontWeight: FontWeight.w600),
                    isScrollable: true,
                    // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                    indicatorPadding: EdgeInsets.zero,
                    dividerColor: Colors.transparent,
                    dividerHeight: 0,
                    labelPadding: EdgeInsets.only(right: 12.w), // 调整标签间的间距
                    indicatorSize: TabBarIndicatorSize.label,
                    padding: EdgeInsets.zero,
                    indicatorColor: Colors.transparent,
                    physics: const NeverScrollableScrollPhysics(),
                    tabAlignment: TabAlignment.start,
                    tabs: List.generate(logic.tabNameList.length, (index) {
                      return Obx(() {
                        return SizedBox(
                          width: 40.w,
                          height: 25.w,
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              if (logic.tabbarIndex.value == index)
                                Container(
                                    width: 16.w,
                                    height: 3.w,
                                    margin: EdgeInsets.only(top: 4.w),
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF,
                                        ],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                      ),
                                      borderRadius: BorderRadius.circular(2.r),
                                    )),
                              Positioned(
                                  bottom: 7.w,
                                  child: logic.tabbarIndex.value == index
                                      ? ShaderMask(
                                          shaderCallback: (bounds) =>
                                              const LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: [
                                              Color(0xFF7732ED),
                                              Color(0xFFA555EF),
                                            ],
                                          ).createShader(bounds),
                                          child: Text(
                                            logic.tabNameList[index],
                                            style:
                                                TextStyles.display14.copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        )
                                      : Text(
                                          logic.tabNameList[index],
                                          style: TextStyles.display12.copyWith(
                                            color: Colours.color5C5C6E,
                                          ),
                                        )),
                            ],
                          ),
                        );
                      });
                    })),
              ),
            ),
            Expanded(
              // height: 320.w, // Increased height to prevent overflow
              child: TabBarView(
                controller: logic.tabController,
                children: List.generate(logic.tabNameList.length, (index) {
                  return _getInfoWidget(context, index);
                }),
              ),
            ),
          ],
        ));
  }

  Widget _getInfoWidget(BuildContext context, int index) {
    return Obx(() {
      return logic.playerRankingList.isEmpty
          ? myNoDataView(
              context,
              msg: S.current.No_data_available,
              imagewidget: WxAssets.images.battleEmptyIcon.image(),
            )
          : Column(
              children: [
                SizedBox(
                  height: 15.w,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      width: 50.w,
                      child: Text(S.current.ranking,
                          style: TextStyles.bold.copyWith(fontSize: 14.sp)),
                    ),
                    Container(
                      alignment: Alignment.center,
                      width: 68.w,
                      child: Text(S.current.players,
                          style: TextStyles.bold.copyWith(fontSize: 14.sp)),
                    ),
                    Container(
                        alignment: Alignment.center,
                        width: 50.w,
                        child: Text(logic.tabNameList[index],
                            style: TextStyles.bold.copyWith(fontSize: 14.sp)))
                  ],
                ),
                SizedBox(height: 10.w),
                Expanded(
                    child: Obx(
                  () => logic.requestError.value
                      ? const SizedBox.shrink()
                      : ListView.builder(
                          padding: EdgeInsets.only(
                              bottom: (MediaQuery.of(context).padding.bottom > 0
                                      ? MediaQuery.of(context).padding.bottom
                                      : 20.w) +
                                  48.w),
                          itemCount: logic.playerRankingList.length,
                          itemBuilder: (context, index) {
                            PlayerRankingModel model =
                                logic.playerRankingList[index];
                            return Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                    width: 29.w,
                                    child: Text(
                                      '${index + 1}',
                                      style: TextStyles.bold.copyWith(
                                          fontSize: 16.sp, fontFamily: 'DIN'),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 102.w,
                                    child: Row(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12.w),
                                          child: CachedNetworkImage(
                                            imageUrl: model.avatar ?? '',
                                            width: 24.w,
                                            height: 24.w,
                                            fit: BoxFit.fill,
                                          ),
                                        ).marginSymmetric(vertical: 10.w),
                                        SizedBox(width: 8.w),
                                        SizedBox(
                                          width: 70.w,
                                          child: Text(
                                            model.userName ?? '',
                                            style: TextStyles.regular
                                                .copyWith(fontSize: 12.sp),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    width: 52.w,
                                    child: Text(_getLastStr(model),
                                        style: TextStyles.regular
                                            .copyWith(fontSize: 12.sp),
                                        textAlign: TextAlign.center),
                                  ),
                                ]);
                          }),
                )),
              ],
            );
    });
  }

  _getLastStr(PlayerRankingModel model) {
    switch (logic.tabbarIndex.value) {
      case 0:
        return (model.score ?? 0).toString();
      case 1:
        return (model.reboundCount ?? 0).toString();
      case 2:
        return (model.assistCount ?? 0).toString();
      default:
        return (model.score ?? 0).toString();
    }
  }
}
