import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/team_ranking_model.dart';

class RankingTeamInfoLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final int competitionId;
  RankingTeamInfoLogic(this.competitionId);
  var teamRankingList = <TeamRankingModel>[].obs;
  var isInit = false;
  TabController? tabController;
  final requestError = false.obs;
  var tabNameList = [
    S.current.team_tag4,
    S.current.team_tag5,
    S.current.team_tag6,
    S.current.session,
  ].obs;
  var tabbarIndex = 0.obs;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabNameList.length, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
        getData();
      },
    );
    getData();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  var cancelToken = CancelToken();

  /// Maps tab index to sortBy value for player data
  /// index 0 (总得分): sortBy = 4
  /// index 1 (总篮板): sortBy = 3
  /// index 2 (总助攻): sortBy = 2
  /// index 3 (场次): sortBy = 1
  int _getPlayerSortBy(int index) {
    switch (index) {
      case 0:
        return 4; // 总得分
      case 1:
        return 3; // 总篮板
      case 2:
        return 2; // 总助攻
      case 3:
        return 1; // 场次
      default:
        return index + 1; // fallback to original logic
    }
  }

  Future<void> getData() async {
    requestError.value = false;
    final url = ApiUrl.teamRankData(competitionId);
    cancelToken.cancel();
    cancelToken = CancelToken();
    var params = {
      'sortBy': _getPlayerSortBy(tabbarIndex.value),
    };
    log("message!!!!!!@222$params$competitionId");
    final response = await Api.instance
        .get(url, cancelToken: cancelToken, queryParameters: params);
    isInit = true;
    log("message333${response.data}");
    if (response.isSuccessful()) {
      requestError.value = false;
      teamRankingList.value = (response.data as List)
          .map((e) => TeamRankingModel.fromJson(e))
          .toList();
      // if (state.rankingModel.value.billboard?.isEmpty ?? true) {
      //   rankingsLogic.state.noData.value = true;
      // } else {
      //   rankingsLogic.state.noData.value = false;
      // }
      // if (UserManager.instance.isLogin) {
      //   rankingsLogic.state.numbers[index] = state.rankingModel.value.myIndex!;
      //   rankingsLogic.state.myRankData[index] =
      //       state.rankingModel.value.myRankData ?? '0';
      // }
    } else {
      requestError.value = true;
    }
  }
}
