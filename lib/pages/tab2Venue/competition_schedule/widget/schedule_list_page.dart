import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/schedule_list_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

/// 赛程列表页面
class ScheduleListPage extends StatelessWidget {
  ScheduleListPage({super.key});
  final logic = Get.put(ScheduleListLogic());
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _searchWidget(),
        _tabbarWidget(),
        Expanded(
            child: TabBarView(
          controller: logic.tabController,
          children: [
            ...logic.tabList.map((e) => NotificationListener(
                onNotification: (ScrollNotification note) {
                  if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                    logic.loadMore();
                  }
                  return true;
                },
                child: Obx(() => RefreshIndicator(
                      onRefresh: logic.onRefresh,
                      child: Container(
                        color: Colours.bg_color,
                        child: logic.init.value
                            ? (logic.dataList.isEmpty
                                ? _buildEmptyView(context)
                                : _listView(context))
                            : buildLoad(),
                      ),
                    ))))
          ],
        ))
      ],
    );
  }

  Widget _searchWidget() {
    return // 搜索框
        Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15),
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      height: 42.w,
      decoration: BoxDecoration(
        color: Colours.color191921,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        children: [
          WxAssets.images.icSearch.image(),
          const SizedBox(
            width: 10,
          ),
          Expanded(
              child: TextField(
            autocorrect: false,
            controller: logic.searchController,
            keyboardType: TextInputType.text,
            style: TextStyles.display14,
            maxLines: 1,
            // onChanged: logic.onTextChanged,
            decoration: InputDecoration(
              // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
              contentPadding: EdgeInsets.only(top: 0, bottom: 5.w),
              border: InputBorder.none,
              hintText: "请输入赛程名称",
              hintStyle:
                  TextStyles.display14.copyWith(color: Colours.color5C5C6E),
            ),
            textInputAction: TextInputAction.search,
            onSubmitted: (value) {
              logic.onRefresh();
            },
            onChanged: (value) => logic.searchText.value = value,
          )),
          GestureDetector(
              onTap: () {
                logic.searchController.text = '';
                logic.searchText.value = '';
                logic.onRefresh();
              },
              child: Obx(() => Visibility(
                  visible: logic.searchText.value.isNotEmpty,
                  child: WxAssets.images.icSearchDelete.image()))),
        ],
      ),
    );
  }

  Widget _tabbarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(48.w), // 固定高度30
      child: Container(
          width: double.infinity,
          alignment: Alignment.topLeft,
          padding: EdgeInsets.only(bottom: 5.w),
          margin: EdgeInsets.only(left: 10.w),
          child: TabBar(
              controller: logic.tabController,
              unselectedLabelColor: Colours.color5C5C6E,
              unselectedLabelStyle: TextStyle(
                  fontSize: 18.sp,
                  color: Colours.color5C5C6E,
                  fontWeight: FontWeight.w600),
              labelColor: Colours.white,
              labelStyle: TextStyle(
                  fontSize: 20.sp,
                  color: Colours.white,
                  fontWeight: FontWeight.w600),
              isScrollable: true,
              // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
              indicatorPadding: EdgeInsets.zero,
              dividerColor: Colors.transparent,
              dividerHeight: 0,
              labelPadding: EdgeInsets.only(right: 20.w), // 调整标签间的间距
              indicatorSize: TabBarIndicatorSize.label,
              padding: EdgeInsets.zero,
              indicatorColor: Colors.transparent,
              physics: const NeverScrollableScrollPhysics(),
              overlayColor: WidgetStateProperty.all(Colors.transparent),
              tabAlignment: TabAlignment.start,
              tabs: List.generate(logic.tabList.length, (index) {
                Map<String, dynamic> tabData = logic.tabList[index];
                return Obx(() {
                  return SizedBox(
                    width: 40.w,
                    height: 25.w,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        if (logic.currentTabIndex.value == index)
                          Container(
                              width: 16.w,
                              height: 3.w,
                              margin: EdgeInsets.only(top: 4.w),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF,
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.circular(2.r),
                              )),
                        Positioned(
                            bottom: 7.w,
                            child: logic.currentTabIndex.value == index
                                ? ShaderMask(
                                    shaderCallback: (bounds) =>
                                        const LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: [
                                        Color(0xFF7732ED),
                                        Color(0xFFA555EF),
                                      ],
                                    ).createShader(bounds),
                                    child: Text(
                                      tabData["title"],
                                      style: TextStyles.display14.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  )
                                : Text(
                                    tabData['title'],
                                    style: TextStyles.display12.copyWith(
                                      color: Colours.color5C5C6E,
                                    ),
                                  )),
                      ],
                    ),
                  );
                });
              }))),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无赛程',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.all(15.w),
        itemCount: logic.dataList.length,
        itemBuilder: (context, index) {
          var model = logic.dataList[index];
          DateTime parsedStartDate = DateTime.parse(model.startTime ?? '');
          DateTime parsedEndDate = DateTime.parse(model.endTime ?? '');
          String formattedStartDate =
              DateFormat("yyyy.MM.dd").format(parsedStartDate);
          String formattedEndDate =
              DateFormat("yyyy.MM.dd").format(parsedEndDate);
          return Column(
            children: [
              InkWell(
                onTap: () => AppPage.to(Routes.competitionDetailPage,
                    arguments: {'competitionId': model.competitionId}),
                child: Container(
                  height: 130.w,
                  margin: EdgeInsets.only(bottom: 15.w),
                  padding: EdgeInsets.all(15.r),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.all(Radius.circular(8.r))),
                  child: Row(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(8.w),
                          child: CachedNetworkImage(
                            imageUrl: model.arenaImageUrl ?? "",
                            width: 100.w,
                            height: 100.w,
                            fit: BoxFit.fill,
                          )),
                      SizedBox(
                        width: 15.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              model.competitionName ?? '',
                              style:
                                  TextStyles.semiBold14.copyWith(height: 1.5),
                              maxLines: 2,
                            ),
                            Text(
                              '报名截止：${model.registrationDeadline?.split(' ').first}',
                              style: TextStyles.display12,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: 20,
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 9.w),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient: model.status == 1
                                        ? const LinearGradient(colors: [
                                            Color(0xFF7732ED),
                                            Color(0xFFA555EF),
                                          ])
                                        : null,
                                    color: _getStatusColor(model.status ?? 0),
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(8.r),
                                        topRight: Radius.circular(8.r)),
                                  ),
                                  child: Text(
                                    _getStatusStr(model.status ?? 0),
                                    style: TextStyles.display10.copyWith(
                                        color: model.status == 4
                                            ? Colours.color5C5C6E
                                            : Colours.white,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                const SizedBox(
                                  width: 6,
                                ),
                                Text(
                                  '$formattedStartDate-$formattedEndDate',
                                  style: TextStyles.display12
                                      .copyWith(color: Colours.colorA8A8BC),
                                )
                              ],
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              )
            ],
          );
        });
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return Colours.color262626;
      default:
        return Colours.color262626;
    }
  }
}
