import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/registration_model.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/my_registration_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的报名列表
class MyRegistrationPage extends StatelessWidget {
  MyRegistrationPage({super.key});

  final logic = Get.put(MyRegistrationLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_registration),
      ),
      body: Obx(() {
        return NotificationListener(
            onNotification: (ScrollNotification note) {
              if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                logic.loadMore();
              }
              return true;
            },
            child: RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Container(
                color: Colours.bg_color,
                child: logic.init.value
                    ? (logic.registrationsList.isEmpty
                        ? _emptyView(context)
                        : _listView(context))
                    : buildLoad(),
              ),
            ));
      }),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight - 40,
              child: myNoDataView(
                context,
                msg: '暂时还没有人报名哦，请耐心等待～',
                imagewidget: WxAssets.images.purposeEmpty.image(),
              ),
            ));
      },
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.registrationsList.length,
        itemBuilder: (context, index) {
          final item = logic.registrationsList[index];
          // 列表项
          return _buildListItem(item);
        });
  }

  /// 构建列表项
  Widget _buildListItem(RegistrationModel item) {
    return InkWell(
      onTap: () {
        // AppPage.to(Routes.battleDetailPage,
        //     arguments: {'challengeId': item.id});
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.w),
        decoration: BoxDecoration(
          color: const Color(0xFF191921), // 设置背景颜色
          borderRadius: BorderRadius.circular(8.r), // 设置圆角
        ),
        child: Column(
          children: [
            _cellInfoWidget('报名赛事', item.competitionName ?? ''),
            SizedBox(
              height: 20.w,
            ),
            _cellInfoWidget('球队名称', item.teamName ?? ''),
            SizedBox(
              height: 20.w,
            ),
            _cellInfoWidget('联系电话', item.contactPhone ?? ""),
            SizedBox(
              height: 20.w,
            ),
            _cellInfoWidget('报名时间', item.registrationTime ?? "")
          ],
        ),
      ),
    );
  }

  Widget _cellInfoWidget(String leftStr, String rightStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60.w,
          child: Text(
            textAlign: TextAlign.left,
            leftStr,
            style: TextStyles.display14.copyWith(color: Colours.color5C5C6E),
          ),
        ),
        SizedBox(
          width: 20.w,
        ),
        Expanded(
          child: Text(
            rightStr,
            style: TextStyles.display14,
            maxLines: 2,
          ),
        ),
      ],
    );
  }
}
