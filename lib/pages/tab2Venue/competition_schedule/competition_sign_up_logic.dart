import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_detail_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/utils.dart';

class CompetitionSignUpLogic extends GetxController {
  var isLoading = true.obs;
  int competitionId = 0;
  var competitionModel = CompetitionDetailModel().obs;
  var teamId = "0";
  var leadUserId = 0;
  var teamController = TextEditingController(); //球队
  var leaderController = TextEditingController(); //球队队长
  var phoneController = TextEditingController(); //联系电话
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('competitionId')) {
      competitionId = Get.arguments['competitionId'] ?? '';
    } else {
      isLoading.value = false;
    }
  }

  // 报名
  Future<void> competitionSignUp() async {
    if (teamController.text.trim().isEmpty) {
      WxLoading.showToast('请选择球队');
      return;
    }
    if (phoneController.text.trim().isEmpty) {
      WxLoading.showToast('请输入手机号');
      return;
    }
    if (!Utils.isPhoneNumber(phoneController.text.trim())) {
      WxLoading.showToast('请输入正确的手机号码');
      return;
    }
    isLoading.value = true;
    WxLoading.show();
    var url = ApiUrl.competitionSignUp(competitionId);
    var params = {
      "contactPhone": phoneController.text,
      "leadUserId": leadUserId,
      "teamId": teamId
    };
    log('!!!!!!$competitionId$params');
    final res = await Api().post(url, data: params);
    if (res.isSuccessful()) {
      log("message${res.data}");
      WxLoading.showToast('报名成功');
      AppPage.back();
    } else {
      WxLoading.showToast(res.message);
    }
    isLoading.value = false;
    WxLoading.dismiss();
  }
}
