import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_detail_model.dart';

class CompetitionDetailLogic extends GetxController
    with GetTickerProviderStateMixin {
  late TabController tabController;
  var currentTabIndex = 0.obs;
  var tabList = [
    {'title': '基本信息', 'id': 0},
    {'title': '赛事列表', 'id': 1},
    {'title': '赛程数据', 'id': 2}
  ];
  var isLoading = true.obs;
  int competitionId = 0;
  var competitionModel = CompetitionDetailModel().obs;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabList.length, vsync: this);
    tabController.addListener(() {
      // 只在tab切换完成时调用，避免重复调用
      if (!tabController.indexIsChanging) {
        currentTabIndex.value = tabController.index;
      }
    });
    if (Get.arguments != null && Get.arguments.containsKey('competitionId')) {
      competitionId = Get.arguments['competitionId'] ?? '';
      competitionDetail();
    } else {
      isLoading.value = false;
    }
  }

  // 赛程详情
  Future<void> competitionDetail() async {
    isLoading.value = true;
    WxLoading.show();
    var url = ApiUrl.competitionDetail(competitionId);
    final res = await Api().get(url);
    if (res.isSuccessful()) {
      log("message${res.data}");
      competitionModel.value = CompetitionDetailModel.fromJson(res.data);
    } else {
      WxLoading.showToast(res.message);
    }
    isLoading.value = false;
    WxLoading.dismiss();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
