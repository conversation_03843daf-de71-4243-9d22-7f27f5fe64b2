///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamRankingModel {
/*
{
  "assistCount": 0,
  "matchCount": 0,
  "outcomeRate": "string",
  "reboundCount": 0,
  "score": 0,
  "sort": 0,
  "teamId": 0,
  "teamLogo": "string",
  "teamName": "string",
  "winRate": "string"
} 
*/

  int? assistCount;
  int? matchCount;
  String? outcomeRate;
  int? reboundCount;
  int? score;
  int? sort;
  int? teamId;
  String? teamLogo;
  String? teamName;
  String? winRate;

  TeamRankingModel({
    this.assistCount,
    this.matchCount,
    this.outcomeRate,
    this.reboundCount,
    this.score,
    this.sort,
    this.teamId,
    this.teamLogo,
    this.teamName,
    this.winRate,
  });
  TeamRankingModel.fromJson(Map<String, dynamic> json) {
    assistCount = json['assistCount']?.toInt();
    matchCount = json['matchCount']?.toInt();
    outcomeRate = json['outcomeRate']?.toString();
    reboundCount = json['reboundCount']?.toInt();
    score = json['score']?.toInt();
    sort = json['sort']?.toInt();
    teamId = json['teamId']?.toInt();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    winRate = json['winRate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assistCount'] = assistCount;
    data['matchCount'] = matchCount;
    data['outcomeRate'] = outcomeRate;
    data['reboundCount'] = reboundCount;
    data['score'] = score;
    data['sort'] = sort;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    data['winRate'] = winRate;
    return data;
  }
}

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PlayerRankingModel {
/*
{
  "assistCount": 0,
  "avatar": "string",
  "reboundCount": 0,
  "score": 0,
  "sort": 0,
  "userId": 0,
  "userName": "string"
} 
*/

  int? assistCount;
  String? avatar;
  int? reboundCount;
  int? score;
  int? sort;
  int? userId;
  String? userName;

  PlayerRankingModel({
    this.assistCount,
    this.avatar,
    this.reboundCount,
    this.score,
    this.sort,
    this.userId,
    this.userName,
  });
  PlayerRankingModel.fromJson(Map<String, dynamic> json) {
    assistCount = json['assistCount']?.toInt();
    avatar = json['avatar']?.toString();
    reboundCount = json['reboundCount']?.toInt();
    score = json['score']?.toInt();
    sort = json['sort']?.toInt();
    userId = json['userId']?.toInt();
    userName = json['userName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assistCount'] = assistCount;
    data['avatar'] = avatar;
    data['reboundCount'] = reboundCount;
    data['score'] = score;
    data['sort'] = sort;
    data['userId'] = userId;
    data['userName'] = userName;
    return data;
  }
}
