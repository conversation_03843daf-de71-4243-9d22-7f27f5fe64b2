///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CompetitionModel {
/*
{
  "arenLongitude": 0,
  "arenaAddress": "string",
  "arenaCount": 0,
  "arenaId": "0",
  "arenaImageUrl": "string",
  "arenaLatitude": 0,
  "arenaName": "string",
  "competitionId": 0,
  "competitionName": "string",
  "endTime": "string",
  "nextMatchTime": "string",
  "registrationDeadline": "string",
  "startTime": "string",
  "status": 0
} 
*/

  int? arenLongitude;
  String? arenaAddress;
  int? arenaCount;
  String? arenaId;
  String? arenaImageUrl;
  int? arenaLatitude;
  String? arenaName;
  int? competitionId;
  String? competitionName;
  String? endTime;
  String? nextMatchTime;
  String? registrationDeadline;
  String? startTime;
  int? status;

  CompetitionModel({
    this.arenLongitude,
    this.arenaAddress,
    this.arenaCount,
    this.arenaId,
    this.arenaImageUrl,
    this.arenaLatitude,
    this.arenaName,
    this.competitionId,
    this.competitionName,
    this.endTime,
    this.nextMatchTime,
    this.registrationDeadline,
    this.startTime,
    this.status,
  });
  CompetitionModel.fromJson(Map<String, dynamic> json) {
    arenLongitude = json['arenLongitude']?.toInt();
    arenaAddress = json['arenaAddress']?.toString();
    arenaCount = json['arenaCount']?.toInt();
    arenaId = json['arenaId']?.toString();
    arenaImageUrl = json['arenaImageUrl']?.toString();
    arenaLatitude = json['arenaLatitude']?.toInt();
    arenaName = json['arenaName']?.toString();
    competitionId = json['competitionId']?.toInt();
    competitionName = json['competitionName']?.toString();
    endTime = json['endTime']?.toString();
    nextMatchTime = json['nextMatchTime']?.toString();
    registrationDeadline = json['registrationDeadline']?.toString();
    startTime = json['startTime']?.toString();
    status = json['status']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenLongitude'] = arenLongitude;
    data['arenaAddress'] = arenaAddress;
    data['arenaCount'] = arenaCount;
    data['arenaId'] = arenaId;
    data['arenaImageUrl'] = arenaImageUrl;
    data['arenaLatitude'] = arenaLatitude;
    data['arenaName'] = arenaName;
    data['competitionId'] = competitionId;
    data['competitionName'] = competitionName;
    data['endTime'] = endTime;
    data['nextMatchTime'] = nextMatchTime;
    data['registrationDeadline'] = registrationDeadline;
    data['startTime'] = startTime;
    data['status'] = status;
    return data;
  }
}

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CompetitionDataModel {
/*
{
  "assistCount": 0,
  "matchNum": 0,
  "playNum": 0,
  "reboundCount": 0,
  "score": 0,
  "teamNum": 0
} 
*/

  int? assistCount;
  int? matchNum;
  int? playNum;
  int? reboundCount;
  int? score;
  int? teamNum;

  CompetitionDataModel({
    this.assistCount,
    this.matchNum,
    this.playNum,
    this.reboundCount,
    this.score,
    this.teamNum,
  });
  CompetitionDataModel.fromJson(Map<String, dynamic> json) {
    assistCount = json['assistCount']?.toInt();
    matchNum = json['matchNum']?.toInt();
    playNum = json['playNum']?.toInt();
    reboundCount = json['reboundCount']?.toInt();
    score = json['score']?.toInt();
    teamNum = json['teamNum']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assistCount'] = assistCount;
    data['matchNum'] = matchNum;
    data['playNum'] = playNum;
    data['reboundCount'] = reboundCount;
    data['score'] = score;
    data['teamNum'] = teamNum;
    return data;
  }
}
