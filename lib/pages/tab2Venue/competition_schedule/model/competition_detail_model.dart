///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CompetitionDetailModelTeamInfo {
/*
{
  "rankScore": 0,
  "teamId": 0,
  "teamLogo": "string",
  "teamName": "string"
} 
*/

  int? rankScore;
  int? teamId;
  String? teamLogo;
  String? teamName;

  CompetitionDetailModelTeamInfo({
    this.rankScore,
    this.teamId,
    this.teamLogo,
    this.teamName,
  });
  CompetitionDetailModelTeamInfo.fromJson(Map<String, dynamic> json) {
    rankScore = json['rankScore']?.toInt();
    teamId = json['teamId']?.toInt();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['rankScore'] = rankScore;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    return data;
  }
}

class CompetitionDetailModelArenaInfoList {
/*
{
  "arenLongitude": 0,
  "arenaAddress": "string",
  "arenaId": 0,
  "arenaLatitude": 0,
  "arenaName": "string",
  "rounds": [
    "string"
  ]
} 
*/

  int? arenLongitude;
  String? arenaAddress;
  int? arenaId;
  int? arenaLatitude;
  String? arenaName;
  String? arenaLogo;
  List<String?>? rounds;

  CompetitionDetailModelArenaInfoList({
    this.arenLongitude,
    this.arenaAddress,
    this.arenaId,
    this.arenaLatitude,
    this.arenaName,
    this.arenaLogo,
    this.rounds,
  });
  CompetitionDetailModelArenaInfoList.fromJson(Map<String, dynamic> json) {
    arenLongitude = json['arenLongitude']?.toInt();
    arenaAddress = json['arenaAddress']?.toString();
    arenaId = json['arenaId']?.toInt();
    arenaLatitude = json['arenaLatitude']?.toInt();
    arenaName = json['arenaName']?.toString();
    arenaLogo = json['arenaLogo']?.toString();
    if (json['rounds'] != null) {
      final v = json['rounds'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      rounds = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenLongitude'] = arenLongitude;
    data['arenaAddress'] = arenaAddress;
    data['arenaId'] = arenaId;
    data['arenaLatitude'] = arenaLatitude;
    data['arenaName'] = arenaName;
    data['arenaLogo'] = arenaLogo;
    if (rounds != null) {
      final v = rounds;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['rounds'] = arr0;
    }
    return data;
  }
}

class CompetitionDetailModel {
/*
{
  "PrizeImages": [
    "string"
  ],
  "arenLongitude": 0,
  "arenaAddress": "string",
  "arenaInfoList": [
    {
      "arenLongitude": 0,
      "arenaAddress": "string",
      "arenaId": 0,
      "arenaLatitude": 0,
      "arenaName": "string",
      "rounds": [
        "string"
      ]
    }
  ],
  "arenaLatitude": 0,
  "arenaName": "string",
  "competitionId": 0,
  "competitionImages": [
    "string"
  ],
  "competitionName": "string",
  "contactPhone": "string",
  "endTime": "string",
  "nextMatchTime": "string",
  "prizeName": "string",
  "registrationDeadline": "string",
  "registrationFee": 0,
  "startTime": "string",
  "status": 0,
  "teamInfo": [
    {
      "rankScore": 0,
      "teamId": 0,
      "teamLogo": "string",
      "teamName": "string"
    }
  ]
} 
*/

  List<String?>? PrizeImages;
  int? arenLongitude;
  String? arenaAddress;
  List<CompetitionDetailModelArenaInfoList?>? arenaInfoList;
  int? arenaLatitude;
  String? arenaName;
  int? competitionId;
  List<String?>? competitionImages;
  String? competitionName;
  String? contactPhone;
  String? endTime;
  String? nextMatchTime;
  String? prizeName;
  String? registrationDeadline;
  int? registrationFee;
  String? startTime;
  int? status;
  List<CompetitionDetailModelTeamInfo?>? teamInfo;

  CompetitionDetailModel({
    this.PrizeImages,
    this.arenLongitude,
    this.arenaAddress,
    this.arenaInfoList,
    this.arenaLatitude,
    this.arenaName,
    this.competitionId,
    this.competitionImages,
    this.competitionName,
    this.contactPhone,
    this.endTime,
    this.nextMatchTime,
    this.prizeName,
    this.registrationDeadline,
    this.registrationFee,
    this.startTime,
    this.status,
    this.teamInfo,
  });
  CompetitionDetailModel.fromJson(Map<String, dynamic> json) {
    if (json['PrizeImages'] != null) {
      final v = json['PrizeImages'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      PrizeImages = arr0;
    }
    arenLongitude = json['arenLongitude']?.toInt();
    arenaAddress = json['arenaAddress']?.toString();
    if (json['arenaInfoList'] != null) {
      final v = json['arenaInfoList'];
      final arr0 = <CompetitionDetailModelArenaInfoList>[];
      v.forEach((v) {
        arr0.add(CompetitionDetailModelArenaInfoList.fromJson(v));
      });
      arenaInfoList = arr0;
    }
    arenaLatitude = json['arenaLatitude']?.toInt();
    arenaName = json['arenaName']?.toString();
    competitionId = json['competitionId']?.toInt();
    if (json['competitionImages'] != null) {
      final v = json['competitionImages'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      competitionImages = arr0;
    }
    competitionName = json['competitionName']?.toString();
    contactPhone = json['contactPhone']?.toString();
    endTime = json['endTime']?.toString();
    nextMatchTime = json['nextMatchTime']?.toString();
    prizeName = json['prizeName']?.toString();
    registrationDeadline = json['registrationDeadline']?.toString();
    registrationFee = json['registrationFee']?.toInt();
    startTime = json['startTime']?.toString();
    status = json['status']?.toInt();
    if (json['teamInfo'] != null) {
      final v = json['teamInfo'];
      final arr0 = <CompetitionDetailModelTeamInfo>[];
      v.forEach((v) {
        arr0.add(CompetitionDetailModelTeamInfo.fromJson(v));
      });
      teamInfo = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (PrizeImages != null) {
      final v = PrizeImages;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['PrizeImages'] = arr0;
    }
    data['arenLongitude'] = arenLongitude;
    data['arenaAddress'] = arenaAddress;
    if (arenaInfoList != null) {
      final v = arenaInfoList;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['arenaInfoList'] = arr0;
    }
    data['arenaLatitude'] = arenaLatitude;
    data['arenaName'] = arenaName;
    data['competitionId'] = competitionId;
    if (competitionImages != null) {
      final v = competitionImages;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['competitionImages'] = arr0;
    }
    data['competitionName'] = competitionName;
    data['contactPhone'] = contactPhone;
    data['endTime'] = endTime;
    data['nextMatchTime'] = nextMatchTime;
    data['prizeName'] = prizeName;
    data['registrationDeadline'] = registrationDeadline;
    data['registrationFee'] = registrationFee;
    data['startTime'] = startTime;
    data['status'] = status;
    if (teamInfo != null) {
      final v = teamInfo;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['teamInfo'] = arr0;
    }
    return data;
  }
}
