///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class RegistrationModel {
/*
{
  "competitionName": "string",
  "contactPhone": "string",
  "registrationTime": "string",
  "teamLeaderName": "string",
  "teamName": "string"
} 
*/

  String? competitionName;
  String? contactPhone;
  String? registrationTime;
  String? teamLeaderName;
  String? teamName;

  RegistrationModel({
    this.competitionName,
    this.contactPhone,
    this.registrationTime,
    this.teamLeaderName,
    this.teamName,
  });
  RegistrationModel.fromJson(Map<String, dynamic> json) {
    competitionName = json['competitionName']?.toString();
    contactPhone = json['contactPhone']?.toString();
    registrationTime = json['registrationTime']?.toString();
    teamLeaderName = json['teamLeaderName']?.toString();
    teamName = json['teamName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['competitionName'] = competitionName;
    data['contactPhone'] = contactPhone;
    data['registrationTime'] = registrationTime;
    data['teamLeaderName'] = teamLeaderName;
    data['teamName'] = teamName;
    return data;
  }
}
