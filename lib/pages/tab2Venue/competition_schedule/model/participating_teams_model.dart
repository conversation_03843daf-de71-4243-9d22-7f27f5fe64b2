///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ParticipatingTeamsModel {
/*
{
  "rankScore": 0,
  "teamId": 0,
  "teamLogo": "string",
  "teamName": "string"
} 
*/

  int? rankScore;
  int? teamId;
  String? teamLogo;
  String? teamName;
  String? imagePath;

  ParticipatingTeamsModel(
      {this.rankScore,
      this.teamId,
      this.teamLogo,
      this.teamName,
      this.imagePath});
  ParticipatingTeamsModel.fromJson(Map<String, dynamic> json) {
    rankScore = json['rankScore']?.toInt();
    teamId = json['teamId']?.toInt();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    final rankScoreInt = rankScore ?? 0;
    imagePath = rankScoreInt >= 3000
        ? 'assets/images/five_stars_icon.png'
        : rankScoreInt >= 2000
            ? 'assets/images/four_stars_icon.png'
            : rankScoreInt >= 1500
                ? 'assets/images/three_stars_icon.png'
                : rankScoreInt >= 1200
                    ? 'assets/images/two_stars_icon.png'
                    : 'assets/images/one_stars_icon.png';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['rankScore'] = rankScore;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    data['imagePath'] = imagePath;
    return data;
  }
}
