import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/competition_detail_logic.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/basic_info_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/competition_list.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/schedule_info_page.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///赛事赛程主页
class CompetitionDetailPage extends StatelessWidget {
  CompetitionDetailPage({super.key});

  final logic = Get.put(CompetitionDetailLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: _createDetailWidget(context));
  }

  _createDetailWidget(BuildContext context) {
    return Column(
      children: [
        _topBarWidget(context),
        // TabBarView 内容区域
        Expanded(
          child: TabBarView(
            controller: logic.tabController,
            children: [
              _createBasicInfoWidget(context),
              CompetitionList(
                competitionId: logic.competitionId,
              ),
              ScheduleInfoPage(competitionId: logic.competitionId)
            ],
          ),
        ),
      ],
    );
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Column(
        children: [
          // 顶部导航栏
          Container(
            height: 50.w,
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 60.w,
                  padding: EdgeInsets.only(left: 8.w, right: 0.w, top: 6.w),
                  child: IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      AppPage.back();
                    },
                  ),
                ),
                // TabBar
                Flexible(
                  child: TabBar(
                    controller: logic.tabController,
                    indicator: const UnderlineTabIndicator(
                      borderSide: BorderSide.none,
                      insets: EdgeInsets.zero,
                    ),
                    labelColor: Colors.transparent,
                    unselectedLabelColor: Colors.transparent,
                    labelStyle: TextStyles.titleSemiBold16,
                    dividerColor: Colors.transparent,
                    dividerHeight: 0,
                    unselectedLabelStyle:
                        TextStyles.regular.copyWith(fontSize: 14.sp),
                    overlayColor: WidgetStateProperty.all(Colors.transparent),
                    isScrollable: true,
                    tabAlignment: TabAlignment.center,
                    tabs: [
                      ...logic.tabList.map(
                        (e) => Tab(
                          child: Container(
                            constraints: BoxConstraints(maxWidth: 70.w),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(height: 12.w),
                                Obx(() => logic.currentTabIndex.value == e['id']
                                    ? ShaderMask(
                                        shaderCallback: (bounds) =>
                                            const LinearGradient(
                                          colors: [
                                            Colours.colorFFF9DC,
                                            Colours.colorE4C8FF,
                                            Colours.colorE5F3FF,
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ).createShader(bounds),
                                        child: Text(
                                          (e['title'] ?? '').toString(),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          textAlign: TextAlign.center,
                                          style: TextStyles.titleSemiBold16
                                              .copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      )
                                    : Text(
                                        (e['title'] ?? '').toString(),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center,
                                        style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: Colours.color5C5C6E,
                                        ),
                                      )),
                                SizedBox(
                                  height: 4.w,
                                ),
                                Obx(() => logic.currentTabIndex.value == e['id']
                                    ? WxAssets.images.imgCheckIn2.image()
                                    : SizedBox(height: 4.w)),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 20.w,
                  padding: EdgeInsets.only(left: 0.w, right: 8.w),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 详情数据
  Widget _createBasicInfoWidget(BuildContext context) {
    return Obx(() {
      if (logic.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(
            color: Colours.color7732ED,
          ),
        );
      }

      // 检查数据是否有效
      if (logic.competitionModel.value.competitionId == null) {
        return Center(
          child: myNoDataView(context,
              msg: '暂无赛事数据',
              imagewidget: WxAssets.images.battleEmptyIcon.image()),
        );
      }
      return BasicInfoPage(competitionModel: logic.competitionModel.value);
    });
  }
}
