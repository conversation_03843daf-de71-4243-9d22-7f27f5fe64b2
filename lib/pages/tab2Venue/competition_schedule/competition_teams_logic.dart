import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/participating_teams_model.dart';

class CompetitionTeamsLogic extends GetxController {
  var competitionId = 0;
  //数据列表
  var dataTeamList = <ParticipatingTeamsModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('competitionId')) {
      competitionId = Get.arguments['competitionId'];
    }
    getdataList();
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList() async {
    WxLoading.show();
    var res = await Api()
        .get(ApiUrl.competitionTeams(competitionId), queryParameters: {});
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      dataTeamList.value =
          list.map((e) => ParticipatingTeamsModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
