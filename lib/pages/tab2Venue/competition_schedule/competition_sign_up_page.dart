import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/competition_sign_up_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

///赛事赛程主页
class CompetitionSignUpPage extends StatelessWidget {
  CompetitionSignUpPage({super.key});

  final logic = Get.put(CompetitionSignUpLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MyAppBar(title: const Text('报名'), actions: [
          Container(
            height: 30.w,
            margin: EdgeInsets.only(left: 0.w, right: 15.w),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [Colours.color7C4CCE, Colours.color191523],
              ),
              borderRadius: BorderRadius.all(Radius.circular(32.r)),
            ),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              margin: EdgeInsets.all(1.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(31.r)),
              ),
              child: InkWell(
                onTap: () => AppPage.to(Routes.myRegistrationPage),
                child: Row(
                  children: [
                    WxAssets.images.signUpIcon.image(),
                    SizedBox(
                      width: 3.w,
                    ),
                    Text(
                      '我的报名',
                      style: TextStyles.display12.copyWith(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ]),
        body: _createDetailWidget(context));
  }

  _createDetailWidget(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.all(15.w),
          padding: EdgeInsets.only(left: 15.w, right: 15.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.all(Radius.circular(8.r)),
          ),
          child: Column(
            children: [
              _cellInfo('球队名称',
                  controller: logic.teamController,
                  hintText: '请选择球队',
                  controllerReadOnly: true,
                  context: context),
              _cellInfo('球队队长',
                  controller: logic.leaderController,
                  hintText: '请输入球队队长',
                  controllerReadOnly: true,
                  showRightIcon: false,
                  context: context),
              _cellInfo('联系电话',
                  controller: logic.phoneController,
                  hintText: '请输入手机号',
                  controllerReadOnly: false,
                  showLine: false,
                  context: context)
            ],
          ),
        ),
        InkWell(
          onTap: () {
            logic.competitionSignUp();
          },
          child: Container(
            width: double.infinity,
            height: 50.w,
            alignment: Alignment.center,
            margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 22.w),
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              '确定报名',
              style: TextStyles.semiBold14,
            ),
          ),
        ),
        RichText(
          text: TextSpan(
            style: TextStyles.regular.copyWith(
              color: Colours.color9393A5,
              fontSize: 14.sp,
            ),
            children: [
              const TextSpan(text: '还没有球队？点击'),
              TextSpan(
                text: '创建球队',
                style: TextStyles.regular.copyWith(
                  color: Colours.color922BFF,
                  fontSize: 14.sp,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    AppPage.to(Routes.addTeamPage, arguments: {"teamId": ""});
                  },
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _cellInfo(String leftTitle,
      {bool showLine = true,
      TextEditingController? controller,
      String hintText = '请选择',
      bool controllerReadOnly = true,
      bool showRightIcon = true,
      BuildContext? context}) {
    return Container(
      decoration: BoxDecoration(
          border: showLine
              ? Border(
                  bottom: BorderSide(color: Colours.color99292937, width: 1.w))
              : null),
      height: 54,
      child: Row(
        children: [
          Text(
            leftTitle,
            style: TextStyles.regular,
          ),
          SizedBox(
            width: 30.w,
          ),
          Expanded(
              child: InkWell(
            onTap: controllerReadOnly
                ? () async {
                    if (leftTitle == '球队名称') {
                      AppPage.to(Routes.teamListPage,
                          arguments: {"selectTeam": true}).then((v) {
                        if (v != null) {
                          logic.teamController.text = v.name ?? "";
                          logic.leadUserId = v.leaderUserId ?? 0;
                          logic.teamId = v.id;
                          logic.leaderController.text = v.leaderUserName ?? "";
                        }
                      });
                    }
                  }
                : null,
            child: AbsorbPointer(
              absorbing: controllerReadOnly,
              child: TextField(
                controller: controller,
                readOnly: controllerReadOnly,
                style: TextStyles.regular,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle:
                      TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                  //让文字垂直居中,
                  border: InputBorder.none,
                ),
              ),
            ),
          )),
          if (controllerReadOnly && controller != null && showRightIcon)
            Icon(
              Icons.arrow_forward_ios,
              size: 14.w,
              color: Colours.color5C5C6E,
            )
        ],
      ),
    );
  }
}
