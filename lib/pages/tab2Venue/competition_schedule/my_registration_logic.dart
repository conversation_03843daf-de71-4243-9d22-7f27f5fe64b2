import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/registration_model.dart';

class MyRegistrationLogic extends GetxController {
  var registrationsList = <RegistrationModel>[].obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;
  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await intentionList(false);
  }

  bool hasMore() {
    return registrationsList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await intentionList(true);
    // init.value = true;
  }

  //意向记录列表
  Future<void> intentionList(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> request = {'pageSize': pageSize, 'pageIndex': page};
    var res = await Api().get(ApiUrl.myRegistrations, queryParameters: request);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      cc.log("result${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => RegistrationModel.fromJson(e))
          .toList();
      totalRows = res.data["totalCount"];
      if (isRefresh) {
        registrationsList.value = list;
      } else {
        registrationsList.addAll(list);
      }
    } else {
      if (isRefresh) {
        registrationsList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
