import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'tab_venue_state.dart';

class TabVenueLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  final TabVenueState state = TabVenueState();

  var tabNameList = [
    S.current.stadium_list,
    S.current.site_list,
  ];
  TabController? tabController;
  var tabbarIndex = 0.obs;
  @override
  void onInit() async {
    super.onInit();
    tabController = TabController(length: tabNameList.length, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
  }

  @override
  void onClose() {
    super.onClose();
  }
}
