import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab/logic.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_controller.dart';
import 'package:shoot_z/widgets/view.dart';

class CreateArenaLogic extends GetxController with WidgetsBindingObserver {
  List siteType = [
    {'title': '室内', 'id': '0'},
    {'title': '室外', 'id': '1'}
  ];
  var siteTypeSelectIndex = (-1).obs;
  List siteCategory = [
    {'title': '木地板', 'id': '0'},
    {'title': '塑胶', 'id': '1'},
    {'title': '悬浮拼接地板', 'id': '2'},
    {'title': '水泥地', 'id': '3'}
  ];
  var siteCategorySelectIndex = (-1).obs;
  List openTime = [
    {'title': '不对外开放', 'id': '0'},
    {'title': '全天开放', 'id': '1'},
    {'title': '白天开放', 'id': '2'},
    {'title': '晚上开放', 'id': '3'}
  ];
  var openTimeSelectIndex = (-1).obs;
  List lightType = [
    {'title': '有灯光', 'id': '0'},
    {'title': '无灯光', 'id': '1'}
  ];
  var lightTypeSelectIndex = (-1).obs;
  List isFree = [
    {'title': '免费', 'id': '0'},
    {'title': '收费', 'id': '1'}
  ];
  var isFreeSelectIndex = (-1).obs;
  var halfCourtPics = [
    {'name': '1-1', 'images': <String>[].obs}
  ].obs;
  final RxList<String> logoPics = <String>[].obs;
  final RxList<String> envPics = <String>[].obs;
  TextEditingController siteNameController = TextEditingController(); //地点
  var siteNameStr = ''.obs;
  var addressName = "".obs;
  bool fromPoint = false;
  bool fromAICreation = false;
  double latitude = 0;
  double longitude = 0;
  // 表单验证状态
  RxBool isFormValid = false.obs;
  var venueId = 0;
  var venueModel = VenueModel().obs;
  @override
  void onInit() {
    super.onInit();
    initFormListeners();
    log('!!!!!!!!!${Get.arguments}');
    if (Get.arguments != null) {
      if (Get.arguments.containsKey('from')) {
        if (Get.arguments["from"] == "points") {
          fromPoint = true;
        }
        if (Get.arguments["from"] == "AICreation") {
          fromAICreation = true;
        }
      } else if (Get.arguments.containsKey('venueId')) {
        venueId = Get.arguments["venueId"];
        getVenueDetail();
      }
    }
  }

  @override
  void onClose() {
    siteNameController.removeListener(_textChangeListener);
    siteNameController.dispose();
    // 清理所有半场相关的ImageUploadWidget控制器
    cleanupAllHalfCourtControllers();
    super.onClose();
  }

  void _textChangeListener() {
    siteNameStr.value = siteNameController.text;
  }

// 监听所有必填字段的变化
  void initFormListeners() {
    siteNameController.addListener(_textChangeListener);
    // 使用 ever 监听 halfCourtPics 的变化
    everAll([
      siteTypeSelectIndex,
      siteCategorySelectIndex,
      openTimeSelectIndex,
      lightTypeSelectIndex,
      isFreeSelectIndex,
      logoPics,
      envPics,
      halfCourtPics,
      siteNameStr,
      addressName
    ], (_) {
      _validateForm();
    });
  }

  // 查找pics是否有空
  bool hasEmptyPics() {
    return halfCourtPics.any((item) => (item['images'] as List).isEmpty);
  }

  // 验证表单
  void _validateForm() {
    final bool isValid = (siteTypeSelectIndex.value != -1 &&
        siteCategorySelectIndex.value != -1 &&
        openTimeSelectIndex.value != -1 &&
        lightTypeSelectIndex.value != -1 &&
        isFreeSelectIndex.value != -1 &&
        siteNameStr.isNotEmpty &&
        addressName.isNotEmpty &&
        logoPics.isNotEmpty &&
        envPics.isNotEmpty &&
        !hasEmptyPics());

    isFormValid.value = isValid;
  }

  // 更新半场图片的专用方法
  void updateHalfCourtPics(String title, List<String> images) {
    // 找到对应的半场并更新图片
    for (var halfCourt in halfCourtPics) {
      if (halfCourt['name'] == title) {
        RxList<String> pics = halfCourt['images'] as RxList<String>;
        pics.value = images;
        break;
      }
    }
    // 手动触发验证
    _validateForm();
    // 触发 RxList 更新
    halfCourtPics.refresh();
  }

  /// 清理所有半场相关的ImageUploadWidget控制器
  void cleanupAllHalfCourtControllers() {
    for (int i = 0; i < halfCourtPics.length; i++) {
      var serialNum1 = i ~/ 2 + 1;
      var serialNum2 = i % 2 == 0 ? 1 : 2;
      String tag = 'half_court_$serialNum1-$serialNum2';
      if (Get.isRegistered<ImageUploadController>(tag: tag)) {
        Get.delete<ImageUploadController>(tag: tag);
      }
    }
  }

  Future<void> getVenueDetail() async {
    WxLoading.show();
    final res =
        await Api().get(ApiUrl.venueDetail, queryParameters: {'id': venueId});
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      log('getVenueDetail${res.data}');
      venueModel.value = VenueModel.fromJson(res.data);
      siteNameController.text = venueModel.value.name ?? '';
      addressName.value = venueModel.value.address ?? '';
      latitude = venueModel.value.latitude ?? 0;
      longitude = venueModel.value.longitude ?? 0;
      siteTypeSelectIndex.value = (venueModel.value.type ?? 0) - 1;
      siteCategorySelectIndex.value = (venueModel.value.floorMaterial ?? 0) - 1;
      openTimeSelectIndex.value = (venueModel.value.openTime ?? 0) - 1;
      lightTypeSelectIndex.value = (venueModel.value.hasLight ?? 0) - 1;
      isFreeSelectIndex.value = (venueModel.value.isFree ?? 0) - 1;
      logoPics.value =
          venueModel.value.coverUrl == null ? [] : [venueModel.value.coverUrl!];
      envPics.value = (venueModel.value.environmentImages ?? [])
          .where((image) => image != null)
          .cast<String>()
          .toList();
      cleanupAllHalfCourtControllers();
      halfCourtPics.value =
          (venueModel.value.halves ?? []).asMap().entries.map((entry) {
        int index = entry.key;

        VenueModelHalves? e = entry.value;
        var serialNum1 = index ~/ 2 + 1;
        var serialNum2 = index % 2 == 0 ? 1 : 2;
        return {
          'name': '$serialNum1-$serialNum2',
          'images': (e?.images ?? [])
              .where((image) => image != null)
              .cast<String>()
              .toList()
              .obs
        };
      }).toList();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<bool> checkCreateVenue(double latitude, double longitude) async {
    WxLoading.show();
    final res = await Api().get(ApiUrl.createVenueCheck,
        queryParameters: {'latitude': latitude, 'longitude': longitude});
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      log("message!!!!!${res.data}$latitude$longitude");
      if (res.data["canCreate"]) {
        return res.data["canCreate"];
      }
    } else {
      WxLoading.showToast(res.message);
    }
    return false;
  }

  Future<void> createSite() async {
    submitDialog();
    return;
    if (!isFormValid.value) {
      WxLoading.showToast('请填写完整信息');
      return;
    }
    WxLoading.show();
    var map = {
      'address': addressName.value,
      'coverUrl': logoPics[0],
      'environmentImages': envPics,
      'floorMaterial': siteCategorySelectIndex.value + 1,
      'hasLight': lightTypeSelectIndex.value + 1,
      'isFree': isFreeSelectIndex.value + 1,
      'latitude': latitude,
      'longitude': longitude,
      'name': siteNameStr.value,
      'openTime': openTimeSelectIndex.value + 1,
      'type': siteTypeSelectIndex.value + 1,
      'halves': halfCourtPics
    };
    var url = '';
    if (venueId != 0) {
      url = ApiUrl.editVenue;
      map['id'] = venueId;
    } else {
      url = ApiUrl.createVenue;
    }
    log('$map');
    final res = await Api().post(url, data: map);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      submitDialog();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void submitDialog() {
    getMyDialog(
      '提示',
      '前往场地列表',
      content: '场地信息已提交审核，请耐心等待。审核通过后，奖励将自动发放到账',
      () {
        AppPage.back();
        if (fromAICreation) {
          AppPage.to(Routes.courtFootageHomePage, closePreviousPage: true);
          return;
        }
        AppPage.returnRoot();
        if (venueId != 0) {
          //重新编辑
          final tabVenueLogic = Get.find<TabVenueLogic>();
          tabVenueLogic.tabController?.animateTo(1);
        }
        if (fromPoint) {
          final tabVenueLogic = Get.find<TabVenueLogic>();
          final tabLogic = Get.find<TabLogic>();
          tabLogic.barOnTap(1);
          tabVenueLogic.tabController?.animateTo(1);
        }
      },
      isShowClose: false,
      btnIsHorizontal: true,
      btnText2: '知道了',
      onPressed2: () {
        AppPage.back();
        AppPage.back(result: true);
      },
    );
  }
}
