import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/my_arena/create_arena/create_arena_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/select_location/select_location_logic.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class CreateArenaPage extends StatelessWidget {
  CreateArenaPage({super.key});

  final logic = Get.put(CreateArenaLogic());
  final bool showBuy = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(logic.venueId != 0 ? '重新编辑' : '创建场地'),
      ),
      body: _createBattleWidget(context),
      bottomNavigationBar: Obx(() {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                logic.createSite();
              },
              child: Container(
                width: double.infinity,
                height: 50.w,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  left: 15.w,
                  right: 15.w,
                ),
                decoration: BoxDecoration(
                    color: logic.isFormValid.value ? null : Colours.color5C5C6E,
                    gradient: logic.isFormValid.value
                        ? const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    borderRadius: BorderRadius.circular(25.r)),
                child: Text(
                  '确认创建',
                  style: logic.isFormValid.value
                      ? TextStyles.semiBold14
                      : TextStyles.semiBold14
                          .copyWith(color: Colours.colorA8A8BC),
                ),
              ),
            ),
            SizedBox(
              height: 20.w,
            ),
            SafeArea(
              bottom: true,
              child: Text(
                '创建后需等待系统审核，审核通过后方可生效',
                style: TextStyles.display12,
              ),
            ),
          ],
        );
      }),
    );
  }

  /// 列表数据
  _createBattleWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (logic.venueModel.value.id != null)
              Container(
                height: 40.w,
                width: double.infinity,
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(bottom: 22.w),
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                child: Text(
                  '驳回原因：${logic.venueModel.value.remark ?? '无'}',
                  style: TextStyles.semiBold14
                      .copyWith(color: Colours.colorFF3F3F),
                ),
              ),
            const TextWithIcon(title: '场地位置'),
            SizedBox(
              height: 15.w,
            ),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(15.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '选择场地在地图大致位置',
                    style: TextStyles.semiBold14,
                  ),
                  SizedBox(
                    height: 15.w,
                  ),
                  InkWell(
                    onTap: () {
                      AppPage.to(Routes.selectLocationPage).then((onValue) {
                        if (onValue != null) {
                          Place place = onValue;
                          log("!!!!!!place${place}");
                          logic.addressName.value = place.name;
                          logic.latitude = place.location.latitude;
                          logic.longitude = place.location.longitude;
                        }
                      });
                    },
                    child: Container(
                      width: double.infinity,
                      height: 50.w,
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      decoration: BoxDecoration(
                        color: Colours.color0F0F16,
                        borderRadius: BorderRadius.all(Radius.circular(25.r)),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            logic.addressName.isEmpty
                                ? '选择定位'
                                : logic.addressName.value,
                            style: logic.addressName.isEmpty
                                ? TextStyles.display14
                                    .copyWith(color: Colours.color5C5C6E)
                                : TextStyles.semiBold14,
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 14.sp,
                            color: Colours.white,
                          )
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 20.w,
            ),
            const TextWithIcon(title: '场地信息'),
            SizedBox(
              height: 15.w,
            ),
            Container(
                width: double.infinity,
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '场地名称',
                          style: TextStyles.semiBold14,
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        Text(
                          '请结合场地定位命名，如:中电1期篮球场',
                          style: TextStyles.display12
                              .copyWith(color: Colours.colorA8A8BC),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Container(
                      width: double.infinity,
                      height: 50.w,
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      decoration: BoxDecoration(
                        color: Colours.color0F0F16,
                        borderRadius: BorderRadius.all(Radius.circular(25.r)),
                      ),
                      child: TextField(
                        controller: logic.siteNameController,
                        style: TextStyles.semiBold14,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(15),
                        ],
                        decoration: InputDecoration(
                          hintText: '请输入场地名称',
                          hintStyle: TextStyles.regular
                              .copyWith(color: Colours.color5C5C6E),
                          contentPadding:
                              const EdgeInsets.only(top: 0, bottom: 0),
                          //让文字垂直居中,
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    Text(
                      '场地封面',
                      style: TextStyles.semiBold14,
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    ImageUploadWidget(
                      selectedImagesUrl: logic.logoPics,
                      maxImages: 1,
                      tag: 'logo_upload',
                      onImagesChanged: _handleLogoImagesChanged,
                    ),
                    // WxAssets.images.logoSelectIcon.image(),
                    SizedBox(
                      height: 20.w,
                    ),
                    Text(
                      '场地类型',
                      style: TextStyles.semiBold14,
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Wrap(
                      spacing: 40.w,
                      runSpacing: 20.w,
                      children: List.generate(logic.siteType.length, (index) {
                        return TextButton(
                          onPressed: () {
                            logic.siteTypeSelectIndex.value = index;
                          },
                          style: TextButton.styleFrom(
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            padding: EdgeInsets.zero,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              logic.siteTypeSelectIndex.value == index
                                  ? WxAssets.images.selectIcon.image()
                                  : WxAssets.images.unselectIcon.image(),
                              SizedBox(width: 6.w),
                              Text(logic.siteType[index]['title'],
                                  style: TextStyles.display14),
                            ],
                          ),
                        );
                      }),
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    Row(
                      children: [
                        Text(
                          '场地半场',
                          style: TextStyles.semiBold14,
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        Text(
                          '请根据场地实际半场位置上传对应的图片',
                          style: TextStyles.display12
                              .copyWith(color: Colours.colorA8A8BC),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    WxButton(
                      backgroundColor: Colours.color0F0F16,
                      borderRadius: BorderRadius.circular(20.w),
                      borderSide: BorderSide(color: Colours.white, width: 1.w),
                      height: 40.w,
                      text: '添加场地',
                      textStyle: TextStyles.semiBold14,
                      onPressed: () {
                        if (logic.halfCourtPics.length < 20) {
                          logic.halfCourtPics
                              .add({'name': '', 'images': <String>[].obs});
                          logic.halfCourtPics.refresh();
                          return;
                        }
                        WxLoading.showToast('最多只能创建20个半场');
                      },
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    Column(
                      children:
                          List.generate(logic.halfCourtPics.length, (index) {
                        var serialNum1 = (index) ~/ 2 + 1;
                        var serialNum2 = (index) % 2 == 0 ? 1 : 2;
                        var halfCourtDic = logic.halfCourtPics[index];
                        // var lastHalfCourtDic =
                        //     logic.halfCourtPics[index == 0 ? 0 : index - 1];
                        // final lastSerialNum1 = int.parse(
                        //     lastHalfCourtDic['title'].toString().split('-')[0]);
                        // final lastSerialNum2 = int.parse(
                        //     lastHalfCourtDic['title'].toString().split('-')[1]);
                        // var serialNum1 = lastSerialNum2 == 2
                        //     ? lastSerialNum1 + 1
                        //     : lastSerialNum1;
                        // var serialNum2 = lastSerialNum2 == 2 ? 1 : 2;
                        if (halfCourtDic['name'] == '') {
                          halfCourtDic['name'] = "$serialNum1-$serialNum2";
                        }
                        return Column(
                          children: [
                            Row(
                              children: [
                                Text(
                                  '半场$serialNum1-$serialNum2展示图',
                                  style: TextStyles.semiBold14,
                                ),
                                SizedBox(
                                  width: 5.w,
                                ),
                                Text(
                                  '最多上传3张',
                                  style: TextStyles.display12
                                      .copyWith(color: Colours.colorA8A8BC),
                                ),
                                if (index != 0)
                                  InkWell(
                                      onTap: () {
                                        getMyDialog(
                                          '是否删除',
                                          '确定',
                                          content: '是否确认删除当前半场？',
                                          () {
                                            AppPage.back();
                                            // 清理所有半场相关的ImageUploadWidget控制器
                                            _cleanupHalfCourtControllers();
                                            // 删除半场数据
                                            logic.halfCourtPics.removeAt(index);
                                          },
                                          isShowClose: false,
                                          btnIsHorizontal: true,
                                          btnText2: S.current.cancel,
                                          onPressed2: () {
                                            AppPage.back();
                                          },
                                        );
                                      },
                                      child: WxAssets.images.icDelete
                                          .image(
                                              color: Colors.red,
                                              width: 16.w,
                                              height: 16.w)
                                          .marginOnly(left: 10.w))
                              ],
                            ),
                            SizedBox(
                              height: 15.w,
                            ),
                            ImageUploadWidget(
                              selectedImagesUrl:
                                  halfCourtDic['images'] as RxList<String>,
                              maxImages: 3,
                              tag: 'half_court_$serialNum1-$serialNum2',
                              onImagesChanged: (List<String> images) {
                                log('!!!!!!!!!!!halfCourtDic images changed: $images');
                                logic.updateHalfCourtPics(
                                    halfCourtDic['name'].toString(), images);
                              },
                            ),
                            if (index != logic.halfCourtPics.length - 1)
                              SizedBox(
                                height: 15.w,
                              ),
                          ],
                        );
                      }),
                    ),
                  ],
                )),
            SizedBox(
              height: 20.w,
            ),
            const TextWithIcon(title: '场地其他信息'),
            SizedBox(
              height: 15.w,
            ),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(15.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '场地类型',
                    style: TextStyles.semiBold14,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Wrap(
                    spacing: 40.w,
                    runSpacing: 20.w,
                    children: List.generate(logic.siteCategory.length, (index) {
                      return TextButton(
                        onPressed: () {
                          logic.siteCategorySelectIndex.value = index;
                        },
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          padding: EdgeInsets.zero,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            logic.siteCategorySelectIndex.value == index
                                ? WxAssets.images.selectIcon.image()
                                : WxAssets.images.unselectIcon.image(),
                            SizedBox(width: 6.w),
                            Text(logic.siteCategory[index]['title'],
                                style: TextStyles.display14),
                          ],
                        ),
                      );
                    }),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  const Divider(
                    color: Colours.color1AFFFFFF,
                    height: 0,
                    thickness: 0.5,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Text(
                    '开放时间',
                    style: TextStyles.semiBold14,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Wrap(
                    spacing: 30.w,
                    runSpacing: 20.w,
                    children: List.generate(logic.openTime.length, (index) {
                      return TextButton(
                        onPressed: () {
                          logic.openTimeSelectIndex.value = index;
                        },
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          padding: EdgeInsets.zero,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            logic.openTimeSelectIndex.value == index
                                ? WxAssets.images.selectIcon.image()
                                : WxAssets.images.unselectIcon.image(),
                            SizedBox(width: 6.w),
                            Text(logic.openTime[index]['title'],
                                style: TextStyles.display14),
                          ],
                        ),
                      );
                    }),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  const Divider(
                    color: Colours.color1AFFFFFF,
                    height: 0,
                    thickness: 0.5,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Text(
                    '有无灯光',
                    style: TextStyles.semiBold14,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Wrap(
                    spacing: 40.w,
                    runSpacing: 20.w,
                    children: List.generate(logic.lightType.length, (index) {
                      return TextButton(
                        onPressed: () {
                          logic.lightTypeSelectIndex.value = index;
                        },
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          padding: EdgeInsets.zero,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            logic.lightTypeSelectIndex.value == index
                                ? WxAssets.images.selectIcon.image()
                                : WxAssets.images.unselectIcon.image(),
                            SizedBox(width: 6.w),
                            Text(logic.lightType[index]['title'],
                                style: TextStyles.display14),
                          ],
                        ),
                      );
                    }),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  const Divider(
                    color: Colours.color1AFFFFFF,
                    height: 0,
                    thickness: 0.5,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Text(
                    '是否免费',
                    style: TextStyles.semiBold14,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Wrap(
                    spacing: 40.w,
                    runSpacing: 20.w,
                    children: List.generate(logic.isFree.length, (index) {
                      return TextButton(
                        onPressed: () {
                          logic.isFreeSelectIndex.value = index;
                        },
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          padding: EdgeInsets.zero,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            logic.isFreeSelectIndex.value == index
                                ? WxAssets.images.selectIcon.image()
                                : WxAssets.images.unselectIcon.image(),
                            SizedBox(width: 6.w),
                            Text(logic.isFree[index]['title'],
                                style: TextStyles.display14),
                          ],
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 20.w,
            ),
            const TextWithIcon(title: '场地周边环境'),
            SizedBox(
              height: 15.w,
            ),
            Container(
                width: double.infinity,
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.all(Radius.circular(8.r)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '周边环境',
                          style: TextStyles.semiBold14,
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        Text(
                          '最多上传3张',
                          style: TextStyles.display12
                              .copyWith(color: Colours.colorA8A8BC),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    ImageUploadWidget(
                      selectedImagesUrl: logic.envPics,
                      maxImages: 3,
                      tag: 'gallery_upload',
                      onImagesChanged: _handleGalleryImagesChanged,
                    ),
                  ],
                )),
          ],
        ).marginSymmetric(horizontal: 15.w, vertical: 15);
      }),
    );
  }

  void _handleLogoImagesChanged(List<String> images) {
    logic.logoPics.value = images;
    // 处理logo图片变化
    log('Logo images changed: $images');
  }

  void _handleGalleryImagesChanged(List<String> images) {
    logic.envPics.value = images;
    // 处理相册图片变化
    log('Gallery images changed: $images');
  }

  /// 清理所有半场相关的ImageUploadWidget控制器
  void _cleanupHalfCourtControllers() {
    logic.cleanupAllHalfCourtControllers();
  }
}
