import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';

class MyArenaLogic extends GetxController {
  var init = false.obs;
  var allArenaList = <VenueModel>[].obs;

  /// 是否正在加载数据
  bool _isLoading = false;
  var page = 1;
  var pageSize = 20;
  var totalRows = 0;
  @override
  void onInit() async {
    super.onInit();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    page = 1;
    await getAllArenaList(true);
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await getAllArenaList(false);
  }

  bool hasMore() {
    return allArenaList.length < totalRows;
  }

  Future<void> getAllArenaList(bool isRefresh) async {
    WxLoading.show();
    _isLoading = true;
    log("getAllArenaList!!!!!!!!!!");
    Map<String, dynamic> params = {'limit': pageSize, 'page': page};
    final res = await Api().get(ApiUrl.myVenueList, queryParameters: params);
    // if (state.init.value) {
    //   carouselController.jumpToPage(0);
    // }
    log("message########getAllArenaList${res.data}");
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      final list = (res.data['list'] as List)
          .map((e) => VenueModel.fromJson(e))
          .toList();
      totalRows = res.data["total"];
      if (isRefresh) {
        allArenaList.value = list;
      } else {
        allArenaList.addAll(list);
      }
    } else {
      if (isRefresh) {
        allArenaList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
