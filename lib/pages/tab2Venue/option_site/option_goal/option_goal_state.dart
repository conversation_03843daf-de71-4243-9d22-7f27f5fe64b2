import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/option_goal_model.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/ai_option_model.dart';

class OptionGoalLogicState {
  TextEditingController codeController = TextEditingController();

  //推荐时段
  var dateRecommendedList = [
    "22:00~23:00",
    "21:00~22:00",
    "20:00~21:00",
    "19:00~20:00",
    "18:00~19:00",
    "17:00~18:00",
    "16:00~17:00",
    "15:00~16:00",
    "14:00~15:00",
    "13:00~14:00",
    "12:00~13:00",
    "11:00~12:00",
    "10:00~11:00",
    "9:00~10:00",
    "8:00~9:00",
  ];
  //推荐时段快速选择 4个时间  推荐时段提供当前已选时段的前半小时、前一小时、后半小时、后一小时 区间快捷选择，若已选时段结束时间为生成至时间，则不提供后半小时和一小时的推荐区间
  var dateFristList2 = [];
  //精准查找时间断
  var datePrecisionList = [];
  //打卡图片的位置
  var offset =
      Offset(ScreenUtil().screenWidth - 50.w, ScreenUtil().screenHeight - 300)
          .obs;
  //数据列表
  RxList<OptionGoalModel> dataList = <OptionGoalModel>[].obs;
  //选中数据列表
  RxList<OptionGoalModel> dataCheckList = <OptionGoalModel>[].obs;

  //数据列表 Ai选球
  RxList<AiOptionModel> aiDataList = <AiOptionModel>[].obs;
  //推荐时段选择今天还是昨天
  var todayRecommended = true.obs;
  var indexVideo = 9999.obs; //选择视频下标
  var indexVideoAngle = 0.obs; //视频录制角度

  var videoDate = "".obs;
  var videoStartTime = "".obs;
  var videoEndTime = "".obs;
  var videoNextStartTime = "".obs;
  var videoNextEndTime = "".obs;

  //精确查找预选时间
  var videoDate1 = "".obs;
  var videoStartTime1 = "".obs;
  var videoEndTime1 = "".obs;

  var rememberOption = "1".obs; //记住我的选择

  //会员权益弹窗
  var vipDialogList = [
    {
      "img": "vip_dialog01.png",
      "name": S.current.vip_dialog_text1,
      "select": "0"
    },
    {
      "img": "vip_dialog02.png",
      "name": S.current.vip_dialog_text2,
      "select": "0"
    },
    {
      "img": "vip_dialog03.png",
      "name": S.current.vip_dialog_text3,
      "select": "0"
    },
    {
      "img": "vip_dialog04.png",
      "name": S.current.vip_dialog_text4,
      "select": "0"
    },
    {
      "img": "vip_dialog05.png",
      "name": S.current.vip_dialog_text5,
      "select": "0"
    },
    {
      "img": "vip_dialog06.png",
      "name": S.current.vip_dialog_text6,
      "select": "0"
    },
    {
      "img": "vip_dialog07.png",
      "name": S.current.vip_dialog_text7,
      "select": "0"
    },
    {
      "img": "vip_dialog08.png",
      "name": S.current.vip_dialog_text8,
      "select": "0"
    },
    {
      "img": "vip_dialog09.png",
      "name": S.current.vip_dialog_text9,
      "select": "0"
    }
  ].obs;

  var invitationDialogList = [
    {
      "img": "vip_Invitation_dialog1.png",
      "name": S.current.vip_dialog_text2,
      "select": "0"
    },
    {
      "img": "vip_Invitation_dialog2.png",
      "name": S.current.vip_dialog_text2,
      "select": "0"
    },
    {
      "img": "vip_Invitation_dialog3.png",
      "name": S.current.vip_dialog_text3,
      "select": "0"
    },
    {
      "img": "vip_Invitation_dialog4.png",
      "name": S.current.vip_dialog_text4,
      "select": "0"
    }
  ].obs;
}
