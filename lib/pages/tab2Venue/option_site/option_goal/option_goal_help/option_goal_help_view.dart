import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_help/option_goal_help_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

///球场主页->三级页面  选择进球->帮助
class OptionGoalHelpPage extends StatelessWidget {
  OptionGoalHelpPage({super.key});
  final logic = Get.find<OptionGoalHelpLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        // appBar:  MyAppBar(
        //   title: Text(S.current.points_rule),
        // ),
        body: Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            children: [
              MyImage(
                "goal_help2.png",
                width: double.infinity,
                height: 248.w,
                isAssetImage: true,
                fit: BoxFit.fitWidth,
                bgColor: Colors.transparent,
              ),
              Transform.translate(
                offset: Offset(0, -25.w),
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      height: 25.w,
                      decoration: BoxDecoration(
                          color: Colours.color000000,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(25.r),
                              topRight: Radius.circular(25.r))),
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(left: 15.w, right: 15.w, top: 10.w),
                      child: MyImage(
                        "goal_help.png",
                        width: double.infinity,
                        height: 1078.w,
                        isAssetImage: true,
                        fit: BoxFit.fitWidth,
                        bgColor: Colors.transparent,
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      padding:
                          EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 1.w),
                            child: WxAssets.images.tips
                                .image(width: 12.w, height: 12.w),
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          Expanded(
                            child: Text(
                              S.current.goal_help_tips,
                              maxLines: 5,
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: Colours.color5C5C6E,
                                fontWeight: AppFontWeight.medium(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 50,
              ),
            ],
          ),
        ),
        Positioned(
          top: 30.w,
          left: 5.w,
          child: IconButton(
            onPressed: () {
              AppPage.back();
            },
            icon: const Icon(
              Icons.arrow_back_ios,
              size: 20,
              color: Colors.white,
            ),
          ),
        )
      ],
    ));
  }
}
