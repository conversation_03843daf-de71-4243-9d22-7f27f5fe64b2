// ignore_for_file: use_build_context_synchronously

import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:marquee/marquee.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/ai_option_model.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_logic.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

///球场主页->三级页面  选择进球
class OptionGoalPage extends StatelessWidget {
  OptionGoalPage({super.key});

  final logic = Get.put(OptionGoalLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.current.Option_goal),
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.optionGoalHelpPage);
            },
            child: Container(
              height: 40.w,
              width: 60.w,
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  WxAssets.images.pointsHelp.image(
                      color: Colours.color9393A5, width: 14.w, height: 14.w),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : KeepAliveWidget(
                child: Stack(
                  children: [
                    SafeArea(
                      bottom: false,
                      child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //视频播放器
                            _videoWidget(),
                            if (logic.showMaintenance.value)
                              Container(
                                width: double.infinity,
                                height: 28.w,
                                margin: EdgeInsets.only(top: 15.w),
                                padding: EdgeInsets.only(left: 20.w),
                                alignment: Alignment.centerLeft,
                                decoration: const BoxDecoration(
                                    gradient: LinearGradient(
                                  colors: [
                                    Color(0x00964AEE),
                                    Color(0x4D964AEE),
                                    Color(0x00964AEE)
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                )),
                                child: Row(
                                  children: [
                                    WxAssets.images.lampIcon.image(),
                                    SizedBox(
                                      width: 10.w,
                                    ),
                                    Expanded(
                                      child: Marquee(
                                        text:
                                            '${logic.maintenanceArenaList.join('，')}场地目前故障，数据可能有缺失',
                                        style: TextStyles.display12
                                            .copyWith(color: Colors.white),
                                        scrollAxis: Axis.horizontal,
                                        velocity: 30.0, // 滚动速度
                                        blankSpace: 20.0, // 文本间的空白
                                        pauseAfterRound: const Duration(
                                            seconds: 1), // 每轮结束暂停时间
                                        startPadding: 10.0, // 起始边距
                                        accelerationDuration:
                                            const Duration(seconds: 1), // 加速时间
                                        decelerationDuration: const Duration(
                                            milliseconds: 500), // 减速时间
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            //选择时间
                            _chooseTime(context),
                            //选球
                            Expanded(child: _optionGoalWidget(context)),
                          ]),
                    ),
                    //if (logic.aIoptionCheckID.value == "")
                    if (logic.type.value == 1)
                      Positioned(
                        top: logic.state.offset.value.dy,
                        left: logic.state.offset.value.dx,
                        child: GestureDetector(
                          onPanUpdate: (detail) {
                            logic.state.offset.value = logic.calOffset(
                                MediaQuery.of(context).size,
                                logic.state.offset.value,
                                detail.delta);
                          },
                          onPanEnd: (detail) {},
                          behavior: HitTestBehavior.translucent,
                          onTap: () async {
                            // AppPage.to(Routes.siteReportPage, arguments: {
                            //   "arenaId": logic.arenaID.value.toString(),
                            //   "classifications":
                            //       "456013,456014,456015,456018", //456013,456014,456015,456018
                            //   "courts": logic.courtId.value,
                            //   "videoEndTime": logic.state.videoEndTime.value,
                            //   "videoStartTime": logic.state.videoStartTime.value
                            // });
                            // return;
                            if (UserManager.instance.isLogin) {
                              // var isVip = await logic
                              //     .getVideosUsedShots(logic.arenaID.value);
                              // if (isVip) {
                              //   //   //ai选球
                              //   var isOk = await logic.getAiOptionGoal(context);
                              //   if (isOk) {
                              //     getAiOptionGoalDialog(context);
                              //   }
                              // }
                              //   //ai选球
                              var isOk = await logic.getAiOptionGoal(context);
                              if (isOk) {
                                getAiOptionGoalDialog(context);
                              }
                            } else {
                              AppPage.to(Routes.login).then((onValue) async {
                                await Future.delayed(
                                    const Duration(milliseconds: 500));
                                if (UserManager.instance.isLogin) {
                                  // var isVip = await logic
                                  //     .getVideosUsedShots(logic.arenaID.value);
                                  // if (isVip) {
                                  //   //   //ai选球
                                  //   var isOk =
                                  //       await logic.getAiOptionGoal(context);
                                  //   if (isOk) {
                                  //     getAiOptionGoalDialog(context);
                                  //   }
                                  // }
                                  //   //ai选球
                                  var isOk =
                                      await logic.getAiOptionGoal(context);
                                  if (isOk) {
                                    getAiOptionGoalDialog(context);
                                  }
                                } else {
                                  WxLoading.showToast(S.current.please_login);
                                }
                              });
                            }
                          },
                          child: WxAssets.images.optionAi
                              .image(width: 50.w, height: 50.w),
                        ),
                      ),
                    // if (logic.aIoptionCheckID.value != "")
                    //   Positioned(
                    //     top: logic.state.offset.value.dy,
                    //     left: logic.aIoptionCheckDx.value <= 65.w
                    //         ? MediaQuery.of(context).size.width -
                    //             35.w -
                    //             logic.aIoptionCheckDx.value
                    //         : MediaQuery.of(context).size.width -
                    //             35.w -
                    //             (130.w - logic.aIoptionCheckDx.value),
                    //     child: GestureDetector(
                    //       onPanUpdate: (detail) {
                    //         logic.state.offset.value = logic.calOffset(
                    //             MediaQuery.of(context).size,
                    //             logic.state.offset.value,
                    //             detail.delta);
                    //       },
                    //       onPanEnd: (detail) {},
                    //       behavior: HitTestBehavior.translucent,
                    //       onTap: () async {
                    //         logic.getDataList(isLoad: false);
                    //       },
                    //       child: Container(
                    //           height: 30.w,
                    //           width: 100.w,
                    //           padding: EdgeInsets.only(left: 10.w, right: 0.w),
                    //           decoration: BoxDecoration(
                    //               color: Colors.white,
                    //               borderRadius: BorderRadius.only(
                    //                   topLeft: Radius.circular(15.r),
                    //                   bottomLeft: Radius.circular(15.r))),
                    //           child: Row(
                    //             children: [
                    //               WxAssets.images.goalAi
                    //                   .image(width: 21.w, height: 11.w),
                    //               SizedBox(
                    //                 width: 6.w,
                    //               ),
                    //               //if((logic.aIoptionCheckDx.value < 60.w))
                    //               WxAssets.images.goalAiText
                    //                   .image(width: 60.w, height: 11.w),
                    //             ],
                    //           )),
                    //     ),
                    //   )
                  ],
                ),
              );
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? const SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    bottom: 25.w, left: 5.w, right: 15.w, top: 10.w),
                child: Row(
                  children: [
                    if (logic.state.indexVideo.value != 9999 &&
                        (logic.state.dataList[logic.state.indexVideo.value]
                                    .cameras?.length ??
                                0) >
                            1)
                      Row(
                        children: List.generate(
                            logic.state.dataList[logic.state.indexVideo.value]
                                    .cameras?.length ??
                                0,
                            (index) => GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    logic.changeVideoAngle(index);
                                  },
                                  child: SizedBox(
                                    width: 44.w,
                                    height: 44.w,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        logic
                                                    .state
                                                    .dataList[logic
                                                        .state.indexVideo.value]
                                                    .cameras?[index]!
                                                    .cameraIndex ==
                                                logic
                                                    .state.indexVideoAngle.value
                                            ? WxAssets.images.optionVideo.image(
                                                width: 20.w, height: 20.w)
                                            : WxAssets.images.optionVideoHui
                                                .image(
                                                    width: 20.w, height: 20.w),
                                        Text(
                                          logic
                                                  .state
                                                  .dataList[logic
                                                      .state.indexVideo.value]
                                                  .cameras?[index]
                                                  ?.cameraName ??
                                              "",
                                          style: TextStyle(
                                            fontSize: 10.sp,
                                            color: Colors.white,
                                            fontWeight: AppFontWeight.medium(),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )),
                      ),
                    if (logic.state.indexVideo.value != 9999 &&
                        (logic.state.dataList[logic.state.indexVideo.value]
                                    .cameras?.length ??
                                0) >
                            1)
                      const Spacer(),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        //素材库
                        if (Utils.isToLogin()) {
                          //素材库
                          AppPage.to(Routes.materialLibraryPage, arguments: {
                            "arenaID": logic.arenaID.value,
                          }).then((v) {
                            logic.getDaoGoalList();
                          });
                        }
                      },
                      child: Stack(
                        children: [
                          // Container(
                          //   width: 44.w,
                          //   height: 44.w,
                          //   decoration: BoxDecoration(
                          //       borderRadius: BorderRadius.circular(16.r),
                          //       color: Colours.color282735),
                          //   child: WxAssets.images.optionDocument
                          //       .image(width: 24.w, height: 24.w),
                          // ),
                          WxAssets.images.optionDocument
                              .image(width: 40.w, height: 40.w),
                          if (logic.state.dataCheckList.isNotEmpty)
                            Positioned(
                              right: 0,
                              child: Transform.translate(
                                offset: const Offset(5, 0), // 移动50像素到右和下
                                child: Container(
                                  padding: EdgeInsets.only(
                                      left: 5.w,
                                      right: 5.w,
                                      top: 1.w,
                                      bottom: 1.w),
                                  decoration: BoxDecoration(
                                    color: Colours.red,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(28.r)),
                                  ),
                                  child: Text(
                                    "${logic.state.dataCheckList.length}",
                                    style: TextStyles.titleMedium18
                                        .copyWith(fontSize: 10.sp),
                                  ),
                                ),
                              ),
                            )
                        ],
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        if (Utils.isToLogin()) {
                          if (await logic.getVideosUsedShots2(
                              logic.arenaID.value,
                              type: 1)) {
                            //没有合成次数和vip的时候弹窗
                            getVIPDialog();
                            // logic.getShareWx(0);
                          } else {
                            //去合成进球
                            AppPage.to(Routes.compositeVideoPage, arguments: {
                              "arenaID": logic.arenaID.value,
                            }).then((v) {
                              logic.getDaoGoalList();
                            });
                          }

                          //没有合成次数和vip的时候弹窗
                          // getVIPDialog();
                          //邀请规则说明
                          // getVIPInvitationDialog2();
                          //邀请成功弹窗公告
                          //getInvitationDialog3();
                          //限时次数即将到期弹窗提醒
                          // getInvitationDialog4();
                        }
                      },
                      child: Container(
                        height: 46.w,
                        width: (logic.state.indexVideo.value != 9999 &&
                                (logic
                                            .state
                                            .dataList[
                                                logic.state.indexVideo.value]
                                            .cameras
                                            ?.length ??
                                        0) >
                                    1)
                            ? 130.w
                            : 295.w,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(left: 14.w, right: 0),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          S.current.desynthesis,
                          style: TextStyles.display16.copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),

      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     // 按钮点击事件处理代码
      //     //logic.getDateInfo();
      //     logic.aIoptionCheckID.value =
      //         logic.aIoptionCheckID.value == "" ? "1" : "";
      //   },
      //   tooltip: 'Increment Counter', // 提示信息（长按显示）
      //   child: Icon(Icons.add), // 按钮内部的图标
      //   backgroundColor: Colors.pink, // 按钮背景颜色
      // ),
      floatingActionButtonLocation:
          FloatingActionButtonLocation.endFloat, // 默认位置
    );
  }

  //没有合成次数和vip的时候弹窗
  void getVIPDialog() {
    return getMyDialog2(
      "",
      S.current.sure,
      () {
        AppPage.back();
      },
      // btnText2: S.current.Go_points,
      // onPressed2: () {
      //   AppPage.back();
      //   AppPage.to(Routes.pointsPage);
      // },
      isShowClose: true,
      imageAsset: "vip_dialog10.png",
      imgHeight: 165.w,
      btnIsHorizontal: true,
      imgWidth: double.infinity,
      bottomBtnWidget: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                AppPage.back();
                AppPage.to(Routes.vipPage).then((v) async {
                  await Future.delayed(const Duration(milliseconds: 1000));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    if (!await logic.getVideosUsedShots2(logic.arenaID.value,
                        type: 1)) {
                      //去合成进球
                      AppPage.to(Routes.compositeVideoPage, arguments: {
                        "arenaID": logic.arenaID.value,
                      }).then((v) {
                        logic.getDaoGoalList();
                      });
                    }
                  }
                });
              },
              child: Container(
                height: 46.w,
                width: double.infinity,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  left: 20.w,
                  top: 15.w,
                  right: 7.5.w,
                ),
                decoration: BoxDecoration(
                  color: Colours.color282735,
                  borderRadius: BorderRadius.all(Radius.circular(28.r)),
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Text(
                  S.current.Open_immediately,
                  style: TextStyles.regular
                      .copyWith(fontSize: 14.sp, color: Colours.white),
                ),
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () async {
              AppPage.back();
              await Future.delayed(const Duration(milliseconds: 500));
              getVIPInvitationDialog2();
            },
            child: Column(
              children: [
                Transform.translate(
                  offset: const Offset(-10, 10),
                  child: MyImage(
                    "vip_dialog11.png",
                    width: 154.w,
                    // height: 30.w,
                    fit: BoxFit.fitWidth,
                    isAssetImage: true,
                  ),
                ),
                Container(
                  height: 46.w,
                  width: 144,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                    left: 7.5.w,
                    right: 20.w,
                  ),
                  decoration: BoxDecoration(
                    color: Colours.color282735,
                    borderRadius: BorderRadius.all(Radius.circular(28.r)),
                    gradient: const LinearGradient(
                      colors: [Colours.colorBFFF9C, Colours.colorEEFC62],
                      begin: Alignment.bottomLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Text(
                    S.current.vip_dialog_text10,
                    style: TextStyles.regular
                        .copyWith(fontSize: 14.sp, color: Colours.color333333),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          margin: EdgeInsets.only(left: 40.w, right: 40.w, top: 10.w),
          child: Wrap(
            spacing: 21.w,
            runSpacing: 10.w,
            children: List.generate(logic.state.vipDialogList.length, (index) {
              return Container(
                width: 54.w,
                alignment: Alignment.center,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 44.w,
                      height: 44.w,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          MyImage(
                            "vip_dialog12.png",
                            width: 44.w,
                            height: 44.w,
                            isAssetImage: true,
                            radius: 11.r,
                          ),
                          MyImage(
                            logic.state.vipDialogList[index]["img"] ?? "",
                            width: 26.w,
                            height: 26.w,
                            isAssetImage: true,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 4.w,
                    ),
                    Text(
                      logic.state.vipDialogList[index]["name"] ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 10.sp, color: Colours.color7732ED),
                    )
                  ],
                ),
              );
            }),
          )),
    );
  }

//规则说明 邀请新人
  void getVIPInvitationDialog2() {
    return getMyDialog2(
      "",
      S.current.sure,
      () {
        AppPage.back();
      },
      isShowClose: false,
      btnIsHorizontal: true,
      imgWidth: double.infinity,
      contentTopRadius: 16.r,
      bottomBtnWidget: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.getShareWx(0);
                },
                child: Column(
                  children: [
                    MyImage(
                      "vip_Invitation_dialog5.png",
                      width: 36.w,
                      height: 36.w,
                      fit: BoxFit.fitWidth,
                      isAssetImage: true,
                    ),
                    SizedBox(
                      height: 6.w,
                    ),
                    Text(
                      S.current.vip_Invitation_dialog_text9,
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp, color: Colours.color333333),
                    )
                  ],
                ),
              ),
              // GestureDetector(
              //   behavior: HitTestBehavior.translucent,
              //   onTap: () {
              //     logic.getShareWx(1);
              //   },
              //   child: Column(
              //     children: [
              //       MyImage(
              //         "vip_Invitation_dialog6.png",
              //         width: 36.w,
              //         height: 36.w,
              //         fit: BoxFit.fitWidth,
              //         isAssetImage: true,
              //       ),
              //       SizedBox(
              //         height: 6.w,
              //       ),
              //       Text(
              //         S.current.vip_Invitation_dialog_text10,
              //         style: TextStyles.regular.copyWith(
              //             fontSize: 13.sp, color: Colours.color333333),
              //       )
              //     ],
              //   ),
              // ),
            ],
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.back();
            },
            child: Container(
              width: 100.w,
              height: 40.w,
              alignment: Alignment.bottomCenter,
              child: Text(
                S.current.cancel,
                style: TextStyles.regular
                    .copyWith(fontSize: 11.sp, color: Colours.colorA44EFF),
              ),
            ),
          )
        ],
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.only(left: 40.w, right: 40.w),
          decoration: BoxDecoration(
            color: Colours.color282735,
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
            gradient: const LinearGradient(
              colors: [Colours.colorD2ABFF, Colours.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            children: [
              SizedBox(
                height: 20.w,
              ),
              MyImage(
                "vip_Invitation_dialog7.png",
                width: 187.w,
                height: 26.w,
                isAssetImage: true,
              ),
              SizedBox(
                height: 20.w,
              ),
              Column(
                children: List.generate(logic.state.invitationDialogList.length,
                    (index) {
                  return Container(
                    width: double.infinity,
                    height: 44.w,
                    alignment: Alignment.center,
                    child: Row(
                      children: [
                        MyImage(
                          logic.state.invitationDialogList[index]["img"] ?? "",
                          width: 13.w,
                          height: 13.w,
                          isAssetImage: true,
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.only(
                                left: 10.w, right: 10.w, top: 6.w, bottom: 4.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8.r),
                                color: Colours.color7732ED),
                            child: Row(
                              children: [
                                RichText(
                                  textAlign: TextAlign.left,
                                  text: TextSpan(
                                      text: index == 0
                                          ? S.current
                                              .vip_Invitation_dialog_text1
                                          : index == 1
                                              ? S.current
                                                  .vip_Invitation_dialog_text3
                                              : index == 2
                                                  ? S.current
                                                      .vip_Invitation_dialog_text5
                                                  : S.current
                                                      .vip_Invitation_dialog_text6,
                                      style: TextStyle(
                                          color: Colours.white,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400),
                                      children: <InlineSpan>[
                                        TextSpan(
                                            text: index == 0
                                                ? S.current
                                                    .vip_Invitation_dialog_text2
                                                : index == 1
                                                    ? S.current
                                                        .vip_Invitation_dialog_text4
                                                    : index == 2
                                                        ? ""
                                                        : S.current
                                                            .vip_Invitation_dialog_text7,
                                            style: TextStyle(
                                                color: Colours.colorCDFDDE,
                                                fontSize: 12.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400)),
                                        TextSpan(
                                            text: index == 3
                                                ? S.current
                                                    .vip_Invitation_dialog_text8
                                                : "",
                                            style: TextStyle(
                                                color: Colours.white,
                                                fontSize: 12.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400)),
                                      ]),
                                ),
                                Expanded(
                                    child: Text(
                                        " -----------------------------",
                                        maxLines: 1,
                                        style: TextStyle(
                                            color: Colours.white,
                                            fontSize: 12.sp,
                                            height: 1,
                                            overflow: TextOverflow.clip,
                                            fontWeight: FontWeight.w400)))
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ],
          )),
    );
  }

  Container _chooseTime(BuildContext context) {
    return Container(
      height: 50.w,
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 5.w, bottom: 5.w, right: 10.w, left: 15.w),
      child: Row(
        children: [
          Text(
            logic.courtName.value,
            style: TextStyle(color: Colours.white, fontSize: 15.sp),
          ),
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                //选择进球的时间
                showDateDialog(context);
              },
              child: Text(
                "${logic.state.videoStartTime.value.trim().contains(logic.todayDate) ? S.current.today : logic.state.videoStartTime.value.trim().contains(logic.yesterdayDate) ? S.current.yesterday : logic.state.videoStartTime.value.trim().length >= 10 ? logic.state.videoStartTime.trim().substring(5, 10) : logic.state.videoDate.value}\t\t\t${logic.state.videoStartTime.value.trim().length >= 16 ? logic.state.videoStartTime.trim().substring(11, 16) : logic.state.videoStartTime}～${logic.state.videoEndTime.value.trim().length >= 16 ? logic.state.videoEndTime.trim().substring(11, 16) : logic.state.videoEndTime}",
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: Colours.white,
                  fontSize: 15.sp,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 3.w,
          ),
          WxAssets.images.icArrowDown.image(height: 10.w, width: 10.w),
          SizedBox(
            width: 30.w,
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              MyShareH5.getShareH5(ShareGoalVideos(
                  fragmentId: logic
                      .state.dataList[logic.state.indexVideo.value].id
                      .toString(),
                  sharedFrom:
                      UserManager.instance.userInfo.value?.userId ?? ""));
            },
            child: SizedBox(
                width: 42.w,
                height: 50.w,
                child: WxAssets.images.optionShare
                    .image(height: 22.w, width: 22.w)),
          ),
          GestureDetector(
            onTap: () {
              logic.getDownLoad();
            },
            child: SizedBox(
                width: 42.w,
                height: 50.w,
                child: WxAssets.images.optionDownload
                    .image(height: 22.w, width: 22.w)),
          ),
        ],
      ),
    );
  }

  Widget _videoWidget() {
    return SizedBox(
      width: double.infinity,
      height: ScreenUtil().screenWidth / 375 * 211,
      child: AspectRatio(
        aspectRatio: 375 / 211, // 宽高比
        child: VideoView(
          controller: logic.videoController,
        ),
      ),
    );
  }

  Widget _optionGoalWidget(BuildContext context) {
    return Obx(() {
      return logic.state.dataList.isEmpty
          ? Column(
              children: [
                Expanded(
                  child: myNoDataView(
                    context,
                    msg: S.current.no_goal,
                    imagewidget: WxAssets.images.noGoal
                        .image(width: 100.w, height: 84.w),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.chooseTime(logic.state.videoNextStartTime.value,
                        logic.state.videoNextEndTime.value, 3);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10.w),
                    margin: EdgeInsets.symmetric(vertical: 40.w),
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                          text: S.current.option_next_time,
                          style: TextStyle(
                              color: Colours.color5D5D6E,
                              fontSize: 13.sp,
                              height: 1,
                              fontWeight: FontWeight.w400),
                          children: <InlineSpan>[
                            TextSpan(
                                text:
                                    "\t\t${logic.state.videoNextStartTime.value.trim().length >= 16 ? logic.state.videoNextStartTime.value.trim().substring(11, 16) : logic.state.videoNextStartTime.value}～${logic.state.videoNextEndTime.value.trim().length >= 16 ? logic.state.videoNextEndTime.value.trim().substring(11, 16) : logic.state.videoNextEndTime.value}",
                                style: TextStyle(
                                    color: Colours.app_main,
                                    fontSize: 13.sp,
                                    height: 1,
                                    fontWeight: FontWeight.w400)),
                          ]),
                    ),
                  ),
                ),
              ],
            )
          : Padding(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    GridView.builder(
                        scrollDirection: Axis.vertical,
                        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        //  const AlwaysScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 15,
                          mainAxisSpacing: 15,
                          childAspectRatio: 101 / 72,
                        ),
                        padding: EdgeInsets.only(bottom: 10.w),
                        itemCount: logic.state.dataList.length,
                        itemBuilder: (context, position) {
                          return Obx(() {
                            return GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () async {
                                if (position == logic.state.indexVideo.value) {
                                  if (await Utils.isToLogin()) {
                                    logic.checkVideo(position);
                                  }
                                } else {
                                  logic.changeVideoIndex(position);
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                      position == logic.state.indexVideo.value
                                          ? Colours.color291A3B
                                          : Colours.color191921,
                                  borderRadius: BorderRadius.circular(18.r),
                                  image: position ==
                                          logic.state.indexVideo.value
                                      ? (logic.state.dataList[position]
                                                  .selected ??
                                              false)
                                          ? const DecorationImage(
                                              image: AssetImage(
                                                  "assets/images/goal_bg2.png"),
                                              fit: BoxFit.fill)
                                          : const DecorationImage(
                                              image: AssetImage(
                                                  "assets/images/goal_bg1.png"),
                                              fit: BoxFit.fill)
                                      : const DecorationImage(
                                          image: AssetImage(
                                              "assets/images/goal_bg.png"),
                                          fit: BoxFit.fill),
                                  // border: position == logic.state.indexVideo.value
                                  //     ? Border.all(
                                  //         width: 1, color: Colours.color7732ED)
                                  //     : null
                                ),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    // WxAssets.images.optionVideoBg.image(
                                    //     height: 76.w,
                                    //     width: 110.w,
                                    //     color: position == logic.indexVideo.value
                                    //         ? Colours.color291A3B
                                    //         : null),
                                    Positioned(
                                      top: 10.w,
                                      right: 10.w,
                                      child: Visibility(
                                        visible:
                                            logic.aIoptionCheckID.value != "",
                                        child: Text(
                                          "AI",
                                          textAlign: TextAlign.center,
                                          style: TextStyles.medium.copyWith(
                                              fontSize: 12.sp,
                                              color: Colours.color767681),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      top: 10.w,
                                      child: Visibility(
                                        visible: position ==
                                            logic.state.indexVideo.value,
                                        child: (logic.state.dataList[position]
                                                    .selected ??
                                                false)
                                            ? WxAssets.images.goalRemove.image(
                                                height: 32.w, width: 32.w)
                                            : WxAssets.images.goalAdd.image(
                                                height: 32.w, width: 32.w),
                                      ),
                                    ),
                                    Positioned(
                                        bottom: 10.w,
                                        child: Container(
                                          width: 103.w,
                                          padding: EdgeInsets.only(
                                              left: 10.w, right: 10.w),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              if ( //logic.videostate.value == 1 &&
                                                  position ==
                                                      logic.state.indexVideo
                                                          .value)
                                                Container(
                                                  height: 10.w,
                                                  width: 10.w,
                                                  alignment: Alignment.center,
                                                  child: const LoadingIndicator(
                                                    pathBackgroundColor:
                                                        Colors.black26,
                                                    indicatorType: Indicator
                                                        .lineScaleParty,
                                                    colors: [Colours.white],
                                                  ),
                                                ),
                                              if ( //logic.videostate.value != 1 &&
                                                  (logic
                                                              .state
                                                              .dataList[
                                                                  position]
                                                              .selected ??
                                                          false) &&
                                                      position !=
                                                          logic.state.indexVideo
                                                              .value)
                                                WxAssets.images.document2.image(
                                                    height: 12.w, width: 12.w),
                                              Expanded(
                                                child: Text(
                                                  logic.state.dataList[position]
                                                          .videoTime ??
                                                      "",
                                                  textAlign: TextAlign.right,
                                                  style: TextStyles.medium
                                                      .copyWith(
                                                    fontSize: 14.sp,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ))
                                  ],
                                ),
                              ),
                            );
                          });
                        }),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.chooseTime(logic.state.videoNextStartTime.value,
                            logic.state.videoNextEndTime.value, 3);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 10.w),
                        margin: EdgeInsets.symmetric(vertical: 40.w),
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                              text: S.current.option_next_time,
                              style: TextStyle(
                                  color: Colours.color5D5D6E,
                                  fontSize: 13.sp,
                                  height: 1,
                                  fontWeight: FontWeight.w400),
                              children: <InlineSpan>[
                                TextSpan(
                                    text:
                                        "\t\t${logic.state.videoNextStartTime.value.trim().length >= 16 ? logic.state.videoNextStartTime.trim().substring(11, 16) : logic.state.videoNextStartTime}～${logic.state.videoNextEndTime.value.trim().length >= 16 ? logic.state.videoNextEndTime.trim().substring(11, 16) : logic.state.videoNextEndTime}",
                                    style: TextStyle(
                                        color: Colours.app_main,
                                        fontSize: 13.sp,
                                        height: 1,
                                        fontWeight: FontWeight.w400)),
                              ]),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
    });
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          height: 410.w,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              Container(
                width: double.infinity,
                height: 50.w,
                padding: EdgeInsets.only(top: 10.w, left: 9.w),
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: Get.width,
                  height: 44,
                  //Screen.navigationBarHeight,
                  padding: EdgeInsets.only(top: 0.w, left: 50, right: 50),
                  decoration: const BoxDecoration(),
                  child: TabBar(
                    controller: logic.tabController,
                    unselectedLabelColor: Colours.color9393A5,
                    unselectedLabelStyle: TextStyles.textBold16
                        .copyWith(color: Colours.color9393A5),
                    labelColor: Colours.white,
                    labelStyle: TextStyles.textBold16,
                    isScrollable: false,
                    // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                    indicatorPadding: EdgeInsets.zero,
                    dividerColor: Colors.transparent,
                    dividerHeight: 0,
                    indicatorColor: Colors.transparent,
                    // indicatorSize: TabBarIndicatorSize.tab,
                    // // dragStartBehavior: DragStartBehavior.start,
                    // indicatorWeight: 1,
                    // indicator: RoundUnderlineTabIndicator(
                    //   borderSide: BorderSide(
                    //     width: 3.5,
                    //     color: Colors.white,
                    //   ),
                    // ),

                    tabs: [S.current.Recommended_period, S.current.Exact_search]
                        .map((type) => Tab(text: "${type}"))
                        .toList(),
                  ),
                ),
              ),
              SizedBox(
                height: 10.w,
              ),
              Expanded(
                child: TabBarView(controller: logic.tabController, children: [
                  recommendedTimeWidget1(),
                  recommendedTimeWidget2(context),
                ]),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget recommendedTimeWidget1() {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          children: [
            Wrap(
              runSpacing: 15.w,
              spacing: 15.w,
              children:
                  List.generate(logic.state.dateFristList2.length, (index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.back();
                    logic.chooseTime(
                        logic.state.dateFristList2[index]["startTime"].trim(),
                        logic.state.dateFristList2[index]["endTime"].trim(),
                        0);
                  },
                  child: Container(
                    width: 102.w,
                    height: 44.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                        color: Colours.color22222D),
                    child: Text(
                      "${logic.state.dateFristList2[index]["startTime"].trim().length >= 16 ? logic.state.dateFristList2[index]["startTime"].trim().substring(10, 16) : logic.state.dateFristList2[index]["startTime"]}～${logic.state.dateFristList2[index]["endTime"].trim().length >= 16 ? logic.state.dateFristList2[index]["endTime"].trim().substring(10, 16) : logic.state.dateFristList2[index]["endTime"]}",
                      textAlign: TextAlign.center,
                      style: TextStyles.medium.copyWith(
                          fontSize: 13.sp, color: Colours.color9393A5),
                    ),
                  ),
                );
              }),
            ),
            Center(
              child: Container(
                margin: EdgeInsets.only(top: 20.w, bottom: 20.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(7.r)),
                    border: Border.all(width: 1, color: Colours.color2F2F3B)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.state.todayRecommended.value = true;
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 6.w, horizontal: 16.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(7.r)),
                          gradient: logic.state.todayRecommended.value
                              ? const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                        ),
                        child: Text(
                          S.current.today,
                          style: TextStyles.medium.copyWith(fontSize: 14.sp),
                        ),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.state.todayRecommended.value = false;
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 6.w, horizontal: 16.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(7.r)),
                          gradient: logic.state.todayRecommended.value
                              ? null
                              : const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                ),
                        ),
                        child: Text(
                          S.current.yesterday,
                          style: TextStyles.medium.copyWith(fontSize: 14.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Wrap(
              runSpacing: 15.w,
              spacing: 15.w,
              children: List.generate(logic.state.dateRecommendedList.length,
                  (index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.back();
                    if (!(logic.state.dateRecommendedList[index]
                            .contains("~") &&
                        logic.state.dateRecommendedList[index].contains(":"))) {
                      log("dateRecommendedList1=${logic.state.dateRecommendedList[index]}");
                      return;
                    }
                    log("dateRecommendedList2");
                    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                      // 解析日期时间字符串
                      DateTime originalTime = DateTime.now();
                      //      DateTime.parse(logic.state.videoDate.value);
                      if (!logic.state.todayRecommended.value) {
                        //昨天减去一天
                        originalTime = originalTime.subtract(Duration(days: 1));
                      }

                      //今天
                      var list =
                          logic.state.dateRecommendedList[index].split("~");

                      var list1 = list[0].split(":");
                      DateTime startDateTime1 = DateTime(
                          originalTime.year,
                          originalTime.month,
                          originalTime.day,
                          int.tryParse(list1[0]) ?? 0,
                          int.tryParse(list1[1]) ?? 0,
                          00);
                      var list2 = list[1].split(":");
                      DateTime endDdteTime2 = DateTime(
                          originalTime.year,
                          originalTime.month,
                          originalTime.day,
                          int.tryParse(list2[0]) ?? 0,
                          int.tryParse(list2[1]) ?? 0,
                          00);
                      logic.chooseTime(dateFormat.format(startDateTime1),
                          dateFormat.format(endDdteTime2), 1);
                    } catch (e) {
                      print('Error parsing date string: $e');
                    }
                  },
                  child: Container(
                    width: 102.w,
                    height: 44.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                        color: Colours.color22222D),
                    child: Text(
                      logic.state.dateRecommendedList[index],
                      textAlign: TextAlign.center,
                      style: TextStyles.medium.copyWith(
                          fontSize: 13.sp, color: Colours.color9393A5),
                    ),
                  ),
                );
              }),
            ),
            SizedBox(
              height: 30.w,
            )
          ],
        ),
      );
    });
  }

  Widget recommendedTimeWidget2(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Container(
          child: Column(
            children: [
              SizedBox(
                height: 5.w,
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(logic.state.datePrecisionList.length,
                      (index) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.state.videoDate1.value =
                            (logic.state.datePrecisionList[index]["date"] ??
                                "");
                        log("videoDate112=${logic.state.videoDate1.value}");
                      },
                      child: Container(
                        width: 70.w,
                        height: 70.w,
                        margin: EdgeInsets.only(
                            right: 10.w, left: index == 0 ? 20.w : 0),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20.r),
                            color: Colours.color22222D),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  (logic.state.datePrecisionList[index]["week"] ?? "")
                                              .toString()
                                              .replaceAll("星期", "") ==
                                          "一"
                                      ? S.current.Mon
                                      : (logic.state.datePrecisionList[index]["week"] ?? "")
                                                  .toString()
                                                  .replaceAll("星期", "") ==
                                              "二"
                                          ? S.current.Tue
                                          : (logic.state.datePrecisionList[index]["week"] ?? "")
                                                      .toString()
                                                      .replaceAll("星期", "") ==
                                                  "三"
                                              ? S.current.Wed
                                              : (logic.state.datePrecisionList[index]
                                                                  ["week"] ??
                                                              "")
                                                          .toString()
                                                          .replaceAll(
                                                              "星期", "") ==
                                                      "四"
                                                  ? S.current.Thu
                                                  : (logic.state.datePrecisionList[index]["week"] ?? "")
                                                              .toString()
                                                              .replaceAll("星期", "") ==
                                                          "五"
                                                      ? S.current.Fri
                                                      : (logic.state.datePrecisionList[index]["week"] ?? "").toString().replaceAll("星期", "") == "六"
                                                          ? S.current.Sat
                                                          : (logic.state.datePrecisionList[index]["week"] ?? "").toString().replaceAll("星期", "") == "日"
                                                              ? S.current.Sun
                                                              : "",
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: logic.state.videoDate1.value ==
                                              (logic.state.datePrecisionList[
                                                      index]["date"] ??
                                                  "")
                                          ? Colours.white
                                          : Colours.color9393A5),
                                ),
                                SizedBox(
                                  height: 10.w,
                                ),
                                Text(
                                  (logic.state.datePrecisionList[index]
                                                  ["datestr"] ??
                                              "") !=
                                          ""
                                      ? logic.state.datePrecisionList[index]
                                          ["datestr"]
                                      : (logic.state.datePrecisionList[index]
                                                          ["date"] ??
                                                      "")
                                                  .length >=
                                              10
                                          ? (logic.state.datePrecisionList[
                                                      index]["date"] ??
                                                  "")
                                              .substring(5, 10)
                                          : logic.state.datePrecisionList[index]
                                                  ["date"] ??
                                              "",
                                  style: TextStyles.medium.copyWith(
                                      fontSize:
                                          (logic.state.datePrecisionList[index]
                                                          ["datestr"] ??
                                                      "") !=
                                                  ""
                                              ? 14.sp
                                              : 15.sp,
                                      color: logic.state.videoDate1.value ==
                                              (logic.state.datePrecisionList[
                                                      index]["date"] ??
                                                  "")
                                          ? Colours.white
                                          : Colours.color9393A5),
                                ),
                              ],
                            ),
                            if (logic.state.videoDate1.value ==
                                (logic.state.datePrecisionList[index]["date"] ??
                                    ""))
                              WxAssets.images.selectTime
                                  .image(width: 50.w, height: 50.w)
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
              Container(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 30.w, bottom: 20.w),
                      child: Text(
                        S.current.Select_time_period,
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp, color: Colours.color5C5C6E),
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              logic.showDatePicker(context, 0);
                            },
                            child: Container(
                              width: double.infinity,
                              height: 50.w,
                              margin: EdgeInsets.only(right: 10.w),
                              padding: EdgeInsets.only(left: 20.w, right: 10.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: Colours.color22222D),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Text(
                                      logic.state.videoStartTime1.value
                                              .isNotEmpty
                                          ? logic.state.videoStartTime1.value
                                          : S.current.Please_select_start_time,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: logic.state.videoStartTime1
                                                  .value.isNotEmpty
                                              ? Colours.white
                                              : Colours.color5C5C6E),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down,
                                      color: Colours.white)
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              logic.showDatePicker(context, 1);
                            },
                            child: Container(
                              width: double.infinity,
                              height: 50.w,
                              padding: EdgeInsets.only(left: 20.w, right: 10.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: Colours.color22222D),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Text(
                                      logic.state.videoEndTime1.value.isNotEmpty
                                          ? logic.state.videoEndTime1.value
                                          : S.current.Please_select_end_time,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: logic.state.videoEndTime1.value
                                                  .isNotEmpty
                                              ? Colours.white
                                              : Colours.color5C5C6E),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down,
                                      color: Colours.white)
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 15.w, bottom: 30.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          WxAssets.images.tips.image(height: 12.w, width: 12.w),
                          SizedBox(
                            width: 2.w,
                          ),
                          Expanded(
                            child: Text(
                              S.current.select_time_tips,
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.color5C5C6E),
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.sureTimeSearch(context);
                      },
                      child: Container(
                        height: 46.w,
                        width: double.infinity,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                        ),
                        child: Text(
                          S.current.sure,
                          style: TextStyles.regular.copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  //ai选球弹窗
  void getAiOptionGoalDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 632,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.Please_select_your_photo,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Expanded(
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      SizedBox(
                        height: 580.w,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  crossAxisSpacing: 15,
                                  mainAxisSpacing: 15,
                                  childAspectRatio: 102 / 140,
                                ),
                                padding:
                                    EdgeInsets.only(bottom: 60.w, top: 0.w),
                                itemCount: logic.state.aiDataList.length,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        logic.state.aiDataList[index].isSelect =
                                            !(logic.state.aiDataList[index]
                                                    .isSelect ??
                                                false);
                                        logic.state.aiDataList.refresh();
                                      },
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          MyImage(
                                            logic.state.aiDataList[index]
                                                    .picture ??
                                                '',
                                            //  holderImg: "home/index/df_banner_top",
                                            fit: BoxFit.fill,
                                            width: 102.w,
                                            height: 140.w,
                                            isAssetImage: false,
                                            // errorImg: "home/index/df_banner_top"
                                            radius: 8.r,
                                          ),
                                          Container(
                                            width: 102.w,
                                            height: 140.w,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                color: !(logic
                                                            .state
                                                            .aiDataList[index]
                                                            .isSelect ??
                                                        false)
                                                    ? null
                                                    : Colours.color50000000),
                                            child: !(logic
                                                        .state
                                                        .aiDataList[index]
                                                        .isSelect ??
                                                    false)
                                                ? null
                                                : Container(
                                                    width: 20.w,
                                                    height: 20.w,
                                                    margin: EdgeInsets.only(
                                                        right: 8.w,
                                                        bottom: 3.w,
                                                        top: 8.w),
                                                    child: const Icon(
                                                      Icons.check,
                                                      color: Colours.white,
                                                      size: 20,
                                                    ),
                                                  ),
                                          )
                                        ],
                                      ),
                                    );
                                  });
                                }),
                            // GestureDetector(
                            //   behavior: HitTestBehavior.translucent,
                            //   onTap: () {
                            //     logic.state.rememberOption.value =
                            //         logic.state.rememberOption.value == "0"
                            //             ? "1"
                            //             : "0";
                            //   },
                            //   child: Container(
                            //     margin:
                            //         EdgeInsets.only(top: 20.w, bottom: 85.w),
                            //     alignment: Alignment.center,
                            //     child: Row(
                            //       mainAxisSize: MainAxisSize.min,
                            //       crossAxisAlignment: CrossAxisAlignment.center,
                            //       children: [
                            //         Container(
                            //           width: 14.w,
                            //           height: 14.w,
                            //           margin: EdgeInsets.only(
                            //               right: 8.w, bottom: 3.w),
                            //           child: Icon(
                            //             logic.state.rememberOption.value == "0"
                            //                 ? Icons.check_circle
                            //                 : Icons.radio_button_unchecked,
                            //             color:
                            //                 logic.state.rememberOption.value ==
                            //                         "0"
                            //                     ? Colours.white
                            //                     : Colours.color9393A5,
                            //             size: 17,
                            //           ),
                            //         ),
                            //         Text(
                            //           S.current.Also_add_the_goal_selected,
                            //           style: TextStyles.medium.copyWith(
                            //               fontSize: 12.sp,
                            //               color: Colours.color9393A5),
                            //         )
                            //       ],
                            //     ),
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          List<AiOptionModel> filteredNumbers =
                              logic.state.aiDataList.where((value) {
                            bool result = value.isSelect ?? false;
                            return result;
                          }).toList();
                          if (filteredNumbers.isEmpty) {
                            WxLoading.showToast(
                                S.current.select_your_photo_tips);
                            return;
                          }
                          AppPage.back();
                          List<int?> ids =
                              filteredNumbers.map((item) => item.id).toList();
                          // logic.getDataList2(ids.join(","), isLoad: false);
                          //  log("classifications2=${ids.join(",")}");
                          // 在导航前重置状态
                          //SiteReportLogic.to.reset();
                          AppPage.to(Routes.siteReportPage, arguments: {
                            "arenaId": logic.arenaID.value.toString(),
                            "classifications":
                                ids.join(","), //456013,456014,456015,456018
                            "courts": logic.courtId.value,
                            "videoEndTime": logic.state.videoEndTime.value,
                            "videoStartTime": logic.state.videoStartTime.value
                          });
                        },
                        child: Container(
                          height: 46.w,
                          width: double.infinity,
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(bottom: 7.w),
                          padding: EdgeInsets.only(
                              left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                          decoration: BoxDecoration(
                            color: Colours.color282735,
                            borderRadius:
                                BorderRadius.all(Radius.circular(28.r)),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Text(
                            S.current.select_your_photo_tips2,
                            style: TextStyles.titleMedium18
                                .copyWith(fontSize: 16.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
