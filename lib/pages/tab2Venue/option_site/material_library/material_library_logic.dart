import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/dao/OptionGoalDao.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/entities/material_library_model.dart';
import 'package:shoot_z/entities/option_goal_model.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/material_library/material_library_state.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';

class MaterialLibraryLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  final MaterialLibraryLogicState state = MaterialLibraryLogicState();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;

  final VideoController videoController = VideoController();
  var arenaID = 0.obs;
  var isFrist = true.obs;
  var isVip = false.obs;
  // var videostate = 0.obs; //0初始 1播放  2暂停  3完成
//侧面 cameraIndex = 0  全景  cameraIndex = 126
  @override
  Future<void> onInit() async {
    super.onInit();
    arenaID.value = Get.arguments["arenaID"] as int; //39 ??
    WidgetsBinding.instance.addObserver(this);
    ever(isVip, (value) {
      videoController.showWatermark(!value);
    });
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
    getDaoGoalList();
  }

  //查询用户已使用的片段以及是否vip
  Future<bool> getVideosUsedShots(var arenaId, {int type = 0}) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getVideosUsedShots, queryParameters: param);
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      log("getVideosUsedShots=${res.data["vip"]}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      if (!((res.data["vip"] ?? false) as bool)) {
        return false;
      } else {
        return true;
      }
    } else {
      if (type == 0) {
        WxLoading.showToast(res.message);
      }
      return false;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {}

  reloadVideo(String videoUrl) async {
    if (videoUrl.isEmpty) {
      return;
    }
    videoController.setData(videoPath: videoUrl, showWatermark: !isVip.value);
  }

  @override
  void onClose() {
    //isVip.close();
    videoController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  //改变选择视频index
  void changeVideoIndex(int indexGroup, int indexChild) {
    log("changeVideoIndex111=1-$indexGroup-$indexChild");
    state.indexVideo.value = indexChild;
    state.indexGroupVideo.value = indexGroup;
    state.indexVideoAngle.value = 0;
    if (state.indexVideo.value != 9999) {
      reloadVideo(
          state.dataGroupList[indexGroup].otherVideos?[indexChild]?.videoPath ??
              "");
    }
  }

  //数据库取数据
  Future<void> getDaoGoalList() async {
    // 获取当前本地时间
    DateTime now = DateTime.now();

    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    await optionGoalDao
        .findAllGoal(arenaID.value.toString(),
            UserManager.instance.userInfo.value?.userId ?? "")
        .then((v) {
      // // 使用 where 方法过滤列表
      List<OptionGoalModel> filteredNumbers = v.where((value) {
        bool result = false;
        String? timeString = value.videoDate;
        if (timeString != null) {
          try {
            // 解析日期时间字符串
            DateTime dateTime = DateTime.parse(timeString);

            // 判断两个日期是否相差8天以内
            result = Utils.areDatesWithinEightDays(now, dateTime);
            // 打印 UTC 时间和本地时间
            // log("getDaoSql5=${result}");
          } catch (e) {
            log("getDaoSql6catch=${e}");
          }
        }

        return result;
      }).toList();
      if (filteredNumbers.isNotEmpty) {
        getDataList(filteredNumbers, optionGoalDao);
      } else {
        state.indexVideo.value = 9999;
        state.dataList.clear();
        state.allCheck.value = false;
        state.dataGroupList.clear();

        if (dataFag["isFrist"] as bool) {
          dataFag["isFrist"] = false;
        }
      }
    });
  }

  //获得最新列表
  getDataList(
    List<OptionGoalModel> filteredNumbers,
    OptionGoalDao optionGoalDao, {
    isLoad = false,
  }) async {
    List<String?> ids = filteredNumbers.map((item) => item.id).toList();
    var param = {
      'videos': ids.join(","),
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.getIdsVideos, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      List list = res.data;
      List<OptionGoalModel> modelList =
          list.map((e) => OptionGoalModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${modelList.length}");
      if (isLoad) {
        if (modelList.isEmpty) {
          state.dataList.addAll(filteredNumbers);
        } else {
          state.dataList.addAll(modelList);
        }
        state.dataList.refresh();
      } else {
        if (modelList.isEmpty) {
          state.dataList.assignAll(filteredNumbers);
        } else {
          state.dataList.assignAll(modelList);
        }
      }
      await Future.delayed(const Duration(milliseconds: 100));
      log("getDaoSql6catch=${state.dataList.length}");
      await optionGoalDao.deleteAll(arenaID.value.toString(),
          UserManager.instance.userInfo.value?.userId ?? "");
      await Future.delayed(const Duration(milliseconds: 100));
      log("getDaoSql6catch2=${state.dataList.length}");
      await optionGoalDao.insertGoalList(
          state.dataList,
          arenaID.value.toString(),
          UserManager.instance.userInfo.value?.userId ?? "");
      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
      }
      for (int i = 0; i < state.dataList.length; i++) {
        state.dataList[i].selected = false;
      }
      // 使用 groupListsBy 按日期分组
      var groupedEvents = groupBy(
          state.dataList,
          (OptionGoalModel event) => normalizeDate(
              DateFormat("yyyy-MM-dd'T'HH:mm:ssZ").parse(event.videoDate!)));
      state.dataGroupList.clear();
      DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
      // 打印分组结果
      groupedEvents.forEach((date, eventsOnDate) {
        var dataItem = MaterialLibraryModel();
        dataItem.otherVideos = eventsOnDate;
        dataItem.videoTimeStr = dateFormat.format(date);
        dataItem.videoTime = date;
        state.dataGroupList.add(dataItem);
      });

      refresh();
      await getDaoGoalList2();
      if (state.dataGroupList.isNotEmpty &&
          (state.dataGroupList.first.otherVideos?.isNotEmpty ?? false)) {
        changeVideoIndex(0, 0);
        log("changeVideoIndex111-${state.dataGroupList[0].otherVideos?[0]?.videoPath}");
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

//改变选择视频角度
  void changeVideoAngle(
    int index,
  ) {
    state.indexVideoAngle.value =
        state.dataList[state.indexVideo.value].cameras?[index]!.cameraIndex ??
            0;
    log("message11=1");
    if (state.indexVideo.value != 9999) {
      var videoPath = '';
      for (int i = 0;
          i < (state.dataList[state.indexVideo.value].otherVideos?.length ?? 0);
          i++) {
        if (state.dataList[state.indexVideo.value].otherVideos?[i]
                ?.cameraIndex ==
            state.indexVideoAngle.value) {
          videoPath = state.dataList[state.indexVideo.value].otherVideos?[i]
                  ?.videoPath ??
              "";
        }
      }
      if (videoPath.isNotEmpty) {
        reloadVideo(videoPath);
      }
    }
  }

// 定义一个函数来提取日期部分（忽略时间）
  DateTime normalizeDate(DateTime dateTime) {
    //DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    // log("getDaoSql6catch3=${dateFormat.format(dateTime)}");
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }

  //删除单个数据
  Future<void> checkVideo(int indexGroup, int indexChild) async {
    //0新增 1删除
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    var optionGoalModel =
        state.dataGroupList[indexGroup].otherVideos![indexChild]!;

    log("checkVideo2=${optionGoalModel.arenaID ?? ""}--${optionGoalModel.userId ?? ""}--${optionGoalModel.id ?? ""}");
    await optionGoalDao.deleteGoal1(optionGoalModel.arenaID ?? "",
        optionGoalModel.userId ?? "", optionGoalModel.id ?? "");
    state.dataGroupList[indexGroup].otherVideos?.removeAt(indexChild);
    if (state.dataGroupList.isNotEmpty &&
        (state.dataGroupList.first.otherVideos?.isNotEmpty ?? false)) {
      changeVideoIndex(0, 0);
    }
    getDaoGoalList2();
  }

  //单个分组全部选中删除数据
  Future<void> checkAllDeleteVideo(int indexGroup) async {
    state.dataGroupList[indexGroup].selected =
        !(state.dataGroupList[indexGroup].selected ?? false);
    log("message-${indexGroup}-${state.dataGroupList[indexGroup].selected}");

    for (int j = 0;
        j < (state.dataGroupList[indexGroup].otherVideos?.length ?? 0);
        j++) {
      state.dataGroupList[indexGroup].otherVideos?[j]?.selected =
          (state.dataGroupList[indexGroup].selected ?? false);
      log("message-${j}-${state.dataGroupList[indexGroup].otherVideos?[j]?.selected}");
    }
    getDaoGoalList2();
  }

//单个分组全部选中 取消
  Future<void> checkOnlyVideo(int indexGroup, int indexChild) async {
    state.dataGroupList[indexGroup].otherVideos?[indexChild]?.selected =
        !(state.dataGroupList[indexGroup].otherVideos?[indexChild]?.selected ??
            false);
    getDaoGoalList2();
  }

//全选和取消全选
  Future<void> checkAllVideo() async {
    state.allCheck.value = !state.allCheck.value;
    for (int i = 0; i < state.dataGroupList.length; i++) {
      for (int j = 0;
          j < (state.dataGroupList[i].otherVideos?.length ?? 0);
          j++) {
        state.dataGroupList[i].otherVideos?[j]?.selected =
            (state.allCheck.value);
      }
    }
    getDaoGoalList2();
  }

  //删除选中数据
  Future<void> deleteCheckVideo() async {
    int a = 0;
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    for (int i = 0; i < state.dataGroupList.length; i++) {
      for (int j = 0;
          j < (state.dataGroupList[i].otherVideos?.length ?? 0);
          j++) {
        if (state.dataGroupList[i].otherVideos?[j]?.selected ?? false) {
          a += 1;

          var optionGoalModel = state.dataGroupList[i].otherVideos![j]!;
          await optionGoalDao.deleteGoal1(optionGoalModel.arenaID ?? "",
              optionGoalModel.userId ?? "", optionGoalModel.id ?? "");
          //state.dataGroupList[i].otherVideos?.removeAt(j);
        }
      }
    }
    if (a <= 0) {
      WxLoading.showToast(S.current.merge_videos_dialog_tips10);
      return;
    }
    getDaoGoalList();
  }

  //数据库取数据判断
  //数据库取数据判断
  Future<void> getDaoGoalList2() async {
    var havaAllSumCount = 0;
    var allSumCount = 0;
    for (int i = 0; i < state.dataGroupList.length; i++) {
      var allCount = 0;
      for (int j = 0;
          j < (state.dataGroupList[i].otherVideos?.length ?? 0);
          j++) {
        havaAllSumCount += 1;
        if (state.dataGroupList[i].otherVideos?[j]?.selected ?? false) {
          allCount += 1;
        }
      }
      state.dataGroupList[i].selecteCount = allCount;
      allSumCount += allCount;
      if (allCount >= (state.dataGroupList[i].otherVideos?.length ?? 0)) {
        state.dataGroupList[i].selected = true;
      } else {
        state.dataGroupList[i].selected = false;
      }
    }
    if (allSumCount >= (state.dataList.length)) {
      state.allCheck.value = true;
    } else {
      state.allCheck.value = false;
    }

    if (havaAllSumCount == 0) {
      state.dataGroupList.clear();
      state.dataList.clear();
      videoController.pause();
    }
    state.dataGroupList.sort((a, b) {
      return b.videoTime!.compareTo(a.videoTime!);
    });

    state.dataGroupList.refresh();
  }

// 定义一个函数来解析时间字符串为 TimeOfDay 对象
  // 定义一个函数来解析时间字符串为 DateTime 对象
  DateTime parseDateTimeString(String dateTimeStr) {
    return DateFormat("yyyy-MM-dd'T'HH:mm:ssZ").parse(dateTimeStr);
  }
}
