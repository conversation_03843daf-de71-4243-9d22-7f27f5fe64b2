import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/material_library_model.dart';
import 'package:shoot_z/network/model/option_goal_model.dart';

class MaterialLibraryLogicState {
  TextEditingController codeController = TextEditingController();
  //数据列表
  RxList<OptionGoalModel> dataList = <OptionGoalModel>[].obs;
  //var groupedEvents = <DateTime, List<OptionGoalModel>>{}.obs;
  //选中数据列表
  RxList<OptionGoalModel> dataCheckList = <OptionGoalModel>[].obs;
  //选中数据列表
  RxList<MaterialLibraryModel> dataGroupList = <MaterialLibraryModel>[].obs;

  var indexVideo = 9999.obs; //选择视频下标
  var indexGroupVideo = 9999.obs; //选择视频下标
  var indexVideoAngle = 0.obs; //视频录制角度
  var allControls = false.obs; //批量操作
  var allCheck = false.obs; //全选
}
