import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/material_library/material_library_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///球场主页->三级页面  选球的素材库
class MaterialLibraryPage extends StatelessWidget {
  MaterialLibraryPage({super.key});

  final logic = Get.put(MaterialLibraryLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.Material_library),
        actions: [
          Obx(() {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                logic.state.allControls.value = !logic.state.allControls.value;
              },
              child: Container(
                height: 40.w,
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.menu,
                      size: 18,
                      color: Colours.color964AEE,
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Text(
                      logic.state.allControls.value
                          ? S.current.Cancel_operation
                          : S.current.Batch_operation,
                      style: TextStyles.medium.copyWith(
                        fontSize: 13.sp,
                        color: Colours.color964AEE,
                      ),
                    ),
                    SizedBox(
                      width: 15.w,
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : KeepAliveWidget(
                child: SafeArea(
                  bottom: false,
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //视频播放器
                        _videoWidget(),
                        //选球
                        Expanded(child: _optionGoalWidget(context)),
                      ]),
                ),
              );
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool) ||
                logic.state.dataList.isEmpty
            ? SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    bottom: 25.w, left: 5.w, right: 15.w, top: 10.w),
                child: Row(
                  children: [
                    if (logic.state.indexVideo.value != 9999 &&
                        (logic.state.dataList[logic.state.indexVideo.value]
                                    .cameras?.length ??
                                0) >
                            1)
                      Row(
                        children: List.generate(
                            logic.state.dataList[logic.state.indexVideo.value]
                                    .cameras?.length ??
                                0,
                            (index) => GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    logic.changeVideoAngle(index);
                                  },
                                  child: Container(
                                    width: 44.w,
                                    height: 44.w,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        logic
                                                    .state
                                                    .dataList[logic
                                                        .state.indexVideo.value]
                                                    .cameras?[index]!
                                                    .cameraIndex ==
                                                logic
                                                    .state.indexVideoAngle.value
                                            ? WxAssets.images.optionVideo.image(
                                                width: 20.w, height: 20.w)
                                            : WxAssets.images.optionVideoHui
                                                .image(
                                                    width: 20.w, height: 20.w),
                                        Text(
                                          logic
                                                  .state
                                                  .dataList[logic
                                                      .state.indexVideo.value]
                                                  .cameras?[index]
                                                  ?.cameraName ??
                                              "",
                                          style: TextStyle(
                                            fontSize: 10.sp,
                                            color: Colors.white,
                                            fontWeight: AppFontWeight.medium(),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )),
                      ),
                    if (logic.state.indexVideo.value != 9999 &&
                        (logic.state.dataList[logic.state.indexVideo.value]
                                    .cameras?.length ??
                                0) >
                            1)
                      Spacer(),
                    if (logic.state.allControls.value)
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          logic.checkAllVideo();
                        },
                        child: Container(
                          width: 45.w,
                          height: 44.w,
                          alignment: Alignment.center,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                width: 14.w,
                                height: 14.w,
                                margin: EdgeInsets.only(bottom: 5.w),
                                child: Icon(
                                  !logic.state.allCheck.value
                                      ? Icons.check_box_outline_blank_rounded
                                      : Icons.check_box,
                                  color: Colours.white,
                                  size: 17,
                                ),
                              ),
                              Text(
                                logic.state.allCheck.value
                                    ? S.current.Deselect_all
                                    : S.current.select_all,
                                style:
                                    TextStyles.medium.copyWith(fontSize: 10.sp),
                              )
                            ],
                          ),
                        ),
                      ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        if (logic.state.allControls.value) {
                          //删除选中的视频
                          logic.deleteCheckVideo();
                        } else {
                          if (await Utils.isToLogin()) {
                            //合成进球
                            AppPage.to(Routes.compositeVideoPage, arguments: {
                              "arenaID": logic.arenaID.value,
                            }).then((v) {
                              logic.getDaoGoalList();
                            });
                          }
                        }
                      },
                      child: Container(
                        height: 46.w,
                        width: (logic.state.indexVideo.value != 9999 &&
                                (logic
                                            .state
                                            .dataList[
                                                logic.state.indexVideo.value]
                                            .cameras
                                            ?.length ??
                                        0) >
                                    1)
                            ? !logic.state.allControls.value
                                ? 174.w
                                : 130.w
                            : logic.state.allControls.value
                                ? 295.w
                                : 340.w,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(left: 14.w, right: 0.w),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          logic.state.allControls.value
                              ? S.current.delete
                              : S.current.desynthesis,
                          style: TextStyles.display16.copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),
    );
  }

  Widget _videoWidget() {
    return Obx(() {
      return logic.state.dataGroupList.isEmpty
          ? MyImage(
              "error_image_width.png",
              fit: BoxFit.fill,
              bgColor: Colors.transparent,
              isAssetImage: true,
              radius: 0.r,
              width: double.infinity,
              height: ScreenUtil().screenWidth / 375 * 211,
            )
          : SizedBox(
              width: double.infinity,
              height: ScreenUtil().screenWidth / 375 * 211,
              child: VideoView(
                controller: logic.videoController,
              ),
            );
    });
  }

  Widget _optionGoalWidget(BuildContext context) {
    return Obx(() {
      return logic.state.dataList.isEmpty
          ? myNoDataView(
              context,
              msg: S.current.no_goal,
              imagewidget:
                  WxAssets.images.noGoal.image(width: 100.w, height: 84.w),
            )
          : SingleChildScrollView(
              child: Column(
                children: List.generate(logic.state.dataGroupList.length,
                    (indexGroup) {
                  return logic
                          .state.dataGroupList[indexGroup].otherVideos!.isEmpty
                      ? const SizedBox()
                      : Padding(
                          padding: EdgeInsets.only(
                              left: 15.w, right: 15.w, top: 20.w),
                          child: Column(
                            children: [
                              Container(
                                height: 30.w,
                                width: double.infinity,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    if (logic.state.allControls.value)
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          logic.checkAllDeleteVideo(indexGroup);
                                        },
                                        child: Container(
                                          alignment: Alignment.topRight,
                                          padding: EdgeInsets.only(
                                              top: 7.w,
                                              bottom: 7.w,
                                              right: 5.w),
                                          child: (logic
                                                      .state
                                                      .dataGroupList[indexGroup]
                                                      .selected ??
                                                  false)
                                              ? WxAssets.images.check.image(
                                                  height: 16.w, width: 16.w)
                                              : WxAssets.images.uncheck.image(
                                                  height: 16.w, width: 16.w),
                                        ),
                                      ),
                                    Text(
                                      (logic.state.dataGroupList[indexGroup]
                                                      .selecteCount ??
                                                  0) >
                                              0
                                          ? "${(logic.state.dataGroupList[indexGroup].videoTimeStr?.length ?? 0) > 10 ? logic.state.dataGroupList[indexGroup].videoTimeStr?.substring(5, 10) : logic.state.dataGroupList[indexGroup].videoTimeStr ?? ""} (${logic.state.dataGroupList[indexGroup].selecteCount ?? ""})"
                                          : "${(logic.state.dataGroupList[indexGroup].videoTimeStr?.length ?? 0) > 10 ? logic.state.dataGroupList[indexGroup].videoTimeStr?.substring(5, 10) : logic.state.dataGroupList[indexGroup].videoTimeStr ?? ""}",
                                      textAlign: TextAlign.right,
                                      style: TextStyles.medium.copyWith(
                                          fontSize: 14.sp,
                                          color: Colours.color5C5C6E),
                                    )
                                  ],
                                ),
                              ),
                              GridView.builder(
                                  scrollDirection: Axis.vertical,
                                  // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                  shrinkWrap: true,
                                  physics:
                                      const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                  gridDelegate:
                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 3,
                                    crossAxisSpacing: 15,
                                    mainAxisSpacing: 15,
                                    childAspectRatio: 101 / 72,
                                  ),
                                  padding: EdgeInsets.only(bottom: 0.w),
                                  itemCount: logic
                                      .state
                                      .dataGroupList[indexGroup]
                                      .otherVideos
                                      ?.length,
                                  itemBuilder: (context, indexChild) {
                                    return Obx(() {
                                      return GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          logic.changeVideoIndex(
                                              indexGroup, indexChild);
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                              color:
                                                  indexChild == logic.state.indexVideo.value && indexGroup == logic.state.indexGroupVideo.value
                                                      ? Colours.color291A3B
                                                      : Colours.color191921,
                                              borderRadius:
                                                  BorderRadius.circular(18.r),
                                              image: indexChild == logic.state.indexVideo.value &&
                                                      indexGroup ==
                                                          logic
                                                              .state
                                                              .indexGroupVideo
                                                              .value
                                                  ? null
                                                  : const DecorationImage(
                                                      image: AssetImage(
                                                          "assets/images/goal_bg.png"),
                                                      fit: BoxFit.fill),
                                              border: indexChild == logic.state.indexVideo.value &&
                                                      indexGroup == logic.state.indexGroupVideo.value
                                                  ? Border.all(width: 1, color: Colours.color7732ED)
                                                  : null),
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              if (logic.state.allControls.value)
                                                Positioned(
                                                  top: 0.w,
                                                  right: 0.w,
                                                  child: GestureDetector(
                                                    behavior: HitTestBehavior
                                                        .translucent,
                                                    onTap: () {
                                                      logic.checkOnlyVideo(
                                                          indexGroup,
                                                          indexChild);
                                                    },
                                                    child: Container(
                                                      alignment:
                                                          Alignment.topRight,
                                                      padding: EdgeInsets.only(
                                                          top: 10.w,
                                                          right: 10.w,
                                                          left: 10.w,
                                                          bottom: 5.w),
                                                      child: (logic
                                                                  .state
                                                                  .dataGroupList[
                                                                      indexGroup]
                                                                  .otherVideos?[
                                                                      indexChild]!
                                                                  .selected ??
                                                              false)
                                                          ? WxAssets
                                                              .images.check
                                                              .image(
                                                                  height: 16.w,
                                                                  width: 16.w)
                                                          : WxAssets
                                                              .images.uncheck
                                                              .image(
                                                                  height: 16.w,
                                                                  width: 16.w),
                                                    ),
                                                  ),
                                                ),
                                              if (!logic.state.allControls
                                                      .value &&
                                                  indexChild ==
                                                      logic.state.indexVideo
                                                          .value &&
                                                  indexGroup ==
                                                      logic
                                                          .state
                                                          .indexGroupVideo
                                                          .value)
                                                Positioned(
                                                  top: 0.w,
                                                  right: 0.w,
                                                  child: Visibility(
                                                    visible: indexChild ==
                                                        logic.state.indexVideo
                                                            .value,
                                                    child: GestureDetector(
                                                      behavior: HitTestBehavior
                                                          .translucent,
                                                      onTap: () {
                                                        logic.checkVideo(
                                                            indexGroup,
                                                            indexChild);
                                                      },
                                                      child: Container(
                                                        alignment:
                                                            Alignment.topRight,
                                                        padding:
                                                            EdgeInsets.only(
                                                                top: 10.w,
                                                                right: 10.w,
                                                                left: 10.w,
                                                                bottom: 5.w),
                                                        child: WxAssets
                                                            .images.delete
                                                            .image(
                                                                height: 16.w,
                                                                width: 16.w),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              Positioned(
                                                  bottom: 10.w,
                                                  child: Container(
                                                    width: 103.w,
                                                    padding: EdgeInsets.only(
                                                        left: 10.w,
                                                        right: 10.w),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        if ( //logic.videostate.value == 1 &&
                                                            indexChild ==
                                                                    logic
                                                                        .state
                                                                        .indexVideo
                                                                        .value &&
                                                                indexGroup ==
                                                                    logic
                                                                        .state
                                                                        .indexGroupVideo
                                                                        .value)
                                                          Container(
                                                            height: 10.w,
                                                            width: 10.w,
                                                            alignment: Alignment
                                                                .center,
                                                            child:
                                                                const LoadingIndicator(
                                                              pathBackgroundColor:
                                                                  Colors
                                                                      .black26,
                                                              indicatorType:
                                                                  Indicator
                                                                      .lineScaleParty,
                                                              colors: [
                                                                Colours.white
                                                              ],
                                                            ),
                                                          ),
                                                        Expanded(
                                                          child: Text(
                                                            logic
                                                                    .state
                                                                    .dataGroupList[
                                                                        indexGroup]
                                                                    .otherVideos?[
                                                                        indexChild]
                                                                    ?.videoTime ??
                                                                "",
                                                            textAlign:
                                                                TextAlign.right,
                                                            style: TextStyles
                                                                .medium
                                                                .copyWith(
                                                              fontSize: 14.sp,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  )),
                                              // Text(
                                              //   "${logic.state.dataGroupList[indexGroup].otherVideos?[indexChild]?.userId ?? ""}-${logic.state.dataGroupList[indexGroup].otherVideos?[indexChild]?.id ?? ""}-${logic.state.dataGroupList[indexGroup].otherVideos?[indexChild]?.arenaID ?? ""}",
                                              //   style: TextStyles.textSize14
                                              //       .copyWith(
                                              //           color: Colours.white),
                                              // ),
                                            ],
                                          ),
                                        ),
                                      );
                                    });
                                  }),
                            ],
                          ),
                        );
                }),
              ),
            );
    });
  }
}
