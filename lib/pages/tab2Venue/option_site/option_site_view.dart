import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:marquee/marquee.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_site_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///球场主页->三级页面  选择场地
class OptionSitePage extends StatelessWidget {
  OptionSitePage({super.key});

  final logic = Get.put(OptionSiteLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.Site_selection),
      ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : KeepAliveWidget(
                child: SingleChildScrollView(
                  child: Container(
                    width: double.infinity,
                    child: SafeArea(
                      bottom: false,
                      child: optionSiteWidget(),
                    ),
                  ),
                ),
              );
      }),
    );
  }

  Widget optionSiteWidget() {
    return Column(
      children: [
        Stack(
          children: [
            Column(
              children: [
                Container(
                  width: double.infinity,
                  height: 370.w,
                  margin: EdgeInsets.only(
                      left: 15.w, right: 15.w, bottom: 15.w, top: 15.w),
                  decoration: BoxDecoration(
                      color: Colours.color302353,
                      borderRadius: BorderRadius.circular(8.r)),
                  // border: Border.all(width: 2, color: Colours.color2F2F3B)),
                  child: Padding(
                    padding: EdgeInsets.all(15.w),
                    child: MyImage(
                      logic.optionSiteModel.value.layoutImage ?? "",
                      fit: BoxFit.contain,
                      bgColor: Colors.transparent,
                      radius: 16.r,
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      width: 10.w,
                      height: 10.w,
                      decoration: BoxDecoration(
                          color: Colours.color6D4DCC,
                          borderRadius: BorderRadius.all(Radius.circular(5.r))),
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Text(
                      '紫色区域为球秀视频采集区域',
                      style: TextStyles.display12.copyWith(color: Colors.white),
                    )
                  ],
                ).marginOnly(bottom: 15.w, right: 15.w),
                if (logic.maintenanceArenaList.isNotEmpty)
                  Container(
                    width: double.infinity,
                    height: 28.w,
                    margin: EdgeInsets.only(bottom: 15.w),
                    padding: EdgeInsets.only(left: 20.w),
                    alignment: Alignment.centerLeft,
                    decoration: const BoxDecoration(
                        gradient: LinearGradient(
                      colors: [
                        Color(0x00964AEE),
                        Color(0x4D964AEE),
                        Color(0x00964AEE)
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    )),
                    child: Row(
                      children: [
                        WxAssets.images.lampIcon.image(),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          child: Marquee(
                            text:
                                '${logic.maintenanceArenaList.join('，')}场地目前故障，数据可能有缺失',
                            style: TextStyles.display12
                                .copyWith(color: Colors.white),
                            scrollAxis: Axis.horizontal,
                            velocity: 30.0, // 滚动速度
                            blankSpace: 20.0, // 文本间的空白
                            pauseAfterRound:
                                const Duration(seconds: 1), // 每轮结束暂停时间
                            startPadding: 10.0, // 起始边距
                            accelerationDuration:
                                const Duration(seconds: 1), // 加速时间
                            decelerationDuration:
                                const Duration(milliseconds: 500), // 减速时间
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            if (logic.optionSiteModelCourts.value.courtName != "" &&
                logic.startTime.value.trim().length > 10 &&
                logic.endTime.value.trim().length > 10)
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  AppPage.to(Routes.optionGoalPage, arguments: {
                    "arenaID": logic.arenaID.value,
                    "type": logic.type.value, //0普通球馆  1高级球馆 有ai身形
                    "courtId": logic.optionSiteModelCourts.value
                        .courtId, //logic.startTime.value
                    "startTime": logic.startTime.value,
                    "endTime": logic.endTime.value,
                    "courtName": logic.optionSiteModelCourts.value.courtName,
                    "maintenanceArenaList": logic.maintenanceArenaList
                  }).then((onValue) {
                    logic.getData2();
                  });
                },
                child: Container(
                  width: double.infinity,
                  height: 40.w,
                  padding: EdgeInsets.only(left: 12.w, right: 12.w),
                  color: Colours.color70000000,
                  child: Row(
                    children: [
                      Container(
                        height: 30.w,
                        alignment: Alignment.center,
                        margin:
                            EdgeInsets.only(right: 20.w, top: 3.w, bottom: 3.w),
                        padding: EdgeInsets.only(
                            left: 10.w, right: 10.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius:
                                BorderRadius.all(Radius.circular(20.r))),
                        child: Text(
                          S.current.last_selected,
                          style: TextStyles.regular.copyWith(fontSize: 12.sp),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          S.current.site(
                              "${logic.optionSiteModelCourts.value.courtName ?? ""}、\t${logic.startTime.value.trim().contains(logic.todayDate) ? S.current.today : logic.startTime.value.trim().contains(logic.yesterdayDate) ? S.current.yesterday : logic.startTime.value.trim().length >= 10 ? logic.startTime.trim().substring(5, 10) : logic.startTime.value}\t\t\t${logic.startTime.value.trim().length >= 16 ? logic.startTime.value.trim().substring(11, 16) : logic.startTime.value}～${logic.endTime.value.trim().length >= 16 ? logic.endTime.value.trim().substring(11, 16) : logic.endTime.value}"),
                          maxLines: 2,
                          style: TextStyles.regular.copyWith(
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                      const Icon(
                        Icons.arrow_right_alt_sharp,
                        color: Colours.color7732ED,
                      )
                    ],
                  ),
                ),
              )
          ], //site
        ),
        Padding(
          padding: EdgeInsets.only(left: 15.w, right: 15.w),
          child: GridView.builder(
              scrollDirection: Axis.vertical,
              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 15,
                mainAxisSpacing: 15,
                childAspectRatio: 160 / 145,
              ),
              itemCount: logic.optionSiteModel.value.courts?.length ?? 0,
              itemBuilder: (context, index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    //选择进球
                    AppPage.to(Routes.optionGoalPage, arguments: {
                      "arenaID": logic.arenaID.value,
                      "type": logic.type.value, //0普通球馆  1高级球馆 有ai身形
                      "courtId":
                          logic.optionSiteModel.value.courts?[index]?.courtId,
                      "LatestDate": logic
                          .optionSiteModel.value.courts?[index]?.LatestDate,
                      "LatestTime": logic
                          .optionSiteModel.value.courts?[index]?.LatestTime,
                      "courtName":
                          logic.optionSiteModel.value.courts?[index]?.courtName,
                      "maintenanceArenaList": logic.maintenanceArenaList
                    }).then((onValue) {
                      logic.getData2();
                    });
                  },
                  child: Container(
                    height: 145.w,
                    decoration: BoxDecoration(
                      //  color: Colours.color191921,
                      borderRadius: BorderRadius.circular(18.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: MyImage(
                            (!(logic.optionSiteModel.value.courts?[index]
                                                ?.LatestCover ==
                                            null ||
                                        logic
                                            .optionSiteModel
                                            .value
                                            .courts![index]!
                                            .LatestCover!
                                            .isEmpty)
                                    ? logic.optionSiteModel.value
                                        .courts![index]!.LatestCover
                                    : logic.optionSiteModel.value
                                        .courts?[index]!.courtCover) ??
                                '',
                            fit: BoxFit.fill,
                            width: 174.w,
                            height: 120.w,
                            isAssetImage: false,
                            errorImage: "error_image_width.png",
                            radius: 5.r,
                          ),
                        ),
                        SizedBox(
                          height: 10.w,
                        ),
                        Row(
                          children: [
                            MyText(
                              logic.optionSiteModel.value.courts?[index]
                                      ?.courtName ??
                                  '',
                              color: Colours.white,
                              size: 14.sp,
                            ),
                            Expanded(
                              child: MyText(
                                "${S.current.Generate_into((logic.optionSiteModel.value.courts?[index]?.LatestDate ?? '').length >= 10 ? (logic.optionSiteModel.value.courts?[index]?.LatestDate ?? '').substring(5) : logic.optionSiteModel.value.courts?[index]?.LatestDate ?? '')}\t${(logic.optionSiteModel.value.courts?[index]?.LatestTime ?? '').length >= 8 ? (logic.optionSiteModel.value.courts?[index]?.LatestTime ?? '').substring(0, 5) : logic.optionSiteModel.value.courts?[index]?.LatestTime ?? ''}",
                                color: Colours.color9393A5,
                                size: 12.sp,
                                textAlign: TextAlign.end,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              }),
        ),
        const SizedBox(
          height: 30,
        ),
      ],
    );
  }
}
