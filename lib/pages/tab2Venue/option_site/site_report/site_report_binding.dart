import 'package:get/get.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_report/site_report_logic.dart';
import 'package:shoot_z/routes/route.dart';

///球馆 打野报告
// class SiteReportBinding extends Bindings {
//   @override
//   void dependencies() {
//     Get.lazyPut(() => SiteReportLogic());
//   }
// }
// 修改绑定类
class SiteReportBinding implements Bindings {
  // 控制器唯一标识
  static const String _controllerTag = 'site_report_controller';

  @override
  void dependencies() {
    // 确保只创建一个逻辑控制器实例
    if (!Get.isRegistered<SiteReportLogic>(tag: _controllerTag)) {
      Get.lazyPut<SiteReportLogic>(
        () => SiteReportLogic(),
        tag: _controllerTag,
        fenix: false, // 禁用自动重建
      );
    }
  }

  // 安全的销毁方法
  static void dispose() {
    // 正确获取当前路由名称的方法
    String? currentRoute = Get.currentRoute;
    // 检查当前路由是否是目标页
    if (currentRoute != Routes.siteReportPage) {
      Get.delete<SiteReportLogic>(
        tag: _controllerTag,
        force: true,
      );
    }
  }
}
