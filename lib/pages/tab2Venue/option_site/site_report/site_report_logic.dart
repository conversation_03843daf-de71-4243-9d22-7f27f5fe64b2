import 'dart:async';
import 'dart:developer';
import 'package:flutter/painting.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/site_report_model.dart';
import 'package:shoot_z/utils/AndroidDevicePerformance.dart';
import 'package:flutter/src/foundation/print.dart';

class SiteReportLogic extends GetxController {
  var arenaId = "";
  var isFrist = true.obs;
  var classifications = "";
  var courts = "";
  var videoStartTime = "";
  var videoEndTime = "";
  var siteReportModel = SiteReportModel().obs;
  var nameList = [
    S.current.takeCount,
    S.current.goal,
    S.current.shotRate,
    S.current.threeParts,
    S.current.underTheBasket
  ];
  var isPaintingComplete = false.obs;
  DeviceTier deviceTier = DeviceTier.lowEnd;
  late final double containerWidth = ScreenUtil().screenWidth - 30.w;
  late final double containerHeight = containerWidth * 14 / 15;
  final isFrist2 = true.obs;
  @override
  void onInit() {
    super.onInit();
    arenaId = Get.arguments["arenaId"];
    classifications = Get.arguments["classifications"];
    courts = Get.arguments["courts"];
    videoStartTime = Get.arguments["videoStartTime"];
    videoEndTime = Get.arguments["videoEndTime"];
    log("getDataInforeportNew=1");
  }

  @override
  void onReady() {
    super.onReady();

    getDataInfo();
  }

  // 转换数据到绘制需要的格式
  List<SiteReportModelPoints> get paintPoints {
    return (siteReportModel.value.points?.map((point) {
              return SiteReportModelPoints(
                x: point?.x ?? 0.0,
                y: point?.y ?? 0.0,
                hit: point?.hit ?? false,
              );
            }).toList() ??
            [])
        .take(800)
        .toList();
  }

//获得数据
  getDataInfo() async {
    // WxLoading.show();
    await Future.delayed(const Duration(milliseconds: 300));
    Map<String, dynamic> param = {
      "arenaId": arenaId,
      "classifications": classifications,
      "courts": courts,
      "videoStartTime": videoStartTime,
      "videoEndTime": videoEndTime,
    };
    var res = await Api().post(ApiUrl.reportNew, data: param);
    //WxLoading.dismiss();
    // log("getDataInforeportNew=${res.data}");
    if (res.isSuccessful()) {
      siteReportModel.value = SiteReportModel.fromJson(res.data);
      debugPrint('pointslength: '
          '${siteReportModel.value.points?.length}');
      log("getDataInforeportNew=3");
      siteReportModel.refresh();
      if (isFrist.value == true) {
        isFrist.value = false;
        isFrist.refresh();
      }
      //log("getDataInforeportNew2=${siteReportModel.value.fragments?[0]?.cover}\n${siteReportModel.value.fragments?[1]?.cover}");
      // scheduleFirstCheck();
      Future.delayed(const Duration(milliseconds: 1500)).then((onValue) {
        if (isClosed) return; // 防止在销毁后操作
        if (isFrist2.value) {
          log("getDataInforeportNew=7");
          isFrist2.value = false; // 自动通知监听者
        }
        if (!isPaintingComplete.value &&
            !isFrist.value &&
            deviceTier != DeviceTier.lowEnd) {
          log("getDataInforeportNew=7");
          isPaintingComplete.value = true; // 自动通知监听者
        }
      });
      Future.delayed(const Duration(milliseconds: 3500)).then((onValue) {
        if (isClosed) return; // 防止在销毁后操作
        if (!isPaintingComplete.value &&
            !isFrist.value &&
            deviceTier != DeviceTier.lowEnd) {
          log("getDataInforeportNew=7");
          isPaintingComplete.value = true; // 自动通知监听者
        }
      });
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
    imageCache.clear();
    siteReportModel.value = SiteReportModel();
    siteReportModel.value.photo = "";
    siteReportModel.refresh();
    debugPrint('pointslength: '
        '${siteReportModel.value.points?.length}');
    log("getDataInforeportNew=3");
    debugPrint('onClose111: '
        '${siteReportModel.value.points?.length}');
  }

  @override
  void dispose() {
    debugPrint('dispose111: '
        '${siteReportModel.value.points?.length}');
    debugPrint('dispose112: '
        '${siteReportModel.value.points?.length}');
    super.dispose();
  }
}
