// ignore_for_file: unnecessary_type_check

import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/site_report_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/AndroidDevicePerformance.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/OptimizedImage.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 安全的图像组件包装器
class SafeImageWidget extends StatelessWidget {
  final ui.Image image;

  const SafeImageWidget({super.key, required this.image});

  @override
  Widget build(BuildContext context) {
    try {
      // 尝试使用图像
      return RawImage(
        image: image,
        fit: BoxFit.fill,
      );
    } catch (e) {
      // 图像不可用时的后备方案
      return Container(
        color: Colors.grey[200],
        child: Center(
          child: Icon(Icons.broken_image, size: 50, color: Colors.grey[500]),
        ),
      );
    }
  }
}

// ================== 异步绘制器 ==================

class PointCache {
  final Float32List hitPoints;
  final Float32List noHitPoints;
  final double innerHalf;

  PointCache(this.hitPoints, this.noHitPoints, this.innerHalf);
}

class _AsyncPointsPainter extends StatefulWidget {
  final List<SiteReportModelPoints> points;
  final double containerWidth;
  final double containerHeight;
  final double shiw;
  final double wuw;
  final DeviceTier deviceTier;
  final VoidCallback onComplete;
  final ValueChanged<PointCache> onCacheReady;

  const _AsyncPointsPainter({
    Key? key,
    required this.points,
    required this.containerWidth,
    required this.containerHeight,
    required this.shiw,
    required this.wuw,
    required this.deviceTier,
    required this.onComplete,
    required this.onCacheReady,
  }) : super(key: key);

  @override
  State<_AsyncPointsPainter> createState() => _AsyncPointsPainterState();
}

class _AsyncPointsPainterState extends State<_AsyncPointsPainter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isCacheReady = false;

  // 使用Float32List优化点存储
  Float32List _cachedHitPoints = Float32List(0);
  Float32List _cachedNoHitPoints = Float32List(0);
  double _innerHalf = 0;
  int _pointsPerFrame = 50;

  // GPU帧时间监控
  int _gpuFrameTime = 0;
  bool _isHighLoad = false;

  @override
  void initState() {
    super.initState();
    _initAnimation();
    _calculateParams();
    _startGpuMonitor();
  }

  void _startGpuMonitor() {
    // 监控GPU帧时间
    SchedulerBinding.instance.addPostFrameCallback((_) {
      SchedulerBinding.instance.addTimingsCallback((timings) {
        if (timings.isNotEmpty && mounted) {
          setState(() {
            final frameTiming = timings.last;
            _gpuFrameTime = frameTiming.rasterDuration.inMilliseconds;
            _isHighLoad = _gpuFrameTime > 16; // >16ms 表示低于60FPS
          });
        }
      });
    });
  }

  void _initAnimation() {
    // 动态调整每帧点数
    int calculatePointsPerFrame() {
      if (_isHighLoad) {
        return (widget.deviceTier.pointLoad * 0.5).clamp(5, 30).toInt();
      }
      return widget.deviceTier.pointLoad;
    }

    _pointsPerFrame = calculatePointsPerFrame();

    // 使用保守的持续时间
    final estimatedDuration = Duration(
        milliseconds: (widget.points.length / _pointsPerFrame * 25).toInt());

    _controller = AnimationController(
      vsync: this,
      duration: estimatedDuration,
    )..addStatusListener((status) {
        if (_controller.value >= 1.0 &&
            _controller.status != AnimationStatus.completed) {
          _controller.value = 1.0;
          return;
        }

        switch (status) {
          case AnimationStatus.completed:
            if (mounted) widget.onComplete();
            break;
          case AnimationStatus.dismissed:
            if (mounted) {
              setState(() {
                _cachedHitPoints = Float32List(0);
                _cachedNoHitPoints = Float32List(0);
                _isCacheReady = false;
              });
            }
            _calculateParams();
            if (mounted) _controller.forward();
            break;
          default:
            break;
        }
      });

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) _controller.forward();
    });
  }

  void _calculateParams() {
    _innerHalf = widget.shiw / 2 - widget.shiw * 0.15;

    // 在隔离线程中进行点计算
    _calculatePointsInIsolate();
  }

  Future<void> _calculatePointsInIsolate() async {
    try {
      final maxPoints = widget.deviceTier.maxPoints;

      // 在隔离线程中执行计算
      final result = await compute(_processPoints, {
        'points': widget.points,
        'maxPoints': maxPoints,
        'containerWidth': widget.containerWidth,
        'containerHeight': widget.containerHeight,
      });

      if (mounted) {
        setState(() {
          _cachedHitPoints =
              Float32List.fromList(result['hitPoints'] as List<double>);
          _cachedNoHitPoints =
              Float32List.fromList(result['noHitPoints'] as List<double>);
          _isCacheReady = true;
        });

        // 通知父组件缓存已就绪
        widget.onCacheReady(
            PointCache(_cachedHitPoints, _cachedNoHitPoints, _innerHalf));
      }
    } catch (e) {
      debugPrint("点坐标计算失败: $e");
    }
  }

  static Map<String, dynamic> _processPoints(Map<String, dynamic> params) {
    final List<SiteReportModelPoints> points = params['points'];
    final int maxPoints = params['maxPoints'];
    final double widthRatio = params['containerWidth'];
    final double heightRatio = params['containerHeight'];

    // 收集命中和未命中的点
    final hitPoints = <double>[];
    final noHitPoints = <double>[];

    int count = 0;
    for (final point in points) {
      if (count++ >= maxPoints) break;

      final rawX = (point.x ?? 0.0) * widthRatio;
      final rawY = (point.y ?? 0.0) * heightRatio;

      if (point.hit == true) {
        hitPoints.add(rawX);
        hitPoints.add(rawY);
      } else {
        noHitPoints.add(rawX);
        noHitPoints.add(rawY);
      }
    }

    return {
      'hitPoints': hitPoints,
      'noHitPoints': noHitPoints,
    };
  }

  @override
  void dispose() {
    _controller.stop();
    _controller.dispose();
    // 释放视频编解码器
    SystemChannels.platform.invokeMethod('VideoPlayer.disposeAll');

    // 强制GC（针对视频编解码器泄漏）
    SystemChannels.platform.invokeMethod('Memory.forceGC');
    // 释放内存
    _cachedHitPoints = Float32List(0);
    _cachedNoHitPoints = Float32List(0);
    // 建议添加以下代码
    if (Platform.isAndroid || Platform.isIOS) {
      // 调用引擎级内存释放
      SystemChannels.platform.invokeMethod('Memory.forceGC');
    }
    IsolationController.disposeAll(); // 退出时终止所有隔离
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 根据GPU负载动态调整动画速度
    if (_isHighLoad) {
      _controller.duration =
          Duration(milliseconds: _controller.duration!.inMilliseconds * 2);
    }

    return CustomPaint(
      painter: _AsyncPointsPainterDelegate(
        controller: _controller,
        isCacheReady: _isCacheReady,
        cachedHitPoints: _cachedHitPoints,
        cachedNoHitPoints: _cachedNoHitPoints,
        innerHalf: _innerHalf,
        pointsPerFrame: _pointsPerFrame,
        deviceTier: widget.deviceTier,
        isHighLoad: _isHighLoad,
      ),
    );
  }
}

// ================== 异步绘制代理 ==================
class _AsyncPointsPainterDelegate extends CustomPainter {
  final AnimationController controller;
  final bool isCacheReady;
  final Float32List cachedHitPoints;
  final Float32List cachedNoHitPoints;
  final double innerHalf;
  final int pointsPerFrame;
  final DeviceTier deviceTier;
  final bool isHighLoad;

  // 简化Paint创建 - 避免每帧重新创建
  static final _hitPaint = Paint()
    ..color = const Color(0xFF9045EE)
    ..strokeWidth = 3
    ..strokeCap = StrokeCap.round
    ..isAntiAlias = false
    ..style = PaintingStyle.stroke;

  static final _noHitPaint = Paint()
    ..color = Colors.red
    ..strokeWidth = 2
    ..strokeCap = StrokeCap.square
    ..isAntiAlias = false
    ..style = PaintingStyle.stroke;

  _AsyncPointsPainterDelegate({
    required this.controller,
    required this.isCacheReady,
    required this.cachedHitPoints,
    required this.cachedNoHitPoints,
    required this.innerHalf,
    required this.pointsPerFrame,
    required this.deviceTier,
    required this.isHighLoad,
  }) : super(repaint: controller);

  @override
  void paint(Canvas canvas, Size size) {
    if (!isCacheReady) return;

    final animationValue = controller.value;
    final totalPoints =
        (cachedHitPoints.length + cachedNoHitPoints.length) ~/ 2;
    final targetPoint = (animationValue * totalPoints).toInt();

    // 1. 绘制命中的点
    final hitPointsToDraw = targetPoint.clamp(0, cachedHitPoints.length ~/ 2);
    if (hitPointsToDraw > 0) {
      final points = _generatePoints(cachedHitPoints, 0, hitPointsToDraw);

      // 使用drawPoints替代drawCircle或Path
      canvas.drawPoints(
        ui.PointMode.points,
        points,
        _hitPaint,
      );
    }

    // 2. 绘制未命中的点
    final remaining = targetPoint - hitPointsToDraw;
    if (remaining > 0) {
      final noHitPointsToDraw =
          remaining.clamp(0, cachedNoHitPoints.length ~/ 2);
      final points = _generatePoints(cachedNoHitPoints, 0, noHitPointsToDraw);

      // 在GPU高负载时使用简化模式
      if (isHighLoad) {
        // 模式1: 使用点替代十字线
        canvas.drawPoints(
          ui.PointMode.points,
          points,
          _noHitPaint,
        );
      } else {
        // 模式2: 直接绘制十字线
        for (final center in points) {
          canvas.drawLine(
            Offset(center.dx - innerHalf, center.dy),
            Offset(center.dx + innerHalf, center.dy),
            _noHitPaint,
          );
          canvas.drawLine(
            Offset(center.dx, center.dy - innerHalf),
            Offset(center.dx, center.dy + innerHalf),
            _noHitPaint,
          );
        }
      }
    }
  }

  List<Offset> _generatePoints(Float32List points, int startIndex, int count) {
    final generated = <Offset>[];
    final start = startIndex * 2;
    final end = (startIndex + count) * 2;

    for (int i = start; i < end && i < points.length; i += 2) {
      generated.add(Offset(points[i], points[i + 1]));
    }

    return generated;
  }

  @override
  bool shouldRepaint(covariant _AsyncPointsPainterDelegate oldDelegate) {
    return oldDelegate.controller != controller ||
        oldDelegate.isCacheReady != isCacheReady ||
        !listEquals(oldDelegate.cachedHitPoints, cachedHitPoints) ||
        !listEquals(oldDelegate.cachedNoHitPoints, cachedNoHitPoints) ||
        oldDelegate.innerHalf != innerHalf ||
        oldDelegate.isHighLoad != isHighLoad;
  }
}

// // 设备性能检测类
class DevicePerformance {
  static Future<DeviceTier> determineDeviceTier() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        return androidInfo.isLowRamDevice
            ? DeviceTier.lowEnd
            : DeviceTier.highEnd;
      } else if (Platform.isIOS) {
        final iosInfo = await DeviceInfoPlugin().iosInfo;
        final identifier = iosInfo.utsname.machine;
        // return DeviceTier.midEnd;
        // 分级逻辑
        if (identifier.contains('iPhone13') ||
            identifier.contains('iPhone14') ||
            identifier.contains('iPhone15')) {
          return DeviceTier.highEnd;
        } else if (identifier.contains('iPhone11') ||
            identifier.contains('iPhone12')) {
          return DeviceTier.midEnd;
        } else {
          return DeviceTier.lowEnd;
        }
      }
      return DeviceTier.midEnd;
    } catch (e) {
      debugPrint('确定设备等级失败: $e');
      return DeviceTier.midEnd;
    }
  }
}

class SiteReportPage2 extends StatefulWidget {
  const SiteReportPage2({super.key});

  @override
  State<SiteReportPage2> createState() => _SiteReportPageState();
}

class _SiteReportPageState extends State<SiteReportPage2>
    with AutomaticKeepAliveClientMixin {
  late final double containerWidth = (ScreenUtil().screenWidth - 30.w);
  late final double containerHeight = containerWidth * 14 / 15;

  // 使用GlobalKey访问渲染对象
  final GlobalKey _painterKey = GlobalKey();

  // 使用Float32List优化点存储
  Float32List _cachedHitPoints = Float32List(0);
  Float32List _cachedNoHitPoints = Float32List(0);
  // 离屏Picture缓存
  ui.Picture? _cachedOffscreenPicture;
  ui.Image? _cachedOffscreenImage;
  bool _isImageAvailable = false; // 图像状态标志

  // 图像资源
  ui.Image? _hitMarkerImage;
  ui.Image? _noHitMarkerImage;
  bool _areMarkersLoaded = false;

  // 图像创建任务取消处理
  Completer? _imageCompleter;
  var arenaId = "";
  var isFrist = true.obs;
  var classifications = "";
  var courts = "";
  var videoStartTime = "";
  var videoEndTime = "";
  var siteReportModel = SiteReportModel().obs;
  var nameList = [
    S.current.takeCount,
    S.current.goal,
    S.current.shotRate,
    S.current.threeParts,
    S.current.underTheBasket
  ];
  var isPaintingComplete = false;
  DeviceTier deviceTier = DeviceTier.lowEnd;
  var isFrist2 = true;
  @override
  void initState() {
    super.initState();
    arenaId = Get.arguments["arenaId"];
    classifications = Get.arguments["classifications"];
    courts = Get.arguments["courts"];
    videoStartTime = Get.arguments["videoStartTime"];
    videoEndTime = Get.arguments["videoEndTime"];
    _isImageAvailable = false;
    _initializeDeviceInfo();
    _loadMarkerImages();
    getDataInfo();
  }

  // 转换数据到绘制需要的格式
  List<SiteReportModelPoints> get paintPoints {
    return (siteReportModel.value.points?.map((point) {
              return SiteReportModelPoints(
                x: point?.x ?? 0.0,
                y: point?.y ?? 0.0,
                hit: point?.hit ?? false,
              );
            }).toList() ??
            [])
        .take(800)
        .toList();
  }

//获得数据
  getDataInfo() async {
    // WxLoading.show();
    await Future.delayed(const Duration(milliseconds: 300));
    Map<String, dynamic> param = {
      "arenaId": arenaId,
      "classifications": classifications,
      "courts": courts,
      "videoStartTime": videoStartTime,
      "videoEndTime": videoEndTime,
    };
    var res = await Api().post(ApiUrl.reportNew, data: param);
    //WxLoading.dismiss();
    // log("getDataInforeportNew=${res.data}");
    if (res.isSuccessful()) {
      siteReportModel.value = SiteReportModel.fromJson(res.data);
      debugPrint('pointslength: '
          '${siteReportModel.value.points?.length}');
      log("getDataInforeportNew=3");
      siteReportModel.refresh();
      if (isFrist.value == true) {
        isFrist.value = false;
        isFrist.refresh();
      }
      // scheduleFirstCheck();
      Future.delayed(const Duration(milliseconds: 1500)).then((onValue) {
        if (mounted) return; // 防止在销毁后操作
        if (isFrist2) {
          log("getDataInforeportNew=7");
          setState(() {
            isFrist2 = false; // 自动通知监听者
          });
        }
        if (!isPaintingComplete && !isFrist.value) {
          log("getDataInforeportNew=7");
          setState(() {
            isPaintingComplete = true; // 自动通知监听者
          });
        }
      });
      Future.delayed(const Duration(milliseconds: 3500)).then((onValue) {
        if (mounted) return; // 防止在销毁后操作
        if (!isPaintingComplete && !isFrist.value) {
          log("getDataInforeportNew=7");
          setState(() {
            isPaintingComplete = true; // 自动通知监听者
          });
        }
      });
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> _initializeDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        deviceTier = await AndroidDevicePerformance.determineDeviceTier();
        if (mounted) setState(() {});
      } else {
        deviceTier = await DevicePerformance.determineDeviceTier();
        if (mounted) setState(() {});
      }
    } catch (e) {
      debugPrint('设备检测失败: $e');
    }
  }

  // 加载标记图像资源
  Future<void> _loadMarkerImages() async {
    try {
      // 加载命中标记图片
      final hitBytes = await rootBundle.load('assets/images/half_shoot2.png');
      _hitMarkerImage =
          await decodeImageFromList(hitBytes.buffer.asUint8List());

      // 加载未命中标记图片
      final noHitBytes = await rootBundle.load('assets/images/half_shoot3.png');
      _noHitMarkerImage =
          await decodeImageFromList(noHitBytes.buffer.asUint8List());

      _areMarkersLoaded = true;

      // 如果点数据已缓存，立即生成图片
      if (_cachedHitPoints.isNotEmpty || _cachedNoHitPoints.isNotEmpty) {
        _createOffscreenPicture();
      }
    } catch (e) {
      debugPrint('加载标记图像失败: $e');
    }
  }

  // 构建加载视图
  Widget buildLoad() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colours.color0F0F16,
      appBar: MyAppBar(
        title: Text(S.current.site_report_info1),
      ),
      body: Obx(() {
        return isFrist.value || !_areMarkersLoaded
            ? buildLoad()
            : SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _reportInfo(),
                    _reportInfo2(),
                    _infoWidget(context),
                  ],
                ),
              );
      }),
    );
  }

  GridView _reportInfo2() {
    return GridView.count(
        crossAxisCount: 3,
        mainAxisSpacing: 14, // Space between rows
        crossAxisSpacing: 14, // Space between columns
        shrinkWrap: true,
        childAspectRatio: 105 / 81,
        padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        children: List.generate(5, (index) {
          return Container(
            width: 105.w,
            height: 81.w,
            padding: EdgeInsets.only(left: 17.w, right: 2.w),
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: WxAssets.images.siteReportBg1.provider(),
                    fit: BoxFit.fill)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  nameList[index],
                  style: TextStyle(
                      color: Colours.color665C6E,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400),
                ),
                SizedBox(
                  height: 7.w,
                ),
                Text(
                  index == 0
                      ? "${(siteReportModel.value.shootCount ?? 0)}"
                      : index == 1
                          ? "${siteReportModel.value.hitCount ?? "0"}"
                          : index == 2
                              ? "${siteReportModel.value.shootRate ?? "0"}%"
                              : index == 3
                                  ? "${siteReportModel.value.threeHitCount ?? "0"}/${(siteReportModel.value.threeShootCount ?? 0)}"
                                  : "${siteReportModel.value.twoHitCount ?? "0"}/${(siteReportModel.value.twoShootCount ?? 0)}",
                  style: TextStyle(
                      color: Colours.white,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w400),
                ),
              ],
            ),
          );
        }));
  }

  Widget _reportInfo() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
          color: Colours.color1F1C22, borderRadius: BorderRadius.circular(8.r)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          OptimizedImage(
            imageUrl: siteReportModel.value.photo ?? "",
            width: 90.w,
            height: 124.w,
            fit: BoxFit.fill,
            radius: 8.r,
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                siteReportModel.value.title ?? "",
                style: TextStyle(
                    color: Colours.white,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w400),
              ),
              SizedBox(
                height: 10.w,
              ),
              Wrap(
                runSpacing: 7.w,
                spacing: 7.w,
                children: List.generate(
                    siteReportModel.value.labels?.length ?? 0, (index) {
                  return Container(
                    padding: EdgeInsets.only(
                        left: 10.w, right: 10.w, top: 3.w, bottom: 3.w),
                    decoration: BoxDecoration(
                        color: Colours.color2E1575,
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                            width: 0.7.w, color: Colours.color6435E9)),
                    child: Text(
                      siteReportModel.value.labels?[index] ?? "",
                      style: TextStyle(
                          color: Colours.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  );
                }),
              )
            ],
          ))
        ],
      ),
    );
  }

  Widget _infoWidget(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
                  child: Row(
                    children: [
                      WxAssets.images.selfieShot1.image(
                        height: 10.w,
                        width: 10.w,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        S.current.my_videos,
                        style: TextStyle(
                            color: Colours.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const Spacer(),
                      if (siteReportModel.value.fragments?.isNotEmpty ?? false)
                        GestureDetector(
                          onTap: () {
                            if (siteReportModel.value.id == "") {
                              WxLoading.showToast("生成报告错误");
                            } else {
                              AppPage.to(Routes.siteGoalPage, arguments: {
                                "reportId": siteReportModel.value.id,
                                "arenaId": arenaId,
                              });
                            }
                          },
                          child: Container(
                            height: 30.w,
                            width: 110.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                image: DecorationImage(
                                    image: WxAssets.images.siteReportVideo2
                                        .provider()),
                                borderRadius: BorderRadius.circular(30.r)),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                WxAssets.images.siteReportVideo.image(
                                  width: 16.w,
                                  height: 13.w,
                                  fit: BoxFit.fitWidth,
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  S.current.site_report_info2,
                                  style: TextStyles.display12
                                      .copyWith(color: Colours.white),
                                ),
                              ],
                            ),
                          ),
                        )
                    ],
                  ),
                ),
                (siteReportModel.value.fragments?.isEmpty ?? false)
                    ? Container(
                        margin: EdgeInsets.only(top: 30.w),
                        child: Column(
                          children: [
                            WxAssets.images.noVideos.image(
                              width: 120.w,
                              height: 60.w,
                            ),
                            SizedBox(height: 20.w),
                            MyText(
                              S.current.selfile_shot_info4,
                              size: 14,
                              color: Colors.white,
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                if (siteReportModel.value.id == "") {
                                  WxLoading.showToast("生成报告错误");
                                } else {
                                  AppPage.to(Routes.siteGoalPage, arguments: {
                                    "reportId": siteReportModel.value.id,
                                    "arenaId": arenaId,
                                  });
                                }
                              },
                              child: Container(
                                width: double.infinity,
                                height: 40.w,
                                alignment: Alignment.center,
                                margin: EdgeInsets.only(
                                    left: 94.w,
                                    right: 94.w,
                                    top: 30.w,
                                    bottom: 25.w),
                                decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF
                                      ],
                                      begin: Alignment.bottomLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(20.r)),
                                child: Text(
                                  S.current.player_report_tips18,
                                  style: TextStyles.regular
                                      .copyWith(color: Colours.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if ((siteReportModel.value.fragments?.length ?? 0) >
                                0)
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  AppPage.to(Routes.videoPath, arguments: {
                                    "videoPath": siteReportModel
                                        .value.fragments?[0]?.videoPath,
                                    "teamName": S.current.selfile_shot_info6,
                                  });
                                },
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    (siteReportModel.value.fragments?[0]
                                                    ?.cover ??
                                                "") !=
                                            ""
                                        ? OptimizedImage(
                                            imageUrl: siteReportModel.value
                                                    .fragments?[0]?.cover ??
                                                "",
                                            width: 165.w,
                                            height: 93.w,
                                            radius: 8.r,
                                            fit: BoxFit.fill,
                                          )
                                        : Container(
                                            width: double.infinity,
                                            height: 93.w,
                                            decoration: BoxDecoration(
                                                color: Colours.color10D8D8D8,
                                                borderRadius:
                                                    BorderRadius.circular(8.r)),
                                            alignment: Alignment.center,
                                            child:
                                                WxAssets.images.noVideos.image(
                                              width: 50.w,
                                              height: 36.w,
                                              fit: BoxFit.fill,
                                            )),
                                    if (siteReportModel
                                            .value.fragments?[0]?.shootType !=
                                        0)
                                      Positioned(
                                        left: 0.w,
                                        top: 0.w,
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: 10.w,
                                              right: 10.w,
                                              top: 5.w,
                                              bottom: 5.w),
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: (siteReportModel.value
                                                          .fragments?[0]?.hit ??
                                                      false)
                                                  ? [
                                                      Colours.color7B35ED,
                                                      Colours.colorA253EF
                                                    ]
                                                  : [
                                                      Colours.color666666,
                                                      Colours.color666666
                                                    ],
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                            ),
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8.r),
                                              bottomRight: Radius.circular(8.r),
                                            ),
                                          ),
                                          child: Text(
                                            "${siteReportModel.value.fragments?[0]?.shootType == 1 ? "两分" : siteReportModel.value.fragments?[0]?.shootType == 2 ? "三分" : siteReportModel.value.fragments?[0]?.shootType == 4 ? "罚球" : ""}${(siteReportModel.value.fragments?[0]?.hit ?? false) ? "命中" : "打铁"}",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.medium.copyWith(
                                              fontSize: 10.sp,
                                              color: Colours.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    // Positioned(
                                    //   right: 0.w,
                                    //   bottom: 0.w,
                                    //   child: Container(
                                    //     padding: EdgeInsets.only(
                                    //         left: 10.w,
                                    //         right: 10.w,
                                    //         top: 5.w,
                                    //         bottom: 5.w),
                                    //     child: Text(
                                    //       siteReportModel.value
                                    //               .fragments?[0]?.videoTime ??
                                    //           "",
                                    //       textAlign: TextAlign.right,
                                    //       style: TextStyles.medium.copyWith(
                                    //         fontSize: 11.sp,
                                    //         color: Colours.white,
                                    //       ),
                                    //     ),
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ),
                            if ((siteReportModel.value.fragments?.length ?? 0) >
                                1)
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  AppPage.to(Routes.videoPath, arguments: {
                                    "videoPath": siteReportModel
                                        .value.fragments?[1]?.videoPath,
                                    "teamName": S.current.selfile_shot_info6,
                                  });
                                },
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    (siteReportModel.value.fragments?[1]
                                                    ?.cover ??
                                                "") !=
                                            ""
                                        ? OptimizedImage(
                                            imageUrl: siteReportModel.value
                                                    .fragments?[1]?.cover ??
                                                "",
                                            width: 165.w,
                                            height: 93.w,
                                            radius: 8.r,
                                            fit: BoxFit.fill,
                                          )
                                        : Container(
                                            width: double.infinity,
                                            height: 93.w,
                                            decoration: BoxDecoration(
                                                color: Colours.color10D8D8D8,
                                                borderRadius:
                                                    BorderRadius.circular(8.r)),
                                            alignment: Alignment.center,
                                            child:
                                                WxAssets.images.noVideos.image(
                                              width: 50.w,
                                              height: 36.w,
                                              fit: BoxFit.fill,
                                            )),
                                    if (siteReportModel
                                            .value.fragments?[0]?.shootType !=
                                        0)
                                      Positioned(
                                        left: 0.w,
                                        top: 0.w,
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: 10.w,
                                              right: 10.w,
                                              top: 5.w,
                                              bottom: 5.w),
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: (siteReportModel.value
                                                          .fragments?[1]?.hit ??
                                                      false)
                                                  ? [
                                                      Colours.color7B35ED,
                                                      Colours.colorA253EF
                                                    ]
                                                  : [
                                                      Colours.color666666,
                                                      Colours.color666666
                                                    ],
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                            ),
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8.r),
                                              bottomRight: Radius.circular(8.r),
                                            ),
                                          ),
                                          child: Text(
                                            "${siteReportModel.value.fragments?[1]?.shootType == 1 ? "两分" : siteReportModel.value.fragments?[1]?.shootType == 2 ? "三分" : siteReportModel.value.fragments?[1]?.shootType == 4 ? "罚球" : ""}${(siteReportModel.value.fragments?[1]?.hit ?? false) ? "命中" : "打铁"}",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.medium.copyWith(
                                              fontSize: 10.sp,
                                              color: Colours.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    Positioned(
                                      right: 0.w,
                                      bottom: 0.w,
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            left: 10.w,
                                            right: 10.w,
                                            top: 5.w,
                                            bottom: 5.w),
                                        child: Text(
                                          siteReportModel.value.fragments?[1]
                                                  ?.videoTime ??
                                              "",
                                          textAlign: TextAlign.right,
                                          style: TextStyles.medium.copyWith(
                                            fontSize: 11.sp,
                                            color: Colours.white,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                          ],
                        ),
                      ),

                // if (deviceTier != DeviceTier.lowEnd)
                Container(
                  height: 40.w,
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
                  child: Row(
                    children: [
                      WxAssets.images.selfieShot1.image(
                        height: 10.w,
                        width: 10.w,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        S.current.selfile_shot_info1,
                        style: TextStyle(
                            color: Colours.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                //  if (deviceTier != DeviceTier.lowEnd)
                Container(
                  width: double.infinity,
                  height: containerHeight,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage("assets/images/half_shoot1.png"),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return Obx(() {
                        return RepaintBoundary(
                          key: _painterKey,
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: isPaintingComplete
                                ? _isImageAvailable &&
                                        _cachedOffscreenImage != null
                                    ? SafeImageWidget(
                                        image: _cachedOffscreenImage!,
                                      )
                                    : _buildPlaceholder()
                                : _AsyncPointsPainter(
                                    key: ValueKey(
                                        DateTime.now().millisecondsSinceEpoch),
                                    points: paintPoints,
                                    containerWidth: containerWidth / 1500,
                                    containerHeight: containerHeight / 1400,
                                    wuw: 5.w,
                                    shiw: 10.w,
                                    deviceTier: deviceTier,
                                    onComplete: () {
                                      if (mounted) {
                                        setState(
                                            () => isPaintingComplete = true);
                                      }
                                    },
                                    onCacheReady: (cache) {
                                      if (mounted) {
                                        setState(() {
                                          _cachedHitPoints = cache.hitPoints;
                                          _cachedNoHitPoints =
                                              cache.noHitPoints;

                                          // 创建离线Picture缓存
                                          _createOffscreenPicture();
                                        });
                                      }
                                    },
                                  ),
                          ),
                        );
                      });
                    },
                  ),
                ),
                SizedBox(height: 50.w)
              ],
            ),
          ),
        ],
      );
    });
  }

  // 创建离线Picture并转换为Image
  Future<void> _createOffscreenPicture() async {
    if (!_areMarkersLoaded) {
      return;
    }

    // 检查点数据
    if (_cachedHitPoints.isEmpty && _cachedNoHitPoints.isEmpty) return;

    // 取消可能正在进行的图像创建任务
    if (_imageCompleter != null && !_imageCompleter!.isCompleted) {
      _imageCompleter!.completeError("任务被新创建请求取消");
      _imageCompleter = null;
    }

    // 设置图像不可用状态
    setState(() => _isImageAvailable = false);

    // 释放旧资源
    _cachedOffscreenImage?.dispose();
    _cachedOffscreenImage = null;

    // 创建新的Completer
    _imageCompleter = Completer<ui.Image>();

    try {
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      final size = Size(containerWidth, containerHeight);

      // 使用图片代替绘制
      _drawPointsWithImageMarkers(canvas, size);

      final picture = recorder.endRecording();

      // 转换为Image - 提供更高效的GPU纹理
      final newImage =
          await picture.toImage(size.width.toInt(), size.height.toInt());

      picture.dispose();

      // 如果任务被取消或页面已卸载，直接释放图像
      if ((_imageCompleter == null || _imageCompleter!.isCompleted) ||
          !mounted) {
        newImage.dispose();
        return;
      }

      // 完成任务并更新状态
      _imageCompleter!.complete(newImage);

      setState(() {
        _cachedOffscreenImage = newImage;
        _isImageAvailable = true;
      });
    } catch (e) {
      debugPrint('创建离屏图片失败: $e');

      // 如果任务未被取消，报告错误
      if (_imageCompleter != null && !_imageCompleter!.isCompleted) {
        _imageCompleter!.completeError(e);
      }

      setState(() => _isImageAvailable = false);
    }
  }

  // 使用图片代替绘制
  void _drawPointsWithImageMarkers(Canvas canvas, Size size) {
    // 确定图片标记大小（根据设备等级优化）
    final markerSize = Size(10.w, 10.w);

    // 绘制未命中点 - 使用图片
    log("drawNoHitPoints11");
    if (_cachedNoHitPoints.isNotEmpty && _noHitMarkerImage != null) {
      final points = _convertToOffsetList(_cachedNoHitPoints);
      for (final point in points) {
        canvas.drawImageRect(
            _noHitMarkerImage!,
            Rect.fromLTWH(0, 0, _noHitMarkerImage!.width.toDouble(),
                _noHitMarkerImage!.height.toDouble()),
            Rect.fromCenter(
              center: point,
              width: markerSize.width,
              height: markerSize.height,
            ),
            Paint()..isAntiAlias = false);
      }
    }

    // 绘制命中点 - 使用图片
    if (_cachedHitPoints.isNotEmpty && _hitMarkerImage != null) {
      final points = _convertToOffsetList(_cachedHitPoints);
      for (final point in points) {
        canvas.drawImageRect(
            _hitMarkerImage!,
            Rect.fromLTWH(0, 0, _hitMarkerImage!.width.toDouble(),
                _hitMarkerImage!.height.toDouble()),
            Rect.fromCenter(
              center: point,
              width: markerSize.width,
              height: markerSize.height,
            ),
            Paint()..isAntiAlias = false);
      }
    }
  }

  // 转换Float32List为Offset列表
  List<Offset> _convertToOffsetList(Float32List points) {
    final result = <Offset>[];
    for (int i = 0; i < points.length; i += 2) {
      result.add(Offset(points[i], points[i + 1]));
    }
    return result;
  }

  // 安全的占位符
  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  @override
  void dispose() {
    imageCache.clear();
    siteReportModel.value = SiteReportModel();
    siteReportModel.value.photo = "";
    siteReportModel.refresh();
    log("dispose333");
    // 释放图片资源
    // 确保完全释放图像资源
    _hitMarkerImage?.dispose();
    _hitMarkerImage = null;

    _noHitMarkerImage?.dispose();
    _noHitMarkerImage = null;
// 添加额外的防护性代码
    if (_areMarkersLoaded) {
      // 确保从缓存中清除
      rootBundle.evict('assets/images/half_shoot2.png');
      rootBundle.evict('assets/images/half_shoot3.png');
      _areMarkersLoaded = false;
    }
    // 取消正在进行的图像创建任务
    // 更安全的Completer处理
    if (_imageCompleter != null && !_imageCompleter!.isCompleted) {
      try {
        // 提供额外的日志信息
        debugPrint('Canceling image completer task');
        _imageCompleter!.completeError(Exception('Page disposed'));
      } catch (e) {
        debugPrint('Error canceling completer: $e');
      } finally {
        _imageCompleter = null;
      }
    }
    _imageCompleter = null;

    _isImageAvailable = false;
    // 确保释放离线图片缓存
    _cachedOffscreenImage?.dispose();
    _cachedOffscreenImage = null;

    // 添加对Picture的释放
    _cachedOffscreenPicture?.dispose();
    _cachedOffscreenPicture = null;

    // 清理点数据
    _cachedHitPoints = Float32List(0);
    _cachedNoHitPoints = Float32List(0);

    // 释放渲染上下文
    final renderObject = _painterKey.currentContext?.findRenderObject();
    if (renderObject != null && renderObject is RenderObject) {
      renderObject.dispose();
    }

    // 清理图像缓存
    PaintingBinding.instance.imageCache.clear();
    _releaseRenderObject();
    // 释放GPU资源
    _releaseGpuResources();

    super.dispose();
  }

  void _releaseRenderObject() {
    try {
      final context = _painterKey.currentContext;
      if (context != null) {
        final renderObject = context.findRenderObject();
        if (renderObject != null) {
          // 更精确地释放资源
          renderObject.dispose();
          // 重置渲染树引用
          (context as Element).unmount();
        }
      }
    } catch (e) {
      debugPrint('Error releasing render object: $e');
    }
  }

  // 显式释放GPU资源
  void _releaseGpuResources() {
    // 适用于Android的GPU资源释放
    if (Platform.isAndroid) {
      try {
        const channel = MethodChannel('performance_info');
        channel.invokeMethod('releaseResources');
      } catch (e) {
        debugPrint('释放GPU资源失败: $e');
      }
    }
  }

  @override
  bool get wantKeepAlive => false;
}

class SiteReportRenderer {
  static int _batchIndex = 0;
  static List<Float32List> _pointGroups = [];
  static List<ui.Image?> _images = [];
  static Size _markerSize = Size.zero;
  static Function? _onProgress;
  static Function? _onComplete;

  // 开始批量渲染
  static void startBatchRendering({
    required List<Float32List> pointGroups,
    required List<ui.Image?> images,
    required Size markerSize,
    required VoidCallback onProgress,
    required VoidCallback onComplete,
  }) {
    _batchIndex = 0;
    _pointGroups = pointGroups;
    _images = images;
    _markerSize = markerSize;
    _onProgress = onProgress;
    _onComplete = onComplete;

    // 开始渲染循环
    _renderNextBatch();
  }

  // 渲染下一批
  static void _renderNextBatch() {
    // 使用SchedulerBinding添加一个帧结束回调
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // 确保在下一帧渲染
      WidgetsBinding.instance.scheduleFrameCallback((_) {
        _processCurrentBatch();
        _renderNextBatch();
      });
    });
  }

  // 处理当前批次
  static void _processCurrentBatch() {
    if (_batchIndex >= _pointGroups.length) {
      // 所有批次完成
      _onComplete?.call();
      return;
    }

    // 通知进度更新
    _onProgress?.call();

    // 绘制当前批次（这里应该将当前批次的绘制命令添加到画布）
    // 实际代码中，这里会调用canvas.drawImageRect方法

    // 前进到下一批
    _batchIndex++;
  }

  // 渲染当前批次的点
  static void renderCurrentBatch(Canvas canvas) {
    if (_batchIndex >= _pointGroups.length) return;

    final points = _pointGroups[_batchIndex];
    final image = _images[_batchIndex];

    if (image == null || points.isEmpty) return;

    // 绘制当前批次的所有点
    for (int i = 0; i < points.length; i += 2) {
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        Rect.fromCenter(
          center: Offset(points[i], points[i + 1]),
          width: _markerSize.width,
          height: _markerSize.height,
        ),
        Paint()..isAntiAlias = false,
      );
    }
  }

  // 取消渲染
  static void cancelRendering() {
    _batchIndex = 0;
    _pointGroups.clear();
    _images.clear();
    _onProgress = null;
    _onComplete = null;
  }
}

// 添加全局隔离控制器
class IsolationController {
  static final Map<Isolate, bool> _activeIsolates = {};

  static void run(void Function(dynamic) function, dynamic message) {
    compute((msg) {
      final result = function(msg);
      return result;
    }, message)
        .then((_) {
      _activeIsolates.remove(Isolate.current);
    });
  }

  static void disposeAll() {
    _activeIsolates.keys.forEach((iso) => iso.kill());
    _activeIsolates.clear();
  }
}
