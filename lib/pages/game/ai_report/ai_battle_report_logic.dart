import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AiBattleReportLogic extends GetxController {
  var status = 0.obs; // 0 待生成 1 生成中 2 已生成
  var htmlStr = ''.obs;
  final list = [
    {
      'name': 'AI深度解读：',
      'desc': '基于专业比赛数据，瞬间生成专业战报。',
      'icon': WxAssets.images.aiReportIcon1.image(width: 16.w, height: 16.w)
    },
    {
      'name': '虎扑级毒舌点评：',
      'desc': '化身“冷面笑匠”，用最幽默犀利的角度吐槽球队表现、球员高光（&下饭）时刻！',
      'icon': WxAssets.images.aiReportIcon2.image(width: 16.w, height: 16.w)
    },
    {
      'name': '抽象派观赛体验：',
      'desc': '不是干巴巴的数据堆砌，而是让你拍案叫绝的“人类迷惑篮球行为大赏”！',
      'icon': WxAssets.images.aiReportIcon3.image(width: 16.w, height: 16.w)
    },
    {
      'name': '赛后欢乐源泉：',
      'desc': '比赛结束，吐槽开始！分享战报，和球友一起“开会”更有趣！',
      'icon': WxAssets.images.aiReportIcon4.image(width: 16.w, height: 16.w)
    },
  ];
  late String matchId;
  var webController = WebViewController();

  /// 是否正在加载数据
  var init = false.obs;
  @override
  void onInit() {
    super.onInit();
    matchId = Get.arguments;
    late final PlatformWebViewControllerCreationParams params;

    params = const PlatformWebViewControllerCreationParams();
    webController = WebViewController.fromPlatformCreationParams(params)
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            // 页面加载完成后注入禁止缩放的JS
            _injectZoomDisableScript();
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..setNavigationDelegate(NavigationDelegate(
        onPageFinished: (url) async {
          // 尝试在页面加载后注入 viewport 禁用缩放（作为兜底）
          try {
            await webController.runJavaScript(
                "(function(){var h=document.getElementsByTagName('head')[0];if(!h)return;var m=document.createElement('meta');m.name='viewport';m.content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';h.appendChild(m);})();");
          } catch (e) {
            // ignore injection errors
          }
        },
      ));
    webController.enableZoom(false);
    // 用于测试的HTML内容
    // _setTestHtmlContent();
    getAIReport(); // 注释掉API调用，使用测试内容
  }

  // 注入JavaScript禁止缩放
  Future<void> _injectZoomDisableScript() async {
    await webController.runJavaScript('''
      // 禁用双击缩放
      document.addEventListener('dblclick', function(e) {
        e.preventDefault();
      }, false);
      
      // 禁用双指缩放
      document.addEventListener('touchstart', function(event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, { passive: false });
      
      // 禁用双指缩放手势
      document.addEventListener('touchmove', function(event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, { passive: false });
      
      // 禁用手势缩放
      document.addEventListener('gesturestart', function(e) {
        e.preventDefault();
      });
    ''');
  }

  Future<void> getAIReport() async {
    cc.log("Getting AI Report for matchId: $matchId");
    var res = await Api().get(ApiUrl.getAIReport(matchId));
    init.value = true;
    if (res.isSuccessful()) {
      cc.log("API Response status: ${res.data['status']}");
      status.value = res.data['status'] as int;
      cc.log("Current status: ${status.value}");
      htmlStr.value = res.data['content'] ?? '';
      if (status.value == 2 && htmlStr.value.isNotEmpty) {
        webController.loadHtmlString(htmlStr.value);
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> subscribeAIReport() async {
    var res = await Api().post(ApiUrl.subscribeAIReport, data: {
      "subType": 6,
      "associationId": matchId,
      "templateId": "wU86U6LGJGhAaZPbI7qSBuIOEcHWPxhblIo070Nl0vU"
    });
    if (res.isSuccessful()) {
      status.value = 1;
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
