import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../gen/assets.gen.dart';
import 'models/game_model.dart';

class GameListItem extends StatelessWidget {
  final Matches item;
  final int type;
  final Function tap;
  const GameListItem(
      {super.key, required this.item, required this.type, required this.tap});

  @override
  Widget build(BuildContext context) {
    final status =
        item.status == 0 ? "未开始" : (item.status == 1 ? "比赛中" : "已结束");
    final statusColor = item.status == 1 ? Colours.colorA44EFF : Colours.white;
    final markIcon = item.markStatus == 0
        ? WxAssets.images.icDfx
        : (item.markStatus == 1
            ? WxAssets.images.icFxz
            : WxAssets.images.icYsc);
    final mark =
        item.markStatus == 0 ? "待分析" : (item.markStatus == 1 ? "分析中" : "已生成");
    final markColor =
        item.markStatus == 2 ? Colours.white : Colours.color6F6F84;
    return GestureDetector(
      onTap: () {
        if (item.status == 0 || item.status == 1) {
          AppPage.to(Routes.comparisonPage, arguments: {
            "matchId": item.matchId, // "10000103", //
          });
        } else {
          // if (item.markStatus == 2) {
          //   AppPage.to(Routes.gameDetailsPage, arguments: item.matchId);
          // } else {
          //   var title = '比赛未开始';
          //   if (item.markStatus == 0) {
          //     // title = '比赛待分析';
          //     // Get.dialog(CustomAlertDialog(
          //     //   title: title,
          //     //   content: '请在比赛结束且AI分析完成后\n查看比赛数据报告！',
          //     //   hideCancel: true,
          //     // ));
          //     AppPage.to(Routes.comparisonPage, arguments: {
          //       "matchId": item.matchId, // "10000103", //
          //     });
          //   } else if (item.markStatus == 1) {
          //     // title = 'AI分析中';
          //     // Get.dialog(CustomAlertDialog(
          //     //   title: title,
          //     //   content: '请在比赛结束且AI分析完成后\n查看比赛数据报告！',
          //     //   hideCancel: true,
          //     // ));
          //     AppPage.to(Routes.comparisonPage, arguments: {
          //       "matchId": item.matchId, // "10000103", //
          //     });
          //   } else {
          //     // AppPage.to(Routes.comparisonPage, arguments: {
          //     //   "matchId": item.matchId, // "10000103", //
          //     // });
          //   }
          // }
          AppPage.to(Routes.gameDetailsPage, arguments: item.matchId);
        }
      },
      child: type == 1 && item.status == 0
          ? Container(
              padding: EdgeInsets.only(
                  left: 15.w, right: 15.w, top: 12.w, bottom: 13.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.w),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        "${item.matchDateStr.length >= 10 ? item.matchDateStr.substring(5) : item.matchDateStr} ${item.matchDateWeek} ${item.matchTimeStr}",
                        style: TextStyles.display12,
                      ),
                      const Expanded(child: SizedBox.shrink()),
                      Text(
                        item.courts.join('、'),
                        style: TextStyles.display12,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15.w,
                  ),
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: Text(
                                      item.leftTeamName,
                                      textAlign: TextAlign.end,
                                      style: TextStyles.display14,
                                      maxLines: 2,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  ClipRRect(
                                      borderRadius: BorderRadius.circular(11.w),
                                      child: CachedNetworkImage(
                                        imageUrl: item.leftTeamLogo,
                                        width: 22.w,
                                        height: 22.w,
                                        fit: BoxFit.fill,
                                      )),
                                  SizedBox(
                                    width: 15.w,
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15.w,
                              ),
                              Center(
                                child: GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    tap(0);
                                  },
                                  child: Container(
                                    height: 23.w,
                                    width: 107.w,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: Colours.white,
                                        gradient: const LinearGradient(
                                            colors: [
                                              Colours.colorFFECC1,
                                              Colours.colorE7CEFF,
                                              Colours.colorD1EAFF
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight),
                                        borderRadius:
                                            BorderRadius.circular(22.r)),
                                    child: RichText(
                                      text: TextSpan(
                                          text: "立即获取",
                                          style: TextStyle(
                                              color: Colours.color000000,
                                              fontSize: 10.sp,
                                              height: 0,
                                              fontWeight: FontWeight.normal),
                                          children: <InlineSpan>[
                                            TextSpan(
                                                text: "（享优惠）",
                                                style: TextStyle(
                                                    color: Colours.color000000,
                                                    fontSize: 10.sp,
                                                    height: 0,
                                                    fontWeight:
                                                        FontWeight.normal)),
                                          ]),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                            margin: EdgeInsets.only(
                              left: 3.w,
                              right: 3.w,
                            ),
                            padding: EdgeInsets.only(
                                left: 6.w, right: 6.w, bottom: 5.w, top: 5.w),
                            decoration: BoxDecoration(
                                color: Colours.color2F2F3B,
                                borderRadius: BorderRadius.circular(3.r)),
                            child: Text(
                              status,
                              style: TextStyles.regular.copyWith(
                                fontSize: 12.sp,
                              ),
                            )),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 15.w,
                                  ),
                                  ClipRRect(
                                      borderRadius: BorderRadius.circular(11.w),
                                      child: CachedNetworkImage(
                                        imageUrl: item.rightTeamLogo,
                                        width: 22.w,
                                        height: 22.w,
                                        fit: BoxFit.fill,
                                      )),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Expanded(
                                    child: Text(
                                      item.rightTeamName,
                                      textAlign: TextAlign.start,
                                      style: TextStyles.display14,
                                      maxLines: 2,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15.w,
                              ),
                              Center(
                                child: GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    tap(1);
                                  },
                                  child: Container(
                                    height: 23.w,
                                    width: 107.w,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: Colours.white,
                                        gradient: const LinearGradient(
                                            colors: [
                                              Colours.colorFFECC1,
                                              Colours.colorE7CEFF,
                                              Colours.colorD1EAFF
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight),
                                        borderRadius:
                                            BorderRadius.circular(22.r)),
                                    child: Text(
                                      S.current.game_report1,
                                      style: TextStyle(
                                          color: Colours.color000000,
                                          fontSize: 10.sp,
                                          height: 0,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          : Container(
              padding: EdgeInsets.only(
                  left: 15.w, right: 15.w, top: 12.w, bottom: 13.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.w),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        "${item.matchDateStr.length >= 10 ? item.matchDateStr.substring(5) : item.matchDateStr} ${item.matchDateWeek} ${item.matchTimeStr}",
                        style: TextStyles.display12,
                      ),
                      const Expanded(child: SizedBox.shrink()),
                      Text(
                        item.courts.join('、'),
                        style: TextStyles.display12,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15.w,
                  ),
                  IntrinsicHeight(
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              Column(
                                children: [
                                  Row(
                                    children: [
                                      ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(11.w),
                                          child: CachedNetworkImage(
                                            imageUrl: item.leftTeamLogo,
                                            width: 22.w,
                                            height: 22.w,
                                            fit: BoxFit.fill,
                                          )),
                                      SizedBox(
                                        width: 10.w,
                                      ),
                                      Text(
                                        item.leftTeamName,
                                        style: TextStyles.display14,
                                      ),
                                      SizedBox(
                                        width: 10.w,
                                      ),
                                      Expanded(
                                          child: Text(
                                        '${item.leftScore}',
                                        style: TextStyles.titleMedium18,
                                        textAlign: TextAlign.right,
                                      )),
                                      SizedBox(
                                        width: 23.w,
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Row(
                                    children: [
                                      ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(11.w),
                                          child: CachedNetworkImage(
                                            imageUrl: item.rightTeamLogo,
                                            width: 22.w,
                                            height: 22.w,
                                            fit: BoxFit.fill,
                                          )),
                                      SizedBox(
                                        width: 10.w,
                                      ),
                                      Text(
                                        item.rightTeamName,
                                        style: TextStyles.display14,
                                      ),
                                      SizedBox(
                                        width: 10.w,
                                      ),
                                      Expanded(
                                          child: Text(
                                        '${item.rightScore}',
                                        style: TextStyles.titleMedium18,
                                        textAlign: TextAlign.right,
                                      )),
                                      SizedBox(
                                        width: 23.w,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 1,
                          height: double.infinity,
                          color: Colours.color2F2F3B,
                        ),
                        Container(
                          width: 87.w,
                          height: double.infinity,
                          alignment: Alignment.center,
                          child: item.status == 0
                              ? Text(
                                  status,
                                  style: TextStyles.titleSemiBold16,
                                )
                              : Column(
                                  children: [
                                    SizedBox(
                                      height: 6.w,
                                    ),
                                    Text(
                                      status,
                                      style: TextStyles.titleSemiBold16
                                          .copyWith(color: statusColor),
                                    ),
                                    SizedBox(
                                      height: 12.w,
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 6.w),
                                      width: 56.w,
                                      height: 20.w,
                                      decoration: BoxDecoration(
                                        color: Colours.color282735,
                                        borderRadius:
                                            BorderRadius.circular(3.w),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          markIcon.image(
                                              width: 10.w, height: 10.w),
                                          Text(
                                            mark,
                                            style: TextStyles.display12
                                                .copyWith(
                                                    fontSize: 10.w,
                                                    color: markColor),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
