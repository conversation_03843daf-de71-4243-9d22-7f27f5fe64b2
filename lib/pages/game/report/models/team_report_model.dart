import 'package:json_annotation/json_annotation.dart';

import '../../details/models/matches_model.dart';

part 'team_report_model.g.dart';


@JsonSerializable()
class TeamReportModel extends Object {

  @Json<PERSON><PERSON>(name: 'videoPath')
  String videoPath;

  @Json<PERSON><PERSON>(name: 'videoCover')
  String videoCover;

  @Json<PERSON>ey(name: 'teamId')
  String teamId;

  @JsonKey(name: 'matchStartTime')
  String matchStartTime;

  @Json<PERSON>ey(name: 'teamRank')
  int teamRank;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'winCount')
  int winCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'loseCount')
  int loseCount;

  @J<PERSON><PERSON><PERSON>(name: 'winningStreak')
  int winningStreak;

  @JsonKey(name: 'arenaId')
  int arenaId;

  @<PERSON>son<PERSON>ey(name: 'teamName')
  String teamName;

  @JsonKey(name: 'logo')
  String logo;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'score')
  int score;

  @<PERSON><PERSON><PERSON>ey(name: 'memberCount')
  int memberCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'matchCount')
  int matchCount;

  @<PERSON>son<PERSON><PERSON>(name: 'teamBest')
  TeamBest teamBest;

  @<PERSON>sonKey(name: 'locked')
  int locked;

  bool get locking => locked == 1;

  @JsonKey(name: 'unlockedPlayerCount')
  int? unlockedPlayerCount;

  @JsonKey(name: 'teamScoreDetail')
  TeamScoreDetail teamScoreDetail;

  @JsonKey(name: 'matchTeamPlayerSummary')
  List<MatchTeamPlayerSummary> matchTeamPlayerSummary;

  @JsonKey(name: 'joined')
  bool joined;

  TeamReportModel(this.videoPath,this.videoCover,this.teamId,this.matchStartTime,this.teamRank,this.winCount,this.loseCount,this.winningStreak,this.arenaId,this.teamName,this.logo,this.score,this.memberCount,this.matchCount,this.teamBest,this.locked,this.teamScoreDetail,this.matchTeamPlayerSummary,this.joined,this.unlockedPlayerCount);

  factory TeamReportModel.fromJson(Map<String, dynamic> srcJson) => _$TeamReportModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamReportModelToJson(this);

}


@JsonSerializable()
class TeamBest extends Object {

  @JsonKey(name: 'scoreKing')
  ScoreKing scoreKing;

  @JsonKey(name: 'threePointKing')
  ScoreKing threePointKing;

  @JsonKey(name: 'freeThrowKing')
  ScoreKing freeThrowKing;

  @JsonKey(name: 'assistKing')
  ScoreKing assistKing;

  @JsonKey(name: 'reboundKing')
  ScoreKing reboundKing;

  @JsonKey(name: 'mvp')
  ScoreKing mvp;

  TeamBest(this.scoreKing,this.threePointKing,this.freeThrowKing,this.assistKing,this.reboundKing,this.mvp,);

  factory TeamBest.fromJson(Map<String, dynamic> srcJson) => _$TeamBestFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamBestToJson(this);

}

@JsonSerializable()
class TeamScoreDetail extends Object {

  @JsonKey(name: 'totalScore')
  int totalScore;

  @JsonKey(name: 'markedScore')
  int markedScore;

  @JsonKey(name: 'reboundCount')
  int reboundCount;

  @JsonKey(name: 'offensiveReboundCount')
  int offensiveReboundCount;

  @JsonKey(name: 'defensiveReboundCount')
  int defensiveReboundCount;

  @JsonKey(name: 'assistCount')
  int assistCount;

  @JsonKey(name: 'shootCount')
  int shootCount;

  @JsonKey(name: 'shootHit')
  int shootHit;

  @JsonKey(name: 'shootRate')
  String shootRate;

  @JsonKey(name: 'threePointShootCount')
  int threePointShootCount;

  @JsonKey(name: 'threePointShootHit')
  int threePointShootHit;

  @JsonKey(name: 'threePointShootRate')
  String threePointShootRate;

  @JsonKey(name: 'twoPointShootCount')
  int twoPointShootCount;

  @JsonKey(name: 'twoPointShootHit')
  int twoPointShootHit;

  @JsonKey(name: 'twoPointShootRate')
  String twoPointShootRate;

  @JsonKey(name: 'freeThrowShootCount')
  int freeThrowShootCount;

  @JsonKey(name: 'freeThrowShootHit')
  int freeThrowShootHit;

  @JsonKey(name: 'freeThrowShootRate')
  String freeThrowShootRate;

  TeamScoreDetail(this.totalScore,this.markedScore,this.reboundCount,this.offensiveReboundCount,this.defensiveReboundCount,this.assistCount,this.shootCount,this.shootHit,this.shootRate,this.threePointShootCount,this.threePointShootHit,this.threePointShootRate,this.twoPointShootCount,this.twoPointShootHit,this.twoPointShootRate,this.freeThrowShootCount,this.freeThrowShootHit,this.freeThrowShootRate,);

  factory TeamScoreDetail.fromJson(Map<String, dynamic> srcJson) => _$TeamScoreDetailFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamScoreDetailToJson(this);

}


@JsonSerializable()
class MatchTeamPlayerSummary extends Object {

  @JsonKey(name: 'playerId')
  String playerId;

  @JsonKey(name: 'number')
  String number;

  @JsonKey(name: 'photo')
  String photo;

  @JsonKey(name: 'score')
  int score;

  @JsonKey(name: 'shootCount')
  int shootCount;

  @JsonKey(name: 'shootHit')
  int shootHit;

  @JsonKey(name: 'shootRate')
  String shootRate;

  @JsonKey(name: 'shootFormat')
  String shootFormat;

  @JsonKey(name: 'threePointShootCount')
  int threePointShootCount;

  @JsonKey(name: 'threePointShootHit')
  int threePointShootHit;

  @JsonKey(name: 'threePointShootRate')
  String threePointShootRate;

  @JsonKey(name: 'threePointShootFormat')
  String threePointShootFormat;

  @JsonKey(name: 'twoPointShootCount')
  int twoPointShootCount;

  @JsonKey(name: 'twoPointShootHit')
  int twoPointShootHit;

  @JsonKey(name: 'twoPointShootRate')
  String twoPointShootRate;

  @JsonKey(name: 'twoPointShootFormat')
  String twoPointShootFormat;

  @JsonKey(name: 'midRangeShootCount')
  int midRangeShootCount;

  @JsonKey(name: 'midRangeShootHit')
  int midRangeShootHit;

  @JsonKey(name: 'midRangeShootRate')
  String midRangeShootRate;

  @JsonKey(name: 'midRangeShootFormat')
  String midRangeShootFormat;

  @JsonKey(name: 'layupShootCount')
  int layupShootCount;

  @JsonKey(name: 'layupShootHit')
  int layupShootHit;

  @JsonKey(name: 'layupShootRate')
  String layupShootRate;

  @JsonKey(name: 'layupShootFormat')
  String layupShootFormat;

  @JsonKey(name: 'freeThrowShootCount')
  int freeThrowShootCount;

  @JsonKey(name: 'freeThrowShootHit')
  int freeThrowShootHit;

  @JsonKey(name: 'freeThrowShootRate')
  String freeThrowShootRate;

  @JsonKey(name: 'freeThrowShootFormat')
  String freeThrowShootFormat;

  @JsonKey(name: 'dunkShootCount')
  int dunkShootCount;

  @JsonKey(name: 'dunkShootHit')
  int dunkShootHit;

  @JsonKey(name: 'dunkShootRate')
  String dunkShootRate;

  @JsonKey(name: 'dunkShootFormat')
  String dunkShootFormat;

  @JsonKey(name: 'reboundCount')
  int reboundCount;

  @JsonKey(name: 'assistCount')
  int assistCount;

  @JsonKey(name: 'offensiveReboundCount')
  int offensiveReboundCount;

  @JsonKey(name: 'defensiveReboundCount')
  int defensiveReboundCount;

  @JsonKey(name: 'efg')
  String efg;

  @JsonKey(name: 'ts')
  String ts;

  MatchTeamPlayerSummary(this.playerId,this.number,this.photo,this.score,this.shootCount,this.shootHit,this.shootRate,this.shootFormat,this.threePointShootCount,this.threePointShootHit,this.threePointShootRate,this.threePointShootFormat,this.twoPointShootCount,this.twoPointShootHit,this.twoPointShootRate,this.twoPointShootFormat,this.midRangeShootCount,this.midRangeShootHit,this.midRangeShootRate,this.midRangeShootFormat,this.layupShootCount,this.layupShootHit,this.layupShootRate,this.layupShootFormat,this.freeThrowShootCount,this.freeThrowShootHit,this.freeThrowShootRate,this.freeThrowShootFormat,this.dunkShootCount,this.dunkShootHit,this.dunkShootRate,this.dunkShootFormat,this.reboundCount,this.assistCount,this.offensiveReboundCount,this.defensiveReboundCount,this.efg,this.ts,);

  factory MatchTeamPlayerSummary.fromJson(Map<String, dynamic> srcJson) => _$MatchTeamPlayerSummaryFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MatchTeamPlayerSummaryToJson(this);

}


