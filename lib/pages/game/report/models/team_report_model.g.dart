// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_report_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TeamReportModel _$TeamReportModelFromJson(Map<String, dynamic> json) =>
    TeamReportModel(
      json['videoPath'] as String,
      json['highlightVideoId'] as int,
      json['videoCover'] as String,
      json['teamId'] as String,
      json['matchStartTime'] as String,
      json['teamRank'] as int,
      json['winCount'] as int,
      json['loseCount'] as int,
      json['winningStreak'] as int,
      json['arenaId'] as int,
      json['teamName'] as String,
      json['logo'] as String,
      json['score'] as int,
      json['memberCount'] as int,
      json['matchCount'] as int,
      TeamBest.fromJson(json['teamBest'] as Map<String, dynamic>),
      json['locked'] as int,
      TeamScoreDetail.fromJson(json['teamScoreDetail'] as Map<String, dynamic>),
      (json['matchTeamPlayerSummary'] as List<dynamic>)
          .map(
              (e) => MatchTeamPlayerSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['joined'] as bool,
      json['unlockedPlayerCount'] as int?,
    );

Map<String, dynamic> _$TeamReportModelToJson(TeamReportModel instance) =>
    <String, dynamic>{
      'videoPath': instance.videoPath,
      'highlightVideoId': instance.highlightVideoId,
      'videoCover': instance.videoCover,
      'teamId': instance.teamId,
      'matchStartTime': instance.matchStartTime,
      'teamRank': instance.teamRank,
      'winCount': instance.winCount,
      'loseCount': instance.loseCount,
      'winningStreak': instance.winningStreak,
      'arenaId': instance.arenaId,
      'teamName': instance.teamName,
      'logo': instance.logo,
      'score': instance.score,
      'memberCount': instance.memberCount,
      'matchCount': instance.matchCount,
      'teamBest': instance.teamBest,
      'locked': instance.locked,
      'unlockedPlayerCount': instance.unlockedPlayerCount,
      'teamScoreDetail': instance.teamScoreDetail,
      'matchTeamPlayerSummary': instance.matchTeamPlayerSummary,
      'joined': instance.joined,
    };

TeamBest _$TeamBestFromJson(Map<String, dynamic> json) => TeamBest(
      ScoreKing.fromJson(json['scoreKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['threePointKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['freeThrowKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['assistKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['reboundKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['mvp'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TeamBestToJson(TeamBest instance) => <String, dynamic>{
      'scoreKing': instance.scoreKing,
      'threePointKing': instance.threePointKing,
      'freeThrowKing': instance.freeThrowKing,
      'assistKing': instance.assistKing,
      'reboundKing': instance.reboundKing,
      'mvp': instance.mvp,
    };

TeamScoreDetail _$TeamScoreDetailFromJson(Map<String, dynamic> json) =>
    TeamScoreDetail(
      json['totalScore'] as int,
      json['markedScore'] as int,
      json['reboundCount'] as int,
      json['offensiveReboundCount'] as int,
      json['defensiveReboundCount'] as int,
      json['assistCount'] as int,
      json['shootCount'] as int,
      json['shootHit'] as int,
      json['shootRate'] as String,
      json['threePointShootCount'] as int,
      json['threePointShootHit'] as int,
      json['threePointShootRate'] as String,
      json['twoPointShootCount'] as int,
      json['twoPointShootHit'] as int,
      json['twoPointShootRate'] as String,
      json['freeThrowShootCount'] as int,
      json['freeThrowShootHit'] as int,
      json['freeThrowShootRate'] as String,
    );

Map<String, dynamic> _$TeamScoreDetailToJson(TeamScoreDetail instance) =>
    <String, dynamic>{
      'totalScore': instance.totalScore,
      'markedScore': instance.markedScore,
      'reboundCount': instance.reboundCount,
      'offensiveReboundCount': instance.offensiveReboundCount,
      'defensiveReboundCount': instance.defensiveReboundCount,
      'assistCount': instance.assistCount,
      'shootCount': instance.shootCount,
      'shootHit': instance.shootHit,
      'shootRate': instance.shootRate,
      'threePointShootCount': instance.threePointShootCount,
      'threePointShootHit': instance.threePointShootHit,
      'threePointShootRate': instance.threePointShootRate,
      'twoPointShootCount': instance.twoPointShootCount,
      'twoPointShootHit': instance.twoPointShootHit,
      'twoPointShootRate': instance.twoPointShootRate,
      'freeThrowShootCount': instance.freeThrowShootCount,
      'freeThrowShootHit': instance.freeThrowShootHit,
      'freeThrowShootRate': instance.freeThrowShootRate,
    };

MatchTeamPlayerSummary _$MatchTeamPlayerSummaryFromJson(
        Map<String, dynamic> json) =>
    MatchTeamPlayerSummary(
      json['playerId'] as String,
      json['number'] as String,
      json['photo'] as String,
      json['score'] as int,
      json['shootCount'] as int,
      json['shootHit'] as int,
      json['shootRate'] as String,
      json['shootFormat'] as String,
      json['threePointShootCount'] as int,
      json['threePointShootHit'] as int,
      json['threePointShootRate'] as String,
      json['threePointShootFormat'] as String,
      json['twoPointShootCount'] as int,
      json['twoPointShootHit'] as int,
      json['twoPointShootRate'] as String,
      json['twoPointShootFormat'] as String,
      json['midRangeShootCount'] as int,
      json['midRangeShootHit'] as int,
      json['midRangeShootRate'] as String,
      json['midRangeShootFormat'] as String,
      json['layupShootCount'] as int,
      json['layupShootHit'] as int,
      json['layupShootRate'] as String,
      json['layupShootFormat'] as String,
      json['freeThrowShootCount'] as int,
      json['freeThrowShootHit'] as int,
      json['freeThrowShootRate'] as String,
      json['freeThrowShootFormat'] as String,
      json['dunkShootCount'] as int,
      json['dunkShootHit'] as int,
      json['dunkShootRate'] as String,
      json['dunkShootFormat'] as String,
      json['reboundCount'] as int,
      json['assistCount'] as int,
      json['offensiveReboundCount'] as int,
      json['defensiveReboundCount'] as int,
      json['efg'] as String,
      json['ts'] as String,
    );

Map<String, dynamic> _$MatchTeamPlayerSummaryToJson(
        MatchTeamPlayerSummary instance) =>
    <String, dynamic>{
      'playerId': instance.playerId,
      'number': instance.number,
      'photo': instance.photo,
      'score': instance.score,
      'shootCount': instance.shootCount,
      'shootHit': instance.shootHit,
      'shootRate': instance.shootRate,
      'shootFormat': instance.shootFormat,
      'threePointShootCount': instance.threePointShootCount,
      'threePointShootHit': instance.threePointShootHit,
      'threePointShootRate': instance.threePointShootRate,
      'threePointShootFormat': instance.threePointShootFormat,
      'twoPointShootCount': instance.twoPointShootCount,
      'twoPointShootHit': instance.twoPointShootHit,
      'twoPointShootRate': instance.twoPointShootRate,
      'twoPointShootFormat': instance.twoPointShootFormat,
      'midRangeShootCount': instance.midRangeShootCount,
      'midRangeShootHit': instance.midRangeShootHit,
      'midRangeShootRate': instance.midRangeShootRate,
      'midRangeShootFormat': instance.midRangeShootFormat,
      'layupShootCount': instance.layupShootCount,
      'layupShootHit': instance.layupShootHit,
      'layupShootRate': instance.layupShootRate,
      'layupShootFormat': instance.layupShootFormat,
      'freeThrowShootCount': instance.freeThrowShootCount,
      'freeThrowShootHit': instance.freeThrowShootHit,
      'freeThrowShootRate': instance.freeThrowShootRate,
      'freeThrowShootFormat': instance.freeThrowShootFormat,
      'dunkShootCount': instance.dunkShootCount,
      'dunkShootHit': instance.dunkShootHit,
      'dunkShootRate': instance.dunkShootRate,
      'dunkShootFormat': instance.dunkShootFormat,
      'reboundCount': instance.reboundCount,
      'assistCount': instance.assistCount,
      'offensiveReboundCount': instance.offensiveReboundCount,
      'defensiveReboundCount': instance.defensiveReboundCount,
      'efg': instance.efg,
      'ts': instance.ts,
    };
