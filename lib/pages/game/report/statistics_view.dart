import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../generated/l10n.dart';
import '../details/analyze_item_view.dart';
import 'logic.dart';

class StatisticsView extends StatelessWidget {
  StatisticsView({super.key});
  final logic = Get.find<TeamReportLogic>();
  final state = Get.find<TeamReportLogic>().state;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _statistics(context),
          _mvp(context),
          _best(context),
          _sectionScore(context),
          _analyze(context),
        ],
      ),
    );
  }

  Widget _statistics(BuildContext context) {
    return Container(
      // height: 55.w,
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(12.r)),
      child: GridView.builder(
          scrollDirection: Axis.vertical,
          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
          shrinkWrap: true,
          physics:
              const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 15,
            mainAxisSpacing: 20,
            childAspectRatio: 101 / 52,
          ),
          padding: EdgeInsets.only(bottom: 0.w),
          itemCount: logic.playerDatalist.length,
          itemBuilder: (context, index) {
            return Obx(() {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {},
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(logic.playerDatalist[index]["data"] ?? "0",
                        textAlign: TextAlign.right,
                        style: GoogleFonts.oswald(
                            fontWeight: AppFontWeight.medium(),
                            fontSize: 18.sp,
                            color: Colours.white)),
                    const Spacer(),
                    Text(
                      logic.playerDatalist[index]["name"] ?? "",
                      textAlign: TextAlign.right,
                      style: TextStyles.medium.copyWith(
                          fontSize: 14.sp, color: Colours.color5C5C6E),
                    ),
                  ],
                ),
              );
            });
          }),
    );
  }

  Widget _mvp(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '本场MVP',
          style: TextStyles.regular.copyWith(fontSize: 16.sp),
        ).marginOnly(top: 30.w, bottom: 20.w),
        Container(
          height: 159.w,
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(16.w),
            border: Border.all(color: Colours.color2F2F3B, width: 2),
          ),
          child: Obx(
            () {
              final model = state.model.value.teamBest.mvp;
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => logic.unlockPlayer(model.playerId, model.locking),
                child: Stack(
                  children: [
                    Positioned(
                        top: 22.w,
                        right: 18.w,
                        child: WxAssets.images.icTeamMvp.image(
                            width: 77.w, height: 40.w, fit: BoxFit.fill)),
                    Positioned(
                        bottom: 14.w,
                        right: 9.w,
                        child: WxAssets.images.icTeamMvpBg.image(
                            width: 82.w, height: 47.w, fit: BoxFit.fill)),
                    Positioned(
                      left: 14.w,
                      top: 17.w,
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.w),
                          child: Stack(children: [
                            CachedNetworkImage(
                              imageUrl: model.photo,
                              width: 94.w,
                              height: 125.w,
                              fit: BoxFit.cover,
                            ),
                            Visibility(
                                visible: model.locking,
                                child: Positioned.fill(
                                    child: Container(
                                  color: Colors.black.withOpacity(0.5),
                                  alignment: Alignment.center,
                                  child: WxAssets.images.icGameLock
                                      .image(width: 20.w, fit: BoxFit.fill),
                                ))),
                          ])),
                    ),
                    Positioned(
                      top: 25.w,
                      left: 123.w,
                      child: Container(
                        width: 35.w,
                        height: 35.w,
                        alignment: Alignment.center,
                        // padding: EdgeInsets.only(top: 3.w),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: WxAssets.images.icTeamMvpQy.provider(),
                                fit: BoxFit.fill)),
                        child: Text(model.number,
                            style: GoogleFonts.oswald(
                                fontSize: 16.sp,
                                color: Colours.white,
                                fontWeight: AppFontWeight.semiBold())),
                      ),
                    ),
                    Positioned(
                      top: 88.w,
                      left: 123.w,
                      child: Row(
                        children: [
                          Text(
                            S.current.score,
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp, color: Colours.color5C5C6E),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            model.locking ? '**' : model.score,
                            style: GoogleFonts.oswald(
                                fontSize: 18.sp,
                                color: Colours.white,
                                fontWeight: AppFontWeight.semiBold()),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 116.w,
                      left: 123.w,
                      child: Row(
                        children: [
                          Text(
                            S.current.assist,
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp, color: Colours.color5C5C6E),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            model.locking ? '**' : model.assist.toString(),
                            style: GoogleFonts.oswald(
                                fontSize: 18.sp,
                                color: Colours.white,
                                fontWeight: AppFontWeight.semiBold()),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 88.w,
                      left: 215.w,
                      child: Row(
                        children: [
                          Text(
                            '篮   板',
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp, color: Colours.color5C5C6E),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            model.locking ? '**' : model.rebound.toString(),
                            style: GoogleFonts.oswald(
                                fontSize: 18.sp,
                                color: Colours.white,
                                fontWeight: AppFontWeight.semiBold()),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 116.w,
                      left: 215.w,
                      child: Row(
                        children: [
                          Text(
                            S.current.shotRate,
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp, color: Colours.color5C5C6E),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            model.locking ? '**' : '${model.rate}%',
                            style: GoogleFonts.oswald(
                                fontSize: 18.sp,
                                color: Colours.white,
                                fontWeight: AppFontWeight.semiBold()),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    ).marginSymmetric(horizontal: 15.w);
  }

  Widget _best(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(children: [
          Text(
            '最佳球员',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => logic.tabController?.animateTo(1),
            child: Row(
              children: [
                Text(
                  S.current.all,
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color9393A5),
                ),
                WxAssets.images.icArrowRight
                    .image(width: 14.w, fit: BoxFit.fill),
              ],
            ),
          ),
        ]).marginOnly(top: 30.w, bottom: 20.w, left: 15.w, right: 15.w),
        SizedBox(
          height: 83.w,
          child: ListView.builder(
              padding: EdgeInsets.only(left: 15.w, right: 5.w),
              scrollDirection: Axis.horizontal,
              itemCount: 5,
              itemBuilder: (context, index) {
                return Obx(() => _bestListItem(context, index));
              }),
        ),
      ],
    );
  }

  Widget _bestListItem(BuildContext context, int index) {
    var model = state.model.value.teamBest.scoreKing;
    var title = '得分';
    var number = '${model.score}分';
    switch (index) {
      case 2:
        model = state.model.value.teamBest.assistKing;
        title = '助攻';
        number = '${model.assist}个';
      case 1:
        model = state.model.value.teamBest.reboundKing;
        title = '篮板';
        number = '${model.rebound}个';
      case 4:
        model = state.model.value.teamBest.freeThrowKing;
        title = '罚球';
        number = '${model.hit}个';
      case 3:
        model = state.model.value.teamBest.threePointKing;
        title = '三分';
        number = '${model.hit}个';
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => logic.unlockPlayer(model.playerId, model.locking),
      child: Container(
        margin: EdgeInsets.only(right: 10.w),
        padding: EdgeInsets.all(10.w),
        width: 162.w,
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(12.w),
        ),
        child: Row(
          children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Stack(children: [
                  CachedNetworkImage(
                    imageUrl: model.photo,
                    width: 48.w,
                    height: 63.w,
                    fit: BoxFit.cover,
                  ),
                  Visibility(
                      visible: model.locking,
                      child: Positioned.fill(
                          child: Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: WxAssets.images.icGameLock
                            .image(width: 18.w, fit: BoxFit.fill),
                      ))),
                ])),
            SizedBox(
              width: 10.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 3.w,
                  ),
                  Text(
                    '$title王',
                    style: GoogleFonts.oswald(
                        fontSize: 16.sp,
                        fontWeight: AppFontWeight.bold(),
                        color: Colours.white,
                        height: 1),
                  ),
                  SizedBox(
                    height: 21.w,
                  ),
                  Row(
                    children: [
                      Container(
                        width: 16.w,
                        height: 18.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: WxAssets.images.icTeamQy.provider(),
                                fit: BoxFit.fill)),
                        child: Text(
                          model.number,
                          style: GoogleFonts.oswald(
                              fontSize: 10.sp,
                              fontWeight: AppFontWeight.semiBold(),
                              color: Colours.white,
                              height: 1),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Text(
                        title,
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color5C5C6E),
                      ),
                      SizedBox(
                        width: 3.w,
                      ),
                      Text(
                        model.locking ? '**' : number,
                        style: TextStyles.semiBold.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sectionScore(BuildContext context) {
    final hide =
        state.sectionScoreList == null || state.sectionScoreList!.length != 2;
    return Visibility(
      visible: !hide,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '小节比分',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ).marginOnly(top: 30.w, bottom: 20.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 16.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: 3,
                itemBuilder: (context, index) {
                  return Obx(() => _scoreListItem(context, index));
                }),
          ),
        ],
      ).marginSymmetric(horizontal: 15.w),
    );
  }

  Widget _scoreListItem(BuildContext context, int index) {
    final one = state.sectionScoreList!.first;
    final two = state.sectionScoreList!.last;
    final color = index == 0 ? Colours.color5C5C6E : Colours.white;
    final text2 =
        index == 0 ? "Q1" : (index == 1 ? one.secScore1 : two.secScore1);
    final text3 =
        index == 0 ? "Q2" : (index == 1 ? one.secScore2 : two.secScore2);
    final text4 =
        index == 0 ? "Q3" : (index == 1 ? one.secScore3 : two.secScore3);
    final text5 =
        index == 0 ? "Q4" : (index == 1 ? one.secScore4 : two.secScore4);
    final text6 =
        index == 0 ? "总分" : (index == 1 ? one.totalScore : two.totalScore);
    final hide = (state.currentIndex.value == 0 && index == 2) ||
        (state.currentIndex.value == 1 && index == 1);
    return Visibility(
      visible: !hide,
      child: Row(
        children: [
          Expanded(child: _scoreText(text2.toString(), color)),
          Expanded(child: _scoreText(text3.toString(), color)),
          Expanded(child: _scoreText(text4.toString(), color)),
          Expanded(child: _scoreText(text5.toString(), color)),
          Expanded(child: _scoreText(text6.toString(), color)),
        ],
      ).paddingOnly(bottom: index == 0 ? 20.w : 0),
    );
  }

  Widget _scoreText(String text, Color color) {
    return Text(
      text,
      style: TextStyles.regular.copyWith(fontSize: 12.sp, color: color),
      textAlign: TextAlign.center,
    );
  }

  Widget _analyze(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 25.w, bottom: 20.w, left: 15.w),
          child: Text(
            '技术分析',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ),
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 15.w),
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w, bottom: 6.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                          color: Colours.color7732ED, shape: BoxShape.circle),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(
                      state.modelList.first.teamName,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp, color: Colours.color9393A5),
                    )
                  ],
                ),
                const Spacer(),
                Row(
                  children: [
                    Text(
                      state.modelList.last.teamName,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp, color: Colours.color9393A5),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                          color: Colours.colorE282FF, shape: BoxShape.circle),
                    ),
                  ],
                ),
              ],
            ).marginOnly(bottom: 20.w),
            ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: 10,
                itemBuilder: (context, index) {
                  final leftTeam = state.modelList.first.teamScoreDetail;
                  final rightTeam = state.modelList.last.teamScoreDetail;
                  var needCalculate = true;
                  var left = 0.0;
                  var right = 0.0;
                  var title = '';
                  switch (index) {
                    case 0:
                      title = '得分';
                      left = leftTeam.totalScore.toDouble();
                      right = rightTeam.totalScore.toDouble();
                    case 1:
                      title = '篮板';
                      left = leftTeam.reboundCount.toDouble();
                      right = rightTeam.reboundCount.toDouble();
                    case 2:
                      title = '助攻';
                      left = leftTeam.assistCount.toDouble();
                      right = rightTeam.assistCount.toDouble();
                    case 3:
                      title = '投篮命中率';
                      left = double.parse(leftTeam.shootRate) / 100;
                      right = double.parse(rightTeam.shootRate) / 100;
                      needCalculate = false;
                    case 4:
                      title = '三分';
                      left = leftTeam.threePointShootCount.toDouble();
                      right = rightTeam.threePointShootCount.toDouble();
                    case 5:
                      title = '三分命中率';
                      left = double.parse(
                              leftTeam.threePointShootRate.isNotEmpty
                                  ? leftTeam.threePointShootRate
                                  : '0') /
                          100;
                      left = double.parse(
                              rightTeam.threePointShootRate.isNotEmpty
                                  ? leftTeam.threePointShootRate
                                  : '0') /
                          100;
                      needCalculate = false;
                    case 6:
                      title = '前场篮板';
                      left = leftTeam.offensiveReboundCount.toDouble();
                      right = rightTeam.offensiveReboundCount.toDouble();
                    case 7:
                      title = '后场篮板';
                      left = leftTeam.defensiveReboundCount.toDouble();
                      right = rightTeam.defensiveReboundCount.toDouble();
                    case 8:
                      title = '罚球';
                      left = leftTeam.freeThrowShootCount.toDouble();
                      right = rightTeam.freeThrowShootCount.toDouble();
                    case 9:
                      title = '罚球命中率';
                      left = double.parse(leftTeam.freeThrowShootRate.isNotEmpty
                              ? leftTeam.freeThrowShootRate
                              : '0') /
                          100;
                      left = double.parse(
                              rightTeam.freeThrowShootRate.isNotEmpty
                                  ? leftTeam.freeThrowShootRate
                                  : '0') /
                          100;
                      needCalculate = false;
                  }
                  return AnalyzeItemView(
                    left: left,
                    right: right,
                    title: title,
                    needCalculate: needCalculate,
                  ).marginOnly(bottom: 18.w);
                }),
          ]),
        ),
      ],
    );
  }
}
