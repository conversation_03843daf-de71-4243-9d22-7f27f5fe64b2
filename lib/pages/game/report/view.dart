import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/pages/game/report/logic.dart';
import 'package:shoot_z/pages/game/report/statistics_view.dart';
import 'package:shoot_z/pages/game/report/team_players_view.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../gen/assets.gen.dart';
import '../../../generated/l10n.dart';
import '../../../widgets/keep_alive_widget.dart';
import '../../../widgets/view.dart';

final _tabbarHeight = 50.0.w;

class TeamReportPage extends StatelessWidget {
  TeamReportPage({super.key});

  final logic = Get.put(TeamReportLogic());
  final state = Get.find<TeamReportLogic>().state;

  @override
  Widget build(BuildContext context) {
    logic.subscribe(context);
    return Scaffold(
      appBar: MyAppBar(
        title: const Text('球队报告'),
        leading: Row(children: [
          GestureDetector(
              onTap: () => AppPage.back(),
              child: Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: WxAssets.images.arrowLeft.image(),
              )),
          SizedBox(
            width: 7.w,
          ),
          Visibility(
            visible: state.notDetails,
            child: GestureDetector(
              onTap: () {
                AppPage.to(Routes.gameDetailsPage, arguments: state.matchId);
                //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                UserManager.instance.postApmTracking(1,
                    nowPage: Routes.teamReportPage,
                    nextPage: Routes.gameDetailsPage,
                    remark: "球队报告点击",
                    content: "进入赛事概况页面");
              },
              child: Container(
                width: 60.w,
                height: 23.w,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colours.color22222D,
                  borderRadius: BorderRadius.circular(11.5.w),
                ),
                child: Text(
                  '比赛概况',
                  style: TextStyles.regular
                      .copyWith(fontSize: 11.sp, color: Colours.color9393A5),
                ),
              ),
            ),
          ),
        ]),
        leadingWidth: 120.w,
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: logic.share,
            child: Container(
              width: 40.w,
              height: 40.w,
              alignment: Alignment.centerRight,
              child: WxAssets.images.share3.image(
                  color: Colors.white,
                  width: 20.w,
                  height: 20.w,
                  fit: BoxFit.fill),
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
        ],
      ),
      body: Obx(
        () => state.init.value
            ? NestedScrollView(
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return [
                    SliverToBoxAdapter(
                      child: _topView(context),
                    ),
                    SliverPersistentHeader(
                      pinned: true, // TabBar 固定到顶部时悬停
                      delegate: _TabBarDelegate(_tabbar(context)),
                    ),
                  ];
                },
                body: Column(children: [
                  Expanded(
                    child: TabBarView(
                      controller: logic.tabController,
                      children: [
                        KeepAliveWidget(child: StatisticsView()),
                        KeepAliveWidget(child: TeamPlayersView()),
                      ],
                    ),
                  ),
                  _bottomView(context),
                ]),
              )
            : buildLoad(),
      ),
    );
  }

  Widget _bottomView(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          top: 20.w,
          bottom: MediaQuery.of(context).padding.bottom > 0
              ? MediaQuery.of(context).padding.bottom
              : 34),
      color: Colours.bg_color,
      child: Column(
        children: [
          Obx(
            () => Visibility(
              visible: state.model.value.locking &&
                  !state.closeHint.value &&
                  (state.payInfo.value?.validCouponCount ?? 0) > 0,
              child: Container(
                height: 44.w,
                margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                decoration: BoxDecoration(
                  color: Colours.color282735,
                  borderRadius: BorderRadius.circular(12.w),
                ),
                child: Row(
                  children: [
                    WxAssets.images.icReportUnlockHint
                        .image(width: 28.w, fit: BoxFit.fill),
                    SizedBox(
                      width: 9.w,
                    ),
                    Text(
                      '您有${state.payInfo.value?.validCouponCount}张个人报告解锁券未用，快解锁吧！',
                      style: TextStyles.regular.copyWith(fontSize: 12.sp),
                    ),
                    const Spacer(),
                    GestureDetector(
                        onTap: () => state.closeHint.value = true,
                        child: WxAssets.images.icClose2
                            .image(width: 20.w, fit: BoxFit.fill)),
                  ],
                ),
              ),
            ),
          ),
          Obx(
            () => Visibility(
              visible: state.model.value.locking &&
                  !(UserManager.instance.userInfo.value?.isSVip ?? false),
              child: GestureDetector(
                onTap: () => AppPage.to(Routes.vipPage,
                    arguments: true, needLogin: true),
                child: Container(
                  height: 44.w,
                  margin:
                      EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
                  decoration: BoxDecoration(
                    color: Colours.color282735,
                    borderRadius: BorderRadius.circular(12.w),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 7.w,
                      ),
                      WxAssets.images.icTeamVip
                          .image(width: 42.w, fit: BoxFit.fill),
                      SizedBox(
                        width: 9.w,
                      ),
                      Text(
                        '开通SVIP，免费解锁个人报告',
                        style: TextStyles.regular.copyWith(fontSize: 12.sp),
                      ),
                      const Spacer(),
                      Container(
                        width: 64.w,
                        height: 26.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                              image: WxAssets.images.icTeamKtBg.provider(),
                              fit: BoxFit.fill),
                        ),
                        child: Text(
                          '立即开通',
                          style: TextStyles.semiBold.copyWith(
                              fontSize: 10.sp, color: Colours.color0F0F16),
                        ),
                      ),
                      SizedBox(
                        width: 12.w,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Obx(
            () => WxButton(
              text: state.model.value.locking ? '解锁本队' : '分享比赛报告',
              textStyle: TextStyles.semiBold,
              linearGradient: GradientUtils.mainGradient,
              height: 55.w,
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              borderRadius: BorderRadius.circular(27.5.w),
              onPressed: logic.unlockTeam,
            ),
          ),
        ],
      ),
    );
  }

  Widget _topView(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = 290.w;
    final double spacing = 15.0.w;
    return Column(children: [
      SizedBox(
        height: 100.w,
        width: screenWidth,
        child: CarouselSlider.builder(
          carouselController: logic.carouselController,
          itemCount: 2, // 两个 item
          options: CarouselOptions(
              viewportFraction: (itemWidth + spacing * 1.5) / screenWidth,
              // 每个页面的占比
              initialPage: 0,
              enableInfiniteScroll: false,
              // 禁止无限滚动
              enlargeCenterPage: false,
              // 禁用放大效果
              padEnds: false,
              // 让第一页和最后一页与屏幕边缘对齐
              scrollPhysics: const ClampingScrollPhysics(),
              // 禁用回弹效果
              onPageChanged: (index, resson) {
                logic.switchTeam(index);
              }),
          itemBuilder: (context, index, realIndex) {
            return Container(
              margin: index == 0
                  ? EdgeInsets.only(left: spacing, right: spacing / 2)
                  : EdgeInsets.only(left: spacing / 2, right: spacing),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colours.color2F2F3B, width: 2),
              ),
              child: _team(context, index),
            );
          },
        ),
      ),
      SizedBox(
        height: 10.w,
      ),
      Obx(
        () => AnimatedSmoothIndicator(
          duration: const Duration(milliseconds: 500),
          activeIndex: state.currentIndex.value,
          count: 2,
          effect: ExpandingDotsEffect(
            dotColor: Colours.color252530,
            activeDotColor: Colours.app_main,
            dotHeight: 5.w,
            dotWidth: 8.w,
            expansionFactor: 2.25,
            spacing: 5.w,
          ),
        ),
      ),
      Padding(
        padding: EdgeInsets.all(15.w),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            aspectRatio: 345 / 194, // 宽高比
            child: Stack(children: [
              VideoView(controller: state.videoController),
              Obx(
                () => Visibility(
                  visible: state.model.value.videoPath.isEmpty,
                  child: Container(
                    color: Colours.color191921,
                    alignment: Alignment.center,
                    child: Column(
                      children: [
                        SizedBox(
                          height: 41.w,
                        ),
                        WxAssets.images.icVideoScz
                            .image(width: 80.w, fit: BoxFit.fill),
                        SizedBox(
                          height: 22.w,
                        ),
                        Text(
                          S.current.highlights_generate,
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp, color: Colours.color5C5C6E),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Obx(
                () => Visibility(
                  visible:
                      !state.closeVideoHint.value && state.model.value.locking,
                  child: SizedBox(
                    height: 30.w,
                    child: Stack(
                      children: [
                        // 透明背景 - 穿透点击事件
                        Positioned.fill(
                          child: IgnorePointer(
                            child: Container(
                              color: state.model.value.videoPath.isEmpty
                                  ? Colors.black
                                  : Colors.black.withOpacity(0.5),
                            ),
                          ),
                        ),

                        // 中间文本区域
                        Center(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // 提示文本 - 穿透点击事件
                              IgnorePointer(
                                child: Text(
                                  '解锁球队报告后可获得完整版集锦视频',
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 10.sp,
                                      color: Colours.colorFCFCFC),
                                ),
                              ),
                              SizedBox(width: 5.w),
                              // 立即解锁按钮 - 响应点击
                              GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: logic.unlockTeam,
                                child: Text(
                                  '立即解锁',
                                  style: TextStyles.semiBold.copyWith(
                                      fontSize: 10.sp,
                                      color: Colours.colorFFF280),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 关闭按钮 - 响应点击
                        Positioned(
                          right: 0,
                          top: 0,
                          bottom: 0,
                          child: GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () => state.closeVideoHint.value = true,
                            child: Container(
                              width: 28.w,
                              alignment: Alignment.center,
                              child: WxAssets.images.icClose3
                                  .image(width: 8.w, fit: BoxFit.fill),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ]),
          ),
        ),
      ),
    ]);
  }

  Widget _team(BuildContext context, int index) {
    final model = state.modelList[index];
    return Stack(
      children: [
        LayoutBuilder(
          builder: (context, constraints) => SizedBox(
            height: constraints.maxHeight,
            child: Row(
              children: [
                if (index == 1) _teamName(context, index),
                ClipRRect(
                  borderRadius: BorderRadius.circular(30.w),
                  child: MyImage(
                    model.logo,
                    width: 60.w,
                    height: 60.w,
                    radius: 30.r,
                    isAssetImage: false,
                    fit: BoxFit.cover,
                    errorImage: "my_team_head4.png",
                  ),
                  // CachedNetworkImage(
                  //   imageUrl: model.logo,
                  //   width: 60.w,
                  //   height: 60.w,
                  //   fit: BoxFit.cover,
                  // )
                ).marginOnly(
                    left: index == 0 ? 15.w : 12.w,
                    right: index == 0 ? 12.w : 15.w),
                if (index == 0) _teamName(context, index),
              ],
            ),
          ),
        ),
        if (index == 0)
          Positioned(
              top: 15.w,
              right: 20.w,
              child: Obx(
                () => Text(
                  '${model.score}',
                  style: GoogleFonts.oswald(
                    fontSize: 30.sp,
                    color: index == state.currentIndex.value
                        ? Colours.white
                        : Colours.color5C5C6E,
                    fontWeight: AppFontWeight.medium(),
                  ),
                ),
              )),
        if (index == 1)
          Positioned(
              top: 15.w,
              left: 20.w,
              child: Obx(
                () => Text(
                  '${model.score}',
                  style: GoogleFonts.oswald(
                    fontSize: 30.sp,
                    color: index == state.currentIndex.value
                        ? Colours.white
                        : Colours.color5C5C6E,
                    fontWeight: AppFontWeight.medium(),
                  ),
                ),
              )),
      ],
    );
  }

  Widget _teamName(BuildContext context, int index) {
    final model = state.modelList[index];
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment:
            index == 0 ? CrossAxisAlignment.start : CrossAxisAlignment.end,
        children: [
          Text(
            model.teamName,
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ),
          SizedBox(
            height: 15.w,
          ),
          Text(
            '${model.winCount}胜/${model.loseCount}负 ${model.memberCount}球员 ${model.winningStreak}连胜',
            style: TextStyles.regular
                .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
          ),
        ],
      ),
    );
  }

  Widget _tabbar(BuildContext context) {
    return Stack(children: [
      Obx(
        () => Visibility(
          visible: state.showReportHint.value,
          child: state.showReportHint.value
              ? Positioned(
                  right: 36.w,
                  top: 0.w,
                  child: AnimatedBuilder(
                    animation: logic.arrowAnimation!,
                    builder: (context, child) => Transform.translate(
                        offset:
                            Offset(logic.arrowAnimation!.value, 0), // 水平移动偏移
                        child: child),
                    child: WxAssets.images.icReportHint
                        .image(width: 132.w, height: 28.w, fit: BoxFit.fill),
                  ))
              : const SizedBox.shrink(),
        ),
      ),
      Obx(() {
        final text = !state.model.value.locking
            ? '全部解锁'
            : '${state.model.value.unlockedPlayerCount ?? 0}人解锁';
        return Visibility(
          visible: !state.showReportHint.value,
          child: Positioned(
              left: 210.w,
              top: 5.w,
              child: Container(
                width: 60.w,
                height: 20.w,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colours.bg_color,
                  borderRadius: BorderRadius.circular(10.w),
                  border: Border.all(color: Colours.color2F2F3B, width: 1.w),
                ),
                child: Text(
                  text,
                  style: TextStyles.regular
                      .copyWith(fontSize: 10.sp, color: Colours.color9D4FEF),
                ),
              )),
        );
      }),
      Container(
        alignment: Alignment.bottomLeft,
        child: TabBar(
          controller: logic.tabController,
          isScrollable: true,
          // 从左对齐
          tabAlignment: TabAlignment.start,
          unselectedLabelColor: Colours.color5C5C6E,
          unselectedLabelStyle: TextStyle(
              fontSize: 18.sp,
              color: Colours.color5C5C6E,
              fontWeight: FontWeight.w600),
          labelColor: Colours.white,
          labelStyle: TextStyle(
              fontSize: 20.sp,
              color: Colours.white,
              fontWeight: FontWeight.w600),
          indicatorColor: Colors.transparent,
          dividerColor: Colors.transparent,
          labelPadding: EdgeInsets.only(left: 15.w),
          padding: EdgeInsets.only(left: 5.w),
          tabs: [
            SizedBox(
              width: 90.w,
              height: 35.w,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Obx(
                    () => Positioned(
                      right: 0,
                      bottom: 0,
                      child: Visibility(
                        visible: logic.tabbarIndex.value == 0,
                        child: WxAssets.images.imgCheckIn
                            .image(width: 52.w, height: 22.w, fit: BoxFit.fill),
                      ),
                    ),
                  ),
                  const Positioned(left: 0, top: 0, child: Text('数据统计')),
                ],
              ),
            ),
            SizedBox(
              width: 90.w,
              height: 35.w,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Obx(
                    () => Positioned(
                      right: 0,
                      bottom: 0,
                      child: Visibility(
                        visible: logic.tabbarIndex.value == 1,
                        child: WxAssets.images.imgCheckIn
                            .image(width: 52.w, height: 22.w, fit: BoxFit.fill),
                      ),
                    ),
                  ),
                  const Positioned(left: 0, top: 0, child: Text('球员报告')),
                ],
              ),
            ),
          ],
        ),
      ),
    ]);
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget tabBar;

  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => _tabbarHeight;

  @override
  double get maxExtent => _tabbarHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colours.bg_color, // TabBar 背景色
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
