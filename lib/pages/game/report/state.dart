import 'package:get/get.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import '../details/models/match_pay_info_model.dart';
import '../details/models/section_score_model.dart';
import 'models/team_players_model.dart';
import 'models/team_report_model.dart';

class TeamReportState {
  late String matchId;
  bool notDetails = true;
  late RxBool showReportHint;
  var init = false.obs;
  late Rx<TeamReportModel> model;
  late List<TeamReportModel> modelList;
  var currentIndex = 0.obs;
  List<SectionScoreModel>? sectionScoreList;
  var teamPlayers = Rx<TeamPlayersModel?>(null);
  List<TeamPlayersModel>? teamPlayersList;
  var payInfo = Rx<MatchPayInfoModel?>(null);
  var closeHint = false.obs;
  var closeVideoHint = false.obs;
  late VideoController videoController;
}
