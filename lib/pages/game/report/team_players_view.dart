import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/pages/game/report/player_score_view.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../gen/assets.gen.dart';
import '../../../utils/utils.dart';
import '../../../widgets/view.dart';
import 'logic.dart';

class TeamPlayersView extends StatelessWidget {
  TeamPlayersView({super.key});

  final logic = Get.find<TeamReportLogic>();
  final state = Get.find<TeamReportLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => state.teamPlayers.value != null
          ? Column(
              children: [
                Container(
                  height: 32.w,
                  margin: EdgeInsets.only(top: 10.w, bottom: 12.w),
                  padding: EdgeInsets.only(left: 24.w, right: 29.w),
                  decoration: BoxDecoration(
                    color: Colours.color15151D,
                    borderRadius: BorderRadius.circular(8.w),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '球员',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '得分',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '篮板',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '助攻',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '命中率',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                    ],
                  ),
                ),
                Expanded(
                    child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: state.teamPlayers.value!.players.length,
                        itemBuilder: (context, index) {
                          return _listItem(context, index);
                        })),
              ],
            ).paddingSymmetric(horizontal: 15.w)
          : buildLoad(),
    );
  }

  Widget _listItem(BuildContext context, int index) {
    final model = state.teamPlayers.value!.players[index];
    return GestureDetector(
      onTap: () => logic.unlockPlayer(model.playerId, model.locking),
      child: Container(
        margin: EdgeInsets.only(bottom: 12.w),
        padding: EdgeInsets.only(top: 5.w, bottom: 10.w, left: 5.w),
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12.w)),
        child: Stack(children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(4.w),
                  child: Stack(children: [
                    CachedNetworkImage(
                      imageUrl: model.photo,
                      width: 67.w,
                      height: 90.w,
                      fit: BoxFit.cover,
                    ),
                    Visibility(
                        visible: model.locking,
                        child: Positioned.fill(
                            child: Container(
                          color: Colors.black.withOpacity(0.5),
                          alignment: Alignment.center,
                          child: WxAssets.images.icGameLock
                              .image(width: 18.w, fit: BoxFit.fill),
                        ))),
                  ])),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                child: Column(
                  children: [
                    SizedBox(
                      height: 6.w,
                    ),
                    Row(
                      children: [
                        Container(
                          width: 22.w,
                          height: 22.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  image: WxAssets.images.icTeamMvpQy.provider(),
                                  fit: BoxFit.fill)),
                          child: Text(model.number,
                              style: GoogleFonts.oswald(
                                  fontSize: 10.sp,
                                  color: Colours.white,
                                  fontWeight: AppFontWeight.semiBold())),
                        ),
                        if (model.playerName?.isNotEmpty ?? false)
                          Text(
                            model.playerName ?? '',
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp, color: Colours.color5C5C6E),
                          ).marginOnly(left: 5.w),
                        SizedBox(
                          width: 8.w,
                        ),
                        if (model.scoreKing)
                          WxAssets.images.icTeamDfw
                              .image(width: 18.w, fit: BoxFit.fill)
                              .marginOnly(right: 5.w),
                        if (model.reboundKing)
                          WxAssets.images.icTeamLbw
                              .image(width: 18.w, fit: BoxFit.fill)
                              .marginOnly(right: 5.w),
                        if (model.assistKing)
                          WxAssets.images.icTeamZgw
                              .image(width: 18.w, fit: BoxFit.fill)
                              .marginOnly(right: 5.w),
                        if (model.threePointKing)
                          WxAssets.images.icTeamSfw
                              .image(width: 18.w, fit: BoxFit.fill)
                              .marginOnly(right: 5.w),
                        if (model.freeThrowKing)
                          WxAssets.images.icTeamFqw
                              .image(width: 18.w, fit: BoxFit.fill)
                              .marginOnly(right: 5.w),
                        const Spacer(),
                        Row(
                          children: [
                            Text(
                              '比赛报告',
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.color5C5C6E),
                            ),
                            WxAssets.images.icArrowRight.image(
                                width: 14.w,
                                color: Colours.color9393A5,
                                fit: BoxFit.fill),
                          ],
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: PlayerScoreView(
                                    locking: model.locking,
                                    text: model.score.toString(),
                                    percentage: model.scoreRate,
                                    color: Colours.color474CA4)
                                .marginOnly(right: 10.w)),
                        Expanded(
                            child: PlayerScoreView(
                                    locking: model.locking,
                                    text: model.reboundCount.toString(),
                                    percentage: model.reboundRate,
                                    color: Colours.color513663)
                                .marginOnly(right: 10.w)),
                        Expanded(
                            child: PlayerScoreView(
                                    locking: model.locking,
                                    text: model.assistCount.toString(),
                                    percentage: model.assistRate,
                                    color: Colours.color505583)
                                .marginOnly(right: 10.w)),
                        Expanded(
                            child: PlayerScoreView(
                          locking: model.locking,
                          text: Utils.formatToPercentage(
                              double.parse(model.rate) / 100),
                          percentage: model.rate,
                          color: Colours.color432B8A,
                          showPercentageText: false,
                        ).marginOnly(right: 10.w)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ).marginOnly(top: 5.w, left: 5.w),
          Visibility(
              visible: model.mvp,
              child: Positioned(
                left: 2.w,
                top: 2.w,
                child: WxAssets.images.icTeamMvp2
                    .image(width: 25.w, fit: BoxFit.fill),
              )),
        ]),
      ),
    );
  }
}
