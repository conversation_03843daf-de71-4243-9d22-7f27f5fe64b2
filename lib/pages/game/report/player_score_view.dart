import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:ui_packages/ui_packages.dart';

class PlayerScoreView extends StatelessWidget {
  final String text;
  final String percentage;
  final Color color;
  final bool showPercentageText;
  final bool locking;
  const PlayerScoreView({super.key, required this.text, required this.percentage, required this.color, this.showPercentageText = true,this.locking = false});

  @override
  Widget build(BuildContext context) {
    final p = locking ? 0.0 : double.parse(percentage) / 100;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(locking ? '**' : text,style: GoogleFonts.oswald(fontSize: 18.sp,height: 1,fontWeight: AppFontWeight.medium(),color: Colours.white),),
        SizedBox(height: 4.w,),
        Text(showPercentageText ? (locking ? '0%': '$percentage%') : '',style: TextStyles.regular.copyWith(fontSize: 10.sp,color: Colours.color5C5C6E),),
        SizedBox(height: 5.w,),
        LayoutBuilder(builder: (context,constraints) {
          return Container(
            width: constraints.maxWidth,
            height: 5.w,
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              color: Colours.color22222D,
              borderRadius: BorderRadius.circular(2.5.w),
            ),
            child: Container(
              width: constraints.maxWidth * p,
              height: 5.w,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2.5.w),
              ),
            ),
          );
        }),
      ],
    );
  }
}
