import 'package:json_annotation/json_annotation.dart';

part 'section_score_model.g.dart';


@JsonSerializable()
class SectionScoreModel extends Object {

  @J<PERSON><PERSON><PERSON>(name: 'teamLogo')
  String teamLogo;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'teamId')
  String teamId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'teamName')
  String teamName;

  @Json<PERSON>ey(name: 'secScore1')
  int secScore1;

  @Json<PERSON><PERSON>(name: 'secScore2')
  int secScore2;

  @<PERSON>son<PERSON><PERSON>(name: 'secScore3')
  int secScore3;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'secScore4')
  int secScore4;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalScore')
  int totalScore;

  SectionScoreModel(this.teamLogo,this.teamId,this.teamName,this.secScore1,this.secScore2,this.secScore3,this.secScore4,this.totalScore,);

  factory SectionScoreModel.fromJson(Map<String, dynamic> srcJson) => _$SectionScoreModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SectionScoreModelToJson(this);

}


