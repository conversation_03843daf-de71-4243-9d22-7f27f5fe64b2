import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/network/model/mactches_info_model.dart';
import 'package:shoot_z/pages/game/details/models/team_player_model.dart';
import 'package:shoot_z/pages/game/details/widget/star_rating_view.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../gen/assets.gen.dart';
import '../../../generated/l10n.dart';
import '../../../routes/app.dart';
import '../../../widgets/vertical_dashed_line.dart';
import 'analyze_item_view.dart';
import 'logic.dart';

class GameDetailsPage extends StatelessWidget {
  GameDetailsPage({super.key});

  final logic = Get.put(GameDetailsLogic());
  final state = Get.find<GameDetailsLogic>().state;

  @override
  Widget build(BuildContext context) {
    logic.subscribe(context);
    return Scaffold(
        resizeToAvoidBottomInset: true,
        body: Container(
          color: Colours.bg_color,
          child: Stack(
            children: [
              _bodyView(context),
              _title(context),
            ],
          ),
        ));
  }

  Widget _bodyView(BuildContext context) {
    return Obx(
      () => state.init.value
          ? Stack(children: [
              Column(children: [
                Expanded(
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 10.w,
                        top: kToolbarHeight +
                            MediaQuery.of(context).padding.top),
                    controller: state.scrollController,
                    child: Column(
                      children: [
                        _topView(context),
                        Column(
                          children: [
                            _best(context),
                            _score(context),
                            _analyze(context),
                            _history(context),
                            _marking(context)
                          ],
                        ).paddingSymmetric(horizontal: 15.w),
                      ],
                    ),
                  ),
                ),
                if (!logic.showKeybord.value) _bottomView(context),
              ]),
              // 动画显示的视图
              Obx(
                () => AnimatedPositioned(
                  duration: const Duration(milliseconds: 300),
                  top: state.showTop.value
                      ? kToolbarHeight + MediaQuery.of(context).padding.top
                      : kToolbarHeight +
                          MediaQuery.of(context).padding.top -
                          state.topViewHeight,
                  left: 0,
                  right: 0,
                  child: _animatedTopView(context),
                ),
              ),
            ])
          : buildLoad(),
    );
  }

  Widget _bottomView(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          top: 12.w,
          bottom: MediaQuery.of(context).padding.bottom > 0
              ? MediaQuery.of(context).padding.bottom
              : 34),
      color: Colours.bg_color,
      child: Column(
        children: [
          Obx(
            () => WxButton(
              text: state.allLocked.value ? '分享比赛报告' : '解锁本场',
              textStyle: TextStyles.semiBold.copyWith(fontSize: 14.sp),
              linearGradient: GradientUtils.mainGradient,
              height: 48.w,
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              borderRadius: BorderRadius.circular(27.5.w),
              onPressed: logic.unlockAll,
            ),
          ),
        ],
      ),
    );
  }

  Widget _animatedTopView(BuildContext context) {
    return Container(
      // height: state.topViewHeight,
      decoration: BoxDecoration(
        gradient: GradientUtils.mainGradient,
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 110.w,
                  child: Row(children: [
                    Image.asset(
                        width: 24.w,
                        height: 24.w,
                        state.model.value.teams?.first?.imagePath ??
                            'assets/images/one_stars_icon.png'),
                    SizedBox(
                      width: 5.w,
                    ),
                    Expanded(
                        child: Text(
                      maxLines: 1,
                      state.model.value.teams?.first?.teamName ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp,
                          color: Colours.white.withOpacity(0.9)),
                    )),
                  ]),
                ),
                Row(children: [
                  Text(
                    "${state.model.value.teams?.first?.score ?? ""}",
                    style: GoogleFonts.oswald(
                        fontWeight: AppFontWeight.medium(),
                        fontSize: 18.sp,
                        color: Colours.white),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  WxAssets.images.icGameVs.image(),
                  SizedBox(
                    width: 15.w,
                  ),
                  Text(
                    "${state.model.value.teams?.last?.score ?? ""}",
                    style: GoogleFonts.oswald(
                        fontWeight: AppFontWeight.medium(),
                        fontSize: 18.sp,
                        color: Colours.white),
                    textAlign: TextAlign.center,
                  ),
                ]),
                SizedBox(
                  width: 110.w,
                  child:
                      Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                    Expanded(
                        child: Text(
                      maxLines: 1,
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                      state.model.value.teams?.last?.teamName ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp,
                          color: Colours.white.withOpacity(0.9)),
                    )),
                    SizedBox(
                      width: 5.w,
                    ),
                    Image.asset(
                        width: 24.w,
                        height: 24.w,
                        state.model.value.teams?.last?.imagePath ??
                            'assets/images/one_stars_icon.png')
                  ]),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 12.w,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                WxButton(
                  text: '查看比赛报告',
                  textStyle: TextStyles.regular.copyWith(fontSize: 12.sp),
                  width: 155.w,
                  height: 32.w,
                  backgroundColor: Colours.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  onPressed: () =>
                      AppPage.to(Routes.teamReportPage, arguments: {
                    'teamId': state.model.value.teams?.first?.teamId,
                    'matchId': state.model.value.matchId,
                    'notDetails': false
                  }),
                ),
                WxButton(
                  text: '查看比赛报告',
                  textStyle: TextStyles.regular.copyWith(fontSize: 12.sp),
                  width: 155.w,
                  height: 32.w,
                  backgroundColor: Colours.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  onPressed: () =>
                      AppPage.to(Routes.teamReportPage, arguments: {
                    'teamId': state.model.value.teams?.last?.teamId,
                    'matchId': state.model.value.matchId,
                    'notDetails': false
                  }),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.w,
          ),
        ],
      ),
    );
  }

  Widget _topView(BuildContext context) {
    final cd =
        state.model.value.courts?.map((e) => e?.courtName).toList().join('、');
    return Stack(
      children: [
        Container(
          height: 247.w,
          decoration: BoxDecoration(gradient: GradientUtils.mainGradient),
        ),
        SizedBox(
          width: Get.width,
          child: Column(
            children: [
              SizedBox(
                height: 5.w,
              ),
              Text(
                '${state.model.value.arenaName}、场地$cd',
                style: TextStyles.regular
                    .copyWith(fontSize: 12.sp, color: Colours.colorA8A8BC),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 12.w),
                      width: 98.w,
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 38.w,
                              height: 38.w,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  width: 1,
                                  color: Colours.white,
                                ),
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                    image: (state.model.value.teams?.first
                                                ?.logo) ==
                                            ""
                                        ? WxAssets.images.myTeamHead4.provider()
                                        : CachedNetworkImageProvider(state.model
                                                .value.teams?.first?.logo ??
                                            "")),
                              ),
                            ),
                            SizedBox(
                              height: 12.w,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                    width: 24.w,
                                    height: 24.w,
                                    state.model.value.teams?.first?.imagePath ??
                                        'assets/images/one_stars_icon.png'),
                                SizedBox(
                                  width: 5.w,
                                ),
                                Flexible(
                                    child: Text(
                                  maxLines: 1,
                                  state.model.value.teams?.first?.teamName ??
                                      "",
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: Colours.white.withOpacity(0.9)),
                                ))
                              ],
                            ),
                          ]),
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 54.w,
                                child: Text(
                                  "${state.model.value.teams?.first?.score ?? ""}",
                                  style: GoogleFonts.oswald(
                                      fontWeight: AppFontWeight.medium(),
                                      fontSize: 26.sp,
                                      color: Colours.white),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 14.w,
                              ),
                              WxAssets.images.icGameVs.image(),
                              SizedBox(
                                width: 14.w,
                              ),
                              SizedBox(
                                width: 54.w,
                                child: Text(
                                  "${state.model.value.teams?.last?.score ?? ""}",
                                  style: GoogleFonts.oswald(
                                      fontWeight: AppFontWeight.medium(),
                                      fontSize: 26.sp,
                                      color: Colours.white),
                                ),
                              ),
                            ]),
                        SizedBox(
                          height: 6.w,
                        ),
                        Text(
                          '${state.model.value.matchWeek} ${state.model.value.matchDate} ${state.model.value.matchTime}',
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp, color: Colours.colorD5B6F8),
                        ),
                      ],
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 12.w),
                      width: 98.w,
                      child: Column(children: [
                        Container(
                          width: 38.w,
                          height: 38.w,
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: Colours.white,
                            ),
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image: (state.model.value.teams?.last?.logo) ==
                                        ""
                                    ? WxAssets.images.myTeamHead4.provider()
                                    : CachedNetworkImageProvider(
                                        state.model.value.teams?.last?.logo ??
                                            "")),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                                child: Text(
                              maxLines: 1,
                              state.model.value.teams?.last?.teamName ?? "",
                              textAlign: TextAlign.right,
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp,
                                  color: Colours.white.withOpacity(0.9)),
                            )),
                            SizedBox(
                              width: 5.w,
                            ),
                            Image.asset(
                                width: 24.w,
                                height: 24.w,
                                state.model.value.teams?.last?.imagePath ??
                                    'assets/images/one_stars_icon.png')
                          ],
                        ),
                      ]),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 14.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    WxButton(
                      text: '查看比赛报告',
                      textStyle: TextStyles.semiBold.copyWith(fontSize: 12.sp),
                      width: 155.w,
                      height: 38.w,
                      backgroundColor: Colours.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      onPressed: () =>
                          AppPage.to(Routes.teamReportPage, arguments: {
                        'teamId': state.model.value.teams?.first?.teamId,
                        'matchId': state.model.value.matchId,
                        'notDetails': false
                      }),
                    ),
                    WxButton(
                      text: '查看比赛报告',
                      textStyle: TextStyles.semiBold.copyWith(fontSize: 12.sp),
                      width: 155.w,
                      height: 38.w,
                      backgroundColor: Colours.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      onPressed: () =>
                          AppPage.to(Routes.teamReportPage, arguments: {
                        'teamId': state.model.value.teams?.last?.teamId,
                        'matchId': state.model.value.matchId,
                        'notDetails': false
                      }),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 15.w,
              ),
              if (logic.state.model.value.status == 2 &&
                  logic.state.model.value.markStatus == 2)
                Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: InkWell(
                        onTap: () => AppPage.to(Routes.aiBattleReportpage,
                            arguments: logic.matchId),
                        child: Container(
                          padding: EdgeInsets.only(left: 15.w, right: 10.w),
                          height: 40.w,
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.r)),
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              )),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                  child: Text(
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                '[篮球AI战报]— 赛后复盘不装正经',
                                style: TextStyles.semiBold14
                                    .copyWith(color: Colours.color191921),
                              )),
                              SizedBox(
                                width: 8.w,
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 14.w,
                                color: Colours.color191921,
                              )
                            ],
                          ),
                        ))),
              SizedBox(
                height: 15.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: AspectRatio(
                    aspectRatio: 345 / 194, // 宽高比
                    child: Stack(
                      children: [
                        VideoView(controller: state.videoController),
                        // Obx(
                        //   () {
                        //     final videoPath = state.matchVideoPath.value;
                        //     if (videoPath.isEmpty) return const SizedBox();

                        //     return Container(
                        //       color: Colors.black,
                        //       child: state.chewieController.value == null
                        //         ? (() {
                        //             logic.initVideo();
                        //             return const Center(
                        //               key: ValueKey('loading'),
                        //               child: CircularProgressIndicator(),
                        //             );
                        //           })()
                        //         : Chewie(
                        //             key: const ValueKey('video'),
                        //             controller: state.chewieController.value!,
                        //           ),
                        //     );
                        //   },
                        // ),
                        Obx(() {
                          return Visibility(
                            visible: state.matchVideoPath.value.isEmpty,
                            child: Container(
                              color: Colours.color191921,
                              alignment: Alignment.center,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 36.w,
                                  ),
                                  WxAssets.images.icVideoScz
                                      .image(width: 80.w, fit: BoxFit.fill),
                                  SizedBox(
                                    height: 22.w,
                                  ),
                                  Text(
                                    S.current.highlights_generate,
                                    style: TextStyles.regular.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.color5C5C6E),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _best(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
          child: const TextWithIcon(title: '本场最佳'),
        ),
        Container(
          padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.w),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3), // 阴影颜色
                offset: const Offset(0, 4), // 阴影偏移 (水平, 垂直)
                blurRadius: 10, // 阴影模糊半径
                spreadRadius: 1, // 阴影扩散半径
              ),
            ],
          ),
          child: ListView.builder(
              physics: const ClampingScrollPhysics(),
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: 3,
              itemBuilder: (context, index) {
                return Obx(() => _bestListItem(context, index));
              }),
        ),
      ],
    );
  }

  Widget _bestListItem(BuildContext context, int index) {
    // final leftBest = logic.state.model.value.teams?.first!;
    // final rightBest = logic.state.model.value.teams?.last!;
    // leftBest.
    // leftBest?.scoreKing;
    // leftBest?.scoreKing;
    // leftBest?.scoreKing;
    // final left = index == 0
    //     ? leftBest?.scoreKing
    //     : (index == 1 ? leftBest?.reboundKing : leftBest?.assistKing);
    // final right = index == 0
    //     ? rightBest?.scoreKing
    //     : (index == 1 ? rightBest?.reboundKing : rightBest?.assistKing);
    // final leftS = left?.score ?? 0;
    // final rightS = right?.score ?? 0;
    // final text = index == 0 ? '得分' : (index == 1 ? '篮板' : '助攻');

    final leftBest = state.leftTeam.value;
    final rightBest = state.rightTeam.value;
    var left = index == 0
        ? leftBest.scoreKing
        : index == 1
            ? leftBest.reboundKing
            : leftBest.assistKing;
    var right = index == 0
        ? rightBest.scoreKing
        : index == 1
            ? rightBest.reboundKing
            : rightBest.assistKing;

    final leftS = index == 0
        ? int.parse(
            left?.score == "" || left?.score == null ? '0' : left?.score ?? '0')
        : (index == 1 ? left?.rebound ?? 0 : left?.assist ?? 0);
    final rightS = index == 0
        ? int.parse(right?.score == "" || right?.score == null
            ? '0'
            : right?.score ?? '0')
        : (index == 1 ? right?.rebound ?? 0 : right?.assist ?? 0);

    final text = index == 0 ? '得分' : (index == 1 ? '篮板' : '助攻');
    return Row(
      children: [
        Expanded(
            child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => logic.unlockPlayer(
              state.model.value.teams?.first?.teamId ?? "",
              left?.playerId ?? "",
              left?.locked == 1),
          child: Row(
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Stack(children: [
                    MyImage(
                      left?.photo ?? "",
                      width: 50.w,
                      height: 60.w,
                      isAssetImage: false,
                      radius: 4.r,
                    ),
                    Visibility(
                      visible: left?.locked == 1,
                      child: Positioned.fill(
                          child: Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: WxAssets.images.icGameLock
                            .image(width: 18.w, fit: BoxFit.fill),
                      )),
                    ),
                  ])),
              SizedBox(
                width: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    left?.locked == 1 ? '**' : leftS.toString(),
                    style: GoogleFonts.oswald(
                        fontSize: 20.sp,
                        fontWeight: AppFontWeight.medium(),
                        color: Colours.white,
                        height: 1),
                  ),
                  // SizedBox(
                  //   height: 18.w,
                  // ),
                  Visibility(
                    visible: left?.locked == 0,
                    child: Container(
                      height: 20.w,
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      decoration: BoxDecoration(
                          color: Colours.color2E1575,
                          borderRadius: BorderRadius.all(Radius.circular(10.w)),
                          border: Border.all(
                            width: 1,
                            color: Colours.color6435E9,
                          )),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.contributeIcon
                              .image(width: 12.w, height: 12.w),
                          SizedBox(
                            width: 3.w,
                          ),
                          SizedBox(
                              // width: 50.w,
                              child: Text(
                            '贡献${left?.contributionValue}',
                            style: TextStyles.medium.copyWith(
                                color: Colours.white, fontSize: 10.sp),
                          ))
                        ],
                      ),
                    ),
                  ).marginSymmetric(vertical: 6.w),
                  Text(
                    '#${left?.number ?? ""}',
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  )
                ],
              ),
            ],
          ),
        )),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  width: 11.w,
                  height: (leftS >= rightS ? 1 : leftS / rightS) * 32.w,
                  decoration: BoxDecoration(
                    color: leftS < rightS
                        ? Colours.color2F2F3B
                        : Colours.color7732ED,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
                SizedBox(
                  width: 6.w,
                ),
                Container(
                  width: 11.w,
                  height: (leftS > rightS ? rightS / leftS : 1) * 32.w,
                  decoration: BoxDecoration(
                    color: rightS < leftS
                        ? Colours.color2F2F3B
                        : Colours.colorE282FF,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            Text(
              text,
              style: TextStyles.regular.copyWith(fontSize: 12.sp),
            ),
          ],
        ),
        Expanded(
            child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => logic.unlockPlayer(
              state.model.value.teams?.last?.teamId ?? "",
              right?.playerId ?? "",
              right?.locked == 1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    right?.locked == 1 ? '**' : rightS.toString(),
                    style: GoogleFonts.oswald(
                        fontSize: 20.sp,
                        fontWeight: AppFontWeight.medium(),
                        color: Colours.white,
                        height: 1),
                  ),
                  // SizedBox(
                  //   height: 18.w,
                  // ),
                  Visibility(
                    visible: right?.locked == 0,
                    child: Container(
                      height: 20.w,
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      decoration: BoxDecoration(
                          color: Colours.color2E1575,
                          borderRadius: BorderRadius.all(Radius.circular(10.w)),
                          border: Border.all(
                            width: 1,
                            color: Colours.color6435E9,
                          )),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.contributeIcon
                              .image(width: 12.w, height: 12.w),
                          SizedBox(
                            width: 3.w,
                          ),
                          SizedBox(
                              // width: 50.w,
                              child: Text(
                            '贡献${right?.contributionValue}',
                            style: TextStyles.medium.copyWith(
                                color: Colours.white, fontSize: 10.sp),
                          ))
                        ],
                      ),
                    ),
                  ).marginSymmetric(vertical: 6.w),
                  Text(
                    '#${right?.number ?? ""}',
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  )
                ],
              ),
              SizedBox(
                width: 10.w,
              ),
              ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Stack(children: [
                    MyImage(
                      right?.photo ?? "",
                      width: 50.w,
                      height: 60.w,
                      isAssetImage: false,
                      radius: 4.r,
                    ),
                    Visibility(
                      visible: right?.locked == 1,
                      child: Positioned.fill(
                          child: Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: WxAssets.images.icGameLock
                            .image(width: 18.w, fit: BoxFit.fill),
                      )),
                    ),
                  ])),
            ],
          ),
        )),
      ],
    ).paddingOnly(bottom: 20.w);
  }

  Widget _score(BuildContext context) {
    final hide =
        state.sectionScoreList == null || state.sectionScoreList!.length != 2;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
          child: const TextWithIcon(title: '比分走势'),
        ),
        Visibility(
          visible: !hide,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 16.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: 3,
                itemBuilder: (context, index) {
                  return _scoreListItem(context, index);
                }),
          ).marginOnly(bottom: 13.w),
        ),
        Container(
          width: 345.w,
          height: 180.w,
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.w),
          ),
          child: _lineChart(context),
        ),
      ],
    );
  }

  Widget _lineChart(BuildContext context) {
    final left = (state.model.value.teams?.first?.maxOffsetScore ?? 0) <= 0
        ? '无领先时刻'
        : '最高领先${state.model.value.teams?.first?.maxOffsetScore ?? 0}分';
    final right = (state.model.value.teams?.last?.maxOffsetScore ?? 0) <= 0
        ? '无领先时刻'
        : '最高领先${state.model.value.teams?.last?.maxOffsetScore}分';
    return Stack(children: [
      LayoutBuilder(
        builder: (context, constraints) => Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            VerticalDashedLine(
              height: constraints.maxHeight, // 高度
              dashHeight: 3.w, // 虚线段高度
              dashSpacing: 3.w, // 虚线间距
              color: Colours.color282835, // 虚线颜色
            ),
            VerticalDashedLine(
              height: constraints.maxHeight, // 高度
              dashHeight: 3.w, // 虚线段高度
              dashSpacing: 3.w, // 虚线间距
              color: Colours.color282835, // 虚线颜色
            ),
            VerticalDashedLine(
              height: constraints.maxHeight, // 高度
              dashHeight: 3.w, // 虚线段高度
              dashSpacing: 3.w, // 虚线间距
              color: Colours.color282835, // 虚线颜色
            ),
          ],
        ).paddingSymmetric(horizontal: 92.w),
      ),
      LineChart(logic.lineChartData)
          .paddingOnly(left: 12.w, right: 12.w, top: 32.w, bottom: 12.w),
      Positioned(
          left: 15.w,
          top: 18.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                        color: Colours.color7732ED,
                        borderRadius: BorderRadius.circular(2.w)),
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    '${state.model.value.teams?.first?.teamName ?? ""}（$left）',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              ),
              SizedBox(
                height: 10.w,
              ),
              Row(
                children: [
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                        color: Colours.colorE282FF,
                        borderRadius: BorderRadius.circular(2.w)),
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    '${state.model.value.teams?.last?.teamName ?? ""}（$right）',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              ),
            ],
          )),
    ]);
  }

  Widget _scoreListItem(BuildContext context, int index) {
    final one = state.sectionScoreList!.first;
    final two = state.sectionScoreList!.last;
    final color = index == 0 ? Colours.color5C5C6E : Colours.white;
    final text2 =
        index == 0 ? "Q1" : (index == 1 ? one.secScore1 : two.secScore1);
    final text3 =
        index == 0 ? "Q2" : (index == 1 ? one.secScore2 : two.secScore2);
    final text4 =
        index == 0 ? "Q3" : (index == 1 ? one.secScore3 : two.secScore3);
    final text5 =
        index == 0 ? "Q4" : (index == 1 ? one.secScore4 : two.secScore4);
    final text6 =
        index == 0 ? "总分" : (index == 1 ? one.totalScore : two.totalScore);
    final url = index == 1 ? one.teamLogo : two.teamLogo;
    final widget = index == 0
        ? _scoreText('球队', color)
        : Center(
            child: ClipRRect(
                borderRadius: BorderRadius.circular(9),
                child: CachedNetworkImage(
                    imageUrl: url, width: 18.w, fit: BoxFit.cover)));
    return Row(
      children: [
        Expanded(child: widget),
        Expanded(child: _scoreText(text2.toString(), color)),
        Expanded(child: _scoreText(text3.toString(), color)),
        Expanded(child: _scoreText(text4.toString(), color)),
        Expanded(child: _scoreText(text5.toString(), color)),
        Expanded(child: _scoreText(text6.toString(), color)),
      ],
    ).paddingOnly(bottom: index < 2 ? 20.w : 0);
  }

  Widget _scoreText(String text, Color color) {
    return Text(
      text,
      style: TextStyles.regular.copyWith(fontSize: 12.sp, color: color),
      textAlign: TextAlign.center,
    );
  }

  Widget _analyze(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
          child: const TextWithIcon(title: '技术分析'),
        ),
        Container(
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w, bottom: 6.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.w),
          ),
          child: ListView.builder(
              physics: const ClampingScrollPhysics(),
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: 10,
              itemBuilder: (context, index) {
                final leftTeam =
                    state.model.value.teams?.first?.teamScoreDetail;
                final rightTeam =
                    state.model.value.teams?.last?.teamScoreDetail;
                var needCalculate = true;
                var left = 0.0;
                var right = 0.0;
                var title = '';
                switch (index) {
                  case 0:
                    title = '得分';
                    left = (leftTeam?.totalScore ?? 0).toDouble();
                    right = (rightTeam?.totalScore ?? 0).toDouble();
                  case 1:
                    title = '篮板';
                    left = (leftTeam?.reboundCount ?? 0).toDouble();
                    right = (rightTeam?.reboundCount ?? 0).toDouble();
                  case 2:
                    title = '助攻';
                    left = (leftTeam?.assistCount ?? 0).toDouble();
                    right = (rightTeam?.assistCount ?? 0).toDouble();
                  case 3:
                    title = '投篮命中率';
                    left = double.parse(leftTeam?.shootRate != "" &&
                                leftTeam?.shootRate != null
                            ? leftTeam?.shootRate ?? '0.0'
                            : '0.0') /
                        100;
                    right = double.parse(rightTeam?.shootRate != "" &&
                                rightTeam?.shootRate != null
                            ? rightTeam?.shootRate ?? '0.0'
                            : '0.0') /
                        100;
                    needCalculate = false;
                  case 4:
                    title = '三分';

                    left = (leftTeam?.threePointShootCount ?? 0).toDouble();
                    right = (rightTeam?.threePointShootCount ?? 0).toDouble();
                  case 5:
                    title = '三分命中率';
                    left = double.parse(leftTeam?.threePointShootRate != "" &&
                                leftTeam?.threePointShootRate != null
                            ? leftTeam?.threePointShootRate ?? '0.0'
                            : '0.0') /
                        100;
                    right = double.parse(rightTeam?.threePointShootRate != "" &&
                                rightTeam?.threePointShootRate != null
                            ? rightTeam?.threePointShootRate ?? '0.0'
                            : '0.0') /
                        100;

                    needCalculate = false;
                  case 6:
                    title = '前场篮板';
                    left = (leftTeam?.offensiveReboundCount ?? 0).toDouble();
                    right = (rightTeam?.offensiveReboundCount ?? 0).toDouble();
                  case 7:
                    title = '后场篮板';
                    left = (leftTeam?.defensiveReboundCount ?? 0).toDouble();
                    right = (rightTeam?.defensiveReboundCount ?? 0).toDouble();
                  case 8:
                    title = '罚球';
                    left = (leftTeam?.freeThrowShootCount ?? 0).toDouble();
                    right = (rightTeam?.freeThrowShootCount ?? 0).toDouble();
                  case 9:
                    title = '罚球命中率';

                    left = double.parse(leftTeam?.freeThrowShootRate != "" &&
                                leftTeam?.freeThrowShootRate != null
                            ? leftTeam?.freeThrowShootRate ?? '0.0'
                            : '0.0') /
                        100;
                    right = double.parse(rightTeam?.freeThrowShootRate != "" &&
                                rightTeam?.freeThrowShootRate != null
                            ? rightTeam?.freeThrowShootRate ?? '0.0'
                            : '0.0') /
                        100;

                    needCalculate = false;
                }
                return AnalyzeItemView(
                  left: left,
                  right: right,
                  title: title,
                  needCalculate: needCalculate,
                ).marginOnly(bottom: 18.w);
              }),
        ),
      ],
    );
  }

  Widget _history(BuildContext context) {
    final hide =
        state.model.value.history == null || state.model.value.history!.isEmpty;
    return Visibility(
      visible: !hide,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
            child: const TextWithIcon(title: '历史交锋'),
          ),
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(8.w),
            ),
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: state.model.value.history?.length ?? 0,
                itemBuilder: (context, index) {
                  return _historyListItem(context, index);
                }),
          ),
        ],
      ),
    );
  }

  Widget _historyListItem(BuildContext context, int index) {
    final data = state.model.value.history![index];
    final leftText = data?.winner == 0 ? '平' : (data?.winner == 1 ? '胜' : '负');
    final leftColor = data?.winner == 0
        ? Colours.color2F2F3B
        : (data?.winner == 1 ? Colours.color5E2E33 : Colours.color3B5844);
    final leftTextColor =
        data?.winner == 2 ? Colours.color5C5C6E : Colours.white;
    final rightText = data?.winner == 0 ? '平' : (data?.winner == 2 ? '胜' : '负');
    final rightColor = data?.winner == 0
        ? Colours.color2F2F3B
        : (data?.winner == 2 ? Colours.color5E2E33 : Colours.color3B5844);
    final rightTextColor =
        data?.winner == 1 ? Colours.color5C5C6E : Colours.white;
    return Row(
      children: [
        Expanded(
            child: Row(
          children: [
            Container(
              width: 20.w,
              height: 20.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: leftColor,
                borderRadius: BorderRadius.circular(4.w),
              ),
              child: Text(
                leftText,
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
              ),
            ),
            SizedBox(
              width: 20.w,
            ),
            Text(
              (data?.leftScore ?? "").toString(),
              style: GoogleFonts.oswald(
                  fontSize: 18.sp,
                  color: leftTextColor,
                  fontWeight: AppFontWeight.semiBold()),
            )
          ],
        )),
        Text(
          data?.matchTime ?? "",
          style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
        ),
        Expanded(
            child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              (data?.rightScore ?? "").toString(),
              style: GoogleFonts.oswald(
                  fontSize: 18.sp,
                  color: rightTextColor,
                  fontWeight: AppFontWeight.semiBold()),
            ),
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 20.w,
              height: 20.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: rightColor,
                borderRadius: BorderRadius.circular(4.w),
              ),
              child: Text(
                rightText,
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
              ),
            ),
          ],
        )),
      ],
    ).paddingOnly(
        bottom:
            index < (state.model.value.history?.length ?? 0) - 1 ? 25.w : 0);
  }

  Widget _marking(BuildContext context) {
    return Visibility(
      visible: logic.canRate.value,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
            child: const TextWithIcon(title: '评分'),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(8.w),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '本次赛事报告是否达到您的预期？请选择满意度提交评价',
                  style: TextStyles.display12,
                ),
                SizedBox(
                  height: 20.w,
                ),
                StarRating(
                    rating: logic.rate.value,
                    onRatingChanged: (rate) {
                      log('message$rate');
                      logic.rate.value = rate;
                    }),
                if (logic.rate.value > 0)
                  Column(
                    children: [
                      _rateTextTitle(logic.rate.value >= 4 ? '建议类型' : '反馈类型',
                          showStar: logic.rate.value < 4),
                      if (logic.rate < 4)
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              WxButton(
                                onPressed: () {
                                  logic.feedbackType.value = 1;
                                },
                                text: '数据错误',
                                width: 95.w,
                                height: 40.w,
                                textStyle: TextStyles.regular,
                                backgroundColor: Colours.color191921,
                                linearGradient: logic.feedbackType.value == 1
                                    ? GradientUtils.mainGradient
                                    : null,
                                borderSide: logic.feedbackType.value == 1
                                    ? null
                                    : BorderSide(
                                        color: Colours.white, width: 1.w),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.w)),
                              ),
                              WxButton(
                                onPressed: () {
                                  logic.feedbackType.value = 2;
                                },
                                text: '视频缺失',
                                width: 95.w,
                                height: 40.w,
                                textStyle: TextStyles.regular,
                                backgroundColor: Colours.color191921,
                                linearGradient: logic.feedbackType.value == 2
                                    ? GradientUtils.mainGradient
                                    : null,
                                borderSide: logic.feedbackType.value == 2
                                    ? null
                                    : BorderSide(
                                        color: Colours.white, width: 1.w),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.w)),
                              ),
                              WxButton(
                                onPressed: () {
                                  logic.feedbackType.value = 3;
                                },
                                text: '其他问题',
                                width: 95.w,
                                height: 40.w,
                                textStyle: TextStyles.regular,
                                backgroundColor: Colours.color191921,
                                linearGradient: logic.feedbackType.value == 3
                                    ? GradientUtils.mainGradient
                                    : null,
                                borderSide: logic.feedbackType.value == 3
                                    ? null
                                    : BorderSide(
                                        color: Colours.white, width: 1.w),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.w)),
                              ),
                            ]),
                      if (logic.rate >= 4)
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              WxButton(
                                onPressed: () {
                                  logic.suggestionType.value = 1;
                                },
                                text: '更多数据',
                                width: 95.w,
                                height: 40.w,
                                textStyle: TextStyles.regular,
                                backgroundColor: Colours.color191921,
                                linearGradient: logic.suggestionType.value == 1
                                    ? GradientUtils.mainGradient
                                    : null,
                                borderSide: logic.suggestionType.value == 1
                                    ? null
                                    : BorderSide(
                                        color: Colours.white, width: 1.w),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.w)),
                              ),
                              WxButton(
                                onPressed: () {
                                  logic.suggestionType.value = 2;
                                },
                                text: '更多功能',
                                width: 95.w,
                                height: 40.w,
                                textStyle: TextStyles.regular,
                                backgroundColor: Colours.color191921,
                                linearGradient: logic.suggestionType.value == 2
                                    ? GradientUtils.mainGradient
                                    : null,
                                borderSide: logic.suggestionType.value == 2
                                    ? null
                                    : BorderSide(
                                        color: Colours.white, width: 1.w),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.w)),
                              ),
                              WxButton(
                                onPressed: () {
                                  logic.suggestionType.value = 3;
                                },
                                text: '其他问题',
                                width: 95.w,
                                height: 40.w,
                                textStyle: TextStyles.regular,
                                backgroundColor: Colours.color191921,
                                linearGradient: logic.suggestionType.value == 3
                                    ? GradientUtils.mainGradient
                                    : null,
                                borderSide: logic.suggestionType.value == 3
                                    ? null
                                    : BorderSide(
                                        color: Colours.white, width: 1.w),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.w)),
                              ),
                            ]),
                      // _rateTextTitle('球队名称'),
                      // InkWell(
                      //     onTap: () {
                      //       getOpenTeamDialog(context);
                      //     },
                      //     child: Container(
                      //         height: 50.w,
                      //         padding: EdgeInsets.symmetric(horizontal: 15.w),
                      //         decoration: BoxDecoration(
                      //           color: Colours.color0F0F16,
                      //           borderRadius: BorderRadius.circular(25.w),
                      //         ),
                      //         child: Row(
                      //           mainAxisAlignment:
                      //               MainAxisAlignment.spaceBetween,
                      //           children: [
                      //             Text(
                      //               logic.selectedTeamName.value.isEmpty
                      //                   ? '请选择球队名称'
                      //                   : logic.selectedTeamName.value,
                      //               style: logic.selectedTeamName.value.isEmpty
                      //                   ? TextStyles.display14.copyWith(
                      //                       color: Colours.color5C5C6E)
                      //                   : TextStyles.display14,
                      //             ),
                      //             Icon(
                      //               Icons.arrow_forward_ios,
                      //               color: Colours.white,
                      //               size: 14.sp,
                      //             )
                      //           ],
                      //         ))),
                      _rateTextTitle('球员号码'),
                      InkWell(
                          onTap: () {
                            logic.selectedPlayerId.value =
                                logic.selectedPlayer.value.playerId ?? '';
                            getOpenPlayerDialog(context);
                          },
                          child: Container(
                              height: 50.w,
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              decoration: BoxDecoration(
                                color: Colours.color0F0F16,
                                borderRadius: BorderRadius.circular(25.w),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    logic.selectedPlayer.value.playerId != null
                                        ? '${logic.selectedTeamName.value}-${logic.selectedPlayer.value.number ?? '-'}号'
                                        : '请选择球员号码',
                                    style:
                                        logic.selectedPlayer.value.playerId !=
                                                null
                                            ? TextStyles.semiBold14
                                            : TextStyles.display14.copyWith(
                                                color: Colours.color5C5C6E),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colours.white,
                                    size: 14.sp,
                                  )
                                ],
                              ))),
                      _rateTextTitle('问题描述', showStar: false),
                      Container(
                        // height: 176.w,
                        padding: EdgeInsets.all(15.w),
                        decoration: BoxDecoration(
                          color: Colours.color0F0F16,
                          borderRadius: BorderRadius.circular(8.w),
                        ),
                        child: Column(
                          children: [
                            TextField(
                              focusNode: logic.nodeText,
                              maxLines: 8,
                              controller: logic.textController,
                              style: TextStyles.semiBold14,
                              decoration: InputDecoration(
                                hintText: '请输入问题描述',
                                hintStyle: TextStyles.regular
                                    .copyWith(color: Colours.color5C5C6E),
                                contentPadding:
                                    const EdgeInsets.only(top: 0, bottom: 0),
                                border: InputBorder.none,
                              ),
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(150)
                              ],
                              keyboardType: TextInputType.text,
                              onTapOutside: (_) =>
                                  FocusScope.of(context).unfocus(),
                              onTap: () => _handleInputTap,
                            ),
                            Container(
                              alignment: Alignment.bottomRight,
                              child: Text(
                                '${logic.charCount.value}/150',
                                style: TextStyles.display12
                                    .copyWith(fontSize: 14.sp),
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 20.w,
                      ),
                      Container(
                          alignment: Alignment.center,
                          child: WxButton(
                            text: '提交',
                            linearGradient: const LinearGradient(colors: [
                              Colours.colorFFECC1,
                              Colours.colorE7CEFF,
                              Colours.colorD1EAFF
                            ]),
                            textStyle: TextStyles.semiBold14
                                .copyWith(color: Colours.color922BFF),
                            height: 40.w,
                            width: 116.w,
                            borderRadius: BorderRadius.circular(20.w),
                            onPressed: () {
                              logic.scoreSubmit();
                            },
                          )),
                    ],
                  )
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleInputTap() {
    // 使用Get.context前检查mounted
    // if (Get.context != null && Get.context!.mounted) {
    //   Future.delayed(Duration(milliseconds: 300), () {
    //     if (Get.context != null && Get.context!.mounted) {
    //       Scrollable.ensureVisible(
    //         Get.context!,
    //         duration: Duration(milliseconds: 300),
    //         curve: Curves.easeOut,
    //       );
    //     }
    //   });
    // }
  }

  Widget _rateTextTitle(String title, {bool showStar = true}) {
    return Row(children: [
      Opacity(
          opacity: showStar ? 1.0 : 0,
          child: Text('*',
              style:
                  TextStyles.display14.copyWith(color: Colours.colorFF3F3F))),
      SizedBox(
        width: 1.w,
      ),
      Text(title, style: TextStyles.semiBold14),
    ]).marginOnly(top: 20.w, bottom: 15.w);
  }

  Widget _title(BuildContext context) {
    return SizedBox(
      height: kToolbarHeight + MediaQuery.of(context).padding.top,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(gradient: GradientUtils.mainGradient),
          ),
          Positioned(
              top: MediaQuery.of(context).padding.top,
              bottom: 0,
              left: 0,
              right: 0,
              child: Stack(children: [
                Positioned(
                  top: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onTap: () => AppPage.back(),
                    child: WxAssets.images.arrowLeft.image(color: Colors.white),
                  ),
                ),
                Center(
                  child: Text(
                    S.current.competition_overview,
                    style: TextStyles.display16
                        .copyWith(fontWeight: FontWeight.w600),
                  ),
                ),
                Positioned(
                    right: 15.w,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: logic.share,
                        child: Center(
                            child: WxAssets.images.share3
                                .image(width: 20.w, fit: BoxFit.fill)))),
              ])),
        ],
      ),
    );
  }

  //选择解锁球队
  void getOpenTeamDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 276.w,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                SizedBox(
                  height: 18.w,
                ),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Text(
                          S.current.cancel,
                          style: TextStyles.display14
                              .copyWith(color: Colours.color5C5C6E),
                        ),
                      ),
                      Text(
                        S.current.select_team_name,
                        style: TextStyles.titleSemiBold16,
                      ),
                      GestureDetector(
                          onTap: () {
                            Get.back();
                            logic.selectedTeamId = logic
                                    .state
                                    .model
                                    .value
                                    .teams?[logic.selectedTeamIndex.value]
                                    ?.teamId ??
                                '';
                            logic.selectedTeamName.value = logic
                                    .state
                                    .model
                                    .value
                                    .teams?[logic.selectedTeamIndex.value]
                                    ?.teamName ??
                                '';
                          },
                          child: Text(
                            S.current.save,
                            style: TextStyles.display14
                                .copyWith(color: Colours.color922BFF),
                          ))
                    ]),
                Expanded(
                  child: Stack(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildPicker(
                            controller: logic.teamController,
                            items: logic.state.model.value.teams ?? [],
                            selectIndex: logic.selectedTeamIndex,
                            onSelectedItemChanged: (index) {},
                          ),
                        ],
                      ),
                      Positioned(
                          top: 115.w,
                          left: 30.w,
                          right: 30.w,
                          child: Container(
                            width: ScreenUtil().screenWidth - 60,
                            height: 1.w,
                            color: const Color(0xff2F2F3B),
                          )),
                    ],
                  ),
                ),
                SizedBox(
                  height: ScreenUtil().bottomBarHeight,
                )
              ],
            ),
          );
        });
      },
    );
  }

  Widget _buildPicker(
      {required FixedExtentScrollController controller,
      required List<MatchesInfoModelTeams?> items,
      required ValueChanged<int> onSelectedItemChanged,
      required RxInt selectIndex}) {
    return Obx(() {
      return SizedBox(
        width: 200.w,
        // color: Colors.yellow,
        child: ListWheelScrollView(
          controller: controller,
          useMagnifier: true, // 启用放大镜效果
          magnification: 1.067, // 放大倍数
          itemExtent: 40,
          physics: const FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            selectIndex.value = index;
          },
          children: List.generate(items.length, (index) {
            // cc.log('!!!!!!!${selectIndex.value}$index');
            return Center(
              child: Text(
                items[index]?.teamName ?? '',
                style: TextStyle(
                    fontSize: 16.sp,
                    color: index == selectIndex.value
                        ? Colors.white
                        : const Color(0xff9393A5)),
              ),
            );
          }),
        ),
      );
    });
  }

  //选择解锁球员
  void getOpenPlayerDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 500.w,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 20.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.select_player,
                      style: TextStyles.titleSemiBold16,
                    )),
                Container(
                  width: double.infinity,
                  height: 40.w,
                  alignment: Alignment.centerLeft,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          logic.switchTab(0);
                        },
                        child: Column(
                          children: [
                            logic.tabbarIndex.value == 0
                                ? ShaderMask(
                                    shaderCallback: (bounds) =>
                                        const LinearGradient(
                                          colors: [
                                            Colours.colorFFF9DC,
                                            Colours.colorE4C8FF,
                                            Colours.colorE5F3FF,
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ).createShader(bounds),
                                    child: Text(
                                      logic.state.model.value.teams?.first
                                              ?.teamName ??
                                          "",
                                      style:
                                          TextStyles.titleSemiBold16.copyWith(
                                        color: Colors.white,
                                      ),
                                    ))
                                : Text(
                                    logic.state.model.value.teams?.first
                                            ?.teamName ??
                                        "",
                                    style: TextStyles.display14.copyWith(
                                      color: Colours.color5C5C6E,
                                    ),
                                  ),
                            logic.tabbarIndex.value == 0
                                ? WxAssets.images.imgCheckIn2.image()
                                : SizedBox(height: 10.w)
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 25.w,
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          logic.switchTab(1);
                        },
                        child: Column(
                          children: [
                            logic.tabbarIndex.value == 1
                                ? ShaderMask(
                                    shaderCallback: (bounds) =>
                                        const LinearGradient(
                                          colors: [
                                            Colours.colorFFF9DC,
                                            Colours.colorE4C8FF,
                                            Colours.colorE5F3FF,
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ).createShader(bounds),
                                    child: Text(
                                      logic.state.model.value.teams?.last
                                              ?.teamName ??
                                          "",
                                      style:
                                          TextStyles.titleSemiBold16.copyWith(
                                        color: Colors.white,
                                      ),
                                    ))
                                : Text(
                                    logic.state.model.value.teams?.last
                                            ?.teamName ??
                                        "",
                                    style: TextStyles.display14.copyWith(
                                      color: Colours.color5C5C6E,
                                    ),
                                  ),
                            logic.tabbarIndex.value == 1
                                ? WxAssets.images.imgCheckIn2.image()
                                : SizedBox(height: 10.w)
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
                Expanded(
                  child: TabBarView(controller: logic.tabController, children: [
                    tabviewItemWidget(logic.dataPlayerList1),
                    tabviewItemWidget(logic.dataPlayerList2),
                  ]),
                ),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(bottom: 25.w, top: 0.w),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      if (logic.selectedPlayerId.value == "") {
                        WxLoading.showToast(S.current.unlock_person_tips6);
                        return;
                      }
                      if (logic.tabbarIndex.value == 0) {
                        logic.selectedPlayer.value = logic.dataPlayerList1
                            .firstWhere((element) =>
                                element.playerId ==
                                logic.selectedPlayerId.value);
                      } else {
                        logic.selectedPlayer.value = logic.dataPlayerList2
                            .firstWhere((element) =>
                                element.playerId ==
                                logic.selectedPlayerId.value);
                      }
                      logic.selectedTeamId = logic.state.model.value
                              .teams?[logic.tabbarIndex.value]?.teamId ??
                          '';
                      logic.selectedTeamName.value = logic.state.model.value
                              .teams?[logic.tabbarIndex.value]?.teamName ??
                          '';
                      Get.back();
                    },
                    child: Container(
                      height: 50.w,
                      width: double.infinity,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Colours.color282735,
                        borderRadius: BorderRadius.all(Radius.circular(25.r)),
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        S.current.sure,
                        style: TextStyles.semiBold14,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }

  SizedBox tabviewItemWidget(RxList<TeamPlayersModelPlayers> dataPlayerList) {
    return SizedBox(
      height: 580.w,
      child: ListView(
        shrinkWrap: true,
        children: [
          GridView.builder(
              scrollDirection: Axis.vertical,
              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              shrinkWrap: true,
              physics:
                  const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 75 / 130,
              ),
              padding: EdgeInsets.only(bottom: 0.w, top: 0.w),
              itemCount: dataPlayerList.length,
              itemBuilder: (context, index) {
                return Obx(() {
                  return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      if (dataPlayerList[index].locked == 0) {
                        logic.selectedPlayerId.value =
                            dataPlayerList[index].playerId ?? "";
                      }
                    },
                    child: Column(
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            MyImage(
                              dataPlayerList[index].photo ?? '',
                              //  holderImg: "home/index/df_banner_top",
                              fit: BoxFit.fill,
                              width: 75.w,
                              height: 100.w,
                              isAssetImage: false,
                              // errorImg: "home/index/df_banner_top"
                              radius: 4.r,
                            ),
                            if (logic.selectedPlayerId.value ==
                                dataPlayerList[index].playerId)
                              Container(
                                width: 75.w,
                                height: 100.w,
                                alignment: Alignment.center,
                                decoration: const BoxDecoration(
                                    color: Colours.color80000000),
                                child: Container(
                                  width: 20.w,
                                  height: 20.w,
                                  margin: EdgeInsets.only(
                                      right: 8.w, bottom: 3.w, top: 8.w),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colours.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            if (dataPlayerList[index].locked == 1)
                              Container(
                                width: 75.w,
                                height: 100.w,
                                alignment: Alignment.center,
                                decoration: const BoxDecoration(
                                    color: Colours.color80000000),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    WxAssets.images.icGameLock
                                        .image(width: 18.w, height: 18.w),
                                    SizedBox(
                                      height: 8.w,
                                    ),
                                    Text(
                                      S.current.locked,
                                      style: TextStyles.semiBold14
                                          .copyWith(fontSize: 12.sp),
                                    )
                                  ],
                                ),
                              )
                          ],
                        ),
                        SizedBox(
                          height: 10.w,
                        ),
                        Center(
                          child: Text(
                            dataPlayerList[index].number == ''
                                ? '-'
                                : S.current.player_number(
                                    (dataPlayerList[index].number ?? "")),
                            maxLines: 1,
                            textAlign: TextAlign.center,
                            style: dataPlayerList[index].locked == 0
                                ? TextStyles.semiBold14
                                    .copyWith(fontSize: 12.sp)
                                : TextStyles.semiBold14.copyWith(
                                    fontSize: 12.sp,
                                    color: Colours.color5C5C6E),
                          ),
                        )
                      ],
                    ),
                  );
                });
              }),
        ],
      ),
    );
  }
}
