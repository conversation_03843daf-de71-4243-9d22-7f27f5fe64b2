import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';

import 'models/game_date.dart';

class GameState {
  var isLoading = false.obs;
  var init = false.obs;
  var gameDates = GameDate.getDateAndWeekArray();
  final ItemScrollController scrollController = ItemScrollController();
  var selectedIndex = 7.obs;
  var items = <GameModel>[].obs; // 数据源
}