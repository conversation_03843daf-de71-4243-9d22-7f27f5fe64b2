// ignore_for_file: invalid_use_of_protected_member

import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/pages/game/state.dart';

import '../../utils/location_utils.dart';

class GameLogic extends GetxController {
  final GameState state = GameState();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    onRefresh();
  }

  void selectedIndex(int index) async {
    if (index == state.selectedIndex.value) {
      return;
    }
    var old = state.selectedIndex.value;
    state.selectedIndex.value = index;
    var alignment = 0.5 - 1 / 7 * 0.5;
    // if(index < 3) {
    //   alignment -= 1/7 * (3-index);
    // }
    // else if(index > 20 - 1 - 3) {//20 - 1是总数-1 就是最大的index
    //   alignment = 1 - (1/7 * (20 - index) * 100).floor() / 100;
    // }
    if (!((index <= 3 && old <= 3) ||
        (old >= state.gameDates.length - 1 - 3 &&
            index >= state.gameDates.length - 1 - 3))) {
      state.scrollController.scrollTo(
        index: index,
        duration: const Duration(milliseconds: 300),
        alignment: alignment, // 居中对齐
      );
    }

    WxLoading.show();
    await onRefresh();
    WxLoading.dismiss();
  }

  Future<void> onRefresh() async {
    state.isLoading.value = true;
    Map<String, dynamic> param = {
      'date': state.gameDates[state.selectedIndex.value].date
    };
    final position = LocationUtils.instance.position;
    if (position != null) {
      param['latitude'] = '${position.latitude}';
      param['longitude'] = '${position.longitude}';
    }
    var res = await Api().get(ApiUrl.gameList, queryParameters: param);
    log("gameListmessage${res.data}");
    state.init.value = true;
    state.isLoading.value = false;
    if (res.isSuccessful()) {
      state.items.value =
          (res.data as List).map((e) => GameModel.fromJson(e)).toList();
    }
  }

  /// 计算总项目数（包括分组标题和所有列表项）
  int calculateTotalItemCount() {
    int count = 0;
    for (var value in state.items.value) {
      count += 1; // 分组标题
      count += value.matches.length; // 每个分组的列表项
    }
    return count;
  }

  getMatchesSubscribe(Matches matches) async {
    Map<String, dynamic> param = {"matchId": matches.matchId};
    var res = await Api()
        .post(ApiUrl.matchesSubscribe(matches.matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.game_report4);
      onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  /// 根据索引获取对应的内容（分组标题或列表项）
  dynamic getItemAtIndex(int index) {
    int currentIndex = 0;

    for (var entry in state.items.value) {
      // 分组标题
      if (currentIndex == index) {
        return entry;
      }
      currentIndex++;

      // 列表项
      for (var item in entry.matches) {
        if (currentIndex == index) {
          return item;
        }
        currentIndex++;
      }
    }

    return '';
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
