import 'package:intl/intl.dart';

import '../../../generated/l10n.dart';

class GameDate {
  String date;
  String ym;
  String dd;
  String week;

  GameDate(this.date, this.ym, this.dd,this.week);

  static List<GameDate> getDateAndWeekArray() {
    // 初始化日期格式化器
    final DateFormat dateFormatter = DateFormat('yyyy-MM-dd');
    final DateFormat ymFormatter = DateFormat('yyyy.MM');
    final DateFormat ddFormatter = DateFormat('dd');
    final List<String> weekDays = [S.current.Mon, S.current.Tue, S.current.Wed, S.current.Thu, S.current.Fri, S.current.Sat, S.current.Sun];

    // 获取当前日期
    final DateTime today = DateTime.now();

    // 结果数组
    List<GameDate> result = [];

    for (int i = -7; i <= 2; i++) {
      // 计算偏移日期
      DateTime currentDate = today.add(Duration(days: i));

      // 格式化日期
      String formattedDate = dateFormatter.format(currentDate);
      String formattedYm = ymFormatter.format(currentDate);
      String formattedDD = ddFormatter.format(currentDate);

      // 获取星期（DateTime 的 weekday: 1~7 对应周一到周日）
      String weekDay = weekDays[currentDate.weekday - 1];

      // 添加到结果数组
      result.add(GameDate(formattedDate, formattedYm, formattedDD, weekDay));
    }

    return result;
  }

}