import 'package:json_annotation/json_annotation.dart';

part 'game_model.g.dart';

@JsonSerializable()
class GameModel extends Object {
  @Json<PERSON>ey(name: 'arenaAliasName')
  String arenaAliasName;

  @Json<PERSON>ey(name: 'arenaId')
  String arenaId;

  @JsonKey(name: 'arenaLogo')
  String arenaLogo;

  @JsonKey(name: 'arenaName')
  String arenaName;

  @JsonKey(name: 'matches')
  List<Matches> matches;

  GameModel(
    this.arenaAliasName,
    this.arenaId,
    this.arenaLogo,
    this.arenaName,
    this.matches,
  );

  factory GameModel.fromJson(Map<String, dynamic> srcJson) =>
      _$GameModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$GameModelToJson(this);
}

@JsonSerializable()
class Matches extends Object {
  @JsonKey(name: 'courts')
  List<String> courts;

  @JsonKey(name: 'leftScore')
  int leftScore;

  @Json<PERSON>ey(name: 'leftTeamId')
  String leftTeamId;

  @Json<PERSON>ey(name: 'leftTeamLogo')
  String leftTeamLogo;

  @Json<PERSON>ey(name: 'leftTeamName')
  String leftTeamName;

  @JsonKey(name: 'markStatus')
  int markStatus;

  @JsonKey(name: 'matchId')
  String matchId;

  @JsonKey(name: 'matchTimeStr')
  String matchTimeStr;
  @JsonKey(name: 'matchDateStr')
  String matchDateStr;
  @JsonKey(name: 'matchDateWeek')
  String matchDateWeek;

  @JsonKey(name: 'subscribed')
  bool subscribed;
  @JsonKey(name: 'matchLocked')
  bool matchLocked;

  @JsonKey(name: 'rightScore')
  int rightScore;

  @JsonKey(name: 'rightTeamId')
  String rightTeamId;

  @JsonKey(name: 'rightTeamLogo')
  String rightTeamLogo;

  @JsonKey(name: 'rightTeamName')
  String rightTeamName;

  @JsonKey(name: 'status')
  int status;

  Matches(
    this.courts,
    this.leftScore,
    this.leftTeamId,
    this.leftTeamLogo,
    this.leftTeamName,
    this.markStatus,
    this.matchId,
    this.matchTimeStr,
    this.matchDateStr,
    this.matchDateWeek,
    this.subscribed,
    this.matchLocked,
    this.rightScore,
    this.rightTeamId,
    this.rightTeamLogo,
    this.rightTeamName,
    this.status,
  );

  factory Matches.fromJson(Map<String, dynamic> srcJson) =>
      _$MatchesFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MatchesToJson(this);
}
