// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GameModel _$GameModelFromJson(Map<String, dynamic> json) => GameModel(
      json['arenaAliasName'] as String,
      json['arenaId'] as String,
      json['arenaLogo'] as String,
      json['arenaName'] as String,
      (json['matches'] as List<dynamic>)
          .map((e) => Matches.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$GameModelToJson(GameModel instance) => <String, dynamic>{
      'arenaAliasName': instance.arenaAliasName,
      'arenaId': instance.arenaId,
      'arenaLogo': instance.arenaLogo,
      'arenaName': instance.arenaName,
      'matches': instance.matches,
    };

Matches _$MatchesFromJson(Map<String, dynamic> json) => Matches(
      (json['courts'] as List<dynamic>).map((e) => e as String).toList(),
      json['leftScore'] as int,
      json['leftTeamId'] as String,
      json['leftTeamLogo'] as String,
      json['leftTeamName'] as String,
      json['markStatus'] as int,
      json['matchId'] as String,
      json['matchTimeStr'] as String,
      json['matchDateStr'] as String,
      json['matchDateWeek'] as String,
      json['subscribed'] as bool,
      json['matchLocked'] as bool,
      json['rightScore'] as int,
      json['rightTeamId'] as String,
      json['rightTeamLogo'] as String,
      json['rightTeamName'] as String,
      json['status'] as int,
    );

Map<String, dynamic> _$MatchesToJson(Matches instance) => <String, dynamic>{
      'courts': instance.courts,
      'leftScore': instance.leftScore,
      'leftTeamId': instance.leftTeamId,
      'leftTeamLogo': instance.leftTeamLogo,
      'leftTeamName': instance.leftTeamName,
      'markStatus': instance.markStatus,
      'matchId': instance.matchId,
      'matchTimeStr': instance.matchTimeStr,
      'matchDateStr': instance.matchDateStr,
      'matchDateWeek': instance.matchDateWeek,
      'subscribed': instance.subscribed,
      'matchLocked': instance.matchLocked,
      'rightScore': instance.rightScore,
      'rightTeamId': instance.rightTeamId,
      'rightTeamLogo': instance.rightTeamLogo,
      'rightTeamName': instance.rightTeamName,
      'status': instance.status,
    };
