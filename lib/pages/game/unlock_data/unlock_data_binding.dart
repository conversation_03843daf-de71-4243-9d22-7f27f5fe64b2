import 'package:get/get.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item1/unlock_data_item_logic1.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item2/unlock_data_item_logic2.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item3/unlock_data_item_logic3.dart';
import 'package:shoot_z/pages/game/unlock_data/unlock_data_logic.dart';

class UnlockDataBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => UnlockDataLogic());
    Get.lazyPut(() => UnlockDataItemLogic1());
    Get.lazyPut(() => UnlockDataItemLogic2());
    Get.lazyPut(() => UnlockDataItemLogic3());
  }
}
