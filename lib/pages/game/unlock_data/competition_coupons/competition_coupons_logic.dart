import 'package:get/get.dart';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/NumUtil.dart';

class CompetitionCouponsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
  }.obs;
  //数据列表
  var dataList = <GameCouponsModel>[].obs;
  var expiredDataList = <GameCouponsModel>[].obs;
// matchCouponType 赛事解锁券类型 1000 赛事个人解锁券，1001 赛事球队解锁券，1002 赛事整场解锁券
  var matchCouponType = 1002.obs;
  RxInt checkIndex = 999.obs;
  var isOpenExpired = true.obs;
  var money = 0.0.obs; //实际价格
  var preferentialMoney = "0.0".obs; //优惠后价格
  var decreaseMoney = "0.0".obs; //减少金额

  @override
  void onInit() {
    super.onInit();
    matchCouponType.value = Get.arguments["matchCouponType"];
    money.value = Get.arguments["money"];
    getdataList(controller: refreshController);
  }

  //获得最新列表
  getdataList({
    required RefreshController controller,
  }) async {
    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 100,
      'matchCouponType': matchCouponType.value,
    };
    log("zzzzzz12removeAtparam-${param}");
    var res = await Api().get(ApiUrl.gameCouponsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<GameCouponsModel> modelList =
          list.map((e) => GameCouponsModel.fromJson(e)).toList();
      List list2 = res.data["disabledList"] ?? [];
      List<GameCouponsModel> modelList2 =
          list2.map((e) => GameCouponsModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");

      controller.resetNoData();
      dataList.assignAll(modelList);
      expiredDataList.assignAll(modelList2);
      controller.refreshCompleted();
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  checkCoupon(int index) {
    checkIndex.value = checkIndex.value == index ? 999 : index;
    // matchCouponType 赛事解锁券类型 1000 赛事个人解锁券，1001 赛事球队解锁券，1002 赛事整场解锁券
    //type	integer1 次数券；2 金额抵扣券；3 折扣券
    log("money=${money.value.toString()}");
    var item = dataList[index];
    switch (item.type) {
      case 1:
        preferentialMoney.value = "0.0"; //优惠后价格
        decreaseMoney.value = money.value.toString(); //减少金额
        break;
      case 2:
        preferentialMoney.value =
            NumUtil.subtractDecStr(money.value.toString(), item.price ?? "0.0")
                .toStringAsFixed(2);
        decreaseMoney.value = item.price ?? "0.0";
        break;
      case 3:
        preferentialMoney.value =
            NumUtil.multiplyDecStr(money.value.toString(), item.discount ?? "1")
                .toStringAsFixed(2);
        decreaseMoney.value = NumUtil.subtractDecStr(
                money.value.toString(), preferentialMoney.value)
            .toStringAsFixed(2);
        break;
    }
  }

  void sureCoupon() {
    if (checkIndex.value >= dataList.length) {
      WxLoading.showToast(S.current.option_coupon2);
      return;
    }
    AppPage.back(result: {"coupons": dataList[checkIndex.value]});
  }
}
