import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item1/unlock_data_item_view1.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item2/unlock_data_item_view2.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item3/unlock_data_item_view3.dart';
import 'package:shoot_z/pages/game/unlock_data/unlock_data_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

///比赛 解锁数据
class UnlockDataPage extends StatelessWidget {
  UnlockDataPage({super.key});

  final logic = Get.put(UnlockDataLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.unlock_data),
      ),
      body: _tabWidget(context),
    );
  }

  Widget _tabWidget(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Container(
            width: double.infinity,
            height: 40.w,
            alignment: Alignment.centerLeft,
            color: Colours.bg_color,
            child: TabBar(
                controller: logic.tabController,
                unselectedLabelColor: Colours.color5C5C6E,
                unselectedLabelStyle: TextStyle(
                    fontSize: 18.sp,
                    color: Colours.color5C5C6E,
                    fontWeight: FontWeight.w600),
                labelColor: Colours.white,
                labelStyle: TextStyle(
                    fontSize: 20.sp,
                    color: Colours.white,
                    fontWeight: FontWeight.w600),
                isScrollable: false,
                // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                indicatorPadding: EdgeInsets.zero,
                dividerColor: Colors.transparent,
                dividerHeight: 0,
                labelPadding:
                    const EdgeInsets.symmetric(horizontal: 4.0), // 调整标签间的间距
                indicatorSize: TabBarIndicatorSize.label,
                indicatorColor: Colors.transparent,
                tabs: List.generate(logic.tabNameList.length, (index) {
                  return SizedBox(
                    width: 100.w,
                    height: 40.w,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        if (logic.tabbarIndex.value == index)
                          WxAssets.images.imgCheckIn2
                              .image(width: 19.w, height: 9.w),
                        Positioned(
                            bottom: 10.w,
                            child: ShaderMask(
                                shaderCallback: (bounds) =>
                                    const LinearGradient(
                                      colors: [
                                        Colours.colorFFF9DC,
                                        Colours.colorE4C8FF,
                                        Colours.colorE5F3FF,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ).createShader(bounds),
                                child: Text(
                                  logic.tabNameList[index],
                                  style: TextStyles.regular.copyWith(
                                    fontSize: logic.tabbarIndex.value == index
                                        ? 16.sp
                                        : 14.sp,
                                    color: logic.tabbarIndex.value == index
                                        ? Colours.white
                                        : Colours.color5C5C6E,
                                    fontWeight: logic.tabbarIndex.value == index
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ))),
                      ],
                    ),
                  );
                })),
          ),
          // Container(
          //   width: double.infinity,
          //   height: 40.w,
          //   alignment: Alignment.centerLeft,
          //   color: Colours.bg_color,
          //   child: TabBar(
          //       controller: logic.tabController,
          //       unselectedLabelColor: Colours.color5C5C6E,
          //       unselectedLabelStyle: TextStyle(
          //           fontSize: 16.sp,
          //           color: Colours.color5C5C6E,
          //           fontWeight: FontWeight.w600),
          //       labelColor: Colours.white,
          //       labelStyle: TextStyle(
          //           fontSize: 18.sp,
          //           color: Colours.white,
          //           fontWeight: FontWeight.w600),
          //       isScrollable: false,
          //       // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
          //       indicatorPadding: EdgeInsets.zero,
          //       dividerColor: Colors.transparent,
          //       dividerHeight: 0,
          //       indicatorColor: Colors.transparent,
          //       tabs: [
          //         SizedBox(
          //           width: 130.w,
          //           height: 50.w,
          //           child: Stack(
          //             alignment: Alignment.bottomCenter,
          //             children: [
          //               if (logic.tabbarIndex.value == 0)
          //                 Positioned(
          //                   right: 0,
          //                   bottom: 0,
          //                   child: WxAssets.images.imgCheckIn
          //                       .image(width: 52.w, height: 22.w),
          //                 ),
          //               Positioned(
          //                   bottom: 12.w, child: Text(S.current.unlock_all)),
          //             ],
          //           ),
          //         ),
          //         SizedBox(
          //           width: 130.w,
          //           height: 50.w,
          //           child: Stack(
          //             alignment: Alignment.bottomCenter,
          //             children: [
          //               if (logic.tabbarIndex.value == 1)
          //                 Positioned(
          //                   right: 0,
          //                   bottom: 0,
          //                   child: WxAssets.images.imgCheckIn
          //                       .image(width: 52.w, height: 22.w),
          //                 ),
          //               Positioned(
          //                   bottom: 12.w, child: Text(S.current.unlock_team)),
          //             ],
          //           ),
          //         ),
          //         if (logic.showType.value != "1")
          //           SizedBox(
          //             width: 130.w,
          //             height: 50.w,
          //             child: Stack(
          //               alignment: Alignment.bottomCenter,
          //               children: [
          //                 if (logic.tabbarIndex.value == 2)
          //                   Positioned(
          //                     right: 0,
          //                     bottom: 0,
          //                     child: WxAssets.images.imgCheckIn
          //                         .image(width: 52.w, height: 22.w),
          //                   ),
          //                 Positioned(
          //                     bottom: 12.w,
          //                     child: Text(S.current.unlock_person)),
          //               ],
          //             ),
          //           ),
          //       ]),
          // ),

          SizedBox(
            height: 10.w,
          ),
          Expanded(
            child: TabBarView(controller: logic.tabController, children: [
              UnlockDataItemPage1(
                key: const Key("1"),
              ),
              UnlockDataItemPage2(
                key: const Key("2"),
              ),
              if (logic.showType.value != "1")
                UnlockDataItemPage3(
                  key: const Key("3"),
                ),
            ]),
          ),
        ],
      );
    });
  }
}
