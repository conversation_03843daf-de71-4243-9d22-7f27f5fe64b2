import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/utils/pay/unlock_pay_utils.dart';
import 'package:shoot_z/utils/NumUtil.dart';

class UnlockDataItemLogic2 extends GetxController
    with GetSingleTickerProviderStateMixin {
  var isFrist = true.obs;
  //数据列表
  var matchPayInfoModel = Rx<MatchPayInfoModel?>(null);
  // matchCouponType 赛事解锁券类型 1000 赛事个人解锁券，1001 赛事球队解锁券，1002 赛事整场解锁券
  var matchCouponType = 1001;
  var moneyPay = "".obs;
  var maximumReducedAmount = "".obs; //最高可以减少金额
  var gameCouponsModel = GameCouponsModel().obs; //选择的优惠券
  var preferentialMoney = "0.0".obs; //优惠后价格
  var decreaseMoney = "0.0".obs; //减少金额
  var allReadyDeMoney = "0.0".obs; //总的减少金额

  var matchPayInfoModelTeams = MatchPayInfoModelTeams().obs; //选择的解锁的队伍

  setMatchPayInfoModel(MatchPayInfoModel matchPayInfoModel2, String teamId) {
    matchPayInfoModel.value = matchPayInfoModel2;
    matchPayInfoModel.refresh();
    moneyPay.value = (matchPayInfoModel.value?.matchSet == true
            ? (matchPayInfoModel.value?.teamSetPrice ??
                matchPayInfoModel.value?.teamDiscountPrice ??
                0.0)
            : (matchPayInfoModel.value?.teamDiscountPrice ??
                matchPayInfoModel.value?.teamPrice ??
                0.0))
        .toString();
    preferentialMoney.value = moneyPay.value;

    //循环判断teamid
    for (int i = 0; i < (matchPayInfoModel.value?.teams?.length ?? 0); i++) {
      if (matchPayInfoModel.value?.teams![i] != null &&
          ((matchPayInfoModel.value?.teams?[i]?.locked ?? 1) != 0)) {
        if (teamId != matchPayInfoModelTeams.value.teamId) {
          matchPayInfoModelTeams.value =
              matchPayInfoModel.value?.teams?[i] ?? MatchPayInfoModelTeams();
        }
      }
    }
    allReadyDeMoney.value = NumUtil.subtractDecStr(
            (matchPayInfoModel.value?.teamPrice ?? 0.0).toString(),
            preferentialMoney.value)
        .toStringAsFixed(2);
    getdataList();
  }

  //获得最新列表
  getdataList() async {
    Map<String, dynamic> param = {
      'matchCouponType': matchCouponType,
    };
    var res = await Api().get(ApiUrl.gameCouponsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<GameCouponsModel> modelList =
          list.map((e) => GameCouponsModel.fromJson(e)).toList();
      for (var item in modelList) {
        var preferentialMoney2 = "0.0";
        var decreaseMoney2 = "0.0";

        var money2 = moneyPay.value;
        switch (item.type) {
          case 1:
            preferentialMoney2 = "0.0"; //优惠后价格
            decreaseMoney2 = money2; //减少金额
            break;
          case 2:
            preferentialMoney2 =
                NumUtil.subtractDecStr(money2, item.price ?? "0.0")
                    .toStringAsFixed(2);
            decreaseMoney2 = item.price ?? "0.0";
            break;
          case 3:
            preferentialMoney2 =
                NumUtil.multiplyDecStr(money2, item.discount ?? "1")
                    .toStringAsFixed(2);
            decreaseMoney2 = NumUtil.subtractDecStr(money2, preferentialMoney2)
                .toStringAsFixed(2);
            break;
        }
        if (maximumReducedAmount.isEmpty || maximumReducedAmount.value == "") {
          maximumReducedAmount.value = decreaseMoney2;
        } else {
          if (NumUtil.lessThanDecStr(
              maximumReducedAmount.value, decreaseMoney2)) {
            maximumReducedAmount.value = decreaseMoney2;
          }
        }
      }
      maximumReducedAmount.refresh();
    }
  }

  void checkCoupon(GameCouponsModel item) {
    switch (item.type) {
      case 1:
        preferentialMoney.value = "0.0"; //优惠后价格
        decreaseMoney.value = moneyPay.value.toString(); //减少金额
        break;
      case 2:
        preferentialMoney.value = NumUtil.subtractDecStr(
                moneyPay.value.toString(), item.price ?? "0.0")
            .toStringAsFixed(2);
        decreaseMoney.value = item.price ?? "0.0";

        break;
      case 3:
        preferentialMoney.value = NumUtil.multiplyDecStr(
                moneyPay.value.toString(), item.discount ?? "1")
            .toStringAsFixed(2);
        decreaseMoney.value = NumUtil.subtractDecStr(
                moneyPay.value.toString(), preferentialMoney.value)
            .toStringAsFixed(2);
        break;
    }
    allReadyDeMoney.value = NumUtil.subtractDecStr(
            (matchPayInfoModel.value?.teamPrice ?? 0.0).toString(),
            preferentialMoney.value)
        .toStringAsFixed(2);
  }

  void payOrder() {
    if ((matchPayInfoModel.value?.matchId ?? "") == "") {
      WxLoading.showToast(S.current.unlock_team_tips10);
      return;
    }
    if ((matchPayInfoModelTeams.value.teamId ?? "") == "") {
      WxLoading.showToast(S.current.unlock_team_tips9);
      return;
    }

    if ((gameCouponsModel.value.id ?? "") == "") {
      UnlockPayUtils.pay(matchPayInfoModel.value?.matchId ?? "",
          matchPayInfoModel.value?.matchSet ?? false,
          teamId: matchPayInfoModelTeams.value.teamId ?? "");
    } else {
      UnlockPayUtils.pay(matchPayInfoModel.value?.matchId ?? "",
          matchPayInfoModel.value?.matchSet ?? false,
          teamId: matchPayInfoModelTeams.value.teamId ?? "",
          couponId: gameCouponsModel.value.id);
    }
  }
}
