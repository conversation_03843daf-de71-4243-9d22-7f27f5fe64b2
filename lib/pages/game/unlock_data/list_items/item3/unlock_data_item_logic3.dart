import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/utils/pay/unlock_pay_utils.dart';
import 'package:shoot_z/utils/NumUtil.dart';
import 'package:shoot_z/utils/event_bus.dart';

class UnlockDataItemLogic3 extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var isFrist = true.obs;
  //球员列表数据
  var dataPlayerList1 = <MatchPayInfoModelLockedPlayers>[].obs;
  var dataPlayerList2 = <MatchPayInfoModelLockedPlayers>[].obs;
  var matchPayInfoModel = Rx<MatchPayInfoModel?>(null);
  // matchCouponType 赛事解锁券类型 1000 赛事个人解锁券，1001 赛事球队解锁券，1002 赛事整场解锁券
  var matchCouponType = 1000;
  var moneyPay = "".obs;
  var moneyPayx4 = "".obs; //svip省钱4张钱数
  var maximumReducedAmount = "".obs; //最高可以减少金额
  var gameCouponsModel = GameCouponsModel().obs; //选择的优惠券
  var preferentialMoney = "0.0".obs; //优惠后价格
  var decreaseMoney = "0.0".obs; //减少金额
  var matchPayInfoModelUnlockedPlayers =
      MatchPayInfoModelLockedPlayers().obs; //选择的解锁的球员
  var isAgreeUnlock = true.obs; //绑定我的赛事生涯
  StreamSubscription? paySubscription;
  var tabbarIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
    paySubscription = BusUtils.instance.on((event) async {
      if (event.key == EventBusKey.payResult) {
        if (event.action == true) {
          getdataList();
          //UserManager.instance.userInfo.value.isSVip;
        }
      }
    });
  }

  void switchTab(index) {
    //  tabbarIndex.value = index;
    tabController?.index = index;
  }

  @override
  void onClose() {
    super.onClose();
    paySubscription?.cancel();
  }

  setMatchPayInfoModel(MatchPayInfoModel matchPayInfoModel2, String playerId) {
    matchPayInfoModel.value = matchPayInfoModel2;
    matchPayInfoModel.refresh();
    moneyPay.value = (matchPayInfoModel.value?.playerDiscountPrice ??
            matchPayInfoModel.value?.playerPrice ??
            0.0)
        .toString();
    moneyPayx4.value =
        NumUtil.multiplyDecStr(moneyPay.value, "4").toStringAsFixed(0);
    preferentialMoney.value = moneyPay.value;
    getdataList();
    log("message21=${matchPayInfoModel.value!.lockedPlayers?.length}-${matchPayInfoModel.value!.unlockedPlayers?.length}");
    var teamsId1 = matchPayInfoModel.value?.teams?.first?.teamId;
    if (matchPayInfoModel.value!.lockedPlayers is List) {
      for (var item in matchPayInfoModel.value!.lockedPlayers ?? []) {
        if (item != null) {
          if (playerId != "" && playerId == item.playerId) {
            matchPayInfoModelUnlockedPlayers.value = item;
          }
          if (teamsId1 == item.teamId) {
            dataPlayerList1.add(item);
          } else {
            dataPlayerList2.add(item);
          }
        }
      }
    }
    log("message22=${matchPayInfoModel.value!.lockedPlayers?.length}-${matchPayInfoModel.value!.unlockedPlayers?.length}");
    if (matchPayInfoModel.value!.unlockedPlayers is List) {
      log("message23=${matchPayInfoModel.value!.lockedPlayers?.length}-${matchPayInfoModel.value!.unlockedPlayers?.length}");

      for (var item in matchPayInfoModel.value!.unlockedPlayers ?? []) {
        log("message24=${matchPayInfoModel.value!.lockedPlayers?.length}-${matchPayInfoModel.value!.unlockedPlayers?.length}");

        if (item != null) {
          item.locked = "0";
          log("message25=${matchPayInfoModel.value!.lockedPlayers?.length}-${matchPayInfoModel.value!.unlockedPlayers?.length}");
          if (teamsId1 == item.teamId) {
            dataPlayerList1.add(item);
          } else {
            dataPlayerList2.add(item);
          }
        }
      }
    }
    log("message26=${matchPayInfoModel.value!.lockedPlayers?.length}-${matchPayInfoModel.value!.unlockedPlayers?.length}");
  }

  //获得最新列表
  getdataList() async {
    Map<String, dynamic> param = {
      'matchCouponType': matchCouponType,
    };
    var res = await Api().get(ApiUrl.gameCouponsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<GameCouponsModel> modelList =
          list.map((e) => GameCouponsModel.fromJson(e)).toList();
      for (var item in modelList) {
        var preferentialMoney = "0.0";
        var decreaseMoney = "0.0";
        var money = moneyPay.value;
        switch (item.type) {
          case 1:
            preferentialMoney = "0.0"; //优惠后价格
            decreaseMoney = money; //减少金额
            break;
          case 2:
            preferentialMoney =
                NumUtil.subtractDecStr(money, item.price ?? "0.0")
                    .toStringAsFixed(2);
            decreaseMoney = item.price ?? "0.0";
            break;
          case 3:
            preferentialMoney =
                NumUtil.multiplyDecStr(money, item.discount ?? "1")
                    .toStringAsFixed(2);
            decreaseMoney = NumUtil.subtractDecStr(money, preferentialMoney)
                .toStringAsFixed(2);
            break;
        }
        if (maximumReducedAmount.isEmpty || maximumReducedAmount.value == "") {
          maximumReducedAmount.value = decreaseMoney;
        } else {
          if (NumUtil.lessThanDecStr(
              maximumReducedAmount.value, decreaseMoney)) {
            maximumReducedAmount.value = decreaseMoney;
          }
        }
      }
      maximumReducedAmount.refresh();
    }
  }

  void checkCoupon(GameCouponsModel item) {
    switch (item.type) {
      case 1:
        preferentialMoney.value = "0.0"; //优惠后价格
        decreaseMoney.value = moneyPay.value.toString(); //减少金额
        break;
      case 2:
        preferentialMoney.value = NumUtil.subtractDecStr(
                moneyPay.value.toString(), item.price ?? "0.0")
            .toStringAsFixed(2);
        decreaseMoney.value = item.price ?? "0.0";
        break;
      case 3:
        preferentialMoney.value = NumUtil.multiplyDecStr(
                moneyPay.value.toString(), item.discount ?? "1")
            .toStringAsFixed(2);
        decreaseMoney.value = NumUtil.subtractDecStr(
                moneyPay.value.toString(), preferentialMoney.value)
            .toStringAsFixed(2);
        break;
    }
  }

  void payOrder() {
    if ((matchPayInfoModel.value?.matchId ?? "") == "") {
      WxLoading.showToast(S.current.unlock_team_tips10);
      return;
    }
    if ((matchPayInfoModelUnlockedPlayers.value.teamId ?? "") == "") {
      WxLoading.showToast(S.current.unlock_team_tips11);
      return;
    }

    if ((gameCouponsModel.value.id ?? "") == "") {
      UnlockPayUtils.pay(matchPayInfoModel.value?.matchId ?? "",
          matchPayInfoModel.value?.matchSet ?? false,
          teamId: matchPayInfoModelUnlockedPlayers.value.teamId ?? "",
          playerId: matchPayInfoModelUnlockedPlayers.value.playerId ?? "",
          isBindPlayer: isAgreeUnlock.value);
    } else {
      UnlockPayUtils.pay(matchPayInfoModel.value?.matchId ?? "",
          matchPayInfoModel.value?.matchSet ?? false,
          teamId: matchPayInfoModelUnlockedPlayers.value.teamId ?? "",
          playerId: matchPayInfoModelUnlockedPlayers.value.playerId ?? "",
          couponId: gameCouponsModel.value.id,
          isBindPlayer: isAgreeUnlock.value);
    }
  }
}
