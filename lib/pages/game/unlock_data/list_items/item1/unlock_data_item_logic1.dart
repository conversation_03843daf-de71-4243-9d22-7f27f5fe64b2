import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/network/model/orders_info_model.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/utils/pay/unlock_pay_utils.dart';
import 'package:shoot_z/utils/NumUtil.dart';

class UnlockDataItemLogic1 extends GetxController
    with GetSingleTickerProviderStateMixin {
  var isFrist = true.obs;
  //数据列表
  var dataList = <OrdersInfoModel>[].obs;
  var matchPayInfoModel = Rx<MatchPayInfoModel?>(null);
  // matchCouponType 赛事解锁券类型 1000 赛事个人解锁券，1001 赛事球队解锁券，1002 赛事整场解锁券
  var matchCouponType = 1002;
  var moneyPay = "".obs;
  var maximumReducedAmount = "".obs; //最高可以减少金额
  var gameCouponsModel = GameCouponsModel().obs; //选择的优惠券
  var preferentialMoney = "0.0".obs; //优惠后价格
  var decreaseMoney = "0.0".obs; //减少金额
  var allReadyDeMoney = "0.0".obs; //总的减少金额
  setMatchPayInfoModel(MatchPayInfoModel matchPayInfoModel2) {
    matchPayInfoModel.value = matchPayInfoModel2;
    matchPayInfoModel.refresh();
    moneyPay.value = (matchPayInfoModel.value?.matchSet == true
            ? (matchPayInfoModel.value?.matchSetPrice ??
                matchPayInfoModel.value?.matchDiscountPrice ??
                0.0)
            : (matchPayInfoModel.value?.matchDiscountPrice ??
                matchPayInfoModel.value?.matchPrice ??
                0.0))
        .toString();
    preferentialMoney.value = moneyPay.value;
    allReadyDeMoney.value = NumUtil.subtractDecStr(
            (matchPayInfoModel.value?.matchPrice ?? 0.0).toString(),
            preferentialMoney.value)
        .toStringAsFixed(2);
    getdataList();
  }

  //获得最新列表
  getdataInfo() async {
    Map<String, dynamic> param = {
      'pageSize': 20,
    };
    var res = await Api().get(ApiUrl.myOrderList, queryParameters: param);
    if (res.isSuccessful()) {
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //获得最新列表
  getdataList() async {
    Map<String, dynamic> param = {
      'matchCouponType': matchCouponType,
    };
    var res = await Api().get(ApiUrl.gameCouponsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<GameCouponsModel> modelList =
          list.map((e) => GameCouponsModel.fromJson(e)).toList();
      for (var item in modelList) {
        var preferentialMoney = "0.0";
        var decreaseMoney = "0.0";
        var money = moneyPay.value;
        switch (item.type) {
          case 1:
            preferentialMoney = "0.0"; //优惠后价格
            decreaseMoney = money; //减少金额
            break;
          case 2:
            preferentialMoney =
                NumUtil.subtractDecStr(money, item.price ?? "0.0")
                    .toStringAsFixed(2);
            decreaseMoney = item.price ?? "0.0";
            break;
          case 3:
            preferentialMoney =
                NumUtil.multiplyDecStr(money, item.discount ?? "1")
                    .toStringAsFixed(2);
            decreaseMoney = NumUtil.subtractDecStr(money, preferentialMoney)
                .toStringAsFixed(2);
            break;
        }
        if (maximumReducedAmount.isEmpty || maximumReducedAmount.value == "") {
          maximumReducedAmount.value = decreaseMoney;
        } else {
          if (NumUtil.lessThanDecStr(
              maximumReducedAmount.value, decreaseMoney)) {
            maximumReducedAmount.value = decreaseMoney;
          }
        }
      }
      maximumReducedAmount.refresh();
    }
  }

  void checkCoupon(GameCouponsModel item) {
    switch (item.type) {
      case 1:
        preferentialMoney.value = "0.0"; //优惠后价格
        decreaseMoney.value = moneyPay.value.toString(); //减少金额
        break;
      case 2:
        preferentialMoney.value = NumUtil.subtractDecStr(
                moneyPay.value.toString(), item.price ?? "0.0")
            .toStringAsFixed(2);
        decreaseMoney.value = item.price ?? "0.0";
        break;
      case 3:
        preferentialMoney.value = NumUtil.multiplyDecStr(
                moneyPay.value.toString(), item.discount ?? "1")
            .toStringAsFixed(2);
        decreaseMoney.value = NumUtil.subtractDecStr(
                moneyPay.value.toString(), preferentialMoney.value)
            .toStringAsFixed(2);
        break;
    }
    allReadyDeMoney.value = NumUtil.subtractDecStr(
            (matchPayInfoModel.value?.matchPrice ?? 0.0).toString(),
            preferentialMoney.value)
        .toStringAsFixed(2);
  }

  void payOrder() {
    if ((matchPayInfoModel.value?.matchId ?? "") == "") {
      WxLoading.showToast(S.current.unlock_team_tips10);
      return;
    }
    if ((gameCouponsModel.value.id ?? "") == "") {
      UnlockPayUtils.pay(
        matchPayInfoModel.value?.matchId ?? "",
        matchPayInfoModel.value?.matchSet ?? false,
      );
    } else {
      UnlockPayUtils.pay(matchPayInfoModel.value?.matchId ?? "",
          matchPayInfoModel.value?.matchSet ?? false,
          couponId: gameCouponsModel.value.id);
    }
  }
}
