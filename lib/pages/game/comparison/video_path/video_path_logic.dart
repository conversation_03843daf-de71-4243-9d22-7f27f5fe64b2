import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';

class VideoPathLogic extends GetxController {
  final VideoController videoController = VideoController();
  var videoPath = "".obs;
  var videoId = "".obs;
  var teamName = "".obs;
  var isShowShareUpdate = "".obs;
  @override
  void onInit() {
    super.onInit();
    videoPath.value = Get.arguments["videoPath"];
    teamName.value = Get.arguments["teamName"];
    isShowShareUpdate.value = Get.arguments["isShowShareUpdate"] ?? "0";
    if (isShowShareUpdate.value == "1") {
      videoId.value = Get.arguments["videoId"] ?? "";
    }
    getVideosInfo();
  }

  Future<void> getVideosInfo() async {
    if ((videoPath.value).isNotEmpty) {
      videoController.setData(
          videoPath: videoPath.value, videoCover: 'error_image_width');
    }
  }

  @override
  void onClose() {
    super.onClose();
    videoController.dispose();
  }

  void share() async {
    MyShareH5.getShareH5(ShareHighlights(
        sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
        highlightId: videoId.value,
        type: isShowShareUpdate.value));
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(videoPath.value);
  }

  void showDeleteDialog() {
    Get.dialog(CustomAlertDialog(
      title: S.current.confirm_deletion,
      content: S.current.video_removal_tips,
      onPressed: () async {
        AppPage.back();
        getDeleteVideo();
      },
    ));
  }

  Future<void> getDeleteVideo() async {
    Map<String, dynamic> param2 = {"id": videoId.value};
    var url = await ApiUrl.getDeleteVideo(videoId.value);
    var res = await Api().delete(url, data: param2);
    log("getDeleteVideo=${videoId.value}-${res.data}");
    if (res.isSuccessful()) {
      AppPage.back(result: true);
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
