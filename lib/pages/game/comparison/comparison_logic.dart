import 'dart:convert';
import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/comparison_model.dart';
import 'package:shoot_z/utils/event_bus.dart';

class ComparisonLogic extends GetxController {
  var matchId = "".obs;
  var comparisonModel = ComparisonModel().obs;
  var isFrist = true.obs;
  @override
  void onInit() {
    super.onInit();
    matchId.value = Get.arguments["matchId"];
  }

  @override
  void onReady() {
    super.onReady();
    getdataInfo();
  }

  //获得最新列表
  getdataInfo() async {
    Map<String, dynamic> param = {"matchId": matchId.value};
    var res = await Api()
        .get(ApiUrl.comparison(matchId.value), queryParameters: param);
    if (res.isSuccessful()) {
      log(jsonEncode(res.data));
      comparisonModel.value = ComparisonModel.fromJson(res.data);
    } else {
      WxLoading.showToast(res.message);
    }
    if (isFrist.value) {
      isFrist.value = false;
      refresh();
    }
  }

  getMatchesSubscribe() async {
    Map<String, dynamic> param = {"matchId": matchId.value};
    var res = await Api()
        .post(ApiUrl.matchesSubscribe(matchId.value), queryParameters: param);
    if (res.isSuccessful()) {
      getdataInfo();
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.subcribeMatchesRefresh));
      WxLoading.showToast(S.current.game_report4);
    } else {
      WxLoading.showToast(res.message);
    }
  }

//取消预约
  matchesCancelSubscribe() async {
    Map<String, dynamic> param = {"matchId": matchId.value};
    var res = await Api().post(ApiUrl.matchesCancelSubscribe(matchId.value),
        queryParameters: param);
    if (res.isSuccessful()) {
      getdataInfo();
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.subcribeMatchesRefresh));
      WxLoading.showToast(S.current.cancel_subcribe);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
