import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/player_report/composite_player_video/composite_player_video_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///球员报告 去剪辑->三级页面  合成视频
class CompositePlayerVideoPage extends StatelessWidget {
  CompositePlayerVideoPage({super.key});

  final logic = Get.put(CompositePlayerVideoLogic());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
          appBar: MyAppBar(
            title: Text(S.current.composite_video_title),
            actions: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.checkAllVideo();
                },
                child: Container(
                  height: 40.w,
                  alignment: Alignment.centerRight,
                  margin: EdgeInsets.only(right: 15.w),
                  child: Text(
                    logic.state.allCheck.value
                        ? S.current.Deselect_all
                        : S.current.select_all,
                    style: TextStyles.medium.copyWith(
                      fontSize: 13.sp,
                      color: Colours.color964AEE,
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Obx(() {
            return (logic.dataFag["isFrist"] as bool)
                ? buildLoad()
                : KeepAliveWidget(
                    child: SafeArea(
                      bottom: false,
                      child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //视频播放器
                            _videoWidget(),

                            //选球
                            Expanded(child: _optionGoalWidget(context)),
                          ]),
                    ),
                  );
          }),
          bottomNavigationBar: (logic.dataFag["isFrist"] as bool)
              ? const SizedBox()
              : Obx(() {
                  return Container(
                    width: double.infinity,
                    height: 87.w,
                    padding: EdgeInsets.only(
                        bottom: 25.w, left: 5.w, right: 15.w, top: 10.w),
                    child: Row(
                      children: [
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            //设置
                            showDateDialog(context);
                          },
                          child: Stack(
                            children: [
                              Container(
                                width: 44.w,
                                height: 44.w,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16.r),
                                    color: Colours.color282735),
                                child: WxAssets.images.optionGoalSet
                                    .image(width: 22.w, height: 22.w),
                              ),
                              if (logic.state.checkVideosCount.value > 0)
                                Positioned(
                                  right: 0,
                                  child: Transform.translate(
                                    offset: const Offset(5, 0), // 移动50像素到右和下
                                    child: Container(
                                      padding: EdgeInsets.only(
                                          left: 5.w,
                                          right: 5.w,
                                          top: 1.w,
                                          bottom: 1.w),
                                      decoration: BoxDecoration(
                                        color: Colours.red,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(28.r)),
                                      ),
                                      child: Text(
                                        "${logic.state.checkVideosCount.value}",
                                        style: TextStyles.titleMedium18
                                            .copyWith(fontSize: 10.sp),
                                      ),
                                    ),
                                  ),
                                )
                            ],
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              //一键成片  0不是vip 1是vip≈
                              // logic.getVideosUsedShots(logic.arenaID.value);
                              showDateDialog(context);
                            },
                            child: Container(
                              height: 46.w,
                              width: double.infinity,
                              alignment: Alignment.center,
                              margin: EdgeInsets.only(left: 14.w, right: 0.w),
                              padding: EdgeInsets.only(
                                  left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                              decoration: BoxDecoration(
                                color: Colours.color282735,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(28.r)),
                                gradient: const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Text(
                                S.current.One_click_make_piece,
                                style: TextStyles.display16
                                    .copyWith(fontSize: 16.sp),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }));
    });
  }

  Widget _videoWidget() {
    return Obx(() {
      return logic.dataList.isEmpty
          ? MyImage(
              "error_image_width.png",
              fit: BoxFit.fill,
              bgColor: Colors.transparent,
              isAssetImage: true,
              radius: 0.r,
              width: double.infinity,
              height: ScreenUtil().screenWidth / 375 * 211,
            )
          : SizedBox(
              width: double.infinity,
              height: ScreenUtil().screenWidth / 375 * 211,
              child: AspectRatio(
                aspectRatio: 375 / 211, // 宽高比
                child: VideoView(
                  controller: logic.videoController,
                ),
              ),
            );
    });
  }

  Widget _optionGoalWidget(BuildContext context) {
    return Obx(() {
      return
          // logic.dataList.isEmpty
          //     ? myNoDataView(context, msg: S.current.No_data_available)
          //     :
          SingleChildScrollView(
        child: Column(
          children: [
            logic.dataList.isEmpty
                ? Container(
                    margin: EdgeInsets.only(top: 150.w),
                    child: myNoDataView(
                      context,
                      msg: S.current.no_goal,
                      imagewidget: WxAssets.images.noGoal
                          .image(width: 100.w, height: 84.w),
                    ))
                : GridView.builder(
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    physics:
                        const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 101 / 72,
                    ),
                    padding: EdgeInsets.only(bottom: 20.w, top: 20.w),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, index) {
                      return Obx(() {
                        return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.changeVideoIndex(index);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                                color: index == logic.state.indexVideo.value
                                    ? Colours.color291A3B
                                    : Colours.color191921,
                                borderRadius: BorderRadius.circular(18.r),
                                image: index == logic.state.indexVideo.value
                                    ? null
                                    : const DecorationImage(
                                        image: AssetImage(
                                            "assets/images/goal_bg.png"),
                                        fit: BoxFit.fill),
                                border: index == logic.state.indexVideo.value
                                    ? Border.all(
                                        width: 1, color: Colours.color7732ED)
                                    : null),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                Positioned(
                                  top: 0.w,
                                  right: 0.w,
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      logic.checkOnlyVideo(index);
                                    },
                                    child: Container(
                                      alignment: Alignment.topRight,
                                      padding: EdgeInsets.only(
                                          top: 10.w,
                                          right: 10.w,
                                          left: 10.w,
                                          bottom: 5.w),
                                      child: (logic.dataList[index].checked ??
                                              false)
                                          ? WxAssets.images.check
                                              .image(height: 16.w, width: 16.w)
                                          : WxAssets.images.uncheck
                                              .image(height: 16.w, width: 16.w),
                                    ),
                                  ),
                                ),
                                Positioned(
                                    bottom: 10.w,
                                    child: Container(
                                      width: 103.w,
                                      padding: EdgeInsets.only(
                                          left: 10.w, right: 10.w),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          index == logic.state.indexVideo.value
                                              ? Container(
                                                  height: 10.w,
                                                  width: 10.w,
                                                  alignment: Alignment.center,
                                                  child: const LoadingIndicator(
                                                    pathBackgroundColor:
                                                        Colors.black26,
                                                    indicatorType: Indicator
                                                        .lineScaleParty,
                                                    colors: [Colours.white],
                                                  ),
                                                )
                                              : const SizedBox(),
                                          Expanded(
                                            child: Text(
                                              logic.dataList[index].time ?? "",
                                              textAlign: TextAlign.right,
                                              style: TextStyles.medium.copyWith(
                                                fontSize: 14.sp,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        );
                      });
                    }),
          ],
        ),
      );
    });
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            color: Colours.color191921,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 10.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.composite_video_dialog_tips1,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Padding(
                  padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                  child: Text(
                    S.current.composite_video_dialog_tips2,
                    style: TextStyles.medium
                        .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                  ),
                ),
                Wrap(
                  spacing: 15.w,
                  runSpacing: 15.w,
                  children: List.generate(2, (index) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        //单个视频片段时长
                        logic.state.compositeOption1.value = index;
                      },
                      child: Container(
                        width: 100.w,
                        height: 44.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color22222D,
                            borderRadius: BorderRadius.circular(12.r),
                            border: logic.state.compositeOption1.value == index
                                ? Border.all(
                                    width: 1, color: Colours.color9393A5)
                                : null),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            WxAssets.images.optionGoalTime.image(
                                width: 20.w,
                                height: 20.w,
                                color:
                                    logic.state.compositeOption1.value == index
                                        ? Colours.white
                                        : Colours.color5C5C6E),
                            SizedBox(
                              width: 5.w,
                            ),
                            Text(
                              index == 0 ? "5s" : "10s",
                              style: TextStyles.medium.copyWith(
                                  fontSize: 14.sp,
                                  color: logic.state.compositeOption1.value ==
                                          index
                                      ? Colours.white
                                      : Colours.color5C5C6E),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                  child: Text(
                    S.current.composite_video_dialog_tips3,
                    style: TextStyles.medium
                        .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                  ),
                ),
                Wrap(
                  spacing: 15.w,
                  runSpacing: 15.w,
                  children: List.generate(4, (index) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        //单个视频片段时长
                        logic.state.compositeOption2[index] =
                            logic.state.compositeOption2[index] == "0"
                                ? "1"
                                : "0";
                      },
                      child: Container(
                        width: 100.w,
                        height: 44.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color22222D,
                            borderRadius: BorderRadius.circular(12.r),
                            border: logic.state.compositeOption2[index] == "1"
                                ? Border.all(
                                    width: 1, color: Colours.color9393A5)
                                : null),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            index == 0
                                ? WxAssets.images.optionGoalLive.image(
                                    width: 20.w,
                                    height: 20.w,
                                    color:
                                        logic.state.compositeOption2[index] == "1"
                                            ? Colours.white
                                            : Colours.color5C5C6E)
                                : index == 1
                                    ? WxAssets.images.optionGoalAdd.image(
                                        width: 20.w,
                                        height: 20.w,
                                        color: logic.state
                                                    .compositeOption2[index] ==
                                                "1"
                                            ? Colours.white
                                            : Colours.color5C5C6E)
                                    : index == 2
                                        ? WxAssets.images.optionGoalPrimary.image(
                                            width: 20.w,
                                            height: 20.w,
                                            color:
                                                logic.state.compositeOption2[index] ==
                                                        "1"
                                                    ? Colours.white
                                                    : Colours.color5C5C6E)
                                        : WxAssets.images.optionGoalRwater.image(
                                            width: 20.w,
                                            height: 20.w,
                                            color: logic.state
                                                        .compositeOption2[index] ==
                                                    "1"
                                                ? Colours.white
                                                : Colours.color5C5C6E),
                            SizedBox(
                              width: 5.w,
                            ),
                            Text(
                              index == 0
                                  ? S.current.composite_video_dialog_tips4
                                  : index == 1
                                      ? S.current.composite_video_dialog_tips5
                                      : index == 2
                                          ? S.current
                                              .composite_video_dialog_tips6
                                          : S.current
                                              .composite_video_dialog_tips7,
                              style: TextStyles.medium.copyWith(
                                  fontSize: 14.sp,
                                  color:
                                      logic.state.compositeOption2[index] == "1"
                                          ? Colours.white
                                          : Colours.color5C5C6E),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
                // Padding(
                //   padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                //   child: Text(
                //     S.current.composite_video_dialog_tips8,
                //     style: TextStyles.medium
                //         .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                //   ),
                // ),
                // Wrap(
                //   spacing: 15.w,
                //   runSpacing: 15.w,
                //   children: List.generate(2, (index) {
                //     return GestureDetector(
                //       behavior: HitTestBehavior.translucent,
                //       onTap: () {
                //         //单个视频片段时长
                //         logic.state.compositeOption3.value =
                //             logic.state.compositeOption3.value == 1 ? 0 : 1;
                //       },
                //       child: Container(
                //         width: 100.w,
                //         height: 44.w,
                //         alignment: Alignment.center,
                //         decoration: BoxDecoration(
                //             color: Colours.color22222D,
                //             borderRadius: BorderRadius.circular(12.r),
                //             border: logic.state.compositeOption3.value == index
                //                 ? Border.all(
                //                     width: 1, color: Colours.color9393A5)
                //                 : null),
                //         child: Text(
                //           index == 0
                //               ? S.current.composite_video_dialog_tips9
                //               : S.current.composite_video_dialog_tips10,
                //           style: TextStyles.medium.copyWith(
                //               fontSize: 14.sp,
                //               color: logic.state.compositeOption3.value == index
                //                   ? Colours.white
                //                   : Colours.color5C5C6E),
                //         ),
                //       ),
                //     );
                //   }),
                // ),
                // Padding(
                //   padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                //   child: Text(
                //     S.current.composite_video_dialog_tips11,
                //     style: TextStyles.medium
                //         .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                //   ),
                // ),
                // Wrap(
                //   spacing: 15.w,
                //   runSpacing: 15.w,
                //   children: List.generate(2, (index) {
                //     return GestureDetector(
                //       behavior: HitTestBehavior.translucent,
                //       onTap: () {
                //         //单个视频片段时长
                //         logic.state.compositeOption4.value = index;
                //       },
                //       child: Container(
                //         width: 100.w,
                //         height: 44.w,
                //         alignment: Alignment.center,
                //         decoration: BoxDecoration(
                //             color: Colours.color22222D,
                //             borderRadius: BorderRadius.circular(12.r),
                //             border: logic.state.compositeOption4.value == index
                //                 ? Border.all(
                //                     width: 1, color: Colours.color9393A5)
                //                 : null),
                //         child: Text(
                //           index == 0 ? S.current.yes : S.current.no,
                //           style: TextStyles.medium.copyWith(
                //               fontSize: 14.sp,
                //               color: logic.state.compositeOption4.value == index
                //                   ? Colours.white
                //                   : Colours.color5C5C6E),
                //         ),
                //       ),
                //     );
                //   }),
                // ),

                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.back();
                    //一键成片
                    logic.getVideosUsedShots(logic.arenaID.value);
                  },
                  child: Container(
                    height: 46.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 25.w, bottom: 15.w),
                    padding: EdgeInsets.only(
                        left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                    decoration: BoxDecoration(
                      color: Colours.color282735,
                      borderRadius: BorderRadius.all(Radius.circular(28.r)),
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Text(
                      S.current.One_click_make_piece,
                      style: TextStyles.display16.copyWith(fontSize: 16.sp),
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.state.rememberOption.value =
                        logic.state.rememberOption.value == "0" ? "1" : "0";
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 3.w),
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                            width: 14.w,
                            height: 14.w,
                            margin: EdgeInsets.only(right: 8.w, bottom: 3.w),
                            child: logic.state.rememberOption.value == "0"
                                ? MyImage(
                                    "choiceYes.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                  )
                                : MyImage(
                                    "choiceNo.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                    imageColor: Colours.color353542,
                                    bgColor: Colours.color353542,
                                    radius: 8.w,
                                  )

                            // Icon(
                            //   logic.state.rememberOption.value == "0"
                            //       ? Icons.check_circle
                            //       : Icons.radio_button_unchecked,
                            //   color: logic.state.rememberOption.value == "0"
                            //       ? Colours.white
                            //       : Colours.color9393A5,
                            //   size: 17,
                            // ),
                            ),
                        Text(
                          S.current.composite_video_dialog_tips12,
                          style: TextStyles.medium.copyWith(
                              fontSize: 12.sp, color: Colours.color9393A5),
                        )
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 30.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
