import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';

class OptionPlayerGoalLogicState {
  TextEditingController codeController = TextEditingController();

  //推荐时段选择今天还是昨天
  var todayRecommended = true.obs;
  var indexVideo = 9999.obs; //选择视频下标
  var rememberOption = "1".obs; //记住我的选择

  //会员权益弹窗
  var vipDialogList = [
    {
      "img": "vip_dialog01.png",
      "name": S.current.vip_dialog_text1,
      "select": "0"
    },
    {
      "img": "vip_dialog02.png",
      "name": S.current.vip_dialog_text2,
      "select": "0"
    },
    {
      "img": "vip_dialog03.png",
      "name": S.current.vip_dialog_text3,
      "select": "0"
    },
    {
      "img": "vip_dialog04.png",
      "name": S.current.vip_dialog_text4,
      "select": "0"
    },
    {
      "img": "vip_dialog05.png",
      "name": S.current.vip_dialog_text5,
      "select": "0"
    },
    {
      "img": "vip_dialog06.png",
      "name": S.current.vip_dialog_text6,
      "select": "0"
    },
    {
      "img": "vip_dialog07.png",
      "name": S.current.vip_dialog_text7,
      "select": "0"
    },
    {
      "img": "vip_dialog08.png",
      "name": S.current.vip_dialog_text8,
      "select": "0"
    },
    {
      "img": "vip_dialog09.png",
      "name": S.current.vip_dialog_text9,
      "select": "0"
    }
  ].obs;

  var invitationDialogList = [
    {
      "img": "vip_Invitation_dialog1.png",
      "name": S.current.vip_dialog_text2,
      "select": "0"
    },
    {
      "img": "vip_Invitation_dialog2.png",
      "name": S.current.vip_dialog_text2,
      "select": "0"
    },
    {
      "img": "vip_Invitation_dialog3.png",
      "name": S.current.vip_dialog_text3,
      "select": "0"
    },
    {
      "img": "vip_Invitation_dialog4.png",
      "name": S.current.vip_dialog_text4,
      "select": "0"
    }
  ].obs;

  //选择类型
  var typeDialogList = [
    {"name": "全部", "select": "0"},
    {"name": "两分", "select": "0"},
    {"name": "三分", "select": "0"},
    {"name": "罚球", "select": "0"},
    {"name": "取消", "select": "0"}
  ].obs;
  //选择状态
  var stateDialogList = [
    {"name": "全部", "select": "0"},
    {"name": "进球", "select": "0"},
    {"name": "打铁", "select": "0"},
    {"name": "取消", "select": "0"}
  ].obs;
}
