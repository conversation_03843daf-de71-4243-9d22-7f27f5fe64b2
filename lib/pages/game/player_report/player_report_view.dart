import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/inappwebview/router.dart';
import 'package:shoot_z/network/model/player_report_model.dart';
import 'package:shoot_z/pages/game/player_report/player_report_logic.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/AndroidDevicePerformance.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

///比赛  球员报告
class PlayerReportPage extends StatefulWidget {
  const PlayerReportPage({super.key});

  @override
  State<PlayerReportPage> createState() => _PlayerReportPageState();
}

class _PlayerReportPageState extends State<PlayerReportPage>
    with AutomaticKeepAliveClientMixin {
  late final double containerWidth = (ScreenUtil().screenWidth - 30.w);
  late final double containerHeight = containerWidth * 14 / 15;

  // 使用GlobalKey访问渲染对象
  final GlobalKey _painterKey = GlobalKey();
  // 使用Float32List优化点存储
  Float32List _cachedHitPoints = Float32List(0);
  Float32List _cachedNoHitPoints = Float32List(0);
  // 离屏Picture缓存
  ui.Image? _cachedOffscreenImage;
  bool _isImageAvailable = false; // 图像状态标志

  // 图像资源
  ui.Image? _hitMarkerImage;
  ui.Image? _noHitMarkerImage;
  bool _areMarkersLoaded = false;

  // 图像创建任务取消处理
  Completer? _imageCompleter;

  final logic = Get.put(PlayerReportLogic());
  @override
  void initState() {
    super.initState();
    _isImageAvailable = false;
    _initializeDeviceInfo();
    _loadMarkerImages();
  }

  Future<void> _initializeDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        logic.deviceTier = await AndroidDevicePerformance.determineDeviceTier();
        if (mounted) setState(() {});
      } else {
        logic.deviceTier = await DevicePerformance.determineDeviceTier();
        if (mounted) setState(() {});
      }
    } catch (e) {
      debugPrint('设备检测失败: $e');
    }
  }

  // 加载标记图像资源
  Future<void> _loadMarkerImages() async {
    try {
      // 加载命中标记图片
      final hitBytes = await rootBundle.load('assets/images/half_shoot2.png');
      _hitMarkerImage =
          await decodeImageFromList(hitBytes.buffer.asUint8List());

      // 加载未命中标记图片
      final noHitBytes = await rootBundle.load('assets/images/half_shoot3.png');
      _noHitMarkerImage =
          await decodeImageFromList(noHitBytes.buffer.asUint8List());

      _areMarkersLoaded = true;

      // 如果点数据已缓存，立即生成图片
      if (_cachedHitPoints.isNotEmpty || _cachedNoHitPoints.isNotEmpty) {
        _createOffscreenPicture();
      }
    } catch (e) {
      debugPrint('加载标记图像失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Obx(() {
      return Scaffold(
        appBar: MyAppBar(
          title: Text(S.current.player_report),
          actions: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => MyShareH5.getShareH5(SharePlayerReport(
                  matchId: logic.matchId.value,
                  teamId: logic.teamId.value,
                  playerId: logic.playerId.value)),
              child: Container(
                width: 40.w,
                height: 40.w,
                alignment: Alignment.centerRight,
                child: WxAssets.images.share3
                    .image(color: Colors.white, width: 22.w, height: 22.w),
              ),
            ),
            SizedBox(
              width: 18.w,
            ),
          ],
        ),
        body: (logic.isFrist.value)
            ? buildLoad()
            : (logic.playerReportModel.value.playerId == null ||
                    logic.playerReportModel.value.playerId == "")
                ? SizedBox(
                    height: 300.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.no_matches_yet,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 150.w, height: 150.w),
                    ))
                : _createTeamWidget(context),
        bottomNavigationBar: (logic.isFrist.value) ||
                (logic.playerReportModel.value.playerId == null ||
                    logic.playerReportModel.value.playerId == "")
            ? const SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => MyShareH5.getShareH5(SharePlayerReport(
                      matchId: logic.matchId.value,
                      teamId: logic.teamId.value,
                      playerId: logic.playerId.value)),
                  child: Container(
                    height: 46.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(left: 20.w, right: 20.w),
                    decoration: BoxDecoration(
                      color: Colours.color282735,
                      borderRadius: BorderRadius.all(Radius.circular(28.r)),
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Text(
                      "分享比赛报告",
                      style: TextStyles.display16.copyWith(fontSize: 16.sp),
                    ),
                  ),
                ),
              ),
      );
    });
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 15.w,
            ),
            Container(
              width: double.infinity,
              height: 151.w,
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              child: Stack(
                children: [
                  WxAssets.images.imgPlayer2
                      .image(width: 375.w, height: 151.w, fit: BoxFit.fitWidth),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(left: 15.w),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    width: 1.w, color: Colours.white),
                                borderRadius: BorderRadius.circular(8.r)),
                            child: MyImage(
                              (logic.playerReportModel.value.boundUserPhoto ??
                                          "") !=
                                      ""
                                  ? logic.playerReportModel.value
                                          .boundUserPhoto ??
                                      ""
                                  : logic.playerReportModel.value.photo ?? "",
                              width: 74.w,
                              height: 99.w,
                              radius: 8.r,
                            ),
                          ),
                          SizedBox(
                            width: 15.w,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 35.w,
                                ),
                                Row(
                                  children: [
                                    Container(
                                      width: 17.w,
                                      height: 20.w,
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          MyImage(
                                            "img_player1.png",
                                            width: 17.w,
                                            height: 20.w,
                                            isAssetImage: true,
                                            imageColor: (logic.playerReportModel
                                                            .value.number ??
                                                        "") !=
                                                    ""
                                                ? Colours.white
                                                : null,
                                          ),
                                          Text(
                                            logic.playerReportModel.value
                                                    .number ??
                                                "",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.semiBold14
                                                .copyWith(
                                                    fontFamily: 'DIN',
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 12.sp,
                                                    color: Colours.colorA44EFF),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      width: 9.w,
                                    ),
                                    Text(
                                      !(logic.playerReportModel.value
                                                  .bindable ??
                                              false)
                                          ? logic.playerReportModel.value
                                                  .boundUserName ??
                                              ""
                                          : S.current.player_report_claimed,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 18.sp,
                                          fontWeight: FontWeight.w600),
                                    )
                                  ],
                                ),
                                SizedBox(
                                  height: 20.w,
                                ),
                                Row(
                                  children: [
                                    MyImage(
                                      logic.playerReportModel.value.teamLogo ??
                                          "",
                                      width: 18.w,
                                      height: 18.w,
                                      radius: 10.r,
                                      isAssetImage: false,
                                      errorImage: "my_team_head4.png",
                                    ),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    Text(
                                      logic.playerReportModel.value.teamName ??
                                          "",
                                      style: TextStyles.regular.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.colorD5B6F8,
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              SizedBox(
                                height: 40.w,
                                child: logic.playerReportModel.value.mvp == true
                                    ? Transform.translate(
                                        offset: const Offset(-8, -10),
                                        child: WxAssets.images.imgPlayer5
                                            .image(width: 110.w, height: 43.w),
                                      )
                                    : const SizedBox(),
                              ),
                              if (logic.playerReportModel.value.bindable ==
                                  true)
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    //认领报告
                                    if (UserManager.instance.isLogin) {
                                      logic.bindReport();
                                    } else {
                                      AppPage.to(Routes.login).then((value) {
                                        if (value) {
                                          logic.bindReport();
                                        }
                                      });
                                    }
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(right: 15.w),
                                    padding: EdgeInsets.only(
                                        left: 22.w,
                                        right: 22.w,
                                        bottom: 9.w,
                                        top: 9.w),
                                    decoration: BoxDecoration(
                                        gradient: const LinearGradient(colors: [
                                          Color(0xFFFFF9DC),
                                          Color(0xFFE4C8FF),
                                          Color(0xFFE5F3FF)
                                        ]),
                                        borderRadius:
                                            BorderRadius.circular(20.r)),
                                    child: Text(
                                      S.current.player_report_report,
                                      style: TextStyle(
                                          color: Colours.color0F0F16,
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                ),
                              if (logic.playerReportModel.value.bindable !=
                                      true &&
                                  UserManager.instance.user?.userId ==
                                      logic.playerReportModel.value
                                          .boundUserId &&
                                  UserManager.instance.user?.userId != "")
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    //解绑
                                    logic.unBindReport();
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(right: 15.w),
                                    padding: EdgeInsets.only(
                                        left: 22.w, bottom: 9.w, top: 0.w),
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(20.r)),
                                    child: Row(
                                      children: [
                                        WxAssets.images.imgUnbind
                                            .image(width: 12.w, height: 12.w),
                                        SizedBox(
                                          width: 5.w,
                                        ),
                                        Text(
                                          S.current.unbind,
                                          style: TextStyle(
                                              color: Colours.colorD5B6F8,
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                            ],
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 14.w,
                      ),
                      Wrap(
                        spacing: 8.w,
                        children:
                            List.generate(logic.playerTaglist.length, (index) {
                          return Container(
                            // width: 50.w,
                            padding: EdgeInsets.all(4.w),
                            margin:
                                EdgeInsets.only(left: index == 0 ? 15.w : 0),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25.r),
                                gradient: const LinearGradient(colors: [
                                  Color(0x20AEFFAD),
                                  Color(0x30FFF870)
                                ])),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                MyImage(
                                  logic.playerTaglist[index]["img"] ?? "",
                                  width: 9.w,
                                  height: 9.w,
                                  isAssetImage: true,
                                  imageColor: logic
                                              .playerReportModel.value.title
                                              ?.contains(
                                                  logic.playerTaglist[index]
                                                          ["name"] ??
                                                      "") ??
                                          false
                                      ? null
                                      : Colours.colorD5B6F8,
                                ),
                                SizedBox(
                                  width: 2.w,
                                ),
                                Text(
                                  logic.playerTaglist[index]["name"] ?? "",
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 10.sp,
                                      color: logic.playerReportModel.value.title
                                                  ?.contains(
                                                      logic.playerTaglist[index]
                                                              ["name"] ??
                                                          "") ??
                                              false
                                          ? Colours.white
                                          : Colours.colorD5B6F8),
                                )
                              ],
                            ),
                          );
                        }),
                      )
                    ],
                  ),
                  Positioned(
                      bottom: 0.w,
                      right: 0.w,
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.to(Routes.careerHighlightsHomePage,
                              arguments: {
                                'userId': logic.playerReportModel.value.playerId
                              },
                              needLogin: true);
                        },
                        child: Container(
                          width: 45.w,
                          height: 50.w,
                          color: Colors.transparent,
                        ),
                      ))
                ],
              ),
            ),
            //球员数据
            buildRowTitleWidget(
              S.current.player_report_member_data,
              height: 55,
            ),

            Container(
              // height: 55.w,
              margin: EdgeInsets.only(left: 15.w, right: 15.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(12.r)),
              child: GridView.builder(
                  scrollDirection: Axis.vertical,
                  // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                  shrinkWrap: true,
                  physics:
                      const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 15,
                    mainAxisSpacing: 20,
                    childAspectRatio: 101 / 52,
                  ),
                  padding: EdgeInsets.only(bottom: 0.w),
                  itemCount: logic.playerDatalist.length,
                  itemBuilder: (context, index) {
                    return Obx(() {
                      return GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {},
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              logic.playerDatalist[index]["data"] ?? "0",
                              textAlign: TextAlign.right,
                              style: TextStyles.semiBold14.copyWith(
                                  fontFamily: 'DIN',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 18.sp,
                                  color: Colours.white),
                            ),
                            const Spacer(),
                            Text(
                              logic.playerDatalist[index]["name"] ?? "",
                              textAlign: TextAlign.right,
                              style: TextStyles.medium.copyWith(
                                  fontSize: 14.sp, color: Colours.color5C5C6E),
                            ),
                          ],
                        ),
                      );
                    });
                  }),
            ),
            if (!(logic.playerReportModel.value.sectionsScore?.isEmpty ?? true))
              //小节比分
              buildRowTitleWidget(
                S.current.player_report_tips12,
                height: 55,
              ),
            if (!(logic.playerReportModel.value.sectionsScore?.isEmpty ?? true))
              Container(
                // height: 55.w,
                margin: EdgeInsets.only(left: 15.w, right: 15.w),
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(12.r)),
                child: GridView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    physics:
                        const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 20,
                      childAspectRatio: 1,
                    ),
                    padding: EdgeInsets.only(bottom: 0.w),
                    itemCount: logic.playerScorelist.length,
                    itemBuilder: (context, index) {
                      return Obx(() {
                        return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {},
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                logic.playerScorelist[index]["name"] ?? "",
                                textAlign: TextAlign.right,
                                style: TextStyles.medium.copyWith(
                                    fontSize: 14.sp,
                                    color: Colours.color5C5C6E),
                              ),
                              const Spacer(),
                              Text(
                                logic.playerScorelist[index]["data"] ?? "0",
                                textAlign: TextAlign.right,
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                              ),
                            ],
                          ),
                        );
                      });
                    }),
              ),
            //高光集锦
            buildRowTitleWidget(
              S.current.player_report_tips14,
              height: 55,
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                AppPage.to(Routes.videos, arguments: {
                  "videoId": logic.playerReportModel.value.videoMergeId,
                  "matchId": logic.matchId.value,
                  "type": "0", //0合并id  1片段id
                });
              },
              child: Container(
                margin: EdgeInsets.only(left: 15.w, right: 15.w),
                width: double.infinity,
                height: 194.w,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                //videoStatus	integer 视频状态 0，1 合成中，2 合成完成，3 合成失败
                child: logic.playerReportModel.value.videoStatus != 2
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          WxAssets.images.icVideoScz.image(
                              width: 80.w, height: 76.w, fit: BoxFit.fill),
                          const SizedBox(
                            height: 21,
                          ),
                          MyText(
                            S.current.highlights_generate,
                            size: 14,
                            color: Colours.color5C5C6E,
                          ),
                        ],
                      )
                    : MyImage(
                        logic.playerReportModel.value.videoCover ?? "",
                        width: double.infinity,
                        height: 194.w,
                        radius: 12.r,
                        errorImage: "error_image_width.png",
                        placeholderImage: "error_image_width.png",
                      ),
              ),
            ),
            buildRowTitleWidget(
              S.current.selfile_shot_info1,
              height: 55,
            ),
            Container(
              width: double.infinity,
              height: containerHeight,
              margin: EdgeInsets.only(left: 15.w, right: 15.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage("assets/images/half_shoot1.png"),
                  fit: BoxFit.fill,
                ),
              ),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Obx(() {
                    return RepaintBoundary(
                      key: _painterKey,
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: logic.isPaintingComplete.value
                            ? _isImageAvailable && _cachedOffscreenImage != null
                                ? SafeImageWidget(
                                    image: _cachedOffscreenImage!,
                                  )
                                : _buildPlaceholder()
                            : _AsyncPointsPainter(
                                key: ValueKey(
                                    DateTime.now().millisecondsSinceEpoch),
                                points: logic.paintPoints,
                                containerWidth: containerWidth / 1500,
                                containerHeight: containerHeight / 1400,
                                wuw: 5.w,
                                shiw: 10.w,
                                deviceTier: logic.deviceTier,
                                onComplete: () {
                                  if (mounted) {
                                    setState(() =>
                                        logic.isPaintingComplete.value = true);
                                  }
                                },
                                onCacheReady: (cache) {
                                  if (mounted) {
                                    setState(() {
                                      _cachedHitPoints = cache.hitPoints;
                                      _cachedNoHitPoints = cache.noHitPoints;

                                      // 创建离线Picture缓存
                                      _createOffscreenPicture();
                                    });
                                  }
                                },
                              ),
                      ),
                    );
                  });
                },
              ),
            ),

            //球员片段
            buildRowTitleWidget(
              S.current.player_report_tips15,
              height: 55,
              rightName: S.current.player_report_tips18,
              rightOnTap: () {
                //去剪辑
                AppPage.to(Routes.optionPlayerGoalPage, arguments: {
                  "matchId": logic.matchId.value,
                  "playerId": logic.playerId.value,
                  "teamId": logic.teamId.value,
                  "type": "0", //0合并id  1片段id
                });
                // log("getTeamPlayerReport2=${{
                //   "matchId": logic.matchId.value,
                //   "playerId": logic.playerId.value,
                //   "teamId": logic.teamId.value,
                //   "type": "0", //0合并id  1片段id
                // }}");
              },
            ),
            (logic.isFrist.value)
                ? buildLoad()
                : (logic.playerReportModel.value.videos?.isEmpty ?? true)
                    ? SizedBox(
                        height: 300.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 150.w),
                        ))
                    : Container(
                        // height: 55.w,
                        margin: EdgeInsets.only(left: 15.w, right: 15.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r)),
                        child: GridView.builder(
                            scrollDirection: Axis.vertical,
                            // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                            shrinkWrap: true,
                            physics:
                                const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: 15,
                              mainAxisSpacing: 15,
                              childAspectRatio: 166 / 95,
                            ),
                            padding: EdgeInsets.only(bottom: 2.w),
                            itemCount:
                                logic.playerReportModel.value.videos?.length,
                            itemBuilder: (context, index) {
                              return Obx(() {
                                return GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    AppPage.to(Routes.videos, arguments: {
                                      "videoId": logic.playerReportModel.value
                                          .videos?[index]?.videoId,
                                      "matchId": logic.matchId.value,
                                      "type": "1", //0合并id  1片段id
                                    });
                                  },
                                  child: Stack(
                                    children: [
                                      MyImage(
                                        logic.playerReportModel.value
                                                .videos?[index]?.videoCover ??
                                            "",
                                        width: double.infinity,
                                        height: 194.w,
                                        radius: 12.r,
                                        errorImage: "error_image.png",
                                        placeholderImage: "error_image.png",
                                      ),
                                      Positioned(
                                        left: 5.w,
                                        top: 5.w,
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: 7.w,
                                              right: 7.w,
                                              top: 5.w,
                                              bottom: 5.w),
                                          decoration: BoxDecoration(
                                              color: Colours.color50000000,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(4.r))),
                                          child: Text(
                                            logic
                                                    .playerReportModel
                                                    .value
                                                    .videos?[index]
                                                    ?.markedText ??
                                                "",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.medium.copyWith(
                                                fontSize: 10.sp,
                                                color: Colours.white),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        right: 5.w,
                                        bottom: 5.w,
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: 7.w,
                                              right: 7.w,
                                              top: 5.w,
                                              bottom: 5.w),
                                          decoration: BoxDecoration(
                                              color: Colours.color50000000,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(4.r),
                                                  topRight:
                                                      Radius.circular(4.r),
                                                  bottomLeft:
                                                      Radius.circular(4.r),
                                                  bottomRight:
                                                      Radius.circular(12.r))),
                                          child: Text(
                                            "${(((logic.playerReportModel.value.videos?[index]?.duration ?? 0) > 0)) ? logic.formatDuration(logic.playerReportModel.value.videos?[index]?.duration ?? 0) : logic.playerReportModel.value.videos?[index]?.duration ?? ""}",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.medium.copyWith(
                                                fontSize: 10.sp,
                                                color: Colours.white),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              });
                            }),
                      ),

            Container(
              margin: EdgeInsets.all(15.w),
              padding: EdgeInsets.all(15.w),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  color: Colours.color191921),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        "${S.current.dialog_title}：",
                        style: TextStyles.regular.copyWith(fontSize: 12.sp),
                      ),
                      const Spacer(),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          var url = "";
                          if (const String.fromEnvironment('env',
                                  defaultValue: 'dev') !=
                              'pro') {
                            url =
                                "https://idev.shootz.tech/kf/?userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                          } else {
                            url =
                                "https://i.shootz.tech/kf/?userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                          }

                          //测试 https://idev.shootz.tech/kf 正式 https://i.shootz.tech/kf
                          WebviewRouter router = WebviewRouter(
                              url: url,
                              showNavigationBar: true,
                              needBaseHttp: false,
                              title: S.current.feedback);
                          AppPage.to(Routes.webviewh5, arguments: router);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(width: 1, color: Colours.white),
                            borderRadius: BorderRadius.circular(22.r),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 4.w),
                          child: Text(
                            "去反馈",
                            style: TextStyles.regular.copyWith(
                                fontSize: 10.sp, color: Colours.white),
                          ),
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 15.w,
                  ),
                  Text(
                    "如遇数据有误，且与实际数据出入较大，您可以点击右侧按钮反馈留言，请附上具体数据错误项及错误原因。",
                    style: TextStyles.regular.copyWith(
                        fontSize: 12.sp,
                        color: Colours.colorBFBFBF,
                        height: 1.3),
                  )
                ],
              ),
            )
          ],
        ),
      );
    });
  }

  // 创建离线Picture并转换为Image
  Future<void> _createOffscreenPicture() async {
    if (!_areMarkersLoaded) {
      return;
    }

    // 检查点数据
    if (_cachedHitPoints.isEmpty && _cachedNoHitPoints.isEmpty) return;

    // 取消可能正在进行的图像创建任务
    if (_imageCompleter != null && !_imageCompleter!.isCompleted) {
      _imageCompleter!.completeError("任务被新创建请求取消");
      _imageCompleter = null;
    }

    // 设置图像不可用状态
    setState(() => _isImageAvailable = false);

    // 释放旧资源
    _cachedOffscreenImage?.dispose();
    _cachedOffscreenImage = null;

    // 创建新的Completer
    _imageCompleter = Completer<ui.Image>();

    try {
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      final size = Size(containerWidth, containerHeight);

      // 使用图片代替绘制
      _drawPointsWithImageMarkers(canvas, size);

      final picture = recorder.endRecording();

      // 转换为Image - 提供更高效的GPU纹理
      final newImage =
          await picture.toImage(size.width.toInt(), size.height.toInt());

      picture.dispose();

      // 如果任务被取消或页面已卸载，直接释放图像
      if ((_imageCompleter == null || _imageCompleter!.isCompleted) ||
          !mounted) {
        newImage.dispose();
        return;
      }

      // 完成任务并更新状态
      _imageCompleter!.complete(newImage);

      setState(() {
        _cachedOffscreenImage = newImage;
        _isImageAvailable = true;
      });
    } catch (e) {
      debugPrint('创建离屏图片失败: $e');

      // 如果任务未被取消，报告错误
      if (_imageCompleter != null && !_imageCompleter!.isCompleted) {
        _imageCompleter!.completeError(e);
      }

      setState(() => _isImageAvailable = false);
    }
  }

  // 使用图片代替绘制
  void _drawPointsWithImageMarkers(Canvas canvas, Size size) {
    // 确定图片标记大小（根据设备等级优化）
    final markerSize = Size(10.w, 10.w);

    // 绘制未命中点 - 使用图片
    log("drawNoHitPoints11");
    if (_cachedNoHitPoints.isNotEmpty && _noHitMarkerImage != null) {
      final points = _convertToOffsetList(_cachedNoHitPoints);
      for (final point in points) {
        canvas.drawImageRect(
            _noHitMarkerImage!,
            Rect.fromLTWH(0, 0, _noHitMarkerImage!.width.toDouble(),
                _noHitMarkerImage!.height.toDouble()),
            Rect.fromCenter(
              center: point,
              width: markerSize.width,
              height: markerSize.height,
            ),
            Paint()..isAntiAlias = false);
      }
    }

    // 绘制命中点 - 使用图片
    if (_cachedHitPoints.isNotEmpty && _hitMarkerImage != null) {
      final points = _convertToOffsetList(_cachedHitPoints);
      for (final point in points) {
        canvas.drawImageRect(
            _hitMarkerImage!,
            Rect.fromLTWH(0, 0, _hitMarkerImage!.width.toDouble(),
                _hitMarkerImage!.height.toDouble()),
            Rect.fromCenter(
              center: point,
              width: markerSize.width,
              height: markerSize.height,
            ),
            Paint()..isAntiAlias = false);
      }
    }
  }

  // 转换Float32List为Offset列表
  List<Offset> _convertToOffsetList(Float32List points) {
    final result = <Offset>[];
    for (int i = 0; i < points.length; i += 2) {
      result.add(Offset(points[i], points[i + 1]));
    }
    return result;
  }

  // 安全的占位符
  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  @override
  bool get wantKeepAlive => false;
}

// 安全的图像组件包装器
class SafeImageWidget extends StatelessWidget {
  final ui.Image image;

  const SafeImageWidget({super.key, required this.image});

  @override
  Widget build(BuildContext context) {
    try {
      // 尝试使用图像
      return RawImage(
        image: image,
        fit: BoxFit.fill,
      );
    } catch (e) {
      // 图像不可用时的后备方案
      return Container(
        color: Colors.grey[200],
        child: Center(
          child: Icon(Icons.broken_image, size: 50, color: Colors.grey[500]),
        ),
      );
    }
  }
}

// ================== 异步绘制器 ==================

class PointCache {
  final Float32List hitPoints;
  final Float32List noHitPoints;
  final double innerHalf;

  PointCache(this.hitPoints, this.noHitPoints, this.innerHalf);
}

class _AsyncPointsPainter extends StatefulWidget {
  final List<PlayerReportModelShootLocation> points;
  final double containerWidth;
  final double containerHeight;
  final double shiw;
  final double wuw;
  final DeviceTier deviceTier;
  final VoidCallback onComplete;
  final ValueChanged<PointCache> onCacheReady;

  const _AsyncPointsPainter({
    Key? key,
    required this.points,
    required this.containerWidth,
    required this.containerHeight,
    required this.shiw,
    required this.wuw,
    required this.deviceTier,
    required this.onComplete,
    required this.onCacheReady,
  }) : super(key: key);

  @override
  State<_AsyncPointsPainter> createState() => _AsyncPointsPainterState();
}

class _AsyncPointsPainterState extends State<_AsyncPointsPainter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isCacheReady = false;

  // 使用Float32List优化点存储
  Float32List _cachedHitPoints = Float32List(0);
  Float32List _cachedNoHitPoints = Float32List(0);
  double _innerHalf = 0;
  int _pointsPerFrame = 50;

  // GPU帧时间监控
  int _gpuFrameTime = 0;
  bool _isHighLoad = false;

  @override
  void initState() {
    super.initState();
    _initAnimation();
    _calculateParams();
    _startGpuMonitor();
  }

  void _startGpuMonitor() {
    // 监控GPU帧时间
    SchedulerBinding.instance.addPostFrameCallback((_) {
      SchedulerBinding.instance.addTimingsCallback((timings) {
        if (timings.isNotEmpty && mounted) {
          setState(() {
            final frameTiming = timings.last;
            _gpuFrameTime = frameTiming.rasterDuration.inMilliseconds;
            _isHighLoad = _gpuFrameTime > 16; // >16ms 表示低于60FPS
          });
        }
      });
    });
  }

  void _initAnimation() {
    // 动态调整每帧点数
    int calculatePointsPerFrame() {
      if (_isHighLoad) {
        return (widget.deviceTier.pointLoad * 0.5).clamp(5, 30).toInt();
      }
      return widget.deviceTier.pointLoad;
    }

    _pointsPerFrame = calculatePointsPerFrame();

    // 使用保守的持续时间
    final estimatedDuration = Duration(
        milliseconds: (widget.points.length / _pointsPerFrame * 25).toInt());

    _controller = AnimationController(
      vsync: this,
      duration: estimatedDuration,
    )..addStatusListener((status) {
        if (_controller.value >= 1.0 &&
            _controller.status != AnimationStatus.completed) {
          _controller.value = 1.0;
          return;
        }

        switch (status) {
          case AnimationStatus.completed:
            if (mounted) widget.onComplete();
            break;
          case AnimationStatus.dismissed:
            if (mounted) {
              setState(() {
                _cachedHitPoints = Float32List(0);
                _cachedNoHitPoints = Float32List(0);
                _isCacheReady = false;
              });
            }
            _calculateParams();
            if (mounted) _controller.forward();
            break;
          default:
            break;
        }
      });

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) _controller.forward();
    });
  }

  void _calculateParams() {
    _innerHalf = widget.shiw / 2 - widget.shiw * 0.15;

    // 在隔离线程中进行点计算
    _calculatePointsInIsolate();
  }

  Future<void> _calculatePointsInIsolate() async {
    try {
      final maxPoints = widget.deviceTier.maxPoints;

      // 在隔离线程中执行计算
      final result = await compute(_processPoints, {
        'points': widget.points,
        'maxPoints': maxPoints,
        'containerWidth': widget.containerWidth,
        'containerHeight': widget.containerHeight,
      });

      if (mounted) {
        setState(() {
          _cachedHitPoints =
              Float32List.fromList(result['hitPoints'] as List<double>);
          _cachedNoHitPoints =
              Float32List.fromList(result['noHitPoints'] as List<double>);
          _isCacheReady = true;
        });

        // 通知父组件缓存已就绪
        widget.onCacheReady(
            PointCache(_cachedHitPoints, _cachedNoHitPoints, _innerHalf));
      }
    } catch (e) {
      debugPrint("点坐标计算失败: $e");
    }
  }

  static Map<String, dynamic> _processPoints(Map<String, dynamic> params) {
    final List<PlayerReportModelShootLocation> points = params['points'];
    final int maxPoints = params['maxPoints'];
    final double widthRatio = params['containerWidth'];
    final double heightRatio = params['containerHeight'];

    // 收集命中和未命中的点
    final hitPoints = <double>[];
    final noHitPoints = <double>[];

    int count = 0;
    for (final point in points) {
      if (count++ >= maxPoints) break;

      final rawX = (point.x ?? 0.0) * widthRatio;
      final rawY = (point.y ?? 0.0) * heightRatio;

      if (point.hit == true) {
        hitPoints.add(rawX);
        hitPoints.add(rawY);
      } else {
        noHitPoints.add(rawX);
        noHitPoints.add(rawY);
      }
    }

    return {
      'hitPoints': hitPoints,
      'noHitPoints': noHitPoints,
    };
  }

  @override
  void dispose() {
    _controller.stop();
    _controller.dispose();
    // 释放视频编解码器
    SystemChannels.platform.invokeMethod('VideoPlayer.disposeAll');

    // 强制GC（针对视频编解码器泄漏）
    SystemChannels.platform.invokeMethod('Memory.forceGC');
    // 释放内存
    _cachedHitPoints = Float32List(0);
    _cachedNoHitPoints = Float32List(0);
    // 建议添加以下代码
    if (Platform.isAndroid || Platform.isIOS) {
      // 调用引擎级内存释放
      SystemChannels.platform.invokeMethod('Memory.forceGC');
    }
    IsolationController.disposeAll(); // 退出时终止所有隔离
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 根据GPU负载动态调整动画速度
    if (_isHighLoad) {
      _controller.duration =
          Duration(milliseconds: _controller.duration!.inMilliseconds * 2);
    }

    return CustomPaint(
      painter: _AsyncPointsPainterDelegate(
        controller: _controller,
        isCacheReady: _isCacheReady,
        cachedHitPoints: _cachedHitPoints,
        cachedNoHitPoints: _cachedNoHitPoints,
        innerHalf: _innerHalf,
        pointsPerFrame: _pointsPerFrame,
        deviceTier: widget.deviceTier,
        isHighLoad: _isHighLoad,
      ),
    );
  }
}

// ================== 异步绘制代理 ==================
class _AsyncPointsPainterDelegate extends CustomPainter {
  final AnimationController controller;
  final bool isCacheReady;
  final Float32List cachedHitPoints;
  final Float32List cachedNoHitPoints;
  final double innerHalf;
  final int pointsPerFrame;
  final DeviceTier deviceTier;
  final bool isHighLoad;

  // 简化Paint创建 - 避免每帧重新创建
  static final _hitPaint = Paint()
    ..color = const Color(0xFF9045EE)
    ..strokeWidth = 3
    ..strokeCap = StrokeCap.round
    ..isAntiAlias = false
    ..style = PaintingStyle.stroke;

  static final _noHitPaint = Paint()
    ..color = Colors.red
    ..strokeWidth = 2
    ..strokeCap = StrokeCap.square
    ..isAntiAlias = false
    ..style = PaintingStyle.stroke;

  _AsyncPointsPainterDelegate({
    required this.controller,
    required this.isCacheReady,
    required this.cachedHitPoints,
    required this.cachedNoHitPoints,
    required this.innerHalf,
    required this.pointsPerFrame,
    required this.deviceTier,
    required this.isHighLoad,
  }) : super(repaint: controller);

  @override
  void paint(Canvas canvas, Size size) {
    if (!isCacheReady) return;

    final animationValue = controller.value;
    final totalPoints =
        (cachedHitPoints.length + cachedNoHitPoints.length) ~/ 2;
    final targetPoint = (animationValue * totalPoints).toInt();

    // 1. 绘制命中的点
    final hitPointsToDraw = targetPoint.clamp(0, cachedHitPoints.length ~/ 2);
    if (hitPointsToDraw > 0) {
      final points = _generatePoints(cachedHitPoints, 0, hitPointsToDraw);

      // 使用drawPoints替代drawCircle或Path
      canvas.drawPoints(
        ui.PointMode.points,
        points,
        _hitPaint,
      );
    }

    // 2. 绘制未命中的点
    final remaining = targetPoint - hitPointsToDraw;
    if (remaining > 0) {
      final noHitPointsToDraw =
          remaining.clamp(0, cachedNoHitPoints.length ~/ 2);
      final points = _generatePoints(cachedNoHitPoints, 0, noHitPointsToDraw);

      // 在GPU高负载时使用简化模式
      if (isHighLoad) {
        // 模式1: 使用点替代十字线
        canvas.drawPoints(
          ui.PointMode.points,
          points,
          _noHitPaint,
        );
      } else {
        // 模式2: 直接绘制十字线
        for (final center in points) {
          canvas.drawLine(
            Offset(center.dx - innerHalf, center.dy),
            Offset(center.dx + innerHalf, center.dy),
            _noHitPaint,
          );
          canvas.drawLine(
            Offset(center.dx, center.dy - innerHalf),
            Offset(center.dx, center.dy + innerHalf),
            _noHitPaint,
          );
        }
      }
    }
  }

  List<Offset> _generatePoints(Float32List points, int startIndex, int count) {
    final generated = <Offset>[];
    final start = startIndex * 2;
    final end = (startIndex + count) * 2;

    for (int i = start; i < end && i < points.length; i += 2) {
      generated.add(Offset(points[i], points[i + 1]));
    }

    return generated;
  }

  @override
  bool shouldRepaint(covariant _AsyncPointsPainterDelegate oldDelegate) {
    return oldDelegate.controller != controller ||
        oldDelegate.isCacheReady != isCacheReady ||
        !listEquals(oldDelegate.cachedHitPoints, cachedHitPoints) ||
        !listEquals(oldDelegate.cachedNoHitPoints, cachedNoHitPoints) ||
        oldDelegate.innerHalf != innerHalf ||
        oldDelegate.isHighLoad != isHighLoad;
  }
}

// // 设备性能检测类
class DevicePerformance {
  static Future<DeviceTier> determineDeviceTier() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        return androidInfo.isLowRamDevice
            ? DeviceTier.lowEnd
            : DeviceTier.highEnd;
      } else if (Platform.isIOS) {
        final iosInfo = await DeviceInfoPlugin().iosInfo;
        final identifier = iosInfo.utsname.machine;
        // return DeviceTier.midEnd;
        // 分级逻辑
        if (identifier.contains('iPhone13') ||
            identifier.contains('iPhone14') ||
            identifier.contains('iPhone15')) {
          return DeviceTier.highEnd;
        } else if (identifier.contains('iPhone11') ||
            identifier.contains('iPhone12')) {
          return DeviceTier.midEnd;
        } else {
          return DeviceTier.lowEnd;
        }
      }
      return DeviceTier.midEnd;
    } catch (e) {
      debugPrint('确定设备等级失败: $e');
      return DeviceTier.midEnd;
    }
  }
}

class SiteReportRenderer {
  static int _batchIndex = 0;
  static List<Float32List> _pointGroups = [];
  static List<ui.Image?> _images = [];
  static Size _markerSize = Size.zero;
  static Function? _onProgress;
  static Function? _onComplete;

  // 开始批量渲染
  static void startBatchRendering({
    required List<Float32List> pointGroups,
    required List<ui.Image?> images,
    required Size markerSize,
    required VoidCallback onProgress,
    required VoidCallback onComplete,
  }) {
    _batchIndex = 0;
    _pointGroups = pointGroups;
    _images = images;
    _markerSize = markerSize;
    _onProgress = onProgress;
    _onComplete = onComplete;

    // 开始渲染循环
    _renderNextBatch();
  }

  // 渲染下一批
  static void _renderNextBatch() {
    // 使用SchedulerBinding添加一个帧结束回调
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // 确保在下一帧渲染
      WidgetsBinding.instance.scheduleFrameCallback((_) {
        _processCurrentBatch();
        _renderNextBatch();
      });
    });
  }

  // 处理当前批次
  static void _processCurrentBatch() {
    if (_batchIndex >= _pointGroups.length) {
      // 所有批次完成
      _onComplete?.call();
      return;
    }

    // 通知进度更新
    _onProgress?.call();

    // 绘制当前批次（这里应该将当前批次的绘制命令添加到画布）
    // 实际代码中，这里会调用canvas.drawImageRect方法

    // 前进到下一批
    _batchIndex++;
  }

  // 渲染当前批次的点
  static void renderCurrentBatch(Canvas canvas) {
    if (_batchIndex >= _pointGroups.length) return;

    final points = _pointGroups[_batchIndex];
    final image = _images[_batchIndex];

    if (image == null || points.isEmpty) return;

    // 绘制当前批次的所有点
    for (int i = 0; i < points.length; i += 2) {
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        Rect.fromCenter(
          center: Offset(points[i], points[i + 1]),
          width: _markerSize.width,
          height: _markerSize.height,
        ),
        Paint()..isAntiAlias = false,
      );
    }
  }

  // 取消渲染
  static void cancelRendering() {
    _batchIndex = 0;
    _pointGroups.clear();
    _images.clear();
    _onProgress = null;
    _onComplete = null;
  }
}

// 添加全局隔离控制器
class IsolationController {
  static final Map<Isolate, bool> _activeIsolates = {};

  static void run(void Function(dynamic) function, dynamic message) {
    compute((msg) {
      final result = function(msg);
      return result;
    }, message)
        .then((_) {
      _activeIsolates.remove(Isolate.current);
    });
  }

  static void disposeAll() {
    _activeIsolates.keys.forEach((iso) => iso.kill());
    _activeIsolates.clear();
  }
}
