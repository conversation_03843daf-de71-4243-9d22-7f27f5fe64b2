import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../../gen/assets.gen.dart';
import '../../generated/l10n.dart';
import 'game_list_item.dart';
import 'logic.dart';

class GamePage extends StatefulWidget {
  const GamePage({super.key});

  @override
  State<GamePage> createState() => _GamePageState();
}

class _GamePageState extends State<GamePage>
    with AutomaticKeepAliveClientMixin {
  final logic = Get.put(GameLogic());
  final state = Get.find<GameLogic>().state;

  // @override
  // void initState() {
  //   super.initState();
  // }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(children: [
        SizedBox(
          height: double.infinity,
          child: Stack(
            children: [
              Container(
                padding:
                    EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                height: 162 + MediaQuery.of(context).padding.top,
                width: Get.width,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                        padding: EdgeInsets.only(left: 20.w, top: 9),
                        child: Obx(
                          () => Text(
                            state.gameDates[state.selectedIndex.value].ym,
                            style:
                                TextStyles.titleMedium18.copyWith(fontSize: 24),
                          ),
                        )),
                    const SizedBox(
                      height: 10,
                    ),
                    _dateSel(),
                  ],
                ),
              ),
              Positioned(
                  top: MediaQuery.of(context).padding.top + 130,
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20)),
                    child: RefreshIndicator(
                      onRefresh: logic.onRefresh,
                      child: Container(
                        color: Colours.bg_color,
                        child: Obx(() => state.init.value
                            ? (state.items.isEmpty
                                ? _emptyView(context)
                                : _listView(context))
                            : buildLoad()),
                      ),
                    ),
                  )),
            ],
          ),
        ),
        Obx(
          () => opaqueVisibility(state.isLoading.value),
        ),
      ]),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
                height: constraints.maxHeight,
                child: myNoDataView(context,
                    msg: S.current.no_matches_yet,
                    imagewidget: WxAssets.images.icGameNo.image())));
      },
    );
  }

  Widget _dateSel() {
    return Container(
      height: 64,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Obx(
        () => ScrollablePositionedList.builder(
          scrollDirection: Axis.horizontal,
          itemCount: state.gameDates.length,
          itemScrollController: state.scrollController,
          initialScrollIndex: state.selectedIndex.value,
          itemBuilder: (context, index) {
            final bool isSelected = index == state.selectedIndex.value;
            final gameDate = state.gameDates[index];
            return GestureDetector(
              onTap: () {
                logic.selectedIndex(index);
              },
              child: Container(
                width: (Get.width - 24.w) / 7,
                alignment: Alignment.center,
                child: Container(
                  width: 40.w,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.transparent,
                    borderRadius: BorderRadius.circular(20.w),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        gameDate.week,
                        style: TextStyle(
                          color: isSelected
                              ? Colours.color666666
                              : Colors.white.withOpacity(0.5),
                          fontWeight: isSelected
                              ? AppFontWeight.semiBold()
                              : AppFontWeight.regular(),
                          fontSize: 14.sp,
                        ),
                      ),
                      Text(
                        gameDate.dd,
                        style: TextStyle(
                          color:
                              isSelected ? Colours.color333333 : Colors.white,
                          fontWeight: isSelected
                              ? AppFontWeight.semiBold()
                              : AppFontWeight.regular(),
                          fontSize: 16.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.calculateTotalItemCount(),
        itemBuilder: (context, index) {
          final item = logic.getItemAtIndex(index);

          if (item is GameModel) {
            // 分组标题
            return _buildGroupTitle(item);
          } else if (item is Matches) {
            // 列表项
            return _buildListItem(item);
          }

          return const SizedBox.shrink();
        });
  }

  /// 构建分组标题
  Widget _buildGroupTitle(GameModel model) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => AppPage.to(Routes.arenaDetailsPage,
          arguments: {"id": int.parse(model.arenaId)}),
      child: Container(
        height: 34.w,
        padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 10.w),
        child: Row(
          children: [
            WxAssets.images.icCircleBs.image(width: 10.w, height: 10.w),
            SizedBox(
              width: 7.w,
            ),
            Text(
              model.arenaName,
              style: TextStyles.display14,
            ),
            const Spacer(),
            WxAssets.images.icArrowRight.image(width: 14.w, height: 14.w),
          ],
        ),
      ),
    );
  }

  /// 构建列表项
  Widget _buildListItem(Matches item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      child: GameListItem(
          item: item,
          type: 1,
          tap: (v) async {
            AppPage.to(Routes.comparisonPage, arguments: {
              "matchId": item.matchId, // "10000103", //
            });
            // if (v == 0) {
            //   //立即获取
            //   if (item.matchLocked == false) {
            //     WxLoading.showToast(S.current.game_report5);
            //   } else {
            //     AppPage.to(Routes.unlockDataPage,
            //             arguments: {
            //               "type": 0,
            //               "matchId": item.matchId,
            //               "showType": "1",
            //             },
            //             needLogin: true)
            //         .then((onValue) {
            //       logic.onRefresh();
            //     });
            //   }
            // } else {
            //   if (Utils.isToLogin()) {
            //     if (item.subscribed == true) {
            //       WxLoading.showToast(S.current.game_report6);
            //     } else {
            //       logic.getMatchesSubscribe(item);
            //     }
            //   }
            // }
          }),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
