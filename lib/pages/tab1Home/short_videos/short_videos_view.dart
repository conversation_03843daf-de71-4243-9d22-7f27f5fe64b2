import 'dart:async';
import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';
import 'package:shoot_z/pages/tab1Home/short_videos/short_videos_logic.dart';
import 'package:shoot_z/widgets/view.dart';

class VideoFeedScreen extends StatelessWidget {
  const VideoFeedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<ShortVideoController>(
        init: ShortVideoController(),
        builder: (controller) {
          return NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollEndNotification &&
                  notification.metrics.extentAfter < 500 &&
                  controller.hasMore.value &&
                  !controller.isLoading.value) {
                controller.loadVideos(loadMore: true);
              }
              return false;
            },
            child: PageView.builder(
              scrollDirection: Axis.vertical,
              itemCount: controller.dataList.length,
              onPageChanged: (index) {
                controller.changeVideo(index);
                if (index >= controller.dataList.length - 5 &&
                    controller.hasMore.value &&
                    !controller.isLoading.value) {
                  controller.loadVideos(loadMore: true);
                }
              },
              controller: PageController(
                viewportFraction: 1.0,
                initialPage: controller.currentIndex.value,
              ),
              itemBuilder: (context, index) {
                if (index >= controller.dataList.length) {
                  return const SizedBox.shrink();
                }

                final video = controller.dataList[index];
                final isLoading =
                    controller.loadingControllers.containsKey(video.id);

                // 预加载下一个视频
                if (index < controller.dataList.length - 1) {
                  controller.initializeVideo(
                      controller.dataList[index + 1], index + 1);
                }

                return SizedBox.expand(
                  child: VideoItem(
                    video: video,
                    isLoading: isLoading,
                    isActive: controller.currentIndex.value == index,
                    isLast: index == controller.dataList.length - 1,
                    isLoadingMore: controller.isLoading.value,
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class VideoItem extends StatelessWidget {
  final FeaturedListModel video;
  final bool isLoading;
  final bool isActive;
  final bool isLast;
  final bool isLoadingMore;

  const VideoItem({
    super.key,
    required this.video,
    required this.isLoading,
    required this.isActive,
    required this.isLast,
    required this.isLoadingMore,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final controller = Get.find<ShortVideoController>();
      final color =
          controller.dominantColors["${video.id}"] ?? Colors.grey[900]!;
      final videoId = "${video.id}";
      final playerController = controller.controllers[videoId];
      var isPortraitVideo = video.aspectRatio != null && video.aspectRatio! < 1;
      return GestureDetector(
        onTap: () {
          if (!isLoading) {
            controller.togglePlayPause(videoId);
          }
        },
        child: Container(
          color: color,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 视频播放器或加载状态
              if (!isLoading && playerController != null)
                _buildVideoPlayer(playerController, isPortraitVideo)
              else
                myNoDataView(context),

              // 半透明遮罩层
              if (!isLoading)
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.3),
                        Colors.transparent,
                        Colors.transparent,
                        Colors.black.withOpacity(0.5),
                      ],
                      stops: const [0, 0.3, 0.7, 1],
                    ),
                  ),
                ),

              // 播放/暂停图标
              if (isActive && !isLoading && playerController != null)
                Center(
                  child: AnimatedOpacity(
                    opacity: controller.isVideoPlaying(videoId) ? 0 : 1,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.play_circle_fill_outlined,
                      size: 72,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ),

              // 顶部状态栏
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  bottom: false,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() {
                          return Text(
                              "推荐${controller.currentIndex}${video.aspectRatio}",
                              style: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ));
                        }),
                        IconButton(
                          icon: const Icon(Icons.search,
                              size: 28, color: Colors.white),
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // 全屏按钮
              if (!isLoading && playerController != null)
                Positioned(
                  bottom: 100,
                  right: 16,
                  child: IconButton(
                    icon: const Icon(Icons.fullscreen,
                        size: 32, color: Colors.white),
                    onPressed: () =>
                        _enterFullScreen(context, video, playerController),
                  ),
                ),

              // 加载进度
              if (isLoading)
                const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),

              // 底部加载更多指示器
              if (isLast && isLoadingMore)
                Positioned(
                  bottom: 20,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            "加载更多...",
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildVideoPlayer(
      BetterPlayerController controller, bool isPortraitVideo) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Center(
          child: BetterPlayer(controller: controller),
        );
        // 竖屏视频全屏显示
        if (isPortraitVideo) {
          return BetterPlayer(controller: controller);
        }

        // 横屏视频以16:9比例居中显示
        return Center(
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: BetterPlayer(controller: controller),
          ),
        );
      },
    );
  }

  void _enterFullScreen(BuildContext context, FeaturedListModel video,
      BetterPlayerController controller) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullScreenVideoPlayer(
          controller: controller,
          video: video,
        ),
      ),
    );
  }
}

class FullScreenVideoPlayer extends StatefulWidget {
  final BetterPlayerController controller;
  final FeaturedListModel video;

  const FullScreenVideoPlayer({
    super.key,
    required this.controller,
    required this.video,
  });

  @override
  State<FullScreenVideoPlayer> createState() => _FullScreenVideoPlayerState();
}

class _FullScreenVideoPlayerState extends State<FullScreenVideoPlayer> {
  bool _showControls = true;
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    // 根据视频方向设置屏幕方向
    if (widget.video.aspectRatio != null && widget.video.aspectRatio! < 1) {
      // 竖屏视频
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
    } else {
      // 横屏视频
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    // 播放视频
    widget.controller.play();

    // 设置隐藏控制器的计时器
    _startHideControlsTimer();
  }

  @override
  void dispose() {
    // 恢复默认方向
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // 取消计时器
    _hideControlsTimer?.cancel();
    super.dispose();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _showControls = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final isPortraitVideo =
        widget.video.aspectRatio != null && widget.video.aspectRatio! < 1;

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          setState(() {
            _showControls = !_showControls;
          });
          if (_showControls) {
            _startHideControlsTimer();
          }
        },
        child: Stack(
          children: [
            // 全屏播放器
            if (isPortraitVideo)
              BetterPlayer(controller: widget.controller)
            else
              Center(
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: BetterPlayer(controller: widget.controller),
                ),
              ),

            // 控制面板
            if (_showControls) ...[
              // 返回按钮
              Positioned(
                top: 16,
                left: 16,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),

              // 播放/暂停按钮
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: IconButton(
                    icon: Icon(
                      widget.controller.isPlaying() == true
                          ? Icons.pause
                          : Icons.play_arrow,
                      size: 48,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      if (widget.controller.isPlaying() == true) {
                        widget.controller.pause();
                      } else {
                        widget.controller.play();
                      }
                    },
                  ),
                ),
              ),

              // 进度条
              // Positioned(
              //   bottom: 70,
              //   left: 16,
              //   right: 16,
              //   child: videopr(
              //     widget.controller.videoPlayerController!,
              //     allowScrubbing: true,
              //     colors: VideoProgressColors(
              //       playedColor: Colors.red,
              //       bufferedColor: Colors.grey[400]!,
              //       backgroundColor: Colors.grey[800]!,
              //     ),
              //     padding: EdgeInsets.zero,
              //   ),
              // ),

              // 视频信息
              Positioned(
                bottom: 20,
                left: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "@${widget.video.title}",
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.video.content ?? "",
                      style: const TextStyle(color: Colors.white),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 退出全屏按钮
              if (isLandscape)
                Positioned(
                  top: 16,
                  right: 16,
                  child: IconButton(
                    icon:
                        const Icon(Icons.fullscreen_exit, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }
}
