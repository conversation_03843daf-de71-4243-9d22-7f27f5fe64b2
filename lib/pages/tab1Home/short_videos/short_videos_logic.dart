import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:developer' as ccc;
import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';

import '../../../network/model/featured_list_model.dart';

// 扩展方法 - 修复了所有问题
extension BetterPlayerControllerExt on BetterPlayerController {
  bool get isDisposed {
    try {
      // 尝试访问内部属性
      final player = videoPlayerController;
      return player == null || !player.hasListeners;
    } catch (e) {
      return true;
    }
  }

  void safePlay() {
    if (isDisposed) return;
    try {
      play();
    } catch (e) {
      // 忽略错误
    }
  }

  void safePause() {
    if (isDisposed) return;
    try {
      pause();
    } catch (e) {
      // 忽略错误
    }
  }

  void forceDispose() {
    if (isDisposed) return;
    try {
      // 强制释放资源
      pause();
      setVolume(0.0);

      // 直接调用dispose()方法释放资源
      dispose();
    } catch (e) {
      // 忽略错误
    }
  }
}

class ShortVideoController extends GetxController with WidgetsBindingObserver {
  var dataList = <FeaturedListModel>[].obs;
  final RxInt currentIndex = 0.obs;
  final Map<String, BetterPlayerController> controllers = {};
  final RxMap<String, BetterPlayerController?> loadingControllers =
      <String, BetterPlayerController?>{}.obs;
  final RxMap<String, Color> dominantColors = <String, Color>{}.obs;
  var dataFag = {
    "isFrist": true,
    "page": 1,
  }.obs;

  // 分页相关状态
  final RxBool isLoading = false.obs;
  final RxBool hasMore = true.obs;
  final RxInt currentPage = 1.obs;
  var isFrist = true.obs;

  // 预加载相关参数
  final int preloadThreshold = 5;
  final int preloadCount = 10;
  final int keepControllersCount = 30;

  // 添加页面状态标志
  bool _isPageDisposed = false;

  // 添加静音标志
  bool _isMuted = false;

  // 添加强制释放标志
  bool _forceDisposeRequested = false;

  // 添加初始化Completer映射
  final Map<String, Completer<void>> _initializationCompleters = {};

  // 添加页面关闭信号量
  final Completer<void> _pageClosedCompleter = Completer<void>();

  // 添加当前播放的视频ID
  final RxString _currentPlayingVideoId = ''.obs;

  // 添加最近访问的视频队列
  final List<String> _recentlyAccessedVideos = [];
  final int _maxRecentlyAccessed = 20;

  BetterPlayerController? get currentController => dataList.isNotEmpty
      ? controllers["${dataList[currentIndex.value].id ?? 0}"]
      : null;

  FeaturedListModel? get currentVideo =>
      dataList.isNotEmpty ? dataList[currentIndex.value] : null;

  bool isVideoPlaying(String videoId) {
    final controller = controllers[videoId];
    return controller != null &&
        !controller.isDisposed &&
        (controller.isPlaying() ?? false);
  }

  @override
  void onInit() {
    super.onInit();
    // 添加应用生命周期观察者
    WidgetsBinding.instance.addObserver(this);
    loadVideos();
  }

  @override
  void onClose() {
    // 标记页面已关闭
    _pageClosedCompleter.complete();

    // 强制释放所有资源
    forceDisposeAllControllers();

    // 移除应用生命周期观察者
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_isPageDisposed) return;

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        // 应用进入后台或非活动状态时暂停所有视频并静音
        pauseAllVideos();
        muteAllVideos();
        break;
      case AppLifecycleState.detached:
        // 应用被销毁时释放所有资源
        forceDisposeAllControllers();
        break;
      default:
        break;
    }
  }

  // 加载视频数据
  Future<void> loadVideos({bool loadMore = false}) async {
    if (isLoading.value || (!loadMore && !hasMore.value) || _isPageDisposed)
      return;
    isLoading.value = true;
    try {
      getFeaturedList(isLoad: loadMore);
    } catch (e) {
      if (!_isPageDisposed) {
        Get.snackbar('加载失败', '无法加载视频数据: $e');
      }
    } finally {
      if (!_isPageDisposed) {
        isLoading.value = false;
      }
    }
  }

  //查询热点资讯视频
  getFeaturedList({
    bool isLoad = false,
  }) async {
    if (_isPageDisposed) return;

    var page = dataFag["page"] as int;
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 30,
      'page': page,
      'type': 2, //1-资讯 2-视频
    };

    try {
      var res = await Api().get(ApiUrl.getFeaturedList, queryParameters: param);

      // 检查页面是否已释放
      if (_isPageDisposed) return;

      if (res.isSuccessful()) {
        List list = res.data["list"] ?? [];
        List<FeaturedListModel> modelList =
            list.map((e) => FeaturedListModel.fromJson(e)).toList();

        dataFag["page"] = page + 1;
        if (modelList.length < 30) {
          hasMore.value = false;
        }
        if (isLoad) {
          dataList.addAll(modelList);
          dataList.refresh();
        } else {
          dataList.assignAll(modelList);
          currentIndex.value = 0;
        }
        for (int i = 0; i < modelList.length; i++) {
          // 初始化新加载的视频
          initializeVideo(modelList[i], i);
        }

        if (dataFag["isFrist"] as bool) {
          dataFag["isFrist"] = false;
          dataFag.refresh();
        }

        // 预加载周围视频
        _preloadSurroundingVideos();
      } else {
        if (!_isPageDisposed) {
          WxLoading.showToast(res.message);
        }
      }
    } catch (e) {
      if (!_isPageDisposed) {
        Get.snackbar('加载失败', '无法加载视频数据: $e');
      }
    }
  }

  // 初始化单个视频 - 修复了事件监听器问题
  Future<void> initializeVideo(FeaturedListModel video, int index2) async {
    // 检查页面是否已关闭
    if (_pageClosedCompleter.isCompleted) {
      return;
    }

    final String videoId = "${video.id ?? 0}";

    // 如果已经在加载或已加载，跳过
    if (loadingControllers.containsKey(videoId) ||
        (controllers.containsKey(videoId) &&
            !controllers[videoId]!.isDisposed)) {
      return;
    }

    // 标记为正在加载
    loadingControllers[videoId] = null;

    // 确保视频URL不为空
    if (video.videoUrl == null || video.videoUrl!.isEmpty) {
      print('视频URL为空: $videoId');
      loadingControllers.remove(videoId);
      return;
    }

    // 创建控制器
    var controller = BetterPlayerController(
      const BetterPlayerConfiguration(
        autoPlay: false,
        looping: true,
        autoDetectFullscreenDeviceOrientation: true,
        autoDetectFullscreenAspectRatio: true,
        controlsConfiguration: BetterPlayerControlsConfiguration(
          enableSkips: false,
          enableFullscreen: false,
          enableMute: false,
          showControls: false,
        ),
      ),
    );

    // 创建Completer来跟踪初始化状态
    final completer = Completer<void>();
    _initializationCompleters[videoId] = completer;

    // 使用函数声明而不是函数表达式
    void eventListener(BetterPlayerEvent event) {
      if (completer.isCompleted) return;

      if (event.betterPlayerEventType == BetterPlayerEventType.initialized) {
        completer.complete();
      } else if (event.betterPlayerEventType ==
          BetterPlayerEventType.exception) {
        // 使用parameters获取错误描述
        final errorDescription =
            event.parameters?['errorDescription'] ?? 'Unknown error';
        completer.completeError(Exception(errorDescription));
      }
    }

    // 添加事件监听器
    controller.addEventsListener(eventListener);

    try {
      // 设置数据源
      await controller.setupDataSource(
        BetterPlayerDataSource(
          BetterPlayerDataSourceType.network,
          video.videoUrl!,
          bufferingConfiguration: index2 == 0 || index2 == 1 || index2 == 2
              ? const BetterPlayerBufferingConfiguration(
                  minBufferMs: 500, // 更小的缓冲配置
                  maxBufferMs: 3000, // 更小的缓冲配置
                  bufferForPlaybackMs: 300,
                  bufferForPlaybackAfterRebufferMs: 500,
                )
              : const BetterPlayerBufferingConfiguration(
                  minBufferMs: 2000,
                  maxBufferMs: 4000,
                  bufferForPlaybackMs: 1000,
                  bufferForPlaybackAfterRebufferMs: 2000,
                ),
        ),
      );

      // 等待初始化完成或超时
      await completer.future.timeout(Duration(seconds: 10), onTimeout: () {
        throw TimeoutException('视频初始化超时');
      });

      // 检查页面是否已关闭
      if (_pageClosedCompleter.isCompleted) {
        controller.forceDispose();
        return;
      }

      // 获取视频时长
      final duration = controller.videoPlayerController!.value.duration;

      // 更新视频模型中的时长
      final index = dataList.indexWhere((v) => "${v.id ?? 0}" == videoId);
      if (index != -1) {
        // 更新视频时长
        dataList[index].duration = duration ?? Duration.zero;
      }

      // 检测视频方向
      final videoWidth =
          controller.videoPlayerController!.value.size?.width ?? 1;
      final videoHeight =
          controller.videoPlayerController!.value.size?.height ?? 0;
      dataList[index].aspectRatio = videoHeight > videoWidth ? 9 / 16 : 16 / 9;
      ccc.log("betterPlayerController2=$index--$videoWidth $videoHeight");
      refresh();

      if (isFrist.value) {
        isFrist.value = false;
      }

      final color = Colors.grey[800]!;
      dominantColors[videoId] = color;

      // 添加到控制器列表
      controllers[videoId] = controller;
      loadingControllers.remove(videoId);

      // 如果是当前视频，自动播放
      if (currentIndex.value == index) {
        playCurrentVideo();
      }
    } catch (e, stackTrace) {
      if (!_pageClosedCompleter.isCompleted) {
        print('初始化视频失败: $e');
        print('堆栈跟踪: $stackTrace');
      }
    } finally {
      // 清理Completer
      _initializationCompleters.remove(videoId);
      controller.removeEventsListener(eventListener);
      loadingControllers.remove(videoId);
    }
  }

  void changeVideo(int index) {
    if (_pageClosedCompleter.isCompleted ||
        index < 0 ||
        index >= dataList.length) return;

    if (currentIndex.value != index) {
      // 暂停上一个视频
      pausePreviousVideo();

      // 更新当前索引
      currentIndex.value = index;

      // 添加到最近访问列表
      _addToRecentlyAccessed("${dataList[index].id ?? 0}");

      // 尝试播放新视频
      final newVideoId = "${dataList[index].id ?? 0}";
      if (controllers.containsKey(newVideoId)) {
        playCurrentVideo();
      } else {
        initializeVideo(dataList[index], index);
      }

      // 预加载周围视频
      _preloadSurroundingVideos();

      // 释放不需要的控制器
      _releaseUnusedControllers();

      // 检查是否需要加载更多
      _checkLoadMoreNeeded();
    }
  }

  // 添加到最近访问列表
  void _addToRecentlyAccessed(String videoId) {
    // 如果已在列表中，移到最前面
    if (_recentlyAccessedVideos.contains(videoId)) {
      _recentlyAccessedVideos.remove(videoId);
    }

    // 添加到列表开头
    _recentlyAccessedVideos.insert(0, videoId);

    // 限制列表大小
    if (_recentlyAccessedVideos.length > _maxRecentlyAccessed) {
      _recentlyAccessedVideos.removeLast();
    }
  }

  // 播放当前视频
  void playCurrentVideo() {
    if (_pageClosedCompleter.isCompleted) return;

    final videoId = "${dataList[currentIndex.value].id ?? 0}";

    // 确保只有一个视频在播放
    if (_currentPlayingVideoId.value != videoId) {
      // 暂停之前播放的视频
      if (_currentPlayingVideoId.value.isNotEmpty) {
        safePause(_currentPlayingVideoId.value);
      }

      // 播放新视频
      if (controllers.containsKey(videoId)) {
        safePlay(videoId);
        _currentPlayingVideoId.value = videoId;
      }
    }
  }

  // 暂停上一个视频
  void pausePreviousVideo() {
    final previousVideoId = "${dataList[currentIndex.value].id ?? 0}";
    safePause(previousVideoId);

    // 清除当前播放视频ID
    if (_currentPlayingVideoId.value == previousVideoId) {
      _currentPlayingVideoId.value = '';
    }
  }

  // 安全播放
  void safePlay(String videoId) {
    if (_pageClosedCompleter.isCompleted) return;

    final controller = controllers[videoId];
    if (controller != null && !controller.isDisposed) {
      controller.safePlay();
    }
  }

  // 安全暂停
  void safePause(String videoId) {
    if (_pageClosedCompleter.isCompleted) return;

    final controller = controllers[videoId];
    if (controller != null && !controller.isDisposed) {
      controller.safePause();
    }
  }

  // 暂停所有视频
  void pauseAllVideos() {
    controllers.forEach((videoId, controller) {
      if (!controller.isDisposed) {
        controller.safePause();
      }
    });
    _currentPlayingVideoId.value = '';
  }

  // 静音所有视频
  void muteAllVideos() {
    if (!_isMuted) {
      controllers.forEach((videoId, controller) {
        if (!controller.isDisposed) {
          controller.setVolume(0.0);
        }
      });
      _isMuted = true;
    }
  }

  // 强制静音所有视频
  void forceMuteAllVideos() {
    controllers.forEach((videoId, controller) {
      if (!controller.isDisposed) {
        controller.setVolume(0.0);
      }
    });
    _isMuted = true;
  }

  void togglePlayPause(String videoId) {
    if (_pageClosedCompleter.isCompleted) return;

    final controller = controllers[videoId];
    if (controller != null && !controller.isDisposed) {
      if (controller.isPlaying() ?? false) {
        controller.safePause();
        if (_currentPlayingVideoId.value == videoId) {
          _currentPlayingVideoId.value = '';
        }
      } else {
        // 确保只有一个视频在播放
        if (_currentPlayingVideoId.value.isNotEmpty) {
          safePause(_currentPlayingVideoId.value);
        }
        controller.safePlay();
        _currentPlayingVideoId.value = videoId;
      }
    }
  }

  // 预加载周围视频
  void _preloadSurroundingVideos() {
    if (_pageClosedCompleter.isCompleted) return;

    final current = currentIndex.value;
    final total = dataList.length;

    // 计算预加载范围
    final start = max(0, current - preloadCount ~/ 2);
    final end = min(total, current + preloadCount ~/ 2 + 1);

    for (int i = start; i < end; i++) {
      if (i != current) {
        initializeVideo(dataList[i], i);
      }
    }
  }

  // 释放不需要的控制器
  void _releaseUnusedControllers() {
    if (_pageClosedCompleter.isCompleted) return;

    final current = currentIndex.value;
    final total = dataList.length;

    // 计算需要保留的范围
    final retainStart = max(0, current - keepControllersCount);
    final retainEnd = min(total, current + keepControllersCount + 1);

    // 释放范围外的控制器（除非是最近访问的）
    controllers.keys.toList().forEach((videoId) {
      final index = dataList.indexWhere((v) => "${v.id ?? 0}" == videoId);

      // 如果视频在保留范围内或是最近访问的，跳过
      if (index != -1 && (index >= retainStart && index < retainEnd) ||
          _recentlyAccessedVideos.contains(videoId)) {
        return;
      }

      // 释放控制器
      final controller = controllers[videoId]!;
      if (!controller.isDisposed) {
        controller.forceDispose();
      }
      controllers.remove(videoId);
      dominantColors.remove(videoId);
    });
  }

  // 检查是否需要加载更多
  void _checkLoadMoreNeeded() {
    if (_pageClosedCompleter.isCompleted) return;

    final current = currentIndex.value;
    final total = dataList.length;

    // 如果接近末尾且有更多数据，加载更多
    if (total - current <= preloadThreshold &&
        hasMore.value &&
        !isLoading.value) {
      loadVideos(loadMore: true);
    }
  }

  // 强制释放所有控制器
  void forceDisposeAllControllers() {
    _isPageDisposed = true;
    _forceDisposeRequested = true;

    // 强制静音所有视频
    forceMuteAllVideos();

    // 强制释放所有控制器
    controllers.forEach((_, controller) {
      if (!controller.isDisposed) {
        controller.forceDispose();
      }
    });
    controllers.clear();

    // 释放加载中的控制器
    loadingControllers.forEach((_, controller) {
      if (controller != null && !controller.isDisposed) {
        controller.forceDispose();
      }
    });
    loadingControllers.clear();

    dominantColors.clear();

    // 取消所有初始化Completer
    _initializationCompleters.forEach((videoId, completer) {
      if (!completer.isCompleted) {
        completer.completeError(Exception('页面已释放'));
      }
    });
    _initializationCompleters.clear();

    // 清除当前播放视频ID
    _currentPlayingVideoId.value = '';

    // 清除最近访问列表
    _recentlyAccessedVideos.clear();

    // 强制清理内存
    forceCleanMemory();
  }

  // 强制清理内存
  void forceCleanMemory() {
    // 尝试强制垃圾回收
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('flutter/platform');
        platform.invokeMethod('System.gc');
      } catch (e) {
        print('强制GC失败: $e');
      }
    }

    // 清理数据列表
    dataList.clear();
    dataList.refresh();

    // 重置状态
    currentIndex.value = 0;
    isLoading.value = false;
    hasMore.value = true;
    isFrist.value = true;
    dataFag.value = {"isFrist": true, "page": 1};
  }
}
