import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/highlights/highlights_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../generated/l10n.dart';
import '../../../widgets/more_widget.dart';

class HighlightsPage extends StatefulWidget {
  const HighlightsPage({super.key});

  @override
  State<HighlightsPage> createState() => _HighlightsPageState();
}

class _HighlightsPageState extends State<HighlightsPage>
    with AutomaticKeepAliveClientMixin {
  final logic = Get.put(HighlightsLogic());
  final state = Get.find<HighlightsLogic>().state;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colours.color191921,
      body: SafeArea(
          child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(right: 20.w, top: 20.w, bottom: 10.w),
            child: Row(
              children: [
                SizedBox(
                  width: 3.w,
                ),
                SizedBox(
                  width: 50.w,
                  child: IconButton(
                      onPressed: () {
                        AppPage.back();
                      },
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 20,
                      )),
                ),
                Text(
                  S.current.my_highlights,
                  style: TextStyles.titleMedium18.copyWith(fontSize: 24.sp),
                ),
                const Expanded(child: SizedBox.shrink()),
                Container(
                  width: 66.w,
                  height: 26.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: GradientUtils.mainGradient,
                      borderRadius: BorderRadius.circular(13.w)),
                  child: Obx(
                    () => Text(
                      S.current.together(state.totalCount),
                      style: TextStyles.display14,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Obx(
              () => state.init.value ? _refreshList(context) : buildLoad(),
            ),
          ),
        ],
      )),
    );
  }

  Widget _refreshList(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification note) {
        if (note.metrics.pixels == note.metrics.maxScrollExtent) {
          logic.loadMore();
        }
        return true;
      },
      child:
          RefreshIndicator(onRefresh: logic.onRefresh, child: _list(context)),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => ListView.builder(
        itemCount: state.data.length + 1,
        itemBuilder: (context, index) {
          if (index == state.data.length) {
            if (state.data.isEmpty) {
              return _emptyWidget();
            } else {
              return _moreWidget();
            }
          }
          final group = state.data[index];
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 分组标题
              Padding(
                padding: EdgeInsets.all(20.w),
                child: Row(children: [
                  Text(
                    '${group.date} ${group.week}',
                    style: TextStyles.display14,
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => AppPage.to(Routes.arenaDetailsPage,
                          arguments: {"id": int.parse(group.arenaId)}),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            WxAssets.images.icHlLocation
                                .image(width: 12.w, height: 12.w),
                            const SizedBox(
                              width: 5,
                            ),
                            Flexible(
                              child: Text(
                                group.arenaName,
                                style: TextStyles.display14
                                    .copyWith(color: Colours.color5C5C6E),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            )
                          ]),
                    ),
                  ),
                ]),
              ),
              // 分组内容
              Padding(
                padding: EdgeInsets.only(bottom: 6.w, left: 15.w, right: 15.w),
                child: GridView.builder(
                  shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2, // 每行两个 item
                    crossAxisSpacing: 15.w,
                    mainAxisSpacing: 15.w,
                    childAspectRatio: 1, // 控制每个 item 的宽高比例
                  ),
                  itemCount: group.videos.length,
                  itemBuilder: (context, itemIndex) {
                    final video = group.videos[itemIndex];
                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () async {
                        if (!video.isFinish) {
                          return;
                        }
                        video.rxNew.value = false;
                        final result = await AppPage.to(Routes.highlightsVideo,
                            arguments: {'video': video, 'group': group});
                        if (result == true) {
                          // 如果结果为 true，表示需要刷新列表
                          group.videos.removeAt(itemIndex);
                          if (group.videos.isEmpty) {
                            state.data.removeAt(index);
                          }
                          state.data.refresh(); // 刷新数据
                        }
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Stack(
                          children: [
                            Positioned(
                                left: 0,
                                right: 0,
                                bottom: 0,
                                top: 0,
                                child: video.isFinish
                                    ? CachedNetworkImage(
                                        imageUrl: video.videoCover,
                                        fit: BoxFit.cover,
                                      )
                                    : (video.status == 0
                                        ? WxAssets.images.icHlPdz
                                            .image(fit: BoxFit.fill)
                                        : WxAssets.images.icHlHcz
                                            .image(fit: BoxFit.fill))),
                            Obx(
                              () => Visibility(
                                visible: video.rxNew.value && video.isFinish,
                                child: Positioned(
                                    top: 5,
                                    left: 5,
                                    child: WxAssets.images.icHlNew
                                        .image(width: 38.w, height: 18.w)),
                              ),
                            ),
                            Visibility(
                              visible:
                                  (video.isFinish && video.name.isNotEmpty) ||
                                      (video.status == 0),
                              child: Positioned(
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: Container(
                                  padding: video.isFinish
                                      ? EdgeInsets.only(
                                          top: 18.w, left: 10.w, right: 10.w)
                                      : EdgeInsets.only(top: 4.w),
                                  height: 40.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                        image: WxAssets.images.icHlItemBottom
                                            .provider(),
                                        fit: BoxFit.fill),
                                  ),
                                  child: video.isFinish
                                      ? const SizedBox()

                                      // Center(
                                      //     child: Text(
                                      //     video.name,
                                      //     style: TextStyles.display12
                                      //         .copyWith(color: Colors.white),
                                      //     maxLines: 1,
                                      //     overflow: TextOverflow.ellipsis,
                                      //   ))
                                      : Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            WxAssets.images.icHlPeople.image(
                                                width: 16.w, height: 16.w),
                                            const SizedBox(
                                              width: 4,
                                            ),
                                            Text(
                                              "${video.no}/${video.queueSize}",
                                              style: TextStyles.display14,
                                            ),
                                          ],
                                        ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _moreWidget() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child: MoreWidget(state.data.length, logic.hasMore(), state.pageSize));
  }

  Widget _emptyWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15, top: 80.w),
      child: Stack(
        children: [
          WxAssets.images.icHighlightsNo.image(width: 339.w, fit: BoxFit.fill),
          Positioned(
              left: 28.w,
              top: 108.w,
              child: WxButton(
                text: S.current.view_supported_stadiums,
                textStyle: TextStyles.semiBold
                    .copyWith(fontSize: 12.sp, color: Colours.color333333),
                width: 110.w,
                height: 32.w,
                borderRadius: BorderRadius.circular(16.w),
                backgroundColor: Colors.white,
                onPressed: () => AppPage.to(Routes.place),
              )),
        ],
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
