import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab1Home/highlights/highlights_state.dart';

import '../../../utils/event_bus.dart';
import 'models/highlights_model.dart';

class HighlightsLogic extends GetxController {
  final HighlightsState state = HighlightsState();

  /// 是否正在加载数据
  bool _isLoading = false;
  @override
  void onInit() async {
    super.onInit();
    state.refreshSubscription = BusUtils.instance.on((p0) {
      if (p0.key == EventBusKey.toCollectionHighlights) {
        onRefresh();
      }
    });
    await onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
    state.refreshSubscription?.cancel();
  }

  bool hasMore() {
    return state.data.length < state.totalRows;
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    _isLoading = true;
    await request(false);
    _isLoading = false;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    state.page = 1;
    await request(true);
    state.init.value = true;
  }

  Future<void> request(bool isRefresh) async {
    final res = await Api().get(ApiUrl.myHighlights,
        queryParameters: {'pageIndex': state.page, 'pageSize': state.pageSize});
    if (res.isSuccessful()) {
      state.page += 1;
      final response = HighlightsResponse.fromJson(res.data);
      state.totalRows = response.totalRows;
      state.totalCount.value = response.totalCount;
      if (isRefresh) {
        state.data.value = response.result;
      } else {
        state.data.addAll(response.result);
      }
    } else {
      if (isRefresh) {
        state.data.value = [];
        state.totalRows = 0;
        state.totalCount.value = 0;
      }
    }
  }
}
