// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'highlights_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HighlightsResponse _$HighlightsResponseFromJson(Map<String, dynamic> json) =>
    HighlightsResponse(
      json['currentPage'] as int,
      json['isEnd'] as bool,
      (json['result'] as List<dynamic>)
          .map((e) => HighlightsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['totalPages'] as int,
      json['totalRows'] as int,
      json['totalCount'] as int,
    );

Map<String, dynamic> _$HighlightsResponseToJson(HighlightsResponse instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'isEnd': instance.isEnd,
      'result': instance.result,
      'totalPages': instance.totalPages,
      'totalRows': instance.totalRows,
      'totalCount': instance.totalCount,
    };

HighlightsModel _$HighlightsModelFromJson(Map<String, dynamic> json) =>
    HighlightsModel(
      json['arenaId'] as String,
      json['arenaName'] as String,
      json['date'] as String,
      (json['videos'] as List<dynamic>)
          .map((e) => Videos.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['week'] as String,
    );

Map<String, dynamic> _$HighlightsModelToJson(HighlightsModel instance) =>
    <String, dynamic>{
      'arenaId': instance.arenaId,
      'arenaName': instance.arenaName,
      'date': instance.date,
      'videos': instance.videos,
      'week': instance.week,
    };

Videos _$VideosFromJson(Map<String, dynamic> json) => Videos(
      json['compressedVideoPath'] as String,
      json['id'] as String,
      json['name'] as String,
      json['no'] as int,
      json['queueSize'] as int,
      json['status'] as int,
      json['videoCover'] as String,
      json['videoPath'] as String,
      json['videoSize'] as int,
      json['new'] as bool,
    );

Map<String, dynamic> _$VideosToJson(Videos instance) => <String, dynamic>{
      'compressedVideoPath': instance.compressedVideoPath,
      'id': instance.id,
      'name': instance.name,
      'no': instance.no,
      'queueSize': instance.queueSize,
      'status': instance.status,
      'videoCover': instance.videoCover,
      'videoPath': instance.videoPath,
      'videoSize': instance.videoSize,
      'new': instance.isNew,
    };
