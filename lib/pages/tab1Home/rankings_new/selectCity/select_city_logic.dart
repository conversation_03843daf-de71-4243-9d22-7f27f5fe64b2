import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';

class SelectCityLogic extends GetxController {
  var areaList = <AreaModel>[].obs;
  var selectArea = Rxn<AreaModel>();
  @override
  void onInit() {
    super.onInit();
    getAreaList();
  }

  //意向记录列表
  Future<void> getAreaList({int pid = 0}) async {
    WxLoading.show();
    Map<String, dynamic> request = {
      'pid': pid,
    };
    var res = await Api().get(ApiUrl.getAreaList, queryParameters: request);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      cc.log("message&&&&&&&&$list");
      areaList.value = list.map((e) => AreaModel.fromJson(e)).toList();
      if (pid == 0) {
        areaList.insert(0, AreaModel(id: 0, name: '全国', pid: 0));
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }
}

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AreaModel {
/*
{
  "id": 0,
  "name": "string",
  "pid": 0
} 
*/

  int? id;
  String? name;
  int? pid;

  AreaModel({
    this.id,
    this.name,
    this.pid,
  });
  AreaModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    name = json['name']?.toString();
    pid = json['pid']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['pid'] = pid;
    return data;
  }
}
