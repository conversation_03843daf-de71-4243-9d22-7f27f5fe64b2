import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/selectCity/select_city_logic.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:ui_packages/ui_packages.dart';

class SelectCityDialog extends StatelessWidget {
  final logic = Get.put(SelectCityLogic(),
      tag: DateTime.now().millisecondsSinceEpoch.toString());
  SelectCityDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colours.color191919,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.r),
            topRight: Radius.circular(8.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar at top
            Container(
              margin: EdgeInsets.only(top: 12.w),
              width: 38.w,
              height: 4.w,
              decoration: BoxDecoration(
                color: const Color(0x1AD8D8D8),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            // Header
            Container(
              padding: EdgeInsets.all(20.w),
              child: Text(
                '选择地区',
                style: TextStyles.titleSemiBold16,
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              height: 40.w,
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color1E1E1E,
                  borderRadius: BorderRadius.circular(8.r)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                      "${LocationUtils.instance.provinceName ?? '暂未获取到当前地区'} ${LocationUtils.instance.cityName ?? ''}",
                      style:
                          TextStyles.display12.copyWith(color: Colours.white)),
                  if (LocationUtils.instance.cityName != null)
                    InkWell(
                      onTap: () {
                        Get.back(
                            result: AreaModel(
                                id: int.parse(
                                    LocationUtils.instance.cityId ?? '0'),
                                name:
                                    LocationUtils.instance.cityName ?? '全国排行'));
                      },
                      child: Text(
                        "使用当前地区",
                        style: TextStyles.display12
                            .copyWith(color: Colours.color6435E9),
                      ),
                    )
                ],
              ),
            ),
            Container(
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.all(15.w),
                margin: EdgeInsets.only(left: 15.w),
                height: 50.w,
                child: logic.selectArea.value == null
                    ? Text(
                        '省份/地区',
                        style: TextStyles.regular.copyWith(
                          color: Colours.color33FFFFFF,
                          fontSize: 14.sp,
                        ),
                      )
                    : RichText(
                        text: TextSpan(
                          style: TextStyles.regular.copyWith(
                            color: Colours.color33FFFFFF,
                            fontSize: 14.sp,
                          ),
                          children: [
                            TextSpan(
                              text: logic.selectArea.value?.name ?? '',
                              style: TextStyles.regular.copyWith(
                                color: Colours.white,
                                fontSize: 14.sp,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  logic.selectArea.value = null;
                                  logic.areaList.value = [];
                                  logic.getAreaList();
                                },
                            ),
                            const TextSpan(text: ' 城市'),
                          ],
                        ),
                      )),
            Container(
              height: 1.w,
              margin: EdgeInsets.symmetric(horizontal: 30.w),
              color: Colours.color242424,
            ),
            Expanded(
                child: ListView.separated(
              shrinkWrap: true,
              itemCount: logic.areaList.length,
              separatorBuilder: (context, index) => Divider(
                height: 1.w,
                thickness: 1.w,
                color: Colours.color242424,
                indent: 30.w,
                endIndent: 30.w,
              ),
              itemBuilder: (context, index) {
                AreaModel model = logic.areaList[index];
                return GestureDetector(
                  onTap: () {
                    if (index == 0 && logic.selectArea.value == null) {
                      Get.back(result: model);
                    } else {
                      if (model.pid == 0) {
                        logic.selectArea.value = model;
                        //省
                        logic.getAreaList(pid: model.id ?? 0);
                      } else {
                        Get.back(result: model);
                      }
                    }
                  },
                  child: Container(
                    color: Colours.color191919,
                    padding: EdgeInsets.symmetric(horizontal: 30.w),
                    height: 50.w,
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      model.name ?? '',
                      style: TextStyles.regular.copyWith(
                          color: (model.name == '全国')
                              ? Colours.color6435E9
                              : Colors.white),
                    ),
                  ),
                );
              },
            ))
          ],
        ),
      );
    });
  }
}
