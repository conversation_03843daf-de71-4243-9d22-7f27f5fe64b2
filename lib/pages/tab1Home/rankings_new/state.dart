import 'dart:async';

import 'package:get/get.dart';

class RankingsState {
  StreamSubscription? subscription;
  final List<String> playerTabs = ['总得分', '总助攻', '总篮板', '总进球', '单场得分', '场均得分'];
  // 创建月份缩写数组
  final List<String> months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec'
  ];
  var isPlayer = true.obs;
  var playerTabIndex = 0.obs;
  var cityName = '请选择'.obs;
  int cityId = 0;
  var noData = false.obs;
  var teamNoData = false.obs;
  //自己的排名
  var numbers = [0, 0, 0, 0, 0, 0].obs;
  var myRankData = ['0', '0', '0', '0', '0', '0'].obs;
  var month = (-1).obs;
}
