import 'package:flutter/material.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 入口
class RankingRules extends StatelessWidget {
  const RankingRules({super.key});
  @override
  Widget build(BuildContext context) {
    var textList = [
      '默认为1000分一星球队',
      '达到1200分为二星球队',
      '达到1500分为三星球队',
      '达到2000分为四星球队',
      '达到3000分为五星球队'
    ];
    var imageList = [
      WxAssets.images.oneStarsIcon.image(),
      WxAssets.images.twoStarsIcon.image(),
      WxAssets.images.threeStarsIcon.image(),
      WxAssets.images.fourStarsIcon.image(),
      WxAssets.images.fiveStarsIcon.image()
    ];
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.ranking_rules),
      ),
      body: Container(
        margin: const EdgeInsets.only(top: 20, left: 15, right: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                TextWithIcon(
                    title: '段位规则', icon: WxAssets.images.indicatorIcon.image()),
                const SizedBox(
                  width: 8,
                ),
                Text(
                  '需进行一场比赛才会计入排行榜',
                  style:
                      TextStyles.display12.copyWith(color: Colours.colorA8A8BC),
                ),
              ],
            ),
            ...List.generate(
                textList.length,
                (index) => Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.only(left: 15, right: 15),
                    height: 70,
                    width: double.infinity,
                    decoration: const BoxDecoration(
                        color: Colours.color191921,
                        borderRadius: BorderRadius.all(Radius.circular(8))),
                    child: Row(
                      children: [
                        imageList[index],
                        const SizedBox(
                          width: 10,
                        ),
                        Text(
                          textList[index],
                          style: TextStyles.display14,
                        )
                      ],
                    ))),
            const SizedBox(
              height: 20,
            ),
            TextWithIcon(
                title: '殿堂分规则', icon: WxAssets.images.indicatorIcon.image()),
            Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(15),
                width: double.infinity,
                decoration: const BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.all(Radius.circular(8))),
                child: const Text(
                  '1.天梯分每个赛季重置，每一个季度为一个赛季\n2.每次比赛结束胜方队伍＋50分, 败方队伍-30分\n3.根据球队双方水平和分数会有额外的加减分机制\n4.赛季结束后全国榜单中上榜的球队和球员会获得丰厚奖励',
                  style: TextStyle(color: Colors.white, fontSize: 14,height: 1.8),
                ))
          ],
        ),
      ),
    );
  }
}
