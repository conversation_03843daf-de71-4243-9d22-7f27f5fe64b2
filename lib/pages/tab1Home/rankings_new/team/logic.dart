// ignore_for_file: unnecessary_null_comparison

import 'package:dio/dio.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/player/ranking_model.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/team/state.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'dart:developer' as cc;

class TeamLogic extends GetxController {
  final int index;
  TeamLogic({required this.index});
  final rankingsLogic = Get.find<RankingsLogic>();
  final state = TeamState();
  var isInit = false;
  @override
  void onInit() {
    super.onInit();
    cc.log("onInit");
    // getData();
    getTeamsRankingData();
    state.subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.rankingsCitySwitch) {
        cc.log("message^^^^^^^^^^^${rankingsLogic.state.isPlayer.value}");
        state.teamRankingModel.value = [];
        state.teamRankingModel.refresh();
        if (!rankingsLogic.state.isPlayer.value) {
          getTeamsRankingData();
        }
      } else if (action.key == EventBusKey.rankingsRankTypeSwitch) {
        if (!rankingsLogic.state.isPlayer.value) {
          getTeamsRankingData();
        }
      }
    });
  }

  @override
  void onClose() {
    state.subscription?.cancel();
    super.onClose();
  }

  var cancelToken = CancelToken();
  Future<void> getTeamsRankingData() async {
    if (rankingsLogic.state.cityId == null) {
      return;
    }
    state.requestError.value = false;
    final url = ApiUrl.teamsRanking;
    cancelToken.cancel();
    cancelToken = CancelToken();
    var params = {'city': rankingsLogic.state.cityId, 'limit': 100, 'page': 1};
    cc.log("params!!!!!!!!$params");
    final response = await Api.instance
        .get(url, cancelToken: cancelToken, queryParameters: params);
    isInit = true;
    if (response.isSuccessful()) {
      cc.log("params!!!!!!!!222222${response.data}");
      state.requestError.value = false;
      state.teamRankingModel.value = (response.data["list"] as List)
          .map((e) => TeamRankingModel.fromJson(e))
          .toList();
      state.teamRankingModel.refresh();
      if (state.teamRankingModel.isEmpty) {
        rankingsLogic.state.teamNoData.value = true;
        rankingsLogic.state.noData.value = true;
      } else {
        rankingsLogic.state.teamNoData.value = false;
        rankingsLogic.state.noData.value = false;
      }
    } else {
      state.teamRankingModel.value = [];
      state.teamRankingModel.refresh();
      state.requestError.value = true;
      rankingsLogic.state.teamNoData.value = true;
      rankingsLogic.state.noData.value = true;
    }
  }
}
