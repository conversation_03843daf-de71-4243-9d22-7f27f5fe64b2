import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/player/logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/player/state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

class PlayerView extends StatefulWidget {
  final int index;
  const PlayerView({super.key, required this.index});

  @override
  State<PlayerView> createState() => _PlayerViewState();
}

class _PlayerViewState extends State<PlayerView> {
  // 声明 logic 变量但不立即初始化
  late final PlayerLogic logic;
  late final PlayerState state;
  @override
  void initState() {
    super.initState();
    // 在 initState 中初始化 logic
    logic =
        Get.put(PlayerLogic(index: widget.index), tag: widget.index.toString());
    state = Get.find<PlayerLogic>(tag: widget.index.toString()).state;
  }

  @override
  Widget build(BuildContext context) {
    String title = '';
    switch (widget.index) {
      case 0:
        title = '总得分';
        break;
      case 1:
        title = '总助攻';
        break;
      case 2:
        title = '总篮板';
        break;
      case 3:
        title = '总进球';
        break;
      case 4:
        title = '单场得分';
        break;
      case 5:
        title = '场均得分';
        break;
    }
    return Obx(() {
      return Column(
        children: [
          if (state.rankingModel.value.billboard != null &&
              state.rankingModel.value.billboard!.isNotEmpty)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                state.rankingModel.value.billboard!.length >= 2
                    ? Container(
                        width: 91.5.w,
                        height: 131.w,
                        padding: EdgeInsets.only(top: 10.w),
                        margin: EdgeInsets.only(top: 50.w),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image:
                                    WxAssets.images.rankingOtherBg.provider())),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () => AppPage.to(
                                  Routes.careerHighlightsHomePage,
                                  arguments: {
                                    'userId': state.rankingModel.value
                                            .billboard?[1]?.userId ??
                                        '0'
                                  }),
                              child: Container(
                                width: 55.w,
                                height: 61.w,
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                    image: WxAssets.images.numberTwoHeadCircle
                                        .provider(), // 本地边框图片
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                child: Center(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20.w),
                                    child: CachedNetworkImage(
                                      imageUrl: state.rankingModel.value
                                              .billboard?[1]?.logo ??
                                          '',
                                      width: 40.w,
                                      height: 40.w,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                                state.rankingModel.value.billboard?[1]?.name ??
                                    '',
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              state.rankingModel.value.billboard?[1]
                                      ?.rankData ??
                                  '',
                              style: TextStyles.din.copyWith(
                                  fontSize: 16.sp, fontWeight: FontWeight.bold),
                            )
                          ],
                        ),
                      )
                    : SizedBox(
                        width: 91.5.w,
                        height: 160.w,
                      ),
                Container(
                    width: 91.5.w,
                    height: 131.w,
                    padding: EdgeInsets.only(top: 10.w),
                    // margin: EdgeInsets.only(top: 10.w),
                    decoration: BoxDecoration(
                        image: DecorationImage(
                            image: WxAssets.images.numberOneBg.provider())),
                    child: Column(
                      children: [
                        InkWell(
                            onTap: () => AppPage.to(
                                    Routes.careerHighlightsHomePage,
                                    arguments: {
                                      'userId': state.rankingModel.value
                                              .billboard?[0]?.userId ??
                                          '0'
                                    }),
                            child: Container(
                              width: 55.w,
                              height: 61.w,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: WxAssets.images.numberOneHeadCircle
                                      .provider(), // 本地边框图片
                                  fit: BoxFit.cover,
                                ),
                              ),
                              child: Center(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20.w),
                                  child: CachedNetworkImage(
                                    imageUrl: state.rankingModel.value
                                            .billboard?[0]?.logo ??
                                        '',
                                    width: 40.w,
                                    height: 40.w,
                                    fit: BoxFit.fill,
                                  ),
                                ),
                              ),
                            )),
                        SizedBox(
                          height: 10.w,
                        ),
                        Text(state.rankingModel.value.billboard?[0]?.name ?? '',
                            style: TextStyles.regular.copyWith(fontSize: 14.sp),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis),
                        SizedBox(
                          height: 10.w,
                        ),
                        Text(
                          state.rankingModel.value.billboard?[0]?.rankData ??
                              '',
                          style: TextStyles.din.copyWith(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: Colours.color6234E3),
                        )
                      ],
                    )),
                state.rankingModel.value.billboard!.length >= 3
                    ? Container(
                        width: 91.5.w,
                        height: 131.w,
                        padding: EdgeInsets.only(top: 10.w),
                        margin: EdgeInsets.only(top: 50.w),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image:
                                    WxAssets.images.rankingOtherBg.provider())),
                        child: Column(
                          children: [
                            InkWell(
                                onTap: () => AppPage.to(
                                        Routes.careerHighlightsHomePage,
                                        arguments: {
                                          'userId': state.rankingModel.value
                                                  .billboard?[2]?.userId ??
                                              '0'
                                        }),
                                child: Container(
                                  width: 55.w,
                                  height: 61.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: WxAssets
                                          .images.numberThreeHeadCircle
                                          .provider(), // 本地边框图片
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  child: Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20.w),
                                      child: CachedNetworkImage(
                                        imageUrl: state.rankingModel.value
                                                .billboard?[2]?.logo ??
                                            '',
                                        width: 40.w,
                                        height: 40.w,
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                  ),
                                )),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                                state.rankingModel.value.billboard?[2]?.name ??
                                    '',
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              state.rankingModel.value.billboard?[2]
                                      ?.rankData ??
                                  '',
                              style: TextStyles.din.copyWith(
                                  fontSize: 16.sp, fontWeight: FontWeight.bold),
                            )
                          ],
                        ))
                    : SizedBox(
                        width: 91.5.w,
                      ),
              ],
            ),
          SizedBox(
            height: 20.w,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  alignment: Alignment.center,
                  width: 52.w,
                  child: Text('排名',
                      style: TextStyles.regular.copyWith(fontSize: 12.sp))),
              Container(
                  alignment: Alignment.center,
                  width: 72.w,
                  child: Text('球员',
                      style: TextStyles.regular.copyWith(fontSize: 12.sp))),
              Container(
                alignment: Alignment.center,
                width: 52.w,
                child: Text(title,
                    style: TextStyles.regular.copyWith(fontSize: 12.sp)),
              )
            ],
          ),
          SizedBox(height: 10.w),
          Expanded(
              child: Obx(
            () => state.requestError.value
                ? const SizedBox.shrink()
                : ListView.builder(
                    padding: EdgeInsets.only(
                        bottom: (MediaQuery.of(context).padding.bottom > 0
                                ? MediaQuery.of(context).padding.bottom
                                : 20.w) +
                            48.w),
                    itemCount:
                        (state.rankingModel.value.billboard?.length ?? 0) > 3
                            ? (state.rankingModel.value.billboard!.length - 3)
                            : 0,
                    itemBuilder: (context, index) {
                      final actualIndex = index + 3;
                      return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              width: 52.w,
                              child: Text(
                                '${state.rankingModel.value.billboard?[actualIndex]?.index ?? 0}',
                                style: TextStyles.din.copyWith(fontSize: 14.sp),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            InkWell(
                                onTap: () => AppPage.to(
                                        Routes.careerHighlightsHomePage,
                                        arguments: {
                                          'userId': state
                                                  .rankingModel
                                                  .value
                                                  .billboard?[actualIndex]
                                                  ?.userId ??
                                              '0'
                                        }),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(12.w),
                                      child: CachedNetworkImage(
                                        imageUrl: state
                                                .rankingModel
                                                .value
                                                .billboard?[actualIndex]
                                                ?.logo ??
                                            '',
                                        width: 24.w,
                                        height: 24.w,
                                        fit: BoxFit.fill,
                                      ),
                                    ).marginSymmetric(
                                        vertical: index > 2 ? 6.w : 11.w),
                                    SizedBox(width: 8.w),
                                    SizedBox(
                                      width: 70.w,
                                      child: Text(
                                        state
                                                .rankingModel
                                                .value
                                                .billboard?[actualIndex]
                                                ?.name ??
                                            '',
                                        style: TextStyles.regular
                                            .copyWith(fontSize: 12.sp),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                )),
                            Container(
                              alignment: Alignment.center,
                              width: 52.w,
                              child: Text(
                                  state.rankingModel.value
                                          .billboard?[actualIndex]?.rankData ??
                                      '',
                                  style: TextStyles.din.copyWith(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center),
                            ),
                          ]);
                    }),
          )),
        ],
      ).marginSymmetric(horizontal: 20.w);
    });
  }

  @override
  void dispose() {
    super.dispose();
  }
}
