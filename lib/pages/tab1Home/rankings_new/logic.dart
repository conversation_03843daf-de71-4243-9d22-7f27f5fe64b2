import 'package:flutter/material.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/state.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'dart:developer' as cc;

class RankingsLogic extends GetxController with GetTickerProviderStateMixin {
  final state = RankingsState();

  // 将控制器声明为公开属性
  late PageController pageController;
  late final TabController playerTabController;

  @override
  void onInit() {
    super.onInit();
    // if (LocationUtils.instance.cityName != null) {
    state.cityName.value = '全国排行';
    state.cityId = 0;
    // }
    // 初始化控制器
    pageController = PageController(initialPage: state.isPlayer.value ? 0 : 1);
    playerTabController = TabController(
      length: state.playerTabs.length,
      vsync: this,
      initialIndex: 0,
    );

    // 监听页面切换
    pageController.addListener(() {
      if (pageController.page?.round() == 0) {
        state.isPlayer.value = true;
      } else {
        state.isPlayer.value = false;
      }
    });

    // 监听标签切换
    playerTabController.addListener(() {
      if (playerTabController.indexIsChanging) {
        return;
      }
      state.playerTabIndex.value = playerTabController.index;
      BusUtils.instance.fire(EventAction(key: EventBusKey.playerTabItemSwitch));
    });

    state.subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getCity == action.key) {
        // if (state.cityName.value == '请选择') {
        //   state.cityName.value = LocationUtils.instance.cityName ?? '';
        //   state.cityId = LocationUtils.instance.cityId;
        //   //请求数据
        // }
      }
    });
  }

  @override
  void onClose() {
    pageController.dispose();
    playerTabController.dispose();
    state.subscription?.cancel();
    super.onClose();
  }

  void changePlayerType(bool isPlayer) {
    cc.log("changePlayerType$isPlayer${state.isPlayer.value}");
    if (isPlayer != state.isPlayer.value) {
      if (pageController.hasClients) {
        pageController.jumpToPage(isPlayer ? 0 : 1);
      }
      state.isPlayer.value = isPlayer;
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.rankingsRankTypeSwitch));
    }
  }

  void changeRankType(bool isAll) {
    cc.log("changeRankType$isAll");
    // if (isAll != state.isAll.value) {
    // changePlayerType(isAll);
    //   state.isAll.value = isAll;
    //   // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   //   pageController.jumpToPage(state.isPlayer.value ? 0 : 1); // 跳转到第二页
    //   // });

    //   if (state.cityId != null) {
    //     //通知刷新数据
    //     BusUtils.instance
    //         .fire(EventAction(key: EventBusKey.rankingsCitySwitch));
    //   }
    // }
  }

  void showCityPicker() async {
    // Result? result = await CityPickers.showCityPicker(
    //   context: Get.context!,
    //   // theme: ThemeData.dark(),
    //   showType: ShowType.pc,
    //   locationCode: state.cityId ?? '110000',
    //   itemExtent: 34.w,
    //   citiesData: citiesData,
    //   provincesData: provincesData,
    //   itemBuilder: (item, list, index) {
    //     return Text(
    //       item,
    //       style: TextStyles.regular.copyWith(color: Colors.white),
    //     ).paddingOnly(top: 10.w);
    //   },
    // );
    // if (result != null && result.cityId != state.cityId) {
    //   state.cityName.value = result.cityName ?? '';
    //   state.cityId = result.cityId;
    //   BusUtils.instance.fire(EventAction(key: EventBusKey.rankingsCitySwitch));
    //   // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   //   pageController.jumpToPage(state.isPlayer.value ? 0 : 1); // 跳转到第二页
    //   // });
    // }
  }
}
