import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';

class FeaturedInfoLogic extends GetxController with WidgetsBindingObserver {
  var featuredListModel = FeaturedListModel().obs;
  @override
  void onInit() {
    super.onInit();
    featuredListModel.value =
        Get.arguments["featuredListModel"] as FeaturedListModel;
    featuredListModel.refresh();
    log("featuredListModel33=${jsonEncode(featuredListModel.value)}");
  }

  @override
  void onReady() {
    super.onReady();
  }
}
