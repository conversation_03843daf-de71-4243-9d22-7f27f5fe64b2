import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab1Home/featured_info/featured_info_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

///首页 二级页面->热点资讯 详情
class FeaturedInfoPage extends StatelessWidget {
  FeaturedInfoPage({super.key});

  final logic = Get.put(FeaturedInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.tab_home_title2),
      ),
      body: Obx(() {
        return SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.only(bottom: 50.w, top: 13.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 15.w, right: 15.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        logic.featuredListModel.value.title ?? "",
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(
                        height: 20.w,
                      ),
                      Text(
                        logic.featuredListModel.value.createdTime ?? "",
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp,
                            color: Colours.color5C5C6E,
                            height: 1.3),
                      ),
                      // SizedBox(
                      //   height: 20.w,
                      // ),
                      // MyImage(
                      //   logic.featuredListModel.value.coverUrl ?? "",
                      //   width: double.infinity,
                      //   // height: 200.w,
                      //   fit: BoxFit.fill,
                      //   radius: 8.r,
                      //   isAssetImage: false,
                      // ),
                      SizedBox(
                        height: 20.w,
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 7.w, right: 7.w),
                  child: Html(
                    data: logic.featuredListModel.value.content ?? "",
                    style: {
                      // 设置全局文本颜色
                      "body": Style(
                        color: Colors.white, // 修改为黑色
                        fontSize: FontSize(16), // 设置基础字体大小
                        lineHeight: LineHeight(1.5), // 设置行高
                      ),

                      // 设置段落颜色
                      "p": Style(
                        color: Colors.white, // 将段落文本设为蓝色
                        fontSize: FontSize(14.sp),
                        margin: Margins(bottom: Margin(12)), // 段落间距
                      ),

                      // "table": Style(
                      //   backgroundColor:
                      //       const Color.fromARGB(0x50, 0xee, 0xee, 0xee),
                      // ),
                      // "th": Style(
                      //   padding: HtmlPaddings.all(6),
                      //   backgroundColor: Colors.grey,
                      // ),
                      // "td": Style(
                      //   padding: HtmlPaddings.all(6),
                      //   border:
                      //       const Border(bottom: BorderSide(color: Colors.grey)),
                      // ),
                      // 'h5': Style(
                      //   maxLines: 2,
                      //   textOverflow: TextOverflow.ellipsis,
                      // ),
                      // 'flutter': Style(
                      //   display: Display.block,
                      //   fontSize: FontSize(5, Unit.em),
                      // ),
                      // ".second-table": Style(
                      //   backgroundColor: Colors.transparent,
                      // ),
                      // ".second-table tr td:first-child": Style(
                      //   fontWeight: FontWeight.bold,
                      //   textAlign: TextAlign.end,
                      // ),
                    },
                    extensions: [
                      TagExtension.inline(
                        tagsToExtend: {"br"},
                        child: const TextSpan(text: "\n"),
                      ),
                      TagWrapExtension(
                          tagsToWrap: {"table"},
                          builder: (child) {
                            return SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: child,
                            );
                          }),
                      TagExtension.inline(
                        tagsToExtend: {"bird"},
                        child: const TextSpan(text: "🐦"),
                      ),
                      TagExtension(
                        tagsToExtend: {"flutter"},
                        builder: (context) => CssBoxWidget(
                          style: context.styledElement!.style,
                          child: FlutterLogo(
                            style: context.attributes['horizontal'] != null
                                ? FlutterLogoStyle.horizontal
                                : FlutterLogoStyle.markOnly,
                            textColor: context.styledElement!.style.color!,
                            size: context.styledElement!.style.fontSize!.value,
                          ),
                        ),
                      ),
                      ImageExtension(
                        handleAssetImages: false,
                        handleDataImages: false,
                        networkDomains: {"flutter.dev"},
                        child: const FlutterLogo(size: 36),
                      ),
                      ImageExtension(
                        handleAssetImages: false,
                        handleDataImages: false,
                        networkDomains: {"mydomain.com"},
                        networkHeaders: {"Custom-Header": "some-value"},
                      ),
                    ],
                    onLinkTap: (url, _, __) {
                      debugPrint("Opening $url...");
                    },
                    onCssParseError: (css, messages) {
                      debugPrint("css that errored: $css");
                      debugPrint("error messages:");
                      for (var element in messages) {
                        debugPrint(element.toString());
                      }
                      return '';
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
