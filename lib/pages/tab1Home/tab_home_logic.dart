import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:carousel_slider/carousel_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/banner_model.dart';
import 'package:shoot_z/network/model/team_home_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item1/tab_home_item_logic1.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';

class TabHomeLogic extends GetxController
    with GetSingleTickerProviderStateMixin, RouteAware {
  final CarouselSliderController carouselController =
      CarouselSliderController();
  final ScrollController scrollController = ScrollController();
  var imgBannerList = <BannerModel>[].obs; //轮播图
  var currentIndex = 0.obs; //轮播图下标
  TabController? tabController2;
  var tabNameList = [
    S.current.Selected,
    S.current.competitions,
    S.current.Squadron_Battle,
    S.current.team,
    S.current.teaching,
  ].obs;

  var dataFag = {
    "isFrist": true,
  }.obs;
  var tabbarIndex = 0.obs;
  var teamHomeModel = TeamHomeModel().obs;
  final logic1 = Get.put(TabHomeItemLogic1());
  StreamSubscription? subscription;

  @override
  void onInit() {
    super.onInit();
    tabController2 = TabController(length: 5, vsync: this);
    log('postApmTracking1=onInit');
    tabController2?.addListener(
      () {
        tabbarIndex.value = tabController2?.index ?? 0;
        if (tabController2?.indexIsChanging ?? false) {
          switch (tabbarIndex.value) {
            case 0:
              //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
              UserManager.instance.postApmTracking(1,
                  remark: "点击tab切换",
                  nowPage: Routes.homePage,
                  subPage: "tabhome1",
                  content: "切换tab精选视频");
              break;
            case 1:
              //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
              UserManager.instance.postApmTracking(1,
                  remark: "点击tab切换",
                  nowPage: Routes.homePage,
                  subPage: "tabhome2",
                  content: "切换tab赛事");
              break;
            case 2:
              //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
              UserManager.instance.postApmTracking(1,
                  remark: "点击tab切换",
                  nowPage: Routes.homePage,
                  subPage: "tabhome3",
                  content: "切换tab约战");
              break;
            case 3:
              //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
              UserManager.instance.postApmTracking(1,
                  remark: "点击tab切换",
                  nowPage: Routes.homePage,
                  subPage: "tabhome4",
                  content: "切换tab球队");
              break;
            case 4:
              //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
              UserManager.instance.postApmTracking(1,
                  remark: "点击tab切换",
                  nowPage: Routes.homePage,
                  subPage: "tabhome5",
                  content: "切换tab教学");
              break;
          }
        }
      },
    );
    if (UserManager.instance.isLogin) {
      UserManager.instance.getMessageHasUnread();
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (UserManager.instance.isLogin) {
      UserManager.instance.getMessageHasUnread();
    }
  }

  @override
  void onReady() {
    super.onReady();
    getBanner();
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.changeUnread) {
        UserManager.instance.getMessageHasUnread();
      }
      //else if (action.key == EventBusKey.teamChangeTab) {
      //   tabController?.animateTo(4);
      // }
    });
  }

  void getBanner() async {
    final res = await Api().get(ApiUrl.configResource, queryParameters: {
      'type': 'banner',
    });
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      dataFag.refresh();
    }
    if (res.isSuccessful()) {
      if (res.data != null) {
        log("configResource2=${jsonEncode(res.data)}");
        List list = res.data ?? [];
        List<BannerModel> modelList =
            list.map((e) => BannerModel.fromJson(e)).toList();
        imgBannerList.assignAll(modelList);
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }
}
