import 'package:city_pickers/city_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab1Home/rankings/city.dart';
import 'package:shoot_z/pages/tab1Home/rankings/state.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:ui_packages/ui_packages.dart';

class RankingsLogic extends GetxController with GetTickerProviderStateMixin {
  final state = RankingsState();

  // 将控制器声明为公开属性
  late PageController pageController;
  late final TabController playerTabController;
  late final TabController teamTabController;

  @override
  void onInit() {
    super.onInit();
    if (LocationUtils.instance.cityName != null) {
      state.cityName.value = LocationUtils.instance.cityName ?? '';
      state.cityId = LocationUtils.instance.cityId;
    }
    // 初始化控制器
    pageController = PageController();
    playerTabController = TabController(
      length: state.playerTabs.length,
      vsync: this,
      initialIndex: 0,
    );
    teamTabController = TabController(
      length: state.teamTabs.length,
      vsync: this,
      initialIndex: 0,
    );

    // 监听页面切换
    pageController.addListener(() {
      if (pageController.page?.round() == 0) {
        state.isPlayer.value = true;
      } else {
        state.isPlayer.value = false;
      }
    });

    // 监听标签切换
    playerTabController.addListener(() {
      if (playerTabController.indexIsChanging) {
        return;
      }
      state.playerTabIndex.value = playerTabController.index;
      BusUtils.instance.fire(EventAction(key: EventBusKey.playerTabItemSwitch));
    });

    teamTabController.addListener(() {
      if (teamTabController.indexIsChanging) {
        return;
      }
      BusUtils.instance.fire(EventAction(key: EventBusKey.teamTabItemSwitch));
    });

    state.subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getCity == action.key) {
        if (state.cityName.value == '请选择') {
          state.cityName.value = LocationUtils.instance.cityName ?? '';
          state.cityId = LocationUtils.instance.cityId;
          //请求数据
        }
      }
    });
  }

  @override
  void onClose() {
    pageController.dispose();
    playerTabController.dispose();
    teamTabController.dispose();
    state.subscription?.cancel();
    super.onClose();
  }

  void changePlayerType(bool isPlayer) {
    if (isPlayer != state.isPlayer.value) {
      state.isPlayer.value = isPlayer;
      pageController.jumpToPage(isPlayer ? 0 : 1);
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.rankingsRankTypeSwitch));
    }
  }

  void changeRankType(bool isAll) {
    if (isAll != state.isAll.value) {
      state.isAll.value = isAll;
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      //   pageController.jumpToPage(state.isPlayer.value ? 0 : 1); // 跳转到第二页
      // });

      if (state.cityId != null) {
        //通知刷新数据
        BusUtils.instance
            .fire(EventAction(key: EventBusKey.rankingsCitySwitch));
      }
    }
  }

  void showCityPicker() async {
    Result? result = await CityPickers.showCityPicker(
      context: Get.context!,
      theme: ThemeData.light(),
      showType: ShowType.pc,
      locationCode: state.cityId ?? '110000',
      itemExtent: 34.w,
      citiesData: citiesData,
      provincesData: provincesData,
      itemBuilder: (item, list, index) {
        return Text(
          item,
          style: TextStyles.regular.copyWith(color: Colors.black),
        ).paddingOnly(top: 10.w);
      },
    );
    if (result != null && result.cityId != state.cityId) {
      state.cityName.value = result.cityName ?? '';
      state.cityId = result.cityId;
      BusUtils.instance.fire(EventAction(key: EventBusKey.rankingsCitySwitch));
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      //   pageController.jumpToPage(state.isPlayer.value ? 0 : 1); // 跳转到第二页
      // });
    }
  }
}
