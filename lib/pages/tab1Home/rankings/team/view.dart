import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/rankings/team/logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings/team/state.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class TeamView extends StatefulWidget {
  final int index;
  const TeamView({super.key, required this.index});

  @override
  State<TeamView> createState() => _TeamViewState();
}

class _TeamViewState extends State<TeamView> {
  // 声明 logic 变量但不立即初始化
  late final TeamLogic logic;
  late final TeamState state;
  @override
  void initState() {
    super.initState();
    // 在 initState 中初始化 logic
    logic =
        Get.put(TeamLogic(index: widget.index), tag: widget.index.toString());
    state = Get.find<TeamLogic>(tag: widget.index.toString()).state;
  }

  @override
  Widget build(BuildContext context) {
    String title = '';
    switch (widget.index) {
      case 0:
        title = '胜场';
        break;
      case 1:
        title = '胜率';
        break;
    }
    return Column(
      children: [
        SizedBox(
          height: 20.w,
        ),
        Row(
          children: [
            Text('排名', style: TextStyles.bold.copyWith(fontSize: 14.sp)),
            SizedBox(width: 41.w),
            Text('球队', style: TextStyles.bold.copyWith(fontSize: 14.sp)),
            SizedBox(width: 73.w),
            Text('排名变化', style: TextStyles.bold.copyWith(fontSize: 14.sp)),
            const Spacer(),
            Text(title, style: TextStyles.bold.copyWith(fontSize: 14.sp)),
            SizedBox(width: 12.w),
          ],
        ),
        SizedBox(height: 10.w),
        Expanded(child: Obx(() {
          return state.requestError.value
              ? const SizedBox.shrink()
              : state.rankingModel.value.billboard?.isEmpty ?? true
                  ? buildLoad()
                  : ListView.builder(
                      padding: EdgeInsets.only(
                          bottom: (MediaQuery.of(context).padding.bottom > 0
                                  ? MediaQuery.of(context).padding.bottom
                                  : 20.w) +
                              48.w),
                      itemCount:
                          state.rankingModel.value.billboard?.length ?? 0,
                      itemBuilder: (context, index) {
                        String changedNo = state.rankingModel.value
                                .billboard?[index]?.changedNo ??
                            '0';
                        return Row(children: [
                          SizedBox(
                            width: 29.w,
                            child: index > 2
                                ? Text(
                                    '${state.rankingModel.value.billboard?[index]?.index ?? 0}',
                                    style: TextStyles.bold
                                        .copyWith(fontSize: 14.sp),
                                    textAlign: TextAlign.center,
                                  )
                                : AssetGenImage(
                                        'assets/images/ic_ranking_${index + 1}.png')
                                    .image(width: 29.w, fit: BoxFit.fill),
                          ),
                          SizedBox(
                            width: 15.w,
                          ),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12.w),
                            child: CachedNetworkImage(
                              imageUrl: state.rankingModel.value
                                      .billboard?[index]?.logo ??
                                  '',
                              width: 24.w,
                              height: 24.w,
                              fit: BoxFit.fill,
                            ),
                          ).marginSymmetric(vertical: index > 2 ? 6.w : 11.w),
                          SizedBox(width: 8.w),
                          SizedBox(
                            width: 84.w,
                            child: Text(
                              state.rankingModel.value.billboard?[index]
                                      ?.name ??
                                  '',
                              style:
                                  TextStyles.regular.copyWith(fontSize: 12.sp),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 11.w),
                          SizedBox(
                            width: 56.w,
                            child: Text(
                              changedNo == '0' ? '---' : changedNo,
                              style: TextStyles.bold.copyWith(fontSize: 14.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const Spacer(),
                          SizedBox(
                            width: 55.w,
                            child: Text(
                                (state.rankingModel.value.billboard?[index]
                                            ?.rankData ??
                                        '') +
                                    (widget.index == 0 ? '' : '%'),
                                style:
                                    TextStyles.bold.copyWith(fontSize: 18.sp),
                                textAlign: TextAlign.center),
                          ),
                        ]);
                      });
        })),
      ],
    ).marginOnly(left: 13.w, right: 12.w);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}
