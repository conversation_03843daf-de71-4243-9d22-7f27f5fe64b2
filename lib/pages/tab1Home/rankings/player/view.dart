import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/rankings/player/logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings/player/state.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class PlayerView extends StatefulWidget {
  final int index;
  const PlayerView({super.key, required this.index});

  @override
  State<PlayerView> createState() => _PlayerViewState();
}

class _PlayerViewState extends State<PlayerView> {
  // 声明 logic 变量但不立即初始化
  late final PlayerLogic logic;
  late final PlayerState state;
  @override
  void initState() {
    super.initState();
    // 在 initState 中初始化 logic
    logic =
        Get.put(PlayerLogic(index: widget.index), tag: widget.index.toString());
    state = Get.find<PlayerLogic>(tag: widget.index.toString()).state;
  }

  @override
  Widget build(BuildContext context) {
    String title = '';
    switch (widget.index) {
      case 0:
        title = '总得分';
        break;
      case 1:
        title = '总助攻';
        break;
      case 2:
        title = '总篮板';
        break;
      case 3:
        title = '总进球';
        break;
      case 4:
        title = '单场得分';
        break;
      case 5:
        title = '场均得分';
        break;
    }
    return Column(
      children: [
        SizedBox(
          height: 20.w,
        ),
        Row(
          children: [
            Text('排名', style: TextStyles.bold.copyWith(fontSize: 14.sp)),
            SizedBox(width: 64.w),
            Text('球员', style: TextStyles.bold.copyWith(fontSize: 14.sp)),
            const Spacer(),
            Text(title, style: TextStyles.bold.copyWith(fontSize: 14.sp)),
          ],
        ),
        SizedBox(height: 10.w),
        Expanded(
            child: Obx(
          () => state.requestError.value
              ? const SizedBox.shrink()
              : state.rankingModel.value.billboard?.isEmpty ?? true
                  ? buildLoad()
                  : ListView.builder(
                      padding: EdgeInsets.only(
                          bottom: (MediaQuery.of(context).padding.bottom > 0
                                  ? MediaQuery.of(context).padding.bottom
                                  : 20.w) +
                              48.w),
                      itemCount:
                          state.rankingModel.value.billboard?.length ?? 0,
                      itemBuilder: (context, index) {
                        return Row(children: [
                          SizedBox(
                            width: 29.w,
                            child: index > 2
                                ? Text(
                                    '${state.rankingModel.value.billboard?[index]?.index ?? 0}',
                                    style: TextStyles.bold
                                        .copyWith(fontSize: 14.sp),
                                    textAlign: TextAlign.center,
                                  )
                                : AssetGenImage(
                                        'assets/images/ic_ranking_${index + 1}.png')
                                    .image(width: 29.w, fit: BoxFit.fill),
                          ),
                          SizedBox(
                            width: 41.w,
                          ),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12.w),
                            child: CachedNetworkImage(
                              imageUrl: state.rankingModel.value
                                      .billboard?[index]?.logo ??
                                  '',
                              width: 24.w,
                              height: 24.w,
                              fit: BoxFit.fill,
                            ),
                          ).marginSymmetric(vertical: index > 2 ? 6.w : 11.w),
                          SizedBox(width: 8.w),
                          SizedBox(
                            width: 120.w,
                            child: Text(
                              state.rankingModel.value.billboard?[index]
                                      ?.name ??
                                  '',
                              style:
                                  TextStyles.regular.copyWith(fontSize: 12.sp),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const Spacer(),
                          SizedBox(
                            width: 44.w,
                            child: Text(
                                state.rankingModel.value.billboard?[index]
                                        ?.rankData ??
                                    '',
                                style:
                                    TextStyles.bold.copyWith(fontSize: 18.sp),
                                textAlign: TextAlign.center),
                          ),
                        ]);
                      }),
        )),
      ],
    ).marginSymmetric(horizontal: 32.w);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}
