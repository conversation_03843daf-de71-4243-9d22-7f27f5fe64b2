import 'package:dio/dio.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/rankings/logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings/player/ranking_model.dart';
import 'package:shoot_z/pages/tab1Home/rankings/player/state.dart';
import 'package:shoot_z/utils/event_bus.dart';

class PlayerLogic extends GetxController {
  final int index;
  PlayerLogic({required this.index});
  final rankingsLogic = Get.find<RankingsLogic>();
  final state = PlayerState();
  var isInit = false;
  @override
  void onInit() {
    super.onInit();
    getData();
    state.subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.rankingsCitySwitch) {
        state.rankingModel.value = RankingModel();
        state.rankingModel.refresh();
        if (rankingsLogic.state.isPlayer.value &&
            rankingsLogic.playerTabController.index == index) {
          getData();
        }
      } else if (action.key == EventBusKey.rankingsRankTypeSwitch) {
        if (rankingsLogic.state.isPlayer.value &&
            rankingsLogic.playerTabController.index == index &&
            (state.rankingModel.value.billboard?.isEmpty ?? true)) {
          getData();
        }
      } else if (action.key == EventBusKey.playerTabItemSwitch) {
        if (rankingsLogic.state.isPlayer.value &&
            rankingsLogic.playerTabController.index == index &&
            (state.rankingModel.value.billboard?.isEmpty ?? true)) {
          if (isInit) {
            getData();
          }
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    state.subscription?.cancel();
    super.onClose();
  }

  var cancelToken = CancelToken();
  Future<void> getData() async {
    state.requestError.value = false;
    final url = ApiUrl.rankingsPlayers;
    cancelToken.cancel();
    cancelToken = CancelToken();
    final response =
        await Api.instance.get(url, cancelToken: cancelToken, queryParameters: {
      'cityId':
          rankingsLogic.state.isAll.value ? '0' : rankingsLogic.state.cityId,
      'rankType': index + 1,
    });
    isInit = true;
    if (response.isSuccessful()) {
      state.requestError.value = false;
      state.rankingModel.value = RankingModel.fromJson(response.data);
      state.rankingModel.refresh();
      rankingsLogic.state.month.value =
          (state.rankingModel.value.month ?? 1) - 1;
      if (state.rankingModel.value.billboard?.isEmpty ?? true) {
        rankingsLogic.state.noData.value = true;
      } else {
        rankingsLogic.state.noData.value = false;
      }
      if (UserManager.instance.isLogin) {
        rankingsLogic.state.numbers[index] = state.rankingModel.value.myIndex!;
        rankingsLogic.state.myRankData[index] =
            state.rankingModel.value.myRankData ?? '0';
      }
    } else {
      state.rankingModel.value = RankingModel();
      state.rankingModel.refresh();
      state.requestError.value = true;
    }
  }
}
