import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/home_team_list_model.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item4/tab_home_item_logic4.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///首页 一级页面->球队
class TabHomeItemPage4 extends StatefulWidget {
  const TabHomeItemPage4({super.key});

  @override
  State<TabHomeItemPage4> createState() => _HighlightsVideoPageState();
}

class _HighlightsVideoPageState extends State<TabHomeItemPage4> {
  final logic = Get.put(TabHomeItemLogic4());
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面不再可见时关闭键盘
    if (!ModalRoute.of(context)!.isCurrent) {
      FocusScope.of(context).unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          _searchWidget(),
          Expanded(
            child: SmartRefresher(
              controller: logic.refreshController,
              footer: buildFooter(),
              header: buildClassicHeader(),
              enablePullDown: true,
              enablePullUp: logic.dataList.isNotEmpty,
              onRefresh: () {
                logic.getdataList(
                    isLoad: false, controller: logic.refreshController);
              },
              onLoading: () {
                logic.getdataList(controller: logic.refreshController);
              },
              physics: const AlwaysScrollableScrollPhysics(),
              //  physics: const NeverScrollableScrollPhysics(),
              child: (logic.dataFag["isFrist"] as bool)
                  ? buildLoad()
                  : logic.dataList.isEmpty
                      ? SizedBox(
                          height: 300.w,
                          child: myNoDataView(
                            context,
                            msg: S.current.No_data_available,
                            imagewidget: WxAssets.images.icGameNo
                                .image(width: 150.w, height: 150.w),
                          ))
                      : ListView.builder(
                          scrollDirection: Axis.vertical,
                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                          shrinkWrap: true,
                          padding: EdgeInsets.only(bottom: 35.w, top: 15.w),
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: logic.dataList.length,
                          itemBuilder: (context, position) {
                            return _listItemWidget(logic.dataList[position]);
                          }),
            ),
          ),
        ],
      );
    });
  }

  Widget _searchWidget() {
    return Container(
      width: double.infinity,
      height: 42.w,
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(23.r)),
      child: Row(
        children: [
          WxAssets.images.icSearch.image(width: 22.w, height: 22.w),
          SizedBox(
            width: 10.w,
          ),
          Expanded(
              child: TextField(
            controller: logic.txtController1,
            style: TextStyles.regular,
            inputFormatters: [
              FilteringTextInputFormatter.deny(RegExp(r'[" "]')), // 不允许空格
              LengthLimitingTextInputFormatter(20), // 限制输入长度为7
            ],
            decoration: InputDecoration(
              hintText: S.current.add_team_tips3,
              hintStyle:
                  TextStyles.regular.copyWith(color: Colours.color5C5C6E),
              contentPadding: const EdgeInsets.only(top: 0, bottom: 3),
              //让文字垂直居中,
              border: InputBorder.none,
            ),
            textInputAction: TextInputAction.search,
            onSubmitted: (value) {
              // 点击键盘"确定"按钮时调用
              logic.getdataList(
                  //      teamName: value,
                  controller: logic.refreshController,
                  isLoad: false);
            },
            keyboardType: TextInputType.text,
          )),
        ],
      ),
    );
  }

  /// 构建列表项
  Widget _listItemWidget(HomeTeamListModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        AppPage.to(Routes.teamInfoPage, arguments: {
          'teamId': item.teamId.toString(),
        });
        // .then((v) {
        //   logic.getdataList(controller: logic.refreshController, isLoad: false);
        // });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.w),
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.r)),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MyImage(
                  item.teamLogo ?? "",
                  width: 40.w,
                  height: 40.w,
                  radius: 20.r,
                  placeholderImage: "my_team_head4.png",
                  errorImage: "my_team_head4.png",
                ),
                SizedBox(
                  width: 15.w,
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              // 使用 Flexible 替代 Expanded
                              // "item.teamNameitem.teamNameitem.teamNameitem.teamNameitem.teamNameitem.teamNameitem.teamNameitem.teamName",
                              item.teamName ?? "",
                              maxLines: 1,
                              style: TextStyles.regular.copyWith(
                                  fontWeight: FontWeight.w600,
                                  overflow: TextOverflow.ellipsis),
                            ),
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          MyImage(
                            item.levelIcon ?? "",
                            width: 23.w,
                            height: 23.w,
                            radius: 20.r,
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        "${item.teamNum ?? ""}\t${S.current.players}\t\t\t${item.matchNum ?? ""}\t${S.current.session}",
                        style: TextStyles.regular
                            .copyWith(color: Colours.color5C5C6E),
                      ),
                    ],
                  ),
                ),
                WxAssets.images.fire.image(width: 14.w, height: 14.w),
                SizedBox(
                  width: 2.w,
                ),
                Padding(
                  padding: EdgeInsets.only(top: 1.w),
                  child: Text(
                    "${item.hotScore ?? ""}",
                    style: TextStyles.regular.copyWith(fontSize: 12.sp),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 15.w,
            ),
            MyImage(
              item.teamPhoto ?? "",
              width: double.infinity,
              height: 177.w,
              radius: 8.r,
              errorImage: "team_info_nophoto.png",
              placeholderImage: "error_image_width.png",
            ),
          ],
        ),
      ),
    );
  }
}
