import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item1/tab_home_item_logic1.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///首页 一级页面->精选
class TabHomeItemPage1 extends StatelessWidget {
  TabHomeItemPage1({super.key});
  final logic = Get.put(TabHomeItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController1,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: true,
        onRefresh: () {
          logic.getFeaturedList(1, isLoad: false);
          logic.getFeaturedList(2, isLoad: false);
        },
        onLoading: () {
          // WxLoading.showToast("onLoading");
          logic.getFeaturedList(1, isLoad: true);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //   child:
        child: (logic.dataFag["isFrist1"] as bool)
            ? buildLoad(isShowGif: false)
            // : (logic.featuredList1.isEmpty && logic.featuredList2.isEmpty)
            //     ? SizedBox(
            //         height: 200.w,
            //         child: myNoDataView(context,
            //             msg: S.current.No_data_available,
            //             imagewidget: WxAssets.images.teamInfoNodata
            //                 .image(width: 107.w, height: 72.w),
            //             height: 2.w),
            //       )
            : ListView.separated(
                scrollDirection: Axis.vertical,
                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                shrinkWrap: true,
                itemCount: (logic.featuredList1.length) >= 1
                    ? logic.featuredList1.length + 1
                    : 2,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.only(bottom: 35.w),
                itemBuilder: (context, position) {
                  return position == 0
                      ? _videosWidget(context)
                      : position == 1 && ((logic.featuredList1.length) <= 0)
                          ? SizedBox(
                              height: 200.w,
                              child: myNoDataView(context,
                                  msg: S.current.No_data_available,
                                  imagewidget: WxAssets.images.teamInfoNodata
                                      .image(width: 107.w, height: 72.w),
                                  height: 2.w),
                            )
                          : _listItemWidget(
                              logic.featuredList1[position - 1], position - 1);
                },
                separatorBuilder: (BuildContext context, int index) {
                  return SizedBox(
                    height: 15.w,
                  );
                },
              ),
      );
    });
  }

  Widget _videosWidget(context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildRowTitleWidget(
          S.current.tab_home_title1,
          height: 32.w, margin: EdgeInsets.only(top: 6.w),
          //   rightOnTap: () {
          // AppPage.to(Routes.playersPage, arguments: {
          //   'teamId': logic.teamId.value,
          // }); }
        ),
        SizedBox(
          height: 3.w,
        ),
        (logic.dataFag["isFrist2"] as bool)
            ? buildLoad(isShowGif: false)
            : (logic.featuredList2.isEmpty)
                ? SizedBox(
                    height: 200.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.No_data_available,
                      height: 0,
                      imagewidget: WxAssets.images.teamInfoNodata2
                          .image(width: 180.w, height: 120.w),
                    ),
                  )
                : Container(
                    height: 80.w,
                    margin: EdgeInsets.symmetric(horizontal: 15.w),
                    child: ListView.separated(
                      shrinkWrap: true,
                      controller: logic.scrollController,
                      scrollDirection: Axis.horizontal,
                      itemCount: min(logic.featuredList2.length, 25),
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return Obx(() {
                          return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () async {
//                               // 获取渠道信息
//                               final String channel = await Utils.getChannel();
//                               ccc.log("_initChannelConfig=$channel");
//                               WxLoading.showToast("channel=$channel");
// //   渠道英文
// // 抖音 dy
// // 视频号sph
// // 公众号gzh
// // 小红书xhs
// // B站 blbl
// // 麓谷篮球场  lqc01
                              // if ((logic.featuredList2[index].videoUrl ?? "") !=
                              //     "") {
                              //   AppPage.to(Routes.shotVideos2Page, arguments: {
                              //     "featuredListModel":
                              //         logic.featuredList2[index],
                              //     "type": "0",
                              //   });
                              // } else {
                              //   WxLoading.showToast(
                              //       S.current.No_data_available);
                              // }

                              if ((logic.featuredList2[index].videoUrl ?? "") !=
                                  "") {
                                AppPage.to(Routes.videoPlayerPage, arguments: {
                                  "featuredListModel":
                                      logic.featuredList2[index],
                                  "type": "0",
                                });
                              } else {
                                WxLoading.showToast(
                                    S.current.No_data_available);
                              }
                            },
                            child: Container(
                              margin: EdgeInsets.only(top: 5.w, bottom: 5.w),
                              width: 142.w,
                              height: 80.w,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.r)),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  MyImage(
                                    logic.featuredList2[index].coverUrl ?? "",
                                    width: 142.w,
                                    height: 80.w,
                                    radius: 8.r,
                                    errorImage: "error_image_width.png",
                                    placeholderImage: "error_image_width.png",
                                  ),
                                  WxAssets.images.selfieShotPlay
                                      .image(width: 28.w, height: 28.w),
                                  Positioned(
                                    bottom: 0.w,
                                    child: Container(
                                      width: 142.w,
                                      height: 22.w,
                                      alignment: Alignment.center,
                                      decoration: const BoxDecoration(
                                          color: Colours.color800F0F16),
                                      child: Text(
                                        logic.featuredList2[index].title ?? "",
                                        textAlign: TextAlign.center,
                                        maxLines: 1,
                                        style: TextStyles.regular.copyWith(
                                          fontSize: 12.sp,
                                          color: Colours.white,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          );
                        });
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return SizedBox(
                          width: 15.w,
                        );
                      },
                    ),
                  ),
        // buildRowTitleWidget(S.current.tab_home_title2,
        //     margin: EdgeInsets.only(top: 6.w)
        //     //   rightOnTap: () {
        //     // AppPage.to(Routes.playersPage, arguments: {
        //     //   'teamId': logic.teamId.value,
        //     // }); }
        //     ),
        SizedBox(
          height: 20.w,
        ),
        Container(
          margin: EdgeInsets.only(left: 15.w),
          child: TextWithIcon(title: S.current.tab_home_title2),
        )
      ],
    );
  }

  _listItemWidget(FeaturedListModel featuredListModel, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if ((featuredListModel.matchId ?? 0) != 0) {
          AppPage.to(Routes.gameDetailsPage,
              arguments: (featuredListModel.matchId ?? 0).toString());
          //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
          UserManager.instance.postApmTracking(0,
              toPage: Routes.gameDetailsPage,
              nowPage: Routes.messageListPage,
              remark: "首页tab1的精选中的热点资讯点击跳转",
              content: "进入赛事概况");
        } else {
          AppPage.to(Routes.featuredInfoPage, arguments: {
            "featuredListModel": featuredListModel,
          });
        }
      },
      child: Container(
        height: 110.w,
        width: double.infinity,
        padding:
            EdgeInsets.only(top: 15.w, left: 15.w, right: 15.w, bottom: 15.w),
        margin: EdgeInsets.only(
          left: 15.w,
          right: 15.w,
        ),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(8.r)),
            color: Colours.color22222D),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyImage(
              featuredListModel.coverUrl ?? "",
              width: 138.w,
              height: 80.w,
              radius: 8.r,
              errorImage: "error_image_width.png",
              placeholderImage: "error_image_width.png",
            ),
            SizedBox(
              width: 12.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 5.w,
                  ),
                  Text(
                    logic.featuredList1[index].title ?? "",
                    style: TextStyles.regular
                        .copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 8.w,
                  ),
                  SizedBox(
                    height: 32.w,
                    child: Text(
                      featuredListModel.subTitle ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          overflow: TextOverflow.ellipsis,
                          height: 1.3,
                          color: Colours.white),
                      maxLines: 2,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    featuredListModel.createdTime ?? "",
                    style: TextStyles.regular
                        .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                    maxLines: 2,
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
