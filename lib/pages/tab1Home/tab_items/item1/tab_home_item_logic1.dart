import 'package:flutter/widgets.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';

class TabHomeItemLogic1 extends GetxController {
  RefreshController refreshController1 =
      RefreshController(initialRefresh: false);
  // 控制滚动位置的控制器
  final ScrollController scrollController = ScrollController();
  var dataFag = {
    "isFrist1": true,
    "page1": 1,
    "isFrist2": true,
    "page2": 1,
  }.obs;
  var teamId = "172".obs;
  var featuredList1 = <FeaturedListModel>[].obs;
  var featuredList2 = <FeaturedListModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    getFeaturedList(1);
    getFeaturedList(2);
  }

  //查询热点资讯视频
  getFeaturedList(
    int type, {
    isLoad = false,
  }) async {
    // if (!(dataFag["isFrist1"] as bool) && !isLoad) {
    //   return;
    // }
    // if (!(dataFag["isFrist1"] as bool) && !isLoad) {
    //   return;
    // }
    var page = type == 1 ? dataFag["page1"] as int : dataFag["page2"] as int;
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 10,
      'page': page,
      'type': type, //1-资讯 2-视频
    };
    //WxLoading.show();
    var res = await Api().get(ApiUrl.getFeaturedList, queryParameters: param);
    //WxLoading.dismiss();

    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<FeaturedListModel> modelList =
          list.map((e) => FeaturedListModel.fromJson(e)).toList();
      if (type == 1) {
        dataFag["page1"] = page + 1;
        if (isLoad) {
          featuredList1.addAll(modelList);
          featuredList1.refresh();
          if (modelList.length < 10) {
            refreshController1.loadNoData();
          } else {
            refreshController1.loadComplete();
          }
        } else {
          refreshController1.resetNoData();
          featuredList1.assignAll(modelList);
          refreshController1.refreshCompleted();
        }
        if (dataFag["isFrist1"] as bool) {
          dataFag["isFrist1"] = false;
          dataFag.refresh();
        }
      } else {
        dataFag["page2"] = page + 1;
        if (isLoad) {
          featuredList2.addAll(modelList);
          featuredList2.refresh();
        } else {
          featuredList2.assignAll(modelList);
        }
        if (dataFag["isFrist2"] as bool) {
          dataFag["isFrist2"] = false;
          dataFag.refresh();
        }
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }
}

extension DateFormatter on String {
  String toDotFormat() {
    // 支持多种格式
    final formats = [
      RegExp(r'^(\d{1,2})[/](\d{1,2})$'), // 10/05
      RegExp(r'^(\d{2})\.(\d{2})$'), // 10.05
      RegExp(r'^(\d{1,2})\-(\d{1,2})$') // 10-05
    ];

    for (final format in formats) {
      final match = format.firstMatch(this);
      if (match != null) {
        final month = int.parse(match.group(1)!);
        final day = int.parse(match.group(2)!);
        return '$month.$day';
      }
    }

    return this;
  }
}
