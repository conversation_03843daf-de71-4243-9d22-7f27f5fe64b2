// ignore_for_file: unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/DynamicTab.dart';
import 'package:shoot_z/network/model/goods_category_list_model.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item5/tab_home_tab_item5.dart';

class TabHomeItemLogic5 extends GetxController
    with WidgetsBindingObserver, GetTickerProviderStateMixin {
  final ScrollController scrollController = ScrollController();
  TabController? tabController;
  var tabbarIndex = 0.obs;
  var isFrist = true.obs;
  var isFristGoods = true.obs;
  //数据列表
  var goodsCategoryList = <GoodsCategoryListModel>[].obs; //商品类别
  var tabs = <DynamicTab>[].obs;
  @override
  void onInit() {
    super.onInit();
// // 初始化空控制器
//     tabController = TabController(
//       vsync: this,
//       length: 0,
//       initialIndex: 0,
//     );
    //教学类型：1-运球，2-突破，3-投篮，4-防守，5-专项训练
    tabs.add(DynamicTab(
      id: "1",
      title: "运球",
      page: const TabHomeTabItem5Page(
        tabId: "1",
        key: Key("1"),
      ),
      //data: data,
    ));
    tabs.add(DynamicTab(
      id: "2",
      title: "突破",
      page: const TabHomeTabItem5Page(
        tabId: "2",
        key: Key("2"),
      ),
      //data: data,
    ));
    tabs.add(DynamicTab(
      id: "3",
      title: "投篮",
      page: const TabHomeTabItem5Page(
        tabId: "3",
        key: Key("3"),
      ),
      //data: data,
    ));
    tabs.add(DynamicTab(
      id: "4",
      title: "防守",
      page: const TabHomeTabItem5Page(
        tabId: "4",
        key: Key("4"),
      ),
      //data: data,
    ));
    tabs.add(DynamicTab(
      id: "5",
      title: "专项训练",
      page: const TabHomeTabItem5Page(
        tabId: "5",
        key: Key("5"),
      ),
      //data: data,
    ));

    // tabs.refresh();
    // 创建新控制器
    tabController = TabController(
      vsync: this,
      length: tabs.length,
      initialIndex: 0,
    );
    // 添加索引监听
    tabController!.addListener(() {
      tabbarIndex.value = tabController!.index;
    });
  }

  @override
  void onReady() {
    if (isFrist.value) {
      isFrist.value = false;
      isFrist.refresh();
    }
    if (isFristGoods.value) {
      isFristGoods.value = false;
      // isFristGoods.refresh();
    }
    super.onReady();
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }

  //获得商品类别
  getGoodsCategoryList() async {
    // Map<String, dynamic> param = {
    //   'page': 1,
    //   'limit': 3,
    // };
    // var res =
    //     await Api().get(ApiUrl.pointsGoodsCategoryList, queryParameters: param);

    // if (res.isSuccessful()) {
    //   log("pointsGoodsCategoryList-${jsonEncode(res.data)}");
    //   List list = res.data["list"];
    //   List<GoodsCategoryListModel> modelList =
    //       list.map((e) => GoodsCategoryListModel.fromJson(e)).toList();
    //   goodsCategoryList.addAll(modelList);
    //   for (var item in goodsCategoryList) {
    //     // 添加到列表
    //     tabs.add(DynamicTab(
    //       id: "${item.categoryId ?? ""}",
    //       title: item.name ?? "未命名",
    //       page: TabPonitMallItemPage1(
    //         tabId: "${item.categoryId ?? ""}",
    //         key: Key("${item.categoryId ?? item.name ?? ""}"),
    //       ),
    //       //data: data,
    //     ));
    //   }
    //   // tabs.refresh();
    //   // 创建新控制器
    //   tabController = TabController(
    //     vsync: this,
    //     length: tabs.length,
    //     initialIndex: 0,
    //   );
    //   // 添加索引监听
    //   tabController!.addListener(() {
    //     tabbarIndex.value = tabController!.index;
    //   });
    //   if (isFristGoods.value) {
    //     isFristGoods.value = false;
    //     // isFristGoods.refresh();
    //   }
    //   refresh();
    // } else {
    //   WxLoading.showToast(res.message);
    // }
  }

  // 获取当前标签的控制器
  TabController? getCurrentTabController() {
    if (tabs.isEmpty) return null;
    final id = tabs[tabController!.index].id;
    return Get.find<TabController>(tag: id);
  }

  @override
  void onClose() {
    // 销毁TabController
    if (isTabControllerInitialized) {
      tabController?.dispose();
    }
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  bool get isTabControllerInitialized {
    return tabController != null &&
        tabController?.indexIsChanging != null &&
        tabController?.animation != null;
  }
}
