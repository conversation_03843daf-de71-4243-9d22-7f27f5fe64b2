import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';
import 'package:shoot_z/network/model/goods_detail_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

//首页 教学的items
class TabHomeTabItem5Page extends StatefulWidget {
  final String tabId;
  const TabHomeTabItem5Page({super.key, required this.tabId});

  @override
  State<TabHomeTabItem5Page> createState() => _SearchPageState();
}

class _SearchPageState extends State<TabHomeTabItem5Page> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var goodsDetailModel = GoodsDetailModel().obs; //选中的商品详情
  final phoneController = TextEditingController();
  final addressController = TextEditingController();
  final nameController = TextEditingController();
  var colorNameList = <String>[].obs;
  var sizeNameList = <String>[].obs;
  var indexColorName = 0.obs;
  var indexSizeName = 0.obs;
  var indexGoodsList = 0.obs;
  //数据列表
  var dataList = <FeaturedListModel>[].obs;

  @override
  void initState() {
    super.initState();
    getdataList(isLoad: false);
  }

  //获得最新列表
  getdataList({isLoad = true}) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 10,
      'type': widget.tabId, //教学类型：1-运球，2-突破，3-投篮，4-防守，5-专项训练
      //goodsName
    };
    log("pointsGoodsList1-${widget.tabId}-$param");
    var res = await Api().get(ApiUrl.teachingVideo, queryParameters: param);
    if (res.isSuccessful()) {
      log("pointsGoodsList2-${res.data["list"]}");
      List list = res.data["list"] ?? [];
      List<FeaturedListModel> modelList =
          list.map((e) => FeaturedListModel.fromJson(e)).toList();

      log("pointsGoodsList1-${jsonEncode(res.data)}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 10) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        dataList.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      dataFag.refresh();
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: Obx(() {
        return SmartRefresher(
          controller: refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: dataList.isNotEmpty,
          onLoading: () {
            getdataList();
          },
          onRefresh: () {
            getdataList(isLoad: false);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          //  physics: const NeverScrollableScrollPhysics(),
          child: (dataFag["isFrist"] as bool)
              ? buildLoad()
              : dataList.isEmpty
                  ? SizedBox(
                      height: 380.w,
                      child: myNoDataView(
                        context,
                        msg: S.current.No_data_available,
                        imagewidget: WxAssets.images.teamInfoNodata
                            .image(width: 150.w, height: 150.w),
                      ))
                  : CustomScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      slivers: [
                        SliverPadding(
                          padding: EdgeInsets.zero,
                          sliver: SliverMasonryGrid.count(
                              crossAxisCount: 2, // 列数
                              mainAxisSpacing: 15.w, // 垂直间距
                              crossAxisSpacing: 15.w, // 水平间距
                              childCount: dataList.length,
                              itemBuilder: (context, position) {
                                final height = 120.w + (position % 6) * 30.0.w;
                                return GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    if ((dataList[position].videoUrl ?? "") !=
                                        "") {
                                      AppPage.to(Routes.videoPlayerPage,
                                          arguments: {
                                            "featuredListModel":
                                                dataList[position],
                                            "type": "2",
                                          });
                                    } else {
                                      WxLoading.showToast(
                                          S.current.No_data_available);
                                    }
                                  },
                                  child: Container(
                                    height: height,
                                    decoration: BoxDecoration(
                                      color: Colours.color191921,
                                      borderRadius: BorderRadius.circular(8.r),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Stack(
                                          alignment: Alignment.bottomCenter,
                                          children: [
                                            ClipRRect(
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(8.r),
                                                  topRight:
                                                      Radius.circular(8.r)),
                                              child: MyImage(
                                                dataList[position].coverUrl ??
                                                    '',
                                                width: double.infinity,
                                                height: height - 38.w,
                                                radius: 0.r,
                                                bgColor: Colours.color000000,
                                                fit: BoxFit.cover,
                                                errorImage:
                                                    "error_img_white.png",
                                                placeholderImage:
                                                    "error_img_white.png",
                                              ),
                                            ),
                                            Container(
                                              width: double.infinity,
                                              color: Colours.color600F0F16,
                                              padding: EdgeInsets.symmetric(
                                                  vertical: 6.w),
                                              child: Text(
                                                dataList[position].title ?? "",
                                                textAlign: TextAlign.center,
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyles.regular
                                                    .copyWith(fontSize: 12.sp),
                                              ),
                                            )
                                          ],
                                        ),
                                        Container(
                                          height: 38.w,
                                          padding: EdgeInsets.only(
                                              left: 15.w, right: 5.w),
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              MyImage(
                                                dataList[position]
                                                        .uploaderAvatar ??
                                                    "",
                                                width: 18.w,
                                                height: 18.w,
                                                radius: 10.r,
                                                isAssetImage: false,
                                                fit: BoxFit.cover,
                                                errorImage: "my_team_head4.png",
                                              ),
                                              SizedBox(
                                                width: 5.w,
                                              ),
                                              Expanded(
                                                child: Text(
                                                  dataList[position]
                                                          .uploaderName ??
                                                      '',
                                                  style: TextStyles.regular
                                                      .copyWith(
                                                          color: Colours
                                                              .color5C5C6E,
                                                          fontSize: 12.sp,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                        ),
                        SliverToBoxAdapter(
                          child: SizedBox(
                            height: 50.w,
                          ),
                        )
                      ],
                    ),
        );
      }),
    );
  }

  Container buildRowWidget(String name, String value) {
    return Container(
      height: 35.w,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 1,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: Text(
              // 使用 Flexible 替代 Expanded
              value,
              maxLines: 1,
              style:
                  TextStyles.regular.copyWith(overflow: TextOverflow.ellipsis),
            ),
          ),
        ],
      ),
    );
  }
}
