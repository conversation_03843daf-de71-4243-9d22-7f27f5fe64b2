import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item5/tab_home_item_logic5.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///首页 一级页面->教学
class TabHomeItemPage5 extends StatelessWidget {
  TabHomeItemPage5({super.key});

  final logic = Get.put(TabHomeItemLogic5());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Center(
        child: (logic.isFrist.value)
            ? Center(child: buildLoad())
            : Column(
                children: [
                  Container(
                    width: double.infinity,
                    height: 40.w,
                    margin: EdgeInsets.only(left: 3.w, right: 16.w),
                    color: Colours.color0F0F16,
                    child: TabBar(
                        controller: logic.tabController,
                        isScrollable: true,
                        dividerHeight: 0,
                        padding: EdgeInsets.zero,
                        indicatorColor: Colors.transparent, // 设置指示器颜色为透明
                        indicatorSize: TabBarIndicatorSize.tab, // 确保指示器大小与tab相同
                        indicator: const BoxDecoration(), // 使用空装饰移除指示器
                        indicatorWeight: 0, // 设置指示器厚度为0
                        dividerColor: Colors.transparent,
                        dragStartBehavior: DragStartBehavior.start,
                        tabAlignment: TabAlignment.start,
                        // 1. 移除标签之间的间隙
                        labelPadding: EdgeInsets.zero, // 关键设置：去除标签内边距
                        indicatorPadding: EdgeInsets.zero, // 去除指示器内边距
                        tabs: List.generate(logic.tabs.length, (index) {
                          return Obx(() {
                            return Container(
                              width:
                                  logic.tabs[index].title.length * 10.w + 33.w,
                              height: 40.w,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 5.w,
                                  ),
                                  if (logic.tabbarIndex.value == index)
                                    ShaderMask(
                                      shaderCallback: (bounds) =>
                                          const LinearGradient(
                                        colors: [
                                          Colours.color7B35ED,
                                          Colours.colorA253EF
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ).createShader(bounds),
                                      child: Text(
                                        logic.tabs[index].title,
                                        style: TextStyles.regular.copyWith(
                                          fontWeight:
                                              logic.tabbarIndex.value == index
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                          fontSize:
                                              logic.tabbarIndex.value == index
                                                  ? 14.sp
                                                  : 12.sp,
                                          color:
                                              logic.tabbarIndex.value == index
                                                  ? Colours.white
                                                  : Colours.color5C5C6E,
                                        ),
                                      ),
                                    )
                                  else
                                    Text(
                                      logic.tabs[index].title,
                                      style: TextStyles.regular.copyWith(
                                        fontWeight:
                                            logic.tabbarIndex.value == index
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                        fontSize:
                                            logic.tabbarIndex.value == index
                                                ? 16.sp
                                                : 14.sp,
                                        color: logic.tabbarIndex.value == index
                                            ? Colours.white
                                            : Colours.color5C5C6E,
                                      ),
                                    ),
                                  SizedBox(
                                    height: 5.w,
                                  ),
                                  if (logic.tabbarIndex.value == index)
                                    Container(
                                      width: 20.w,
                                      height: 3.w,
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                            colors: [
                                              Colours.color7B35ED,
                                              Colours.colorA253EF
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight),
                                        borderRadius: BorderRadius.vertical(
                                            top: Radius.circular(1.5.r)),
                                      ),
                                    )
                                ],
                              ),
                            );
                          });
                        })),
                  ),
                  logic.isFristGoods.value
                      ? buildLoad()
                      : Expanded(
                          child: TabBarView(
                            controller: logic.tabController,
                            children:
                                logic.tabs.map((tab) => tab.page).toList(),
                          ),
                        )
                ],
              ),
      );
    });
  }
}
