import 'dart:convert';
import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/home_hot_record_model.dart';

class TabHomeItemLogic2 extends GetxController {
  var isFrist = true.obs;
  var homeHotRecordModel = HomeHotRecordModel().obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    getTeamData();
  }

//获得球馆主页详情
  getTeamData() async {
    Map<String, dynamic> param = {};
    var res = await Api().get(ApiUrl.homeHotRecommend, queryParameters: param);
    if (res.isSuccessful()) {
      log("homeHotRecommend22=${jsonEncode(res.data)}");
      homeHotRecordModel.value = HomeHotRecordModel.fromJson(res.data);
      homeHotRecordModel.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    if (isFrist.value) {
      isFrist.value = false;
      isFrist.refresh();
    }
  }
}
