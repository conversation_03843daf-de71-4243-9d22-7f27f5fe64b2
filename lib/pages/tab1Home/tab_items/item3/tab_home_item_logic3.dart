import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/home_challenge_list_model.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';

class TabHomeItemLogic3 extends GetxController {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  StreamSubscription? subscription;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var init = false.obs;
  //数据列表
  var dataList = <HomeChallengeListModel>[].obs;
  @override
  Future<void> onInit() async {
    super.onInit();
    if (Platform.isAndroid) {
      log("TabHomeItemLogic333=1");
      if (await LocationUtils.instance.checkPermission()) {
        log("TabHomeItemLogic333=2");
        getdataList();
      } else {
        log("TabHomeItemLogic333=4");
        init.value = true;
        if (dataFag["isFrist"] as bool) {
          dataFag["isFrist"] = false;
          refresh();
        }
      }
    } else {
      if (await LocationUtils.instance
          .requestPermission(Get.context!, '我们需要您的位置信息以提供附近约战')) {
        getdataList();
      } else {
        init.value = true;
        if (dataFag["isFrist"] as bool) {
          dataFag["isFrist"] = false;
          refresh();
        }
      }
    }

    subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        log("TabHomeItemLogic333=8");
        getdataList(isLoad: false);
      }
      if (EventBusKey.createSquadronBattle == action.key) {
        getdataList(isLoad: false);
      }
    });
    getdataList(isLoad: false);
  }

  //获得最新列表
  getdataList({isLoad = true}) async {
    var position = LocationUtils.instance.position;
    if (position == null) {
      if (await LocationUtils.instance.checkPermission()) {
        await LocationUtils.instance.getCurrentPosition();
      }
      init.value = true;
      final position2 = LocationUtils.instance.position;
      if (position2 == null) {
        return;
      } else {
        position = position2;
      }
    }
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 10,
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
    };
    log("homeChallengeList33-${param}");
    var res = await Api().get(ApiUrl.homeChallengeList, queryParameters: param);
    init.value = true;
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<HomeChallengeListModel> modelList =
          list.map((e) => HomeChallengeListModel.fromJson(e)).toList();
      log("homeChallengeList33-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 10) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        dataList.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }

  void openSettings() async {
    final result = await WxStorage.instance.getBool('requestPermission');
    if (Platform.isAndroid && result == null) {
      WxStorage.instance.setBool('requestPermission', true);
      if (await LocationUtils.instance
          .requestPermission(Get.context!, '我们需要您的位置信息以提供附近约战')) {
        getdataList();
      }
    } else {
      LocationUtils.instance.openSettings('我们需要您的位置信息以提供附近约战');
    }
  }
}
