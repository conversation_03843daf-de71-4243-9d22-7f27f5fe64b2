import 'package:flutter/services.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shoot_z/pages/login/user.dart';

class InviteCodeLogic extends GetxController {
  void invite() {
    final user = UserManager.instance.userInfo.value!;
    //球秀詹姆斯 送您3天球秀SVIP，请下载球秀APP，注册登录后填入推荐码 AS3D3 领取。  下载链接 https://shootz.tech/refinv=as3d3 推荐码 AS3D3
    String url = "${WxEnv.instance.shareUrl}/?code=${user.inviteCode}";
    if (GetPlatform.isAndroid) {
      final content = '${user.userName} 送您3天球秀SVIP，请下载球秀APP，注册登录后填入推荐码 ${user.inviteCode} 领取。\n下载链接 $url\n推荐码 ${user.inviteCode}';
      Share.share(content);
    } else {
      Share.shareUri(Uri.parse(url));
    }
  }

  void copy() {
    // 复制字符串到剪贴板
    Clipboard.setData(ClipboardData(text: UserManager.instance.userInfo.value!.inviteCode!));
    WxLoading.showToast('复制成功');
  }
}