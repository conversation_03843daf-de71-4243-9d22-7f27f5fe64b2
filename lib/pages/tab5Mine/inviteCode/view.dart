import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/inviteCode/logic.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../routes/app.dart';
import '../../login/user.dart';

class InviteCodePage extends StatelessWidget {
  const InviteCodePage({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<InviteCodeLogic>();
    return Scaffold(
      body: Stack(
        children: [
          Positioned(
            top: MediaQuery.of(context).padding.top - 44,
            left: 0,
            right: 0,
            bottom: 0,
            child: WxAssets.images.icInviteCodeBg.image(
              fit: BoxFit.fitWidth,
              alignment: Alignment.topCenter,
            ),
          ),
          Positioned.fill(
              child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height:
                      9.w + MediaQuery.of(context).padding.top + kToolbarHeight,
                ),
                WxAssets.images.icInviteCodeHint
                    .image(width: 175.w, height: 13.w, fit: BoxFit.fill),
                SizedBox(
                  height: 45.w,
                ),
                WxAssets.images.icInviteCodeYqhy
                    .image(width: 291.w, height: 74.w, fit: BoxFit.fill),
                SizedBox(
                  height: 24.w,
                ),
                WxAssets.images.icInviteCodeJf
                    .image(width: 240.w, height: 53.w, fit: BoxFit.fill),
                SizedBox(
                  height: 32.w,
                ),
                WxAssets.images.icInviteCodeDes
                    .image(width: 306.w, height: 38.w, fit: BoxFit.fill),
                SizedBox(
                  height: 48.w,
                ),
                Container(
                  width: 332.w,
                  height: 231.w,
                  decoration: BoxDecoration(
                    color: Colours.white,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                          left: 18.w,
                          top: 37.w,
                          child: WxAssets.images.icTjm.image(
                              width: 103.w, height: 31.w, fit: BoxFit.fill)),
                      Positioned(
                          right: 0,
                          top: 35.w,
                          child: WxAssets.images.icInviteCodeTm.image(
                              width: 162.w, height: 58.w, fit: BoxFit.fill)),
                      Positioned(
                        top: 83.w,
                          left: 18.w,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('请务必让对方填写推荐码',style: TextStyles.regular.copyWith(color: Colours.color9A46FF,fontSize: 12.sp),),
                              SizedBox(height: 5.w,),
                              Text('否则无法获得奖励',style: TextStyles.regular.copyWith(color: Colours.color9393A5,fontSize: 12.sp),),
                            ],
                      )),
                      Positioned(
                        top: 146.w,
                          left: 15.w,
                          right: 15.w,
                          child: Container(
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colours.color9045EE.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                SizedBox(width: 25.w,),
                                Expanded(child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: UserManager.instance.userInfo.value!.inviteCode!.split('').map((value)=>Text(value,style: TextStyles.semiBold.copyWith(color: Colors.black,fontSize: 34.sp),)).toList(),)),
                                SizedBox(width: 26.w,),
                                WxButton(
                                  width: 65.w,
                                  height: 46.w,
                                  text: '复制',
                                  textStyle: TextStyles.regular,
                                  backgroundColor: Colours.color0F0F16,
                                  borderRadius: BorderRadius.circular(12),
                                  onPressed: logic.copy,
                                ),
                                SizedBox(width: 7.w,),
                              ],
                            ),
                      )),
                    ],
                  ),
                ),
                SizedBox(
                  height: 32.w,
                ),
                WxButton(
                  text: '立即邀请',
                  textStyle: TextStyles.semiBold,
                  margin: EdgeInsets.symmetric(horizontal: 20.w),
                  linearGradient: GradientUtils.mainGradient,
                  height: 55.w,
                  borderRadius: BorderRadius.circular(27.5.w),
                  onPressed: logic.invite,
                ),
              ],
            ),
          )),
          Positioned(
            top: MediaQuery.of(context).padding.top,
            left: 0,
            child: GestureDetector(
              onTap: () => AppPage.back(),
              child: WxAssets.images.arrowLeft
                  .image(color: Colors.white, width: kToolbarHeight),
            ),
          ),
        ],
      ),
    );
  }
}
