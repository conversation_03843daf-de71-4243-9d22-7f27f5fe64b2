import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/login/user.dart';
import '../../main.dart';

class MineLogic extends GetxController with RouteAware {
  // @override
  // void onInit() {
  //   super.onInit();
  // }

  void subscribe(BuildContext context) {
    final ModalRoute? route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void onClose() {
    super.onClose();
    routeObserver.unsubscribe(this);
  }

  // ///每日积分签到
  // Future<void> getPointsSignIn() async {
  //   WxLoading.show();
  //   final res = await Api().post(ApiUrl.pointsSignIn, data: {'taskID': 4});
  //   WxLoading.dismiss();
  //   if (res.isSuccessful()) {
  //     UserManager.instance.pullUserInfo();
  //   } else {
  //     WxLoading.showToast(res.message);
  //   }
  // }

  @override
  void didPopNext() {
    // TODO: implement didPopNext
    super.didPopNext();
    if (UserManager.instance.isLogin) {
      UserManager.instance.pullUserInfo();
      UserManager.instance.getMessageHasUnread();
    }
  }

  // //版本升级接口
  // getUpdateVersion() async {
  //   var param = {
  //     'clientType': GetPlatform.isAndroid ? "Android" : "iOS",
  //   };
  //   WxLoading.show();
  //   final res = await Api().get(ApiUrl.commonRelease, queryParameters: param);
  //   WxLoading.dismiss();
  //   if (res.isSuccessful()) {
  //     log("getDaoSql6catch=${res.data}");
  //   } else {
  //     WxLoading.showToast(res.message);
  //   }
  // }
}
