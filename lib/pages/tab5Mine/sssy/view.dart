import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/sssy/logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../generated/l10n.dart';
import '../../../widgets/more_widget.dart';
import '../double_line_widget.dart';

class SssyPage extends StatefulWidget {
  const SssyPage({super.key});

  @override
  State<SssyPage> createState() => _SssyPageState();
}

class _SssyPageState extends State<SssyPage> {
  final logic = Get.put(SssyLogic());
  final state = Get.find<SssyLogic>().state;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.racing_career),
      ),
      body: SafeArea(
        bottom: false,
        child: Obx(
          () => state.init.value ? _refreshList(context) : buildLoad(),
        ),
      ),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
                height: constraints.maxHeight,
                child: myNoDataView(context,
                    msg: S.current.no_records_yet,
                    imagewidget: WxAssets.images.icRecordNo.image())));
      },
    );
  }

  Widget _refreshList(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification note) {
        if (note.metrics.pixels == note.metrics.maxScrollExtent) {
          logic.loadMore();
        }
        return true;
      },
      child: RefreshIndicator(
          onRefresh: logic.onRefresh,
          child: state.list.isEmpty ? _emptyView(context) : _list(context)),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => ListView.builder(
          padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).padding.bottom,
              left: 15,
              right: 15,
              top: 10),
          itemCount: state.list.length + 1,
          itemBuilder: (context, index) {
            if (index == state.list.length) {
              return MoreWidget(
                  state.list.length, logic.hasMore(), state.pageSize);
            }
            return _listItem(context, index);
          }),
    );
  }

  Widget _listItem(BuildContext context, int index) {
    // ignore: invalid_use_of_protected_member
    final model = state.list.value[index];
    return GestureDetector(
      onTap: () => AppPage.to(Routes.playerReportPage, arguments: {
        'teamId': model.myTeamId,
        'matchId': model.matchId,
        'playerId': model.playerId,
      }),
      child: Container(
        margin: EdgeInsets.only(bottom: 15.w),
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.all(15.w),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: CachedNetworkImage(
                            imageUrl: model.photo,
                            fit: BoxFit.cover,
                            width: 45.w,
                            height: 80.w,
                          )),
                      SizedBox(
                        width: 15.w,
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(top: 5.w, right: 15.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                  padding: EdgeInsets.only(right: 35.w),
                                  child: Text(
                                    '${model.leftTeamName} VS ${model.rightTeamName}',
                                    style: TextStyles.titleSemiBold16,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  )),
                              SizedBox(
                                height: 8.w,
                              ),
                              Text(
                                '${model.matchDate} ${model.matchDateWeek} ${model.matchTime}',
                                style: TextStyles.display12
                                    .copyWith(color: Colours.color9393A5),
                              ),
                              SizedBox(
                                height: 4.w,
                              ),
                              Text(
                                '${model.arenaName} 场地${model.courts.join('，')}',
                                style: TextStyles.display12
                                    .copyWith(color: Colours.color9393A5),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  Container(
                    padding: EdgeInsets.only(
                        left: 15.w, right: 15.w, top: 13.w, bottom: 11.w),
                    decoration: BoxDecoration(
                      color: Colours.color1F1F29,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        DoubleLineWidget(text: '${model.score}', value: "得分"),
                        const Spacer(),
                        DoubleLineWidget(text: '${model.rebound}', value: "篮板"),
                        const Spacer(),
                        DoubleLineWidget(text: '${model.assist}', value: "助攻"),
                        const Spacer(),
                        DoubleLineWidget(
                            text:
                                '${model.shotRate.isEmpty ? '0' : model.shotRate}%',
                            value: "命中率"),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Visibility(
              visible: model.mvp,
              child: Positioned(
                  left: 4.w,
                  top: 4.w,
                  child: WxAssets.images.icSssyMvp.image()),
            ),
            Positioned(
                right: 15.w,
                top: 0,
                child: model.win
                    ? WxAssets.images.icSssySuccessfully.image()
                    : WxAssets.images.icSssyFail.image()),
          ],
        ),
      ),
    );
  }
}
