import 'package:flutter_common/api.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab5Mine/sssy/model/career_model.dart';
import 'package:shoot_z/pages/tab5Mine/sssy/state.dart';

import '../../../network/api_url.dart';

class SssyLogic extends GetxController {
  final state = SssyState();

  /// 是否正在加载数据
  bool _isLoading = false;
  @override
  void onInit() async {
    super.onInit();
    await onRefresh();
  }

  bool hasMore() {
    return state.list.length < state.total;
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    _isLoading = true;
    await request(false);
    _isLoading = false;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    state.page = 1;
    await request(true);
    state.init.value = true;
  }

  Future<void> request(bool isRefresh) async {
    final res = await Api().get(ApiUrl.career,
        queryParameters: {'pageIndex': state.page, 'pageSize': state.pageSize});
    if (res.isSuccessful()) {
      state.page += 1;
      final list = (res.data['result'] as List)
          .map((e) => CareerModel.fromJson(e))
          .toList();
      state.total = res.data['totalRows'];
      if (isRefresh) {
        state.list.value = list;
      } else {
        state.list.addAll(list);
      }
    } else {
      if (isRefresh) {
        state.list.value = [];
        state.total = 0;
      }
    }
  }
}
