import 'package:json_annotation/json_annotation.dart';

part 'career_model.g.dart';


@JsonSerializable()
class CareerModel extends Object {

  @Json<PERSON><PERSON>(name: 'arenaId')
  int arenaId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'arenaName')
  String arenaName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'assist')
  int assist;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'courts')
  List<String> courts;

  @<PERSON>son<PERSON><PERSON>(name: 'leftTeamId')
  String leftTeamId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'leftTeamName')
  String leftTeamName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'matchDate')
  String matchDate;

  @Json<PERSON><PERSON>(name: 'matchDateWeek')
  String matchDateWeek;

  @Json<PERSON><PERSON>(name: 'matchId')
  String matchId;

  @J<PERSON><PERSON><PERSON>(name: 'matchTime')
  String matchTime;

  @Json<PERSON>ey(name: 'mvp')
  bool mvp;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'photo')
  String photo;

  @<PERSON>son<PERSON><PERSON>(name: 'playerId')
  String playerId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rebound')
  int rebound;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rightTeamId')
  String rightTeamId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rightTeamName')
  String rightTeamName;

  @<PERSON>son<PERSON><PERSON>(name: 'score')
  int score;

  @JsonKey(name: 'shotRate')
  String shotRate;

  @JsonKey(name: 'win')
  bool win;

  @JsonKey(name: 'myTeamId')
  String myTeamId;

  CareerModel(this.arenaId,this.arenaName,this.assist,this.courts,this.leftTeamId,this.leftTeamName,this.matchDate,this.matchDateWeek,this.matchId,this.matchTime,this.mvp,this.photo,this.playerId,this.rebound,this.rightTeamId,this.rightTeamName,this.score,this.shotRate,this.win,this.myTeamId);

  factory CareerModel.fromJson(Map<String, dynamic> srcJson) => _$CareerModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$CareerModelToJson(this);

}

  
