// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'career_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CareerModel _$CareerModelFromJson(Map<String, dynamic> json) => CareerModel(
      json['arenaId'] as int,
      json['arenaName'] as String,
      json['assist'] as int,
      (json['courts'] as List<dynamic>).map((e) => e as String).toList(),
      json['leftTeamId'] as String,
      json['leftTeamName'] as String,
      json['matchDate'] as String,
      json['matchDateWeek'] as String,
      json['matchId'] as String,
      json['matchTime'] as String,
      json['mvp'] as bool,
      json['photo'] as String,
      json['playerId'] as String,
      json['rebound'] as int,
      json['rightTeamId'] as String,
      json['rightTeamName'] as String,
      json['score'] as int,
      json['shotRate'] as String,
      json['win'] as bool,
      json['myTeamId'] as String,
    );

Map<String, dynamic> _$CareerModelToJson(CareerModel instance) =>
    <String, dynamic>{
      'arenaId': instance.arenaId,
      'arenaName': instance.arenaName,
      'assist': instance.assist,
      'courts': instance.courts,
      'leftTeamId': instance.leftTeamId,
      'leftTeamName': instance.leftTeamName,
      'matchDate': instance.matchDate,
      'matchDateWeek': instance.matchDateWeek,
      'matchId': instance.matchId,
      'matchTime': instance.matchTime,
      'mvp': instance.mvp,
      'photo': instance.photo,
      'playerId': instance.playerId,
      'rebound': instance.rebound,
      'rightTeamId': instance.rightTeamId,
      'rightTeamName': instance.rightTeamName,
      'score': instance.score,
      'shotRate': instance.shotRate,
      'win': instance.win,
      'myTeamId': instance.myTeamId,
    };
