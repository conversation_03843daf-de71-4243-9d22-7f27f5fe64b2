import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/highlights/models/highlights_model.dart';

class MyHighlightsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  var currentTabIndex = 0.obs;
  var tabList = [
    {'title': '全部', 'id': '0'},
    {'title': '报名中', 'id': '1'},
    {'title': '待开赛', 'id': '2'},
    {'title': '进行中', 'id': '3'},
    {'title': '已结束', 'id': '4'},
  ];
  var highlightsTypeList = [
    {'title': '场馆端集锦', 'id': '0'},
    {'title': '移动端半场集锦', 'id': '1'},
  ];
  var currentTypeIndex = 0.obs;

  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;
  var totalCount = 0;
  var userId = "";
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  //数据列表
  var dataList = <HighlightsModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    userId = UserManager.instance.user?.userId ?? "";
    if (Get.arguments != null && Get.arguments.containsKey('userId')) {
      userId = Get.arguments['userId'];
    }
    onRefresh();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await getdataList(false);
  }

  bool hasMore() {
    return dataList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await getdataList(true);
    // init.value = true;
  }

  //获得最新列表
  Future<void> getdataList(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': pageSize,
      'userId': userId,
    };
    var res = await Api().get(ApiUrl.myHighlights, queryParameters: param);
    _isLoading = false;
    init.value = true;
    cc.log("result%%%%%%%%%%${param}${res.data}");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      // cc.log("result${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => HighlightsModel.fromJson(e))
          .toList();
      totalRows = res.data["totalRows"];
      totalCount = res.data["totalCount"];
      if (isRefresh) {
        dataList.value = list;
      } else {
        dataList.addAll(list);
      }
    } else {
      if (isRefresh) {
        dataList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
