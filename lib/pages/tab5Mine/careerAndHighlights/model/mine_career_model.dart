///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MineCareerModelTeams {
/*
{
  "leaderUserId": 0,
  "leaderUserName": "string",
  "rankScore": 0,
  "showId": "0",
  "teamId": "0",
  "teamLogo": "string",
  "teamName": "string"
} 
*/

  int? leaderUserId;
  String? leaderUserName;
  int? rankScore;
  String? showId;
  String? teamId;
  String? teamLogo;
  String? teamName;

  MineCareerModelTeams({
    this.leaderUserId,
    this.leaderUserName,
    this.rankScore,
    this.showId,
    this.teamId,
    this.teamLogo,
    this.teamName,
  });
  MineCareerModelTeams.fromJson(Map<String, dynamic> json) {
    leaderUserId = json['leaderUserId']?.toInt();
    leaderUserName = json['leaderUserName']?.toString();
    rankScore = json['rankScore']?.toInt();
    showId = json['showId']?.toString();
    teamId = json['teamId']?.toString();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['leaderUserId'] = leaderUserId;
    data['leaderUserName'] = leaderUserName;
    data['rankScore'] = rankScore;
    data['showId'] = showId;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    return data;
  }
}

class MineCareerModelCareerTotal {
/*
{
  "totalAssists": 0,
  "totalGames": 0,
  "totalRebound": 0,
  "totalScore": 0,
  "totalThreePointShoot": 0
} 
*/

  int? totalAssists;
  int? totalGames;
  int? totalRebound;
  int? totalScore;
  int? totalThreePointShoot;

  MineCareerModelCareerTotal({
    this.totalAssists,
    this.totalGames,
    this.totalRebound,
    this.totalScore,
    this.totalThreePointShoot,
  });
  MineCareerModelCareerTotal.fromJson(Map<String, dynamic> json) {
    totalAssists = json['totalAssists']?.toInt();
    totalGames = json['totalGames']?.toInt();
    totalRebound = json['totalRebound']?.toInt();
    totalScore = json['totalScore']?.toInt();
    totalThreePointShoot = json['totalThreePointShoot']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['totalAssists'] = totalAssists;
    data['totalGames'] = totalGames;
    data['totalRebound'] = totalRebound;
    data['totalScore'] = totalScore;
    data['totalThreePointShoot'] = totalThreePointShoot;
    return data;
  }
}

class MineCareerModel {
/*
{
  "avatar": "string",
  "avgHit": 0,
  "avgRate": "string",
  "avgScore": "string",
  "avgShoot": "string",
  "careerTotal": {
    "totalAssists": 0,
    "totalGames": 0,
    "totalRebound": 0,
    "totalScore": 0,
    "totalThreePointShoot": 0
  },
  "height": "string",
  "name": "string",
  "photo": "string",
  "position": 0,
  "realName": "string",
  "teams": [
    {
      "leaderUserId": 0,
      "leaderUserName": "string",
      "rankScore": 0,
      "showId": "0",
      "teamId": "0",
      "teamLogo": "string",
      "teamName": "string"
    }
  ],
  "threePointShootRate": "string",
  "weight": "string"
} 
*/

  String? avatar;
  int? avgHit;
  String? avgRate;
  String? avgScore;
  String? avgShoot;
  MineCareerModelCareerTotal? careerTotal;
  String? height;
  String? name;
  String? photo;
  int? position;
  String? realName;
  List<MineCareerModelTeams?>? teams;
  String? threePointShootRate;
  String? weight;

  MineCareerModel({
    this.avatar,
    this.avgHit,
    this.avgRate,
    this.avgScore,
    this.avgShoot,
    this.careerTotal,
    this.height,
    this.name,
    this.photo,
    this.position,
    this.realName,
    this.teams,
    this.threePointShootRate,
    this.weight,
  });
  MineCareerModel.fromJson(Map<String, dynamic> json) {
    avatar = json['avatar']?.toString();
    avgHit = json['avgHit']?.toInt();
    avgRate = json['avgRate']?.toString();
    avgScore = json['avgScore']?.toString();
    avgShoot = json['avgShoot']?.toString();
    careerTotal = (json['careerTotal'] != null)
        ? MineCareerModelCareerTotal.fromJson(json['careerTotal'])
        : null;
    height = json['height']?.toString();
    name = json['name']?.toString();
    photo = json['photo']?.toString();
    position = json['position']?.toInt();
    realName = json['realName']?.toString();
    if (json['teams'] != null) {
      final v = json['teams'];
      final arr0 = <MineCareerModelTeams>[];
      v.forEach((v) {
        arr0.add(MineCareerModelTeams.fromJson(v));
      });
      teams = arr0;
    }
    threePointShootRate = json['threePointShootRate']?.toString();
    weight = json['weight']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['avatar'] = avatar;
    data['avgHit'] = avgHit;
    data['avgRate'] = avgRate;
    data['avgScore'] = avgScore;
    data['avgShoot'] = avgShoot;
    if (careerTotal != null) {
      data['careerTotal'] = careerTotal!.toJson();
    }
    data['height'] = height;
    data['name'] = name;
    data['photo'] = photo;
    data['position'] = position;
    data['realName'] = realName;
    if (teams != null) {
      final v = teams;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['teams'] = arr0;
    }
    data['threePointShootRate'] = threePointShootRate;
    data['weight'] = weight;
    return data;
  }
}
