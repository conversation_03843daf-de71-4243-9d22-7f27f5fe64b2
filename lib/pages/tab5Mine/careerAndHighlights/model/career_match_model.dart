///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CareerMatchModel {
/*
{
  "UserPhoto": "string",
  "arenaName": "string",
  "best": true,
  "courts": [
    "string"
  ],
  "fragmentCount": 0,
  "matchDate": "string",
  "matchId": "0",
  "memberId": "0",
  "photo": "string",
  "playerId": "0",
  "playerNumber": "string",
  "shootRate": "string",
  "shootScore": 0,
  "teamId": "0",
  "teamName": "string",
  "win": true
} 
*/

  // String? UserPhoto;
  String? arenaName;
  bool? best;
  List<String?>? courts;
  int? fragmentCount;
  String? matchDate;
  String? matchId;
  String? memberId;
  String? photo;
  String? playerId;
  String? playerNumber;
  String? shootRate;
  int? shootScore;
  String? teamId;
  String? teamName;
  bool? win;

  CareerMatchModel({
    // this.UserPhoto,
    this.arenaName,
    this.best,
    this.courts,
    this.fragmentCount,
    this.matchDate,
    this.matchId,
    this.memberId,
    this.photo,
    this.playerId,
    this.playerNumber,
    this.shootRate,
    this.shootScore,
    this.teamId,
    this.teamName,
    this.win,
  });
  CareerMatchModel.fromJson(Map<String, dynamic> json) {
    // UserPhoto = json['UserPhoto']?.toString();
    arenaName = json['arenaName']?.toString();
    best = json['best'];
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      courts = arr0;
    }
    fragmentCount = json['fragmentCount']?.toInt();
    matchDate = json['matchDate']?.toString();
    matchId = json['matchId']?.toString();
    memberId = json['memberId']?.toString();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    playerNumber = json['playerNumber']?.toString();
    shootRate = json['shootRate']?.toString();
    shootScore = json['shootScore']?.toInt();
    teamId = json['teamId']?.toString();
    teamName = json['teamName']?.toString();
    win = json['win'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    // data['UserPhoto'] = UserPhoto;
    data['arenaName'] = arenaName;
    data['best'] = best;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['courts'] = arr0;
    }
    data['fragmentCount'] = fragmentCount;
    data['matchDate'] = matchDate;
    data['matchId'] = matchId;
    data['memberId'] = memberId;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['playerNumber'] = playerNumber;
    data['shootRate'] = shootRate;
    data['shootScore'] = shootScore;
    data['teamId'] = teamId;
    data['teamName'] = teamName;
    data['win'] = win;
    return data;
  }
}
