import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';

class CareerHighlightsHomeLogic extends GetxController
    with GetTickerProviderStateMixin {
  late TabController tabController;
  var currentTabIndex = 0.obs;
  var type = 0;
  var tab1Name = S.current.my_career;
  var tab2Name = S.current.my_highlights;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(() {
      currentTabIndex.value = tabController.index;
    });
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      final args = Get.arguments as Map<String, dynamic>;
      type = args['type'] as int? ?? 0;
    }
    if (Get.arguments != null && Get.arguments.containsKey('userId')) {
      tab1Name = S.current.racing_career;
      tab2Name = S.current.collection_of_creations;
    }
    if (type == 1) {
      tabController.animateTo(1);
    }
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
