import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/career_match_model.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/mine_career_model.dart';

class MyCareerLogic extends GetxController {
  var isLoading = false.obs;
  var init = false.obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var userId = "";
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <CareerMatchModel>[].obs;
  //数据列表
  var careerModel = Rxn<MineCareerModel>();
  @override
  void onInit() {
    super.onInit();
    userId = UserManager.instance.user?.userId ?? "";
    if (Get.arguments != null && Get.arguments.containsKey('userId')) {
      userId = Get.arguments['userId'];
    }
    onRefresh();
    onRefreshCareer(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> onRefresh() async {
    isLoading.value = true;
    Map<String, dynamic> param = {'userId': userId};
    var res = await Api().get(ApiUrl.myCareerData, queryParameters: param);
    init.value = true;
    isLoading.value = false;
    if (res.isSuccessful()) {
      cc.log("message!!!!!!!${res.data}");
      careerModel.value = MineCareerModel.fromJson(res.data);
    }
  }

  Future<void> onRefreshCareer({
    isLoad = true,
    required RefreshController controller,
  }) async {
    var page = dataFag["page"] as int;
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'lastRecordId': dataList.isEmpty ? '0' : dataList.last.matchId,
      'pageSize': dataFag["pageSize"],
      'userId': userId //'462119'
    };
    var res =
        await Api().get(ApiUrl.getMatchCareerList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data['result'] ?? [];
      List<CareerMatchModel> modelList =
          list.map((e) => CareerMatchModel.fromJson(e)).toList();
      // cc.log("message!!!!!!!!!$param${res.data}");
      bool isEnd = res.data['isEnd'];
      dataFag["page"] = page + 1;
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
      } else {
        dataList.assignAll(modelList);
      }
      if (isEnd) {
        controller.loadNoData();
        //  controller.loadComplete();
      } else {
        controller.loadComplete();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }
}
