import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/battle_detail_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:flutter_common/wx_loading.dart';

class ContactPublisherDialog extends StatefulWidget {
  final String? publisherPhone;

  const ContactPublisherDialog({
    super.key,
    this.publisherPhone,
  });

  @override
  State<ContactPublisherDialog> createState() => _ContactPublisherDialogState();
}

class _ContactPublisherDialogState extends State<ContactPublisherDialog> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();
  final TextEditingController teamController = TextEditingController();
  var teamId = "";
  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    remarksController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colours.color191921,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar at top
            Container(
              margin: EdgeInsets.only(top: 12.w),
              width: 38.w,
              height: 4.w,
              decoration: BoxDecoration(
                color: const Color(0x1AD8D8D8),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            // Header
            Container(
              padding: EdgeInsets.all(20.w),
              child: Text(
                '联系发布者',
                style: TextStyles.titleSemiBold16,
              ),
            ),

            // Form fields
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      '基本信息（仅发布者可见）',
                      style: TextStyles.display12,
                    ),
                  ),
                  SizedBox(height: 10.w),
                  // Name field
                  _buildInputField(
                    controller: nameController,
                    hintText: '请输入姓名',
                    label: '姓名',
                  ),
                  SizedBox(height: 10.w),
                  Container(
                    color: const Color(0x99292937),
                    width: double.infinity,
                    height: 1,
                  ),
                  SizedBox(height: 10.w),
                  // Phone field
                  _buildInputField(
                    controller: phoneController,
                    hintText: '请输入电话',
                    label: '电话',
                  ),
                  SizedBox(height: 10.w),
                  Container(
                    color: const Color(0x99292937),
                    width: double.infinity,
                    height: 1,
                  ),
                  SizedBox(height: 10.w),
                  _buildInputField(
                      controller: teamController,
                      hintText: '请选择球队',
                      label: '球队',
                      showStar: false,
                      isEnabled: false),
                  SizedBox(height: 10.w),
                  Container(
                    color: const Color(0x99292937),
                    width: double.infinity,
                    height: 1,
                  ),
                  SizedBox(height: 10.w),
                  // Remarks field
                  _buildInputField(
                    controller: remarksController,
                    hintText: '请输入备注信息（30字内）',
                    label: '备注信息',
                    showStar: false,
                    maxNumber: 30,
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.w),

            // Bottom buttons
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.w),
              margin: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
              child: Row(
                children: [
                  // Direct contact button
                  Expanded(
                    child: InkWell(
                      onTap: _onDirectContact,
                      child: Container(
                        height: 44.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Text(
                          '直接联系',
                          style: TextStyles.semiBold14.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // Submit intention button
                  Expanded(
                    child: InkWell(
                      onTap: _onSubmitIntention,
                      child: Container(
                        height: 44.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Text(
                          '提交意向',
                          style: TextStyles.semiBold14.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(
      {required TextEditingController controller,
      required String hintText,
      required String label,
      TextInputType? keyboardType,
      int maxLines = 1,
      bool showStar = true,
      bool isEnabled = true,
      int maxNumber = 0}) {
    return Row(
      children: [
        Opacity(
            opacity: showStar ? 1.0 : 0,
            child: Text(
              '*',
              style: TextStyle(fontSize: 14.sp, color: Colours.colorFF3F3F),
            )),
        SizedBox(
          width: 2.w,
        ),
        SizedBox(
          width: 60.w,
          child: Text(
            label,
            style: TextStyles.regular.copyWith(color: Colors.white),
          ),
        ),
        SizedBox(width: 24.w),
        Expanded(
            child: InkWell(
                onTap: !isEnabled
                    ? () async {
                        if (label == '球队') {
                          AppPage.to(Routes.teamListPage,
                              arguments: {"selectTeam": true}).then((v) {
                            if (v != null) {
                              teamController.text = v.name ?? "";
                              teamId = v.id;
                            }
                          });
                        }
                      }
                    : null,
                child: AbsorbPointer(
                    absorbing: !isEnabled,
                    child: TextField(
                      readOnly: !isEnabled,
                      controller: controller,
                      keyboardType: keyboardType,
                      maxLines: maxLines,
                      style: TextStyles.regular.copyWith(color: Colors.white),
                      inputFormatters: maxNumber == 0
                          ? null
                          : [
                              LengthLimitingTextInputFormatter(maxNumber),
                            ],
                      decoration: InputDecoration(
                        hintText: hintText,
                        hintStyle: TextStyles.regular.copyWith(
                          color: Colours.color5C5C6E,
                        ),
                        border: InputBorder.none,
                      ),
                    )))),
        if (!isEnabled)
          Icon(
            Icons.arrow_forward_ios,
            size: 14.w,
            color: Colours.color5C5C6E,
          )
      ],
    );
  }

  void _onDirectContact() {
    AppPage.back();

    if (widget.publisherPhone != null && widget.publisherPhone!.isNotEmpty) {
      Get.dialog(
        CustomAlertDialog(
          title: '拨打电话 ${widget.publisherPhone}',
          onPressed: () {
            AppPage.back();
            Utils.phoneTelURL(widget.publisherPhone!);
          },
        ),
      );
    } else {
      WxLoading.showToast('暂无发布者联系方式');
    }
  }

  void _onSubmitIntention() {
    // Validate required fields
    if (nameController.text.trim().isEmpty) {
      WxLoading.showToast('请输入您的姓名');
      return;
    }

    if (phoneController.text.trim().isEmpty) {
      WxLoading.showToast('请输入您的联系电话');
      return;
    }

    if (!Utils.isPhoneNumber(phoneController.text.trim())) {
      WxLoading.showToast('请输入正确的手机号码');
      return;
    }

    // TODO: Implement submit intention API call
    // This would typically send the user's contact info to the server
    // so the publisher can contact them back
    final logic = Get.find<BattleDetailLogic>();
    logic.createIntention({
      'contactPhone': phoneController.text,
      'remark': remarksController.text,
      'userName': nameController.text,
      'teamId': teamId.isEmpty ? '0' : teamId
    });

    WxLoading.showToast('意向提交成功，发布者将会联系您');
  }

  // TODO: Implement API call for submitting contact intention
  // Future<void> _submitContactIntention() async {
  //   final data = {
  //     'name': nameController.text.trim(),
  //     'phone': phoneController.text.trim(),
  //     'remarks': remarksController.text.trim(),
  //     'challengeId': widget.challengeId, // You'd need to pass this
  //   };
  //
  //   final res = await Api().post(ApiUrl.submitContactIntention, data: data);
  //   if (res.isSuccessful()) {
  //     WxLoading.showToast('意向提交成功');
  //   } else {
  //     WxLoading.showToast(res.message);
  //   }
  // }
}
