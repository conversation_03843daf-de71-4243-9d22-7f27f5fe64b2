///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ChallengeDetailModelTeamInfo {
/*
{
  "avgFreeThrowShootCount": 0,
  "avgFreeThrowShootHit": 0,
  "avgFreeThrowShootRate": "string",
  "avgScore": 0,
  "avgShootCount": 0,
  "avgShootHit": 0,
  "avgThreePointShootCount": 0,
  "avgThreePointShootHit": 0,
  "avgThreePointShootRate": "string",
  "matchNum": 0,
  "shootRate": "string"
} 
*/

  int? avgFreeThrowShootCount;
  int? avgFreeThrowShootHit;
  String? avgFreeThrowShootRate;
  int? avgScore;
  int? avgShootCount;
  int? avgShootHit;
  int? avgThreePointShootCount;
  int? avgThreePointShootHit;
  String? avgThreePointShootRate;
  int? matchNum;
  String? shootRate;

  ChallengeDetailModelTeamInfo({
    this.avgFreeThrowShootCount,
    this.avgFreeThrowShootHit,
    this.avgFreeThrowShootRate,
    this.avgScore,
    this.avgShootCount,
    this.avgShootHit,
    this.avgThreePointShootCount,
    this.avgThreePointShootHit,
    this.avgThreePointShootRate,
    this.matchNum,
    this.shootRate,
  });
  ChallengeDetailModelTeamInfo.fromJson(Map<String, dynamic> json) {
    avgFreeThrowShootCount = json['avgFreeThrowShootCount']?.toInt();
    avgFreeThrowShootHit = json['avgFreeThrowShootHit']?.toInt();
    avgFreeThrowShootRate = json['avgFreeThrowShootRate']?.toString();
    avgScore = json['avgScore']?.toInt();
    avgShootCount = json['avgShootCount']?.toInt();
    avgShootHit = json['avgShootHit']?.toInt();
    avgThreePointShootCount = json['avgThreePointShootCount']?.toInt();
    avgThreePointShootHit = json['avgThreePointShootHit']?.toInt();
    avgThreePointShootRate = json['avgThreePointShootRate']?.toString();
    matchNum = json['matchNum']?.toInt();
    shootRate = json['shootRate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['avgFreeThrowShootCount'] = avgFreeThrowShootCount;
    data['avgFreeThrowShootHit'] = avgFreeThrowShootHit;
    data['avgFreeThrowShootRate'] = avgFreeThrowShootRate;
    data['avgScore'] = avgScore;
    data['avgShootCount'] = avgShootCount;
    data['avgShootHit'] = avgShootHit;
    data['avgThreePointShootCount'] = avgThreePointShootCount;
    data['avgThreePointShootHit'] = avgThreePointShootHit;
    data['avgThreePointShootRate'] = avgThreePointShootRate;
    data['matchNum'] = matchNum;
    data['shootRate'] = shootRate;
    return data;
  }
}

class ChallengeDetailModel {
/*
{
  "arenaId": "0",
  "arenaName": "string",
  "avatar": "string",
  "challengeCost": 0,
  "challengeFormat": 0,
  "challengeStrength": 0,
  "challengeTitle": "string",
  "id": "0",
  "leftTeamId": "0",
  "leftTeamLogo": "string",
  "leftTeamName": "string",
  "leftTeamRankScore": 0,
  "matchTime": "string",
  "phone": "string",
  "remark": "string",
  "rightTeamId": "0",
  "rightTeamLogo": "string",
  "rightTeamName": "string",
  "rightTeamRankScore": 0,
  "status": 0,
  "teamInfo": {
    "avgFreeThrowShootCount": 0,
    "avgFreeThrowShootHit": 0,
    "avgFreeThrowShootRate": "string",
    "avgScore": 0,
    "avgShootCount": 0,
    "avgShootHit": 0,
    "avgThreePointShootCount": 0,
    "avgThreePointShootHit": 0,
    "avgThreePointShootRate": "string",
    "matchNum": 0,
    "shootRate": "string"
  },
  "userId": "0",
  "userName": "string",
  "week": "string"
} 
*/

  String? arenaId;
  String? arenaName;
  String? avatar;
  int? challengeCost;
  int? challengeFormat;
  int? challengeStrength;
  String? challengeTitle;
  String? id;
  String? leftTeamId;
  String? leftTeamLogo;
  String? leftTeamName;
  int? leftTeamRankScore;
  String? matchTime;
  String? phone;
  String? remark;
  String? rightTeamId;
  String? rightTeamLogo;
  String? rightTeamName;
  int? rightTeamRankScore;
  int? status;
  ChallengeDetailModelTeamInfo? teamInfo;
  String? userId;
  String? userName;
  String? week;
  String? challengeStrengthStr;
  String? challengeCostStr;
  String? challengeFormatStr;
  String? leftImagePath;
  String? rightImagePath;

  ChallengeDetailModel(
      {this.arenaId,
      this.arenaName,
      this.avatar,
      this.challengeCost,
      this.challengeFormat,
      this.challengeStrength,
      this.challengeTitle,
      this.id,
      this.leftTeamId,
      this.leftTeamLogo,
      this.leftTeamName,
      this.leftTeamRankScore,
      this.matchTime,
      this.phone,
      this.remark,
      this.rightTeamId,
      this.rightTeamLogo,
      this.rightTeamName,
      this.rightTeamRankScore,
      this.status,
      this.teamInfo,
      this.userId,
      this.userName,
      this.week,
      this.challengeStrengthStr,
      this.challengeCostStr,
      this.challengeFormatStr,
      this.leftImagePath,
      this.rightImagePath});
  ChallengeDetailModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    arenaName = json['arenaName']?.toString();
    avatar = json['avatar']?.toString();
    challengeCost = json['challengeCost']?.toInt();
    challengeFormat = json['challengeFormat']?.toInt();
    challengeStrength = json['challengeStrength']?.toInt();
    challengeTitle = json['challengeTitle']?.toString();
    id = json['id']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    leftTeamLogo = json['leftTeamLogo']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    leftTeamRankScore = json['leftTeamRankScore']?.toInt();
    matchTime = json['matchTime']?.toString();
    phone = json['phone']?.toString();
    remark = json['remark']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    rightTeamLogo = json['rightTeamLogo']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    rightTeamRankScore = json['rightTeamRankScore']?.toInt();
    status = json['status']?.toInt();
    teamInfo = (json['teamInfo'] != null)
        ? ChallengeDetailModelTeamInfo.fromJson(json['teamInfo'])
        : null;
    userId = json['userId']?.toString();
    userName = json['userName']?.toString();
    week = json['week']?.toString();
    challengeStrengthStr = challengeStrength == 1
        ? '新手局'
        : challengeStrength == 2
            ? '出汗局'
            : challengeStrength == 3
                ? '质量局'
                : '强度局';
    challengeFormatStr = challengeFormat == 1
        ? '计时'
        : challengeFormat == 2
            ? '总分'
            : challengeFormat == 3
                ? '单节轮转'
                : '其他';
    challengeCostStr = challengeCost == 1 ? 'AA' : '其他';
    leftImagePath = (leftTeamRankScore ?? 0) >= 3000
        ? 'assets/images/five_stars_icon.png'
        : (leftTeamRankScore ?? 0) >= 2000
            ? 'assets/images/four_stars_icon.png'
            : (leftTeamRankScore ?? 0) >= 1500
                ? 'assets/images/three_stars_icon.png'
                : (leftTeamRankScore ?? 0) >= 1200
                    ? 'assets/images/two_stars_icon.png'
                    : 'assets/images/one_stars_icon.png';
    rightImagePath = (rightTeamRankScore ?? 0) >= 3000
        ? 'assets/images/five_stars_icon.png'
        : (rightTeamRankScore ?? 0) >= 2000
            ? 'assets/images/four_stars_icon.png'
            : (rightTeamRankScore ?? 0) >= 1500
                ? 'assets/images/three_stars_icon.png'
                : (rightTeamRankScore ?? 0) >= 1200
                    ? 'assets/images/two_stars_icon.png'
                    : 'assets/images/one_stars_icon.png';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['arenaName'] = arenaName;
    data['avatar'] = avatar;
    data['challengeCost'] = challengeCost;
    data['challengeFormat'] = challengeFormat;
    data['challengeStrength'] = challengeStrength;
    data['challengeTitle'] = challengeTitle;
    data['id'] = id;
    data['leftTeamId'] = leftTeamId;
    data['leftTeamLogo'] = leftTeamLogo;
    data['leftTeamName'] = leftTeamName;
    data['leftTeamRankScore'] = leftTeamRankScore;
    data['matchTime'] = matchTime;
    data['phone'] = phone;
    data['remark'] = remark;
    data['rightTeamId'] = rightTeamId;
    data['rightTeamLogo'] = rightTeamLogo;
    data['rightTeamName'] = rightTeamName;
    data['rightTeamRankScore'] = rightTeamRankScore;
    data['status'] = status;
    if (teamInfo != null) {
      data['teamInfo'] = teamInfo!.toJson();
    }
    data['userId'] = userId;
    data['userName'] = userName;
    data['week'] = week;
    data['challengeStrengthStr'] = challengeStrengthStr;
    data['challengeFormatStr'] = challengeFormatStr;
    data['challengeCostStr'] = challengeCostStr;
    data['leftImagePath'] = leftImagePath;
    data['rightImagePath'] = rightImagePath;
    return data;
  }
}
