import 'dart:developer' as cc;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/create_battle_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/widgets/custom_calendar_date_picker.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/time_picker_bottom_sheet.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class CreateBattlePage extends StatelessWidget {
  CreateBattlePage({super.key});

  final logic = Get.put(CreateBattleLogic());
  final bool showBuy = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(logic.isHalf ? "半场约战" : '全场约战'),
      ),
      body: _createBattleWidget(context),
      bottomNavigationBar: InkWell(
        onTap: () {
          logic.createChallenge();
        },
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '创建并发布',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  String formatChineseTime(String timeStr) {
    return timeStr.replaceAll("时", ":").replaceAll("分", "");
  }

  /// 列表数据
  _createBattleWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWithIcon(title: logic.isHalf ? '半场约战' : '全场约战'),
          SizedBox(
            height: 15.w,
          ),
          Container(
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(
              children: [
                _cellInfo('约战标题',
                    controller: logic.matchTitleController,
                    hintText: '请输入标题',
                    controllerReadOnly: false,
                    context: context),
                _cellInfo('我的球队',
                    controller: logic.myTeamController,
                    showStar: !logic.isHalf,
                    context: context),
                _cellInfo('约战日期',
                    controller: logic.matchDateController, context: context),
                _cellInfo('开始时间',
                    controller: logic.startTimeController, context: context),
                _cellInfo('约战地点',
                    controller: logic.addressController, context: context),
                _cellInfo('联系方式',
                    controller: logic.contactController,
                    hintText: '请输入手机号码',
                    controllerReadOnly: false,
                    showLine: false,
                    context: context)
              ],
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          const TextWithIcon(title: '约战说明'),
          Container(
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            margin: EdgeInsets.only(top: 15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(
              children: [
                _cellInfo('强度', showLine: false, context: context),
                _optionsWidget(
                    ['新手局', '出汗局', '质量局', '强度局'], logic.challengeStrength),
                _cellInfo('费用', showLine: false, context: context),
                _optionsWidget(['AA', '其他'], logic.challengeCost),
                _cellInfo('赛制',
                    showLine: false, showStar: false, context: context),
                _optionsWidget(
                    ['计时', '总分', '单节轮转', '其他'], logic.challengeFormat),
                _cellInfo('补充说明',
                    controller: logic.remarksController,
                    hintText: '请输入补充说明（30字内）',
                    maxNumber: 30,
                    controllerReadOnly: false,
                    showLine: false,
                    showStar: false,
                    context: context),
              ],
            ),
          ),
        ],
      ).marginSymmetric(horizontal: 15.w, vertical: 15),
    );
  }

  Widget _cellInfo(String leftTitle,
      {bool showLine = true,
      bool showStar = true,
      TextEditingController? controller,
      String hintText = '请选择',
      bool controllerReadOnly = true,
      BuildContext? context,
      int maxNumber = 0}) {
    DateTime selectDate = DateTime.now();
    return Container(
      decoration: BoxDecoration(
          border: showLine
              ? Border(
                  bottom: BorderSide(color: Colours.color99292937, width: 1.w))
              : null),
      height: 54,
      child: Row(
        children: [
          Opacity(
              opacity: showStar ? 1.0 : 0,
              child: Text(
                '*',
                style: TextStyle(fontSize: 14.sp, color: Color(0xFFFF3F3F)),
              )),
          SizedBox(
            width: 2.w,
          ),
          Text(
            leftTitle,
            style: TextStyles.regular,
          ),
          SizedBox(
            width: 30.w,
          ),
          if (controller != null)
            Expanded(
                child: InkWell(
              onTap: controllerReadOnly
                  ? () async {
                      cc.log("TextField clicked: $leftTitle");
                      if (leftTitle == '我的球队') {
                        AppPage.to(Routes.teamListPage,
                            arguments: {"selectTeam": true}).then((v) {
                          if (v != null) {
                            logic.myTeamController.text = v.name ?? "";
                            logic.teamId = v.id;
                          }
                        });
                      } else if (leftTitle == '约战日期') {
                        if (context == null) return;
                        // 处理日期选择
                        DateTime currentDate = DateTime.now();
                        DateTime threeYearsLater =
                            currentDate.copyWith(year: currentDate.year + 3);
                        final dateResult = await showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return CustomCalendarDatePicker(
                                title: '日期选择',
                                firstDay: currentDate,
                                lastDay: threeYearsLater,
                                curFocusedDay: selectDate,
                              );
                            });
                        selectDate = dateResult;
                        String formattedDate =
                            DateFormat('yyyy-MM-dd').format(dateResult);
                        logic.matchDateController.text = formattedDate;
                      } else if (leftTitle == '开始时间') {
                        if (context == null) return;
                        // 处理时间选择
                        final result = await showModalBottomSheet(
                            context: context,
                            builder: (BuildContext context) {
                              return TimePickerBottomSheet();
                            });
                        logic.startTimeController.text =
                            formatChineseTime(result);
                      } else if (leftTitle == '约战地点') {
                        // 处理地点选择
                        _showVenueListBottomSheet(context);
                      }
                    }
                  : null,
              child: AbsorbPointer(
                absorbing: controllerReadOnly,
                child: TextField(
                  controller: controller,
                  readOnly: controllerReadOnly,
                  style: TextStyles.regular,
                  inputFormatters: maxNumber == 0
                      ? null
                      : [
                          LengthLimitingTextInputFormatter(maxNumber),
                        ],
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                    //让文字垂直居中,
                    border: InputBorder.none,
                  ),
                ),
              ),
            )),
          if (controllerReadOnly && controller != null)
            Icon(
              Icons.arrow_forward_ios,
              size: 14.w,
              color: Colours.color5C5C6E,
            )
        ],
      ),
    );
  }

  Widget _optionsWidget(List options, RxInt selectIndex) {
    return SizedBox(
      width: ScreenUtil().screenWidth - 60.w,
      child: Obx(() {
        return Wrap(
          spacing: 9.w,
          children: List.generate(
              options.length,
              (index) => GestureDetector(
                    onTap: () {
                      selectIndex.value = index + 1;
                    },
                    child: Container(
                      width: (ScreenUtil().screenWidth - 36.w - 60.w) / 4.0,
                      height: 32.w,
                      decoration: BoxDecoration(
                          gradient: selectIndex.value == index + 1
                              ? const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                )
                              : null,
                          border: selectIndex.value == index + 1
                              ? null
                              : Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(16.r)),
                      child: Center(
                        child: Text(
                          options[index],
                          style: TextStyles.display12
                              .copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                  )),
        );
      }),
    );
  }

  /// 显示场馆列表底部弹窗
  void _showVenueListBottomSheet(BuildContext? context) {
    if (context == null) return;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            children: [
              // 顶部指示条
              Container(
                width: 38.w,
                height: 3.w,
                margin: EdgeInsets.only(top: 6.w),
                decoration: BoxDecoration(
                  color: Colours.color1AD8D8D8,
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),

              // 标题栏
              Padding(
                padding: EdgeInsets.symmetric(vertical: 18.w),
                child: Text(
                  '选择场馆',
                  style: TextStyles.semiBold.copyWith(fontSize: 16.sp),
                ),
              ),

              // 搜索框
              Container(
                margin: EdgeInsets.symmetric(horizontal: 20.w),
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                height: 40.w,
                decoration: BoxDecoration(
                  color: Colours.color2A2A32,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Row(
                  children: [
                    WxAssets.images.icSearch.image(),
                    SizedBox(
                      width: 10.w,
                    ),
                    Expanded(
                        child: TextField(
                      autocorrect: false,
                      controller: logic.searchController,
                      keyboardType: TextInputType.text,
                      style: TextStyles.display14,
                      maxLines: 1,
                      // onChanged: logic.onTextChanged,
                      decoration: InputDecoration(
                        // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                        contentPadding: EdgeInsets.only(top: 0, bottom: 5.w),
                        border: InputBorder.none,
                        hintText: "输入球馆名称搜索",
                        hintStyle: TextStyles.display14
                            .copyWith(color: Colours.color5C5C6E),
                      ),
                      textInputAction: TextInputAction.search,
                      onSubmitted: (value) {
                        logic.requestPlace();
                      },
                      // onChanged: (value) => state.text.value = value,
                    )),
                    GestureDetector(
                        onTap: () {
                          logic.searchController.text = '';
                          logic.searchText.value = '';
                        },
                        child: Obx(() => Visibility(
                            visible: logic.searchText.value.isNotEmpty,
                            child: WxAssets.images.icSearchDelete.image()))),
                  ],
                ),
              ),

              SizedBox(height: 15.w),

              // 场馆列表
              Expanded(
                  child: Obx(
                () => !logic.init.value
                    ? const Center(
                        child: CupertinoActivityIndicator(
                        color: Colors.white,
                      ))
                    : _list(context),
              )),

              // 底部确认按钮
              Container(
                padding: EdgeInsets.only(
                    top: 10,
                    left: 15,
                    right: 15,
                    bottom: ScreenUtil().bottomBarHeight + 8),
                child: InkWell(
                  onTap: () {
                    // 确认选择
                    if (logic.selectArenaName.value.isNotEmpty) {
                      logic.addressController.text =
                          logic.selectArenaName.value;
                      Navigator.pop(context);
                    } else {
                      // 提示用户选择场馆
                      WxLoading.showToast('请先选择一个场馆');
                    }
                  },
                  child: Obx(() => Container(
                        width: double.infinity,
                        height: 50.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          gradient: logic.selectArenaName.value.isNotEmpty
                              ? const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                          color: logic.selectArenaName.value.isEmpty
                              ? Colours.color3A3A42
                              : null,
                          borderRadius: BorderRadius.circular(25.r),
                        ),
                        child: Text(
                          '确认',
                          style: TextStyles.semiBold14.copyWith(
                            color: logic.selectArenaName.value.isNotEmpty
                                ? Colors.white
                                : Colours.color999999,
                          ),
                        ),
                      )),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _list(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(
            top: 0, bottom: MediaQuery.of(context).padding.bottom),
        itemCount: logic.arenaList.isEmpty
            ? 1
            : (logic.searchText.value.isEmpty
                ? logic.arenaList.length + 1
                : logic.arenaList.length),
        itemBuilder: (context, index) {
          if (logic.arenaList.isNotEmpty) {
            if (index == logic.arenaList.length) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  WxAssets.images.tips.image(width: 11.5.w, fit: BoxFit.fill),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    '距您15km以外的球馆请在顶部搜索栏进行搜索',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              );
            }
            return _modelItem(context, index);
          }
          return _empty();
        });
  }

  Widget _modelItem(BuildContext context, int index) {
    final model = logic.arenaList[index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        logic.selectArenaName.value = model.arenaName ?? '';
        logic.selectArenaId.value = model.arenaID.toString();
        cc.log("Selected venue: ${model.arenaName}");
      },
      child: Container(
        margin: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 15.w),
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
          color: Colours.color22222D,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(children: [
                CachedNetworkImage(
                  imageUrl: model.logo ?? '',
                  width: 86.w,
                  height: 86.w,
                  fit: BoxFit.cover,
                ),
                if ((model.type ?? 0) > 0)
                  Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 25.w,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(top: 4.w),
                        decoration: BoxDecoration(
                          image: DecorationImage(
                              image: WxAssets.images.icHlItemBottom.provider(),
                              fit: BoxFit.fill),
                        ),
                        child: Text(
                          '',
                          style: TextStyles.display10,
                        ),
                      )),
              ]),
            ),
            SizedBox(
              width: 15.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.arenaName ?? '',
                    style: TextStyles.semiBold,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: 14.w,
                  ),
                  Row(
                    children: [
                      WxAssets.images.icLocation
                          .image(width: 12.w, height: 12.w),
                      SizedBox(
                        width: 3.w,
                      ),
                      Expanded(
                        child: Text(
                          model.address ?? '',
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp, color: Colours.color5C5C6E),
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 14.w,
                  ),
                  Text(
                    "距你${model.distance}km",
                    style:
                        TextStyles.regular.copyWith(color: Colours.colorA44EFF),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 14.w,
            ),
            Obx(() => logic.selectArenaId.value == model.arenaID.toString()
                ? WxAssets.images.selectIcon.image()
                : WxAssets.images.unselectIcon.image())
          ],
        ),
      ),
    );
  }

  Widget _empty() {
    return Container(
      padding: EdgeInsets.only(top: 80.w),
      child: Column(
        children: [
          WxAssets.images.icSearchNo.image(width: 152.w, height: 155.w),
          SizedBox(
            height: 30.w,
          ),
          Text(
            "暂无搜索结果",
            style: TextStyles.display16,
          )
        ],
      ),
    );
  }
}
