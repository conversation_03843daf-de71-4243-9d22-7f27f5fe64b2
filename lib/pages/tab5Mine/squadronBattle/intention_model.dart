///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class IntentionModel {
/*
{
  "challengeId": "0",
  "contactPhone": "string",
  "createdTime": "string",
  "id": "0",
  "remark": "string",
  "teamId": "0",
  "teamName": "string",
  "userId": "0",
  "userName": "string"
} 
*/

  String? challengeId;
  String? contactPhone;
  String? createdTime;
  String? id;
  String? remark;
  String? teamId;
  String? teamName;
  String? userId;
  String? userName;

  IntentionModel({
    this.challengeId,
    this.contactPhone,
    this.createdTime,
    this.id,
    this.remark,
    this.teamId,
    this.teamName,
    this.userId,
    this.userName,
  });
  IntentionModel.fromJson(Map<String, dynamic> json) {
    challengeId = json['challengeId']?.toString();
    contactPhone = json['contactPhone']?.toString();
    createdTime = json['createdTime']?.toString();
    id = json['id']?.toString();
    remark = json['remark']?.toString();
    teamId = json['teamId']?.toString();
    teamName = json['teamName']?.toString();
    userId = json['userId']?.toString();
    userName = json['userName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['challengeId'] = challengeId;
    data['contactPhone'] = contactPhone;
    data['createdTime'] = createdTime;
    data['id'] = id;
    data['remark'] = remark;
    data['teamId'] = teamId;
    data['teamName'] = teamName;
    data['userId'] = userId;
    data['userName'] = userName;
    return data;
  }
}
