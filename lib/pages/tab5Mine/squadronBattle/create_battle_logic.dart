import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/my_matches_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/utils/utils.dart';

class CreateBattleLogic extends GetxController with WidgetsBindingObserver {
  bool isHalf = false;
  var challengeStrength = 1.obs; //约战强度：1新手局 2出汗局 3质量局 4强度局
  var challengeFormat = 1.obs; //约战赛制：1计时 2总分 3单节轮转 4其它
  var challengeCost = 1.obs; //约战费用：1AA 2其它
  var init = false.obs;
  var arenaList = <PlaceModel>[].obs;
  var selectArenaName = ''.obs;
  var selectArenaId = "".obs;
  // var selectArena = Rxn<PlaceModel>();
  var teamId = ''; //发起方球队ID
  MyBattleModel? editChallengeModel;
  TextEditingController matchTitleController = TextEditingController(); //约战标题
  TextEditingController myTeamController = TextEditingController(); //我的球队
  TextEditingController matchDateController = TextEditingController(); //约战日期
  TextEditingController startTimeController = TextEditingController(); //开始时间
  TextEditingController addressController = TextEditingController(); //约战地点
  TextEditingController contactController = TextEditingController(); //联系方式
  TextEditingController remarksController = TextEditingController(); //补充说明
  TextEditingController searchController = TextEditingController(); //球馆搜索框
  var searchText = ''.obs; //搜索框文本状态
  @override
  void onInit() {
    super.onInit();
    isHalf = (Get.arguments != null &&
        Get.arguments.containsKey('isHalf') &&
        Get.arguments['isHalf'] == true);
    if (Get.arguments != null && Get.arguments.containsKey('challengeModel')) {
      editChallengeModel = Get.arguments['challengeModel'];
      DateTime parsedDate = DateTime.parse(editChallengeModel?.matchTime ?? '');
      String formattedTime = DateFormat("HH:mm").format(parsedDate);
      String formattedDate = DateFormat("yyyy-MM-dd").format(parsedDate);
      isHalf = editChallengeModel?.challengeType == 2;
      matchTitleController.text = editChallengeModel?.challengeTitle ?? '';
      matchDateController.text = formattedDate;
      startTimeController.text = formattedTime;
      addressController.text = editChallengeModel?.arenaName ?? '';
      contactController.text = editChallengeModel?.phone ?? '';
      challengeCost.value = editChallengeModel?.challengeCost ?? 1;
      challengeFormat.value = editChallengeModel?.challengeFormat ?? 1;
      challengeStrength.value = editChallengeModel?.challengeStrength ?? 1;
      myTeamController.text = editChallengeModel?.teamName ?? '';
      teamId = editChallengeModel?.teamId ?? '';
      selectArenaName.value = editChallengeModel?.arenaName ?? '';
      selectArenaId.value = editChallengeModel?.arenaId ?? '';
      // selectArena.value =
      // remarksController.text = editChallengeModel?.remark ?? '';
    }
    // 添加搜索框文本监听器
    searchController.addListener(() {
      searchText.value = searchController.text;
    });
    requestPlace();
  }

  @override
  void onClose() {
    searchController.dispose();
    matchTitleController.dispose();
    myTeamController.dispose();
    matchDateController.dispose();
    startTimeController.dispose();
    addressController.dispose();
    contactController.dispose();
    remarksController.dispose();
    super.onClose();
  }

  Future<void> requestPlace() async {
    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    WxLoading.show();
    var map = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}'
    };
    var url = ApiUrl.vipRecommendList;
    if (searchController.text.isNotEmpty) {
      map['arenaName'] = searchController.text;
      url = ApiUrl.arenasSearchModel;
    }
    final res = await Api().get(url, queryParameters: map);
    init.value = true;
    cc.log('mesaage${res.data}');
    if (res.isSuccessful()) {
      arenaList.value =
          (res.data as List).map((e) => PlaceModel.fromJson(e)).toList();
    } else {
      arenaList.value = [];
      WxLoading.showToast(res.message);
    }
    WxLoading.dismiss();
  }

  Future<void> createChallenge() async {
    if (matchTitleController.text.isEmpty) {
      WxLoading.showToast('请输入约战标题');
      return;
    }
    if (!isHalf && teamId.isEmpty) {
      WxLoading.showToast('请选择我的球队');
      return;
    }
    if (matchDateController.text.isEmpty) {
      WxLoading.showToast('请选择约战日期');
      return;
    }
    if (startTimeController.text.isEmpty) {
      WxLoading.showToast('请选择开始时间');
      return;
    }
    if (addressController.text.isEmpty) {
      WxLoading.showToast('请选择约战地点');
      return;
    }
    if (contactController.text.isEmpty) {
      WxLoading.showToast('请输入联系方式');
      return;
    }
    if (!Utils.isPhoneNumber(contactController.text.trim())) {
      WxLoading.showToast('请输入正确的手机号码');
      return;
    }
    WxLoading.show();
    var timeStr = "${matchDateController.text} ${startTimeController.text}";
    DateTime dateTime = DateTime.parse(timeStr);
    var map = {
      'arenaId': selectArenaId.value,
      'challengeCost': challengeCost.value,
      'challengeFormat': challengeFormat.value,
      'challengeStrength': challengeStrength.value,
      'challengeType': isHalf ? 2 : 1, // 约战类型：1全场 2半场
      'challengeTitle': matchTitleController.text,
      'matchTime': DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime),
      'phone': contactController.text,
      'remark': remarksController.text,
      'teamId': teamId.isEmpty ? '0' : teamId,
    };
    cc.log('$map');
    var url = '';
    if (editChallengeModel != null) {
      url = ApiUrl.editChallenge;
      map['challengeId'] = editChallengeModel!.id ?? '';
    } else {
      url = ApiUrl.createChallenge;
    }
    final res = await Api().post(url, data: map);
    if (res.isSuccessful()) {
      if (editChallengeModel != null) {
        WxLoading.showToast('编辑成功');
      } else {
        WxLoading.showToast('发布成功');
      }
      //通知首页刷新数据
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.createSquadronBattle));
      AppPage.back(result: true);
    } else {
      WxLoading.showToast(res.message);
    }
    WxLoading.dismiss();
  }
}
