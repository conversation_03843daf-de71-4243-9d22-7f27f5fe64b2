import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../generated/l10n.dart';
import '../../../inappwebview/router.dart';
import '../../../network/api_url.dart';
import '../../../routes/app.dart';
import '../../../routes/route.dart';
import '../mine_item_view.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.about_us),
      ),
      body: Container(
        padding: const EdgeInsets.only(left: 20, right: 15),
        margin: const EdgeInsets.symmetric(horizontal: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colours.color191921,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            MineItemView(text: S.current.user_policy, onTap: _didUserPolicy),
            MineItemView(
                text: S.current.privacy_policy, onTap: _didPrivacyPolicy),
          ],
        ),
      ),
    );
  }

  void _didUserPolicy() async {
    WebviewRouter router = WebviewRouter(
        url: ApiUrl.userPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.user_policy);
    AppPage.to(Routes.webview, arguments: router);
  }

  void _didPrivacyPolicy() {
    WebviewRouter router = WebviewRouter(
        url: ApiUrl.privacyPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.privacy_policy);
    AppPage.to(Routes.webview, arguments: router);
  }
}
