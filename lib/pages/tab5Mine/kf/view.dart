import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/utils/utils.dart';

import '../../../generated/l10n.dart';

class KfPage extends StatelessWidget {
  const KfPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.contact_customer_service),
      ),
      body: Stack(
        children: [
          Positioned(
              left: 36.w,
              top: 55.w,
              child: WxAssets.images.icKfLogo
                  .image(width: 120.w, fit: BoxFit.fill)),
          Positioned(
              top: 155.w,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                children: [
                  Container(
                    width: 288.w,
                    height: 288.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: Colours.white,
                        borderRadius: BorderRadius.circular(18)),
                    child: WxAssets.images.icKfWx
                        .image(width: 234.w, fit: BoxFit.fill),
                  ),
                  SizedBox(
                    height: 30.w,
                  ),
                  Text(
                    '咨询时间：9:00-12:00  13:30-22:00',
                    style: TextStyles.regular
                        .copyWith(fontSize: 16.w, color: Colours.color5C5C6E),
                  ),
                  const Spacer(),
                  WxButton(
                    text: '保存二维码到相册',
                    textStyle: TextStyles.semiBold,
                    margin: EdgeInsets.symmetric(horizontal: 30.w),
                    linearGradient: GradientUtils.mainGradient,
                    height: 55.w,
                    borderRadius: BorderRadius.circular(27.5.w),
                    onPressed: () => Utils.downloadAndSaveToPhotoAlbum(
                        'https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app-release/contact/qr.jpeg'),
                  ),
                  SizedBox(
                    height: 49.w + MediaQuery.of(context).padding.bottom,
                  ),
                ],
              )),
          Positioned(
              right: 16.w,
              top: 47.w,
              child: WxAssets.images.icKfHint
                  .image(width: 180.w, fit: BoxFit.fill)),
        ],
      ),
    );
  }
}
