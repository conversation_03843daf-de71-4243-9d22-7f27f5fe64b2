import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/message_list_model.dart';

class MessageInfoLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  var isFrist = true.obs;
  var messageId = "".obs;
  var messageName = "".obs;
  var messageListModel = MessageListModel().obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('messageId')) {
      messageId.value = Get.arguments['messageId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('messageName')) {
      messageName.value = Get.arguments['messageName'];
    }

    getMessageInfo();
  }

  //获得球馆主页详情
  getMessageInfo() async {
    Map<String, dynamic> param = {
      'messageId': messageId.value,
    };
    log("getMessageInfo=${param}");
    var url = await ApiUrl.getMessageInfo(messageId.value);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      log("getMessageInfo=${jsonEncode(res.data)}");
      messageListModel.value = MessageListModel.fromJson(res.data);
      messageListModel.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    if (isFrist.value) {
      isFrist.value = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
