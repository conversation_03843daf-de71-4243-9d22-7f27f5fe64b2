import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_info/message_info_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class MessageInfoPage extends StatelessWidget {
  MessageInfoPage({super.key});

  final logic = Get.put(MessageInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Obx(() {
          return Text(logic.messageName.value);
        }),
      ),
      body: _createTeamWidget(context),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return logic.isFrist.value
          ? buildLoad()
          : logic.messageListModel.value.messageId == null
              ? myNoDataView(
                  context,
                  msg: S.current.No_data_available,
                  imagewidget: WxAssets.images.icGameNo
                      .image(width: 150.w, height: 150.w),
                )
              : _teamInfoWidget(context);
    });
  }

  Widget _teamInfoWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  logic.messageListModel.value.title ?? "",
                  maxLines: 20,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyles.bold.copyWith(
                      fontSize: 14.sp, color: Colours.white, height: 1.2),
                ),
                SizedBox(
                  height: 15.w,
                ),
                Row(
                  children: [
                    WxAssets.images.messageJiqi
                        .image(width: 26.w, height: 26.w),
                    SizedBox(
                      width: 6.w,
                    ),
                    Flexible(
                      child: Text(
                        logic.messageListModel.value.sendName ?? "",
                        style: TextStyles.regular
                            .copyWith(fontSize: 14.sp, color: Colours.white),
                      ),
                    ),
                    SizedBox(
                      width: 6.w,
                    ),
                    Text(
                      logic.messageListModel.value.sendTime ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.color5C5C6E,
                          height: 1.2),
                    ),
                  ],
                ),
                if ((logic.messageListModel.value.coverImg ?? "") != "")
                  SizedBox(
                    height: 15.w,
                  ),
                if ((logic.messageListModel.value.coverImg ?? "") != "")
                  MyImage(
                    logic.messageListModel.value.coverImg ?? "",
                    width: double.infinity,
                    errorImage: "error_image_width.png",
                    placeholderImage: "error_image_width.png",
                  ),
                SizedBox(
                  height: 20.w,
                ),
                Html(
                  data: logic.messageListModel.value.content ?? "",
                  style: {
                    // 设置全局文本颜色
                    "body": Style(
                      color: Colors.black, // 修改为黑色
                      fontSize: FontSize(16), // 设置基础字体大小
                      lineHeight: LineHeight(1.5), // 设置行高
                    ),

                    // 设置段落颜色
                    "p": Style(
                      color: Colors.white, // 将段落文本设为蓝色
                      fontSize: FontSize(14),
                      margin: Margins(bottom: Margin(12)), // 段落间距
                    ),
                    "table": Style(
                      backgroundColor:
                          const Color.fromARGB(0x50, 0xee, 0xee, 0xee),
                    ),
                    "th": Style(
                      padding: HtmlPaddings.all(6),
                      backgroundColor: Colors.grey,
                    ),
                    "td": Style(
                      padding: HtmlPaddings.all(6),
                      border:
                          const Border(bottom: BorderSide(color: Colors.grey)),
                    ),
                    'h5': Style(
                      maxLines: 2,
                      textOverflow: TextOverflow.ellipsis,
                    ),
                    'flutter': Style(
                      display: Display.block,
                      fontSize: FontSize(5, Unit.em),
                    ),
                    ".second-table": Style(
                      backgroundColor: Colors.transparent,
                    ),
                    ".second-table tr td:first-child": Style(
                      fontWeight: FontWeight.bold,
                      textAlign: TextAlign.end,
                    ),
                  },
                  extensions: [
                    TagWrapExtension(
                        tagsToWrap: {"table"},
                        builder: (child) {
                          return SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: child,
                          );
                        }),
                    TagExtension.inline(
                      tagsToExtend: {"bird"},
                      child: const TextSpan(text: "🐦"),
                    ),
                    TagExtension(
                      tagsToExtend: {"flutter"},
                      builder: (context) => CssBoxWidget(
                        style: context.styledElement!.style,
                        child: FlutterLogo(
                          style: context.attributes['horizontal'] != null
                              ? FlutterLogoStyle.horizontal
                              : FlutterLogoStyle.markOnly,
                          textColor: context.styledElement!.style.color!,
                          size: context.styledElement!.style.fontSize!.value,
                        ),
                      ),
                    ),
                    ImageExtension(
                      handleAssetImages: false,
                      handleDataImages: false,
                      networkDomains: {"flutter.dev"},
                      child: const FlutterLogo(size: 36),
                    ),
                    ImageExtension(
                      handleAssetImages: false,
                      handleDataImages: false,
                      networkDomains: {"mydomain.com"},
                      networkHeaders: {"Custom-Header": "some-value"},
                    ),
                  ],
                  onLinkTap: (url, _, __) {
                    debugPrint("Opening $url...");
                  },
                  onCssParseError: (css, messages) {
                    debugPrint("css that errored: $css");
                    debugPrint("error messages:");
                    for (var element in messages) {
                      debugPrint(element.toString());
                    }
                    return '';
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
