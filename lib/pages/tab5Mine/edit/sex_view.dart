import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../generated/l10n.dart';
import '../../../widgets/gradient_border_painter.dart';

class SexView extends StatelessWidget {
  final bool isSel;
  final bool isMan;
  const SexView({super.key, required this.isSel, required this.isMan});

  @override
  Widget build(BuildContext context) {
    final image = isMan ? WxAssets.images.icMan : WxAssets.images.icWoman;
    final text = isMan ? S.current.male2 : S.current.female2;
    final gradient = isSel ? null : const LinearGradient(colors: [Colors.transparent,Colors.transparent]);
    return CustomPaint(
      painter: GradientBorderPainter(strokeWidth: 2,gradient: gradient),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: Colours.color191921,
          borderRadius: BorderRadius.circular(16)
      ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            image.image(width: 38.w,height: 38.w),
            SizedBox(width: 15.w,),
            Text(text,style: TextStyles.display16,)
          ],
        ),
      ),
    );
  }
}
