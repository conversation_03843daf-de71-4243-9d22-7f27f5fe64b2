import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:ui_packages/generated/l10n.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:utils_package/utils_package.dart';
import '../../../generated/l10n.dart';
import '../../../network/api_url.dart';
import '../../../widgets/permission_dialog.dart';
import '../../login/models/user_info_model.dart';
import '../../login/user.dart';
import 'state.dart';
import 'package:intl/intl.dart';
import 'package:dio/dio.dart' as dio;

class MineEditLogic extends GetxController {
  final MineEditState state = MineEditState();
  String _date = DateFormat('yyyy-MM-dd').format(DateTime.now());
  final ImagePicker _picker = ImagePicker();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    final user = UserManager.instance.userInfo.value!;
    state.nickNameController.text = user.userName;
    state.birthday.value = user.birthday;
    state.avatar.value = user.avatar;
    state.isMan.value = user.gender == 1;
  }

  void showDatePicker(BuildContext context) {
    Locale currentLocale = Localizations.localeOf(context);
    final locale = currentLocale.languageCode == 'zh'
        ? DateTimePickerLocale.zh_cn
        : DateTimePickerLocale.en_us;
    FocusManager.instance.primaryFocus?.unfocus();
    DatePicker.showDatePicker(
      context,
      locale: locale, // 设置为中文
      pickerTheme: DateTimePickerTheme(
        backgroundColor: Colours.color191921,
        itemTextStyle: TextStyles.display16,
        itemHeight: 60,
        showTitle: true,
        title: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, top: 25),
          height: 50,
          decoration: const BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Text(
                  UiS.current.cancel,
                  style:
                      TextStyles.display14.copyWith(color: Colours.color9393A5),
                ),
              ),
              Text(
                S.current.select_birthday,
                style: TextStyles.titleSemiBold16,
              ),
              GestureDetector(
                onTap: () {
                  state.birthday.value = _date;
                  Navigator.pop(context);
                },
                child: Text(
                  S.current.save,
                  style:
                      TextStyles.display14.copyWith(color: Colours.colorA44EFF),
                ),
              ),
            ],
          ),
        ),
        titleHeight: 50,
      ),
      initialDateTime: state.birthday.value.isEmpty
          ? DateTime.now()
          : DateTime.parse(state.birthday.value),
      dateFormat:
          locale == DateTimePickerLocale.zh_cn ? "yyyy年-MM月-dd日" : "yyyy-MM-dd",
      // maxDateTime: DateTime.parse(MAX_DATETIME),
      // minDateTime: DateTime.parse(MIN_DATETIME),
      onConfirm: (dateTime, _) {},
      onChange: (dateTime, _) {
        _date = DateFormat('yyyy-MM-dd').format(dateTime);
      },
    );
  }

  // void showDatePicker(BuildContext context) {
  //   Locale currentLocale = Localizations.localeOf(context);
  //   final locale = currentLocale.languageCode == 'zh' ? picker.LocaleType.zh : picker.LocaleType.en;
  //   FocusManager.instance.primaryFocus?.unfocus();
  //   picker.DatePicker.showDatePicker(
  //     context,
  //     locale: locale, // 设置为中文
  //     theme: picker.DatePickerTheme(
  //       backgroundColor: Colours.color191921,
  //       itemStyle: TextStyles.display16,
  //       itemHeight: 36,
  //       // showTitle: true,
  //       // title: Container(
  //       //   padding: const EdgeInsets.only(left: 20, right: 20, top: 25),
  //       //   height: 50,
  //       //   decoration: const BoxDecoration(
  //       //       color: Colours.color191921,
  //       //       borderRadius: BorderRadius.only(
  //       //           topLeft: Radius.circular(16), topRight: Radius.circular(16))),
  //       //   child: Row(
  //       //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //       //     children: [
  //       //       GestureDetector(
  //       //         onTap: () => Navigator.pop(context),
  //       //         child: Text(
  //       //           UiS.current.cancel,
  //       //           style:
  //       //               TextStyles.display14.copyWith(color: Colours.color9393A5),
  //       //         ),
  //       //       ),
  //       //       Text(
  //       //         S.current.select_birthday,
  //       //         style: TextStyles.titleSemiBold16,
  //       //       ),
  //       //       GestureDetector(
  //       //         onTap: () {
  //       //           state.birthday.value = _date;
  //       //           Navigator.pop(context);
  //       //         },
  //       //         child: Text(
  //       //           S.current.save,
  //       //           style:
  //       //               TextStyles.display14.copyWith(color: Colours.colorA44EFF),
  //       //         ),
  //       //       ),
  //       //     ],
  //       //   ),
  //       // ),
  //       titleHeight: 50,
  //     ),
  //     currentTime: state.birthday.value.isEmpty
  //         ? DateTime.now()
  //         : DateTime.parse(state.birthday.value),
  //     // dateFormat: locale == DateTimePickerLocale.zh_cn ? "yyyy年-MM月-dd日" : "yyyy-MM-dd",
  //     // maxDateTime: DateTime.parse(MAX_DATETIME),
  //     // minDateTime: DateTime.parse(MIN_DATETIME),
  //     onConfirm: (dateTime) {
  //       state.birthday.value = _date;
  //       print("object-----------------------");
  //     },
  //     onChanged: (dateTime) {
  //       _date = DateFormat('yyyy-MM-dd').format(dateTime);
  //     },
  //   );
  // }
  //
  // void showTest(BuildContext context){
  //   picker.DatePicker.showDatePicker(context,
  //       showTitleActions: true,
  //       minTime: DateTime(2018, 3, 5),
  //       maxTime: DateTime(2019, 6, 7),
  //       theme: picker.DatePickerTheme(
  //           headerColor: Colors.orange,
  //           backgroundColor: Colors.blue,
  //           itemStyle: TextStyle(
  //               color: Colors.white,
  //               fontWeight: FontWeight.bold,
  //               fontSize: 18),
  //           doneStyle:
  //           TextStyle(color: Colors.white, fontSize: 16)),
  //       onChanged: (date) {
  //         print('change $date in time zone ' +
  //             date.timeZoneOffset.inHours.toString());
  //       }, onConfirm: (date) {
  //         print('confirm $date');
  //       }, currentTime: DateTime.now(), locale: picker.LocaleType.en);
  // }

  Future<void> getImage() async {
    try {
      var permission = await WxPermissionUtils.photo();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes: S.current.photo_hint,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        state.avatar.value = '';
        state.xFile.value = pickedFile;
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  Future<void> save() async {
    if (state.nickNameController.text.isEmpty) {
      WxLoading.showToast(S.current.username_cannot_be_empty);
      return;
    }
    WxLoading.show();
    var avatar = state.avatar.value;
    if (state.xFile.value != null) {
      File file = File(state.xFile.value!.path);
      String path = file.path;
      var fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
      String mimeType = mime(fileName) ?? '';
      String mimee = mimeType.split('/')[0];
      String type = mimeType.split('/')[1];
      dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
        "file": await dio.MultipartFile.fromFile(
          path,
          filename: fileName,
          contentType: MediaType(mimee, type),
        ),
      });
      var res = await Api().post(ApiUrl.upload,
          data: formData, headers: {"contentType": 'multipart/form-data'});
      if (res.isSuccessful()) {
        avatar = res.data['path'];
      }
    }
    final gender = state.isMan.value ? 1 : 2;
    var res = await Api().post(ApiUrl.editUser, data: {
      'avatar': avatar,
      'birthday': state.birthday.value,
      'gender': gender,
      'userName': state.nickNameController.text
    });
    if (res.isSuccessful()) {
      // UserManager.instance.userInfo.update((model) {
      //   model?.birthday = state.birthday.value;
      //   model?.avatar = avatar;
      //   model?.userName = state.nickNameController.text;
      //   model?.gender = gender;
      //
      // });
      UserManager.instance.setUserInfo(UserInfoModel.fromJson(res.data));
      WxLoading.showToast(S.current.modification_successful);
      AppPage.back();
    }
    WxLoading.dismiss();
  }
}
