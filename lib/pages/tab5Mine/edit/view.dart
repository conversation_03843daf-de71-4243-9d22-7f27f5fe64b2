import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab5Mine/edit/logic.dart';
import 'package:shoot_z/pages/tab5Mine/edit/sex_view.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../gen/assets.gen.dart';
import '../../../generated/l10n.dart';

class MineEditPage extends StatefulWidget {
  const MineEditPage({super.key});

  @override
  State<MineEditPage> createState() => _MineEditPageState();
}

class _MineEditPageState extends State<MineEditPage> {
  final logic = Get.put(MineEditLogic());
  final state = Get.find<MineEditLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: MyAppBar(
        title: Text(S.current.edit_information),
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 68, left: 20, right: 20),
        child: Column(
          children: [
            Stack(
              children: [
                GestureDetector(
                  onTap: () => logic.getImage(),
                  child: Obx(
                    () => Container(
                      width: 96.w, // 圆形图片的宽度
                      height: 96.w, // 圆形图片的高度
                      decoration: BoxDecoration(
                        shape: BoxShape.circle, // 圆形
                        border: Border.all(
                          color: Colors.white,
                          width: 1,
                        ),
                        color: Colors.grey,
                        image: DecorationImage(
                          image: state.avatar.isEmpty
                              ? (state.xFile.value == null
                                  ? WxAssets.images.icEidtHead.provider()
                                  : FileImage(File(state.xFile.value!.path)))
                              : CachedNetworkImageProvider(state.avatar.value),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                    bottom: 0,
                    right: 0,
                    child: WxAssets.images.icCamera
                        .image(width: 28.w, height: 28.w)),
              ],
            ),
            SizedBox(
              height: 70.w,
            ),
            SizedBox(
              height: 66.w,
              child: Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                          onTap: () => state.isMan.value = true,
                          child: Obx(() => SexView(
                                isSel: state.isMan.value,
                                isMan: true,
                              )))),
                  const SizedBox(
                    width: 25,
                  ),
                  Expanded(
                      child: GestureDetector(
                          onTap: () => state.isMan.value = false,
                          child: Obx(() => SexView(
                                isSel: !state.isMan.value,
                                isMan: false,
                              )))),
                ],
              ),
            ),
            SizedBox(
              height: 30.w,
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              height: 54.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(27.w),
              ),
              child: Row(
                children: [
                  Text(
                    S.current.nick_name,
                    style: TextStyles.display14
                        .copyWith(color: Colours.color5C5C6E),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Expanded(
                    child: TextField(
                      controller: state.nickNameController,
                      style: TextStyles.display14,
                      inputFormatters: [
                        LengthLimitingChineseFormatter(
                            maxCharacters: 14), // 限制 14 个字符（或 7 个汉字）
                      ],
                      decoration: const InputDecoration(
                        contentPadding:
                            EdgeInsets.only(top: 0, bottom: 0), //让文字垂直居中
                        border: InputBorder.none,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 26.w,
            ),
            GestureDetector(
              onTap: () {
                logic.showDatePicker(context);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                height: 54.w,
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(27.w),
                ),
                child: Row(
                  children: [
                    Text(
                      S.current.birthday,
                      style: TextStyles.display14
                          .copyWith(color: Colours.color5C5C6E),
                    ),
                    SizedBox(
                      width: 20.w,
                    ),
                    Expanded(
                      child: Obx(() => Text(
                            state.birthday.value,
                            style: TextStyles.display14,
                          )),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    WxAssets.images.icArrowDown.image(width: 14.w, height: 14.w)
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 40.w,
            ),
            WxButton(
              height: 54,
              text: S.current.save,
              borderRadius: BorderRadius.circular(27),
              linearGradient: GradientUtils.mainGradient,
              onPressed: () => logic.save(),
            )
          ],
        ),
      ),
    );
  }
}

/// 自定义输入限制器
class LengthLimitingChineseFormatter extends TextInputFormatter {
  final int maxCharacters;

  LengthLimitingChineseFormatter({required this.maxCharacters});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    int totalLength = 0;

    // 遍历新输入的字符串，计算总字符长度
    for (int i = 0; i < newValue.text.length; i++) {
      final char = newValue.text[i];
      totalLength += _isChinese(char) ? 2 : 1; // 汉字算 2 个字符，其他算 1 个字符

      if (totalLength > maxCharacters) {
        // 如果超过限制，返回旧的值
        return oldValue;
      }
    }

    return newValue;
  }

  /// 判断字符是否是中文
  bool _isChinese(String char) {
    final regex = RegExp(r'[\u4e00-\u9fa5]');
    return regex.hasMatch(char);
  }
}
