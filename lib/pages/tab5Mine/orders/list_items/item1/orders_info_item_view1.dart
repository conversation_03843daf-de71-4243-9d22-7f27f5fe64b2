import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/orders_info_model.dart';
import 'package:shoot_z/pages/tab5Mine/orders/list_items/item1/orders_info_item_logic1.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的球队 球馆主页->赛程列表
class OrdersItemPage1 extends StatelessWidget {
  OrdersItemPage1({super.key});

  final logic = Get.put(OrdersItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 480.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.no_order,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 105.w, height: 89.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(OrdersInfoModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 20.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colours.color191921),
      child: Column(
        children: [
          SizedBox(
            height: 48.w,
            width: double.infinity,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                WxAssets.images.imgOrder.image(width: 18.w, height: 18.w),
                SizedBox(
                  width: 10.w,
                ),
                Text(
                  S.current.match_report_unlock,
                  style: TextStyles.regular.copyWith(
                      fontSize: 14.sp,
                      color: Colours.white,
                      fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                //订单状态 0 未支付； 1 支付中； 2 支付成功； 3 支付失败
                Text(
                  item.orderStatus == 0
                      ? S.current.order_status1
                      : item.orderStatus == 1
                          ? S.current.order_status2
                          : item.orderStatus == 2
                              ? S.current.order_status3
                              : item.orderStatus == 3
                                  ? S.current.order_status4
                                  : "",
                  style: TextStyles.regular.copyWith(
                      fontSize: 14.sp,
                      color: Colours.color9393A5,
                      fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
            color: Colours.color2F2F3B,
          ),
          SizedBox(
            height: 11.w,
          ),
          rowText(S.current.order_title_tips1, item.orderTime ?? ""),
          rowText(S.current.order_title_tips2, item.matchTime ?? ""),
          rowText(S.current.order_title_tips3, item.arenaName ?? ""),
          rowText(S.current.order_title_tips4,
              "${item.leftTeamName ?? ""} vs ${item.rightTeamName ?? ""}"),
          rowText(S.current.order_title_tips5, item.unlockBy ?? ""),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(top: 5.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (item.orderStatus == 2)
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      if (item.playerId != "0" && item.playerId!.isNotEmpty) {
                        //球员报告
                        AppPage.to(Routes.playerReportPage, arguments: {
                          "teamId": item.teamId,
                          "playerId": item.playerId,
                          "matchId": item.matchId
                        });
                      } else if (item.teamId != "0" &&
                          item.teamId!.isNotEmpty) {
                        //球队报告
                        AppPage.to(Routes.teamReportPage, arguments: {
                          'teamId': item.leftTeamId,
                          'matchId': item.matchId
                        });
                      } else {
                        //赛事报告
                        AppPage.to(Routes.gameDetailsPage,
                            arguments: item.matchId);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          top: 6.w, bottom: 6.w, left: 12.w, right: 12.w),
                      decoration: BoxDecoration(
                          border: Border.all(
                              width: 1.w, color: Colours.color7732ED),
                          borderRadius: BorderRadius.circular(30.r)),
                      child: Text(
                        S.current.look_report,
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.colorA44EFF,
                        ),
                      ),
                    ),
                  ),
                SizedBox(
                  width: 15.w,
                ),
                RichText(
                  textAlign: TextAlign.end,
                  text: TextSpan(
                      text: S.current.out_of_pocket,
                      style: TextStyle(
                          color: Colours.color5C5C6E,
                          fontSize: 12.sp,
                          height: 1,
                          fontWeight: FontWeight.normal),
                      children: <InlineSpan>[
                        TextSpan(
                            text: " ￥",
                            style: TextStyle(
                                color: Colours.white,
                                fontSize: 14.sp,
                                height: 1,
                                fontWeight: FontWeight.normal)),
                        TextSpan(
                            text: "${item.amount ?? "0"}",
                            style: TextStyle(
                                color: Colours.white,
                                fontSize: 18.sp,
                                height: 1,
                                fontWeight: FontWeight.normal)),
                      ]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget rowText(
    String text,
    String content,
  ) {
    return Padding(
      padding: EdgeInsets.only(top: 9.w, bottom: 9.w),
      child: Row(
        children: [
          Text(
            text,
            style: TextStyles.regular
                .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: Text(
              content,
              style: TextStyles.regular
                  .copyWith(fontSize: 14.sp, color: Colours.color9393A5),
              maxLines: 2,
            ),
          )
        ],
      ),
    );
  }
}
