import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrdersLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var tabbarIndex = 0.obs;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }
}
