import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/orders/list_items/item1/orders_info_item_view1.dart';
import 'package:shoot_z/pages/tab5Mine/orders/list_items/item2/orders_info_item_view2.dart';
import 'package:shoot_z/pages/tab5Mine/orders/orders_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的订单
class OrdersPage extends StatelessWidget {
  OrdersPage({super.key});

  final logic = Get.put(OrdersLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_order),
      ),
      body: _TabWidget(context),
    );
  }

  Widget _TabWidget(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Container(
            width: double.infinity,
            height: 40.w,
            alignment: Alignment.centerLeft,
            color: Colours.bg_color,
            child: TabBar(
                controller: logic.tabController,
                unselectedLabelColor: Colours.color5C5C6E,
                unselectedLabelStyle: TextStyle(
                    fontSize: 16.sp,
                    color: Colours.color5C5C6E,
                    fontWeight: FontWeight.w600),
                labelColor: Colours.white,
                labelStyle: TextStyle(
                    fontSize: 18.sp,
                    color: Colours.white,
                    fontWeight: FontWeight.w600),
                isScrollable: false,
                // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                indicatorPadding: EdgeInsets.zero,
                dividerColor: Colors.transparent,
                dividerHeight: 0,
                padding: EdgeInsets.only(left: 40.w, right: 40.w),
                indicatorColor: Colors.transparent,
                //tabAlignment: TabAlignment.start,
                // indicatorSize: TabBarIndicatorSize.tab,
                // // dragStartBehavior: DragStartBehavior.start,
                // indicatorWeight: 1,
                // indicator: RoundUnderlineTabIndicator(
                //   borderSide: BorderSide(
                //     width: 3.5,
                //     color: Colors.white,
                //   ),
                // ),
                tabs: [
                  SizedBox(
                    width: 130.w,
                    height: 50.w,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        if (logic.tabbarIndex.value == 0)
                          Positioned(
                            right: 20,
                            bottom: 0,
                            child: WxAssets.images.imgCheckIn
                                .image(width: 52.w, height: 22.w),
                          ),
                        Positioned(
                            bottom: 10.w, child: Text(S.current.all_order)),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 130.w,
                    height: 50.w,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        if (logic.tabbarIndex.value == 1)
                          Positioned(
                            right: 20,
                            bottom: 0,
                            child: WxAssets.images.imgCheckIn
                                .image(width: 52.w, height: 22.w),
                          ),
                        Positioned(
                            bottom: 10.w, child: Text(S.current.refund_order)),
                      ],
                    ),
                  ),
                ]),
          ),
          SizedBox(
            height: 10.w,
          ),
          Expanded(
            child: TabBarView(controller: logic.tabController, children: [
              OrdersItemPage1(
                key: const Key("1"),
              ),
              OrdersItemPage2(
                key: const Key("2"),
              ),
            ]),
          ),
        ],
      );
    });
  }
}
