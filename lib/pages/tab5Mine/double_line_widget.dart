import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_packages/ui_packages.dart';


class DoubleLineWidget extends StatelessWidget {
  final String text;
  final String value;
  const DoubleLineWidget({super.key, required this.text, required this.value});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(text,style: TextStyles.titleBold22.copyWith(fontSize: 18.w),),
        SizedBox(height: 6.w,),
        Text(value,style: TextStyles.display12,),
      ],
    );
  }
}
