import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/videos_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';

class VideosLogic extends GetxController {
  final VideoController videoController = VideoController();
  var videosModel = VideosModel().obs; //播放信息
  var type = "0".obs; //0合并id  1片段id
  var videoId = "".obs;
  var matchId = "".obs;
  @override
  void onInit() {
    super.onInit();
    type.value = Get.arguments["type"]; //0合并id  1片段id
    videoId.value = Get.arguments["videoId"];
    matchId.value = Get.arguments["matchId"];
    getVideosInfo();
  }

  Future<void> getVideosInfo() async {
    Map<String, dynamic> param = {
      'matchId': matchId.value,
      if (type.value == "0") //0合并id  1片段id
        'videoMergeId': videoId.value,
      if (type.value == "1") //0合并id  1片段id
        'videoId': videoId.value
    };
    WxLoading.show();
    var url = await ApiUrl.getVideoInfo(matchId.value);
    var res = await Api().get(url, queryParameters: param);
    WxLoading.dismiss();
    log("res.data=${res.data}");
    if (res.isSuccessful()) {
      //log("res.data=${res.data}");
      videosModel.value = VideosModel.fromJson(res.data);
      if ((videosModel.value.videoPath ?? "").isNotEmpty) {
        videoController.setData(
            videoPath: videosModel.value.videoPath!,
            videoCover: 'error_image_width');
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
    videoController.dispose();
  }

  void share() async {
    MyShareH5.getShareH5(ShareHighlights(
        sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
        highlightId: videoId.value,
        type: "0"));
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(videosModel.value.videoPath ?? "");
  }

  void showDeleteDialog() {
    Get.dialog(CustomAlertDialog(
      title: S.current.confirm_deletion,
      content: S.current.video_removal_tips,
      onPressed: () async {
        AppPage.back();
        final res =
            await Api().delete("${ApiUrl.deleteHighlights}${videoId.value}");
        if (res.isSuccessful()) {
          AppPage.back(result: true);
        }
      },
    ));
  }
}
