import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/teams/videos/videos_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:ui_packages/ui_packages.dart';

class VideosPage extends StatefulWidget {
  const VideosPage({super.key});

  @override
  State<VideosPage> createState() => _HighlightsVideoPageState();
}

class _HighlightsVideoPageState extends State<VideosPage> {
  final VideosLogic logic = Get.put(VideosLogic());
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return SafeArea(
          child: Column(
            children: [
              _title(context),
              SizedBox(
                height: 47.w,
              ),
              _video(context),
              SizedBox(
                height: 47.w,
              ),
              _action(context),
            ],
          ),
        );
      }),
    );
  }

  Widget _title(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                  child: Text(
                logic.videosModel.value.arenaName ?? "",
                style: TextStyles.titleSemiBold16.copyWith(fontSize: 20.sp),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              )),
              SizedBox(
                width: 10.w,
              ),
              GestureDetector(
                  onTap: () => AppPage.back(),
                  child:
                      WxAssets.images.icClose.image(width: 20.w, height: 20.w)),
            ],
          ),
        ),
        Padding(
            padding: const EdgeInsets.only(top: 10, left: 20),
            child: Text(
              '${logic.videosModel.value.matchDate?.replaceAll("-", ".")} ${logic.videosModel.value.matchWeek} ${logic.videosModel.value.matchTime}',
              style: TextStyles.display14.copyWith(color: Colours.color5C5C6E),
            )),
      ],
    );
  }

  Widget _action(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 26),
      child: Wrap(
        spacing: 64.w,
        children: [
          GestureDetector(
            onTap: () => logic.share(),
            child: Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(23.w),
              ),
              child: WxAssets.images.icShare.image(width: 24.w, height: 24.w),
            ),
          ),
          GestureDetector(
            onTap: () => logic.downloadAndSaveVideo(),
            child: Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(23.w),
              ),
              child: WxAssets.images.icDownload.image(width: 22.w, height: 2.w),
            ),
          ),
          // GestureDetector(
          //   onTap: () => logic.showDeleteDialog(),
          //   child: Container(
          //     width: 46.w,
          //     height: 46.w,
          //     decoration: BoxDecoration(
          //       color: Colours.color191921,
          //       borderRadius: BorderRadius.circular(23.w),
          //     ),
          //     child: WxAssets.images.icDelete.image(width: 22.w, height: 22.w),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _video(BuildContext context) {
    return Expanded(
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              logic.videosModel.value.leftTeamName == ""
                  ? ""
                  : "${logic.videosModel.value.leftTeamName} vs ${logic.videosModel.value.rightTeamName}",
              style: TextStyles.display14.copyWith(
                  color: Colours.color9393A5,
                  fontWeight: FontWeight.w400,
                  fontSize: 16.sp),
              maxLines: 1,
            )),
        SizedBox(
          height: 20.w,
        ),
        SizedBox(
          width: double.infinity,
          height: ScreenUtil().screenWidth / 375 * 211,
          child: AspectRatio(
            aspectRatio: 375 / 211, // 宽高比
            child: VideoView(
              controller: logic.videoController,
            ),
          ),
        ),
        SizedBox(
          height: 20.w,
        ),
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              logic.type.value == "0"
                  ? ""
                  : logic.videosModel.value.shotType == 1
                      ? "两分"
                      : logic.videosModel.value.shotType == 2
                          ? "三分"
                          : logic.videosModel.value.shotType == 3
                              ? "罚球"
                              : logic.videosModel.value.rebound == true
                                  ? "篮板"
                                  : logic.videosModel.value.assist == true
                                      ? "助攻"
                                      : "",
              style: TextStyles.display14.copyWith(
                  color: Colours.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ))
      ]),
    );
  }
}
