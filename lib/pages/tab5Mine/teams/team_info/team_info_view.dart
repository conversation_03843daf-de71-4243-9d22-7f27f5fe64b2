// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item1/team_info_item_view1.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item2/team_info_item_view2.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item3/team_info_item_view3.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item4/team_info_item_view4.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item5/team_info_item_view5.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/team_info_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class TeamInfoPage extends StatelessWidget {
  TeamInfoPage({super.key});

  final logic = Get.put(TeamInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _createTeamWidget(context),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return (logic.dataFag["isFrist"] as bool) ||
              logic.teamHomeModel.value.teamId == ""
          ? Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                teamAppBar(context, S.current.team_homepage),
                Expanded(
                  child: (logic.dataFag["isFrist"] as bool)
                      ? buildLoad()
                      : myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 150.w),
                        ),
                ),
              ],
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                  _teamInfoWidget(context),
                  Expanded(child: _tabWidget(context)),
                ]);
    });
  }

  Widget _tabWidget(BuildContext context) {
    return Column(
      children: [
        Transform.translate(
          offset: Offset(0, -1.w),
          child: Container(
            width: double.infinity,
            height: 35.w,
            alignment: Alignment.centerLeft,
            color: Colours.bg_color,
            margin: EdgeInsets.only(left: 16.w),
            child: TabBar(
                controller: logic.tabController,
                unselectedLabelColor: Colours.color5C5C6E,
                unselectedLabelStyle: TextStyle(
                    fontSize: 18.sp,
                    color: Colours.color5C5C6E,
                    fontWeight: FontWeight.w600),
                labelColor: Colours.white,
                labelStyle: TextStyle(
                    fontSize: 20.sp,
                    color: Colours.white,
                    fontWeight: FontWeight.w600),
                isScrollable: true,
                // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                indicatorPadding: EdgeInsets.zero,
                dividerColor: Colors.transparent,
                dividerHeight: 0,
                labelPadding:
                    const EdgeInsets.symmetric(horizontal: 4.0), // 调整标签间的间距
                indicatorSize: TabBarIndicatorSize.label,
                padding: EdgeInsets.zero,
                indicatorColor: Colors.transparent,
                tabAlignment: TabAlignment.start,
                tabs: List.generate(logic.tabNameList.length, (index) {
                  return SizedBox(
                    width: 50.w,
                    height: 40.w,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        if (logic.tabbarIndex.value == index)
                          WxAssets.images.imgCheckIn2
                              .image(width: 19.w, height: 9.w),
                        Positioned(
                            bottom: 10.w,
                            child: ShaderMask(
                                shaderCallback: (bounds) =>
                                    const LinearGradient(
                                      colors: [
                                        Colours.colorFFF9DC,
                                        Colours.colorE4C8FF,
                                        Colours.colorE5F3FF,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ).createShader(bounds),
                                child: Text(
                                  logic.tabNameList[index],
                                  style: TextStyles.regular.copyWith(
                                    fontSize: logic.tabbarIndex.value == index
                                        ? 16.sp
                                        : 14.sp,
                                    color: logic.tabbarIndex.value == index
                                        ? Colours.white
                                        : Colours.color5C5C6E,
                                    fontWeight: logic.tabbarIndex.value == index
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ))),
                      ],
                    ),
                  );
                })),
          ),
        ),
        SizedBox(
          height: 10.w,
        ),
        Expanded(
          child: TabBarView(controller: logic.tabController, children: [
            const TeamInfoItemPage1(
              key: Key("1"),
            ),
            TeamInfoItemPage2(
              key: const Key("2"),
            ),
            TeamInfoItemPage3(
              key: const Key("3"),
            ),
            TeamInfoItemPage4(
              key: const Key("4"),
            ),
            TeamInfoItemPage5(
              key: const Key("5"),
            ),
          ]),
        ),
      ],
    );
  }

  Widget _teamInfoWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
          gradient: LinearGradient(
              colors: [Colours.color7732ED, Colours.colorA555EF])),
      child: Column(
        children: [
          teamAppBar(context, S.current.team_homepage),
          SizedBox(
            height: 5.w,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 20.w,
                        ),
                        MyImage(
                          logic.teamHomeModel.value.teamLogo ?? "",
                          width: 55.w,
                          height: 55.w,
                          radius: 28.r,
                          placeholderImage: "my_team_head4.png",
                          errorImage: "my_team_head4.png",
                        ),
                        SizedBox(
                          width: 15.w,
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    logic.teamHomeModel.value.teamName ?? "",
                                    style: TextStyles.regular.copyWith(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16.sp),
                                  ),
                                  //one_stars_icon
                                  Container(
                                    height: 40.w,
                                    margin: EdgeInsets.only(left: 20.w),
                                    alignment: Alignment.center,
                                    child: (logic.teamHomeModel.value.rankInfo?.rankScore ?? 0) >
                                            3000
                                        ? WxAssets.images.fiveStarsIcon.image(
                                            width: 24.w,
                                            height: 24.w,
                                            fit: BoxFit.fill)
                                        : (logic.teamHomeModel.value.rankInfo?.rankScore ?? 0) >
                                                2000
                                            ? WxAssets.images.fourStarsIcon.image(
                                                width: 24.w,
                                                height: 24.w,
                                                fit: BoxFit.fill)
                                            : (logic.teamHomeModel.value.rankInfo?.rankScore ?? 0) >
                                                    1500
                                                ? WxAssets.images.threeStarsIcon.image(
                                                    width: 24.w,
                                                    height: 24.w,
                                                    fit: BoxFit.fill)
                                                : (logic
                                                                .teamHomeModel
                                                                .value
                                                                .rankInfo
                                                                ?.rankScore ??
                                                            0) >
                                                        1200
                                                    ? WxAssets.images.twoStarsIcon.image(
                                                        width: 24.w,
                                                        height: 24.w,
                                                        fit: BoxFit.fill)
                                                    : WxAssets.images.oneStarsIcon.image(
                                                        width: 24.w,
                                                        height: 24.w,
                                                        fit: BoxFit.fill),
                                  ),
                                  // GestureDetector(
                                  //   behavior: HitTestBehavior.translucent,
                                  //   onTap: () => MyShareH5.getShareH5(
                                  //       ShareTeamHome(
                                  //           teamId: logic.teamId.value)),
                                  //   child: Container(
                                  //     width: 40.w,
                                  //     height: 40.w,
                                  //     alignment: Alignment.center,
                                  //     child: WxAssets.images.teamInfoQrcode
                                  //         .image(
                                  //             color: Colors.white,
                                  //             width: 16.w,
                                  //             height: 16.w,
                                  //             fit: BoxFit.fill),
                                  //   ),
                                  // ),
                                ],
                              ),
                              SizedBox(
                                height: 1.w,
                              ),
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  Clipboard.setData(ClipboardData(
                                          text: logic
                                                  .teamHomeModel.value.showId ??
                                              ""))
                                      .then((_) {
                                    // 可选：显示一条消息给用户，告知他们文本已复制
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content:
                                            Text(S.current.Copy_to_paste_board),
                                      ),
                                    );
                                  });
                                },
                                child: Row(
                                  children: [
                                    WxAssets.images.imgId
                                        .image(width: 24.w, height: 14.w),
                                    SizedBox(
                                      width: 2.w,
                                    ),
                                    Text(
                                      logic.teamHomeModel.value.showId ?? "",
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: Colours.white),
                                    ),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    WxAssets.images.imgCopy
                                        .image(width: 14.w, height: 14.w),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Container(
                      margin:
                          EdgeInsets.only(left: 19.w, right: 19.w, top: 15.w),
                      height: 30.w,
                      width: double.infinity,
                      child: Text(
                        "${logic.teamHomeModel.value.teamDesc!.isEmpty ? S.current.no_team_desc : logic.teamHomeModel.value.teamDesc}",
                        maxLines: 2,
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.colorD5B6F8),
                      ),
                    ),
                    SizedBox(
                      height: 8.w,
                    ),
                  ],
                ),
              ),

              //	integer申请状态 1 待审核 2已加入 3已拒绝 4 未加入
              if (logic.teamHomeModel.value.applyStatus == 1 ||
                  logic.teamHomeModel.value.applyStatus == 3 ||
                  logic.teamHomeModel.value.applyStatus == 4)
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    if (logic.teamHomeModel.value.applyStatus == 4) {
                      logic.postTeamApply();
                    }
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 7.w),
                    margin: EdgeInsets.only(right: 15.w, top: 14.w),
                    decoration: BoxDecoration(
                        color: logic.teamHomeModel.value.applyStatus == 3
                            ? Colors.red
                            : null,
                        border: logic.teamHomeModel.value.applyStatus == 3
                            ? null
                            : Border.all(width: 1.w, color: Colours.white),
                        borderRadius: BorderRadius.circular(20.r)),
                    child: Text(
                      logic.teamHomeModel.value.applyStatus == 1
                          ? "待审核"
                          : logic.teamHomeModel.value.applyStatus == 2
                              ? "已加入"
                              : logic.teamHomeModel.value.applyStatus == 3
                                  ? "已拒绝"
                                  : logic.teamHomeModel.value.applyStatus == 4
                                      ? "申请加入"
                                      : "",
                      style: TextStyles.display14.copyWith(
                          fontSize: 12.sp,
                          color: Colours.white,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                )
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: "\t\t\t\t${logic.teamHomeModel.value.memberCount}",
                    style: TextStyle(
                        color: Colours.white,
                        fontSize: 16.sp,
                        height: 1,
                        fontWeight: FontWeight.bold),
                    children: <InlineSpan>[
                      TextSpan(
                          text: "\t${S.current.players}\t\t\t\t",
                          style: TextStyle(
                              color: Colours.colorC396F5,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w600)),
                    ]),
              ),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: "${logic.teamHomeModel.value.matchCount}",
                    style: TextStyle(
                        color: Colours.white,
                        fontSize: 16.sp,
                        height: 1,
                        fontWeight: FontWeight.bold),
                    children: <InlineSpan>[
                      TextSpan(
                          text: "\t${S.current.session}",
                          style: TextStyle(
                              color: Colours.colorC396F5,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w600)),
                    ]),
              ),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: "${logic.teamHomeModel.value.winningStreak}",
                    style: TextStyle(
                        color: Colours.white,
                        fontSize: 16.sp,
                        height: 1,
                        fontWeight: FontWeight.bold),
                    children: <InlineSpan>[
                      TextSpan(
                          text: S.current.Winning_streak,
                          style: TextStyle(
                              color: Colours.white,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400)),
                      TextSpan(
                          text: "\t${S.current.trend}",
                          style: TextStyle(
                              color: Colours.colorC396F5,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400)),
                    ]),
              ),
            ],
          ),
          SizedBox(
            height: 15.w,
          ),
          Transform.translate(
            offset: Offset(0, 1.w),
            child: Container(
              width: double.infinity,
              height: 20.w,
              decoration: BoxDecoration(
                  color: Colours.bg_color,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r))),
            ),
          ),
        ],
      ),
    );
  }

  Container teamAppBar(context, String name) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        children: [
          SizedBox(
            width: 3.w,
          ),
          SizedBox(
            width: 50.w,
            child: IconButton(
                onPressed: () {
                  AppPage.back();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                  size: 20,
                )),
          ),
          Expanded(
            child: Center(
              child: Text(
                name,
                style: TextStyles.titleSemiBold16,
              ),
            ),
          ),
          logic.teamHomeModel.value.applyStatus == 2
              ? //已加入
              GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    showDateDialog(context);
                  },
                  child: SizedBox(
                    width: 53.w,
                    child: WxAssets.images.set.image(width: 20.w, height: 20.w),
                  ),
                )
              : SizedBox(
                  width: 53.w,
                ),
        ],
      ),
    );
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              SizedBox(
                height: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(logic.dialogDatalist.length, (index) {
                  return logic.teamHomeModel.value.leader != true &&
                          (index == 0 || index == 2 || index == 4)
                      ? const SizedBox()
                      : GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.getDialogOnClick(
                                (logic.dialogDatalist[index]["id"] ?? "0")
                                    .toString(),
                                index);
                          },
                          child: Container(
                            width: double.infinity,
                            height: 52.w,
                            padding: EdgeInsets.only(left: 20.w, right: 20.w),
                            decoration: BoxDecoration(
                                border:
                                    (index == logic.dialogDatalist.length - 1)
                                        ? null
                                        : Border(
                                            bottom: BorderSide(
                                                width: 1.w,
                                                color: Colours.color242424))),
                            alignment: Alignment.center,
                            child: Text(
                              (index == 5 &&
                                          (logic.teamMemberList.length) <= 1) &&
                                      logic.teamHomeModel.value.leader == true
                                  ? S.current.team_info_diolog_tips7
                                  : "${logic.dialogDatalist[index]["name"] ?? ""}",
                              style:
                                  TextStyles.regular.copyWith(fontSize: 16.sp),
                            ),
                          ),
                        );
                }),
              ),
              SizedBox(
                height: 47.w,
              ),
            ],
          ),
        );
      },
    );
  }
}
