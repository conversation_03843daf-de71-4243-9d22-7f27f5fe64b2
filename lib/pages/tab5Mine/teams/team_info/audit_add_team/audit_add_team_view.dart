import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/audit_apply_team_model.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/audit_add_team/audit_add_team_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 二级页面->我的球队列表 球队主页  入队审核列表
class AuditAddTeamPage extends StatelessWidget {
  AuditAddTeamPage({super.key});

  final logic = Get.put(AuditAddTeamLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.audit_apply_team),
      ),
      body: _listWidget(context),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          onLoading: () {
            logic.getdataList(
                isLoad: true, controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? myNoDataView(context,
                      msg: S.current.no_people_apply_team,
                      imagewidget: WxAssets.images.noDataPeople
                          .image(width: 180.w, height: 120.w),
                      height: 2,
                      margin: EdgeInsets.only(bottom: 140.w))
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(bottom: 40.w),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return _listItemWidget(logic.dataList[position]);
                      }));
    });
  }

  /// 构建列表项
  Widget _listItemWidget(AuditApplyTeamModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r), color: Colours.color191921),
      child: Row(
        children: [
          MyImage(
            item.avatar ?? "",
            width: 46.w,
            height: 46.w,
            radius: 23.r,
            placeholderImage: "my_team_head4.png",
            errorImage: "my_team_head4.png",
          ),
          SizedBox(
            width: 10.w,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "${item.userName}",
                  style: TextStyles.regular
                      .copyWith(fontSize: 16.sp, color: Colours.white),
                ),
                SizedBox(
                  height: 13.w,
                ),
                Text(
                  "${item.createdTime}",
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                ),
              ],
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //拒绝
              getMyDialog(
                S.current.apply_team_info1,
                S.current.sure,
                content: S.current.apply_team_info2,
                () {
                  AppPage.back();
                  logic.postTeamApplyAudit(item.id ?? 0, 3);
                },
                isShowClose: false,
                btnIsHorizontal: true,
                btnText2: S.current.cancel,
                onPressed2: () {
                  AppPage.back();
                },
              );
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 8.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(width: 1.w, color: Colours.white),
              ),
              child: Text(
                S.current.Refuse,
                style: TextStyles.regular
                    .copyWith(fontSize: 12.sp, color: Colours.white),
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //同意
              getMyDialog(
                S.current.apply_team_info1,
                S.current.sure,
                content: S.current.apply_team_info3,
                () {
                  AppPage.back();
                  logic.postTeamApplyAudit(item.id ?? 0, 2);
                },
                isShowClose: false,
                btnIsHorizontal: true,
                btnText2: S.current.cancel,
                onPressed2: () {
                  AppPage.back();
                },
              );
            },
            child: Container(
              margin: EdgeInsets.only(left: 10.w),
              padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 9.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                gradient: const LinearGradient(
                    colors: [Colours.color7B35ED, Colours.colorA253EF],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight),
              ),
              child: Text(
                S.current.agreed,
                style: TextStyles.regular
                    .copyWith(fontSize: 12.sp, color: Colours.white),
              ),
            ),
          )
        ],
      ),
    );
  }
}
