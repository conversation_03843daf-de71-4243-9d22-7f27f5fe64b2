import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/network/model/matches_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

class GameList2Item extends StatelessWidget {
  final MatchesModel item;
  const GameList2Item({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final status =
        item.status == 0 ? "未开始" : (item.status == 1 ? "比赛中" : "已结束");
    final statusColor = item.status == 1 ? Colours.colorA44EFF : Colours.white;
    final markIcon = item.markStatus == 0
        ? WxAssets.images.icDfx
        : (item.markStatus == 1
            ? WxAssets.images.icFxz
            : WxAssets.images.icYsc);
    final mark =
        item.markStatus == 0 ? "待分析" : (item.markStatus == 1 ? "分析中" : "已生成");
    final markColor =
        item.markStatus == 2 ? Colours.white : Colours.color6F6F84;
    return GestureDetector(
      onTap: () => AppPage.to(Routes.gameDetailsPage, arguments: item.matchId),
      child: Container(
        padding:
            EdgeInsets.only(left: 15.w, right: 15.w, top: 12.w, bottom: 13.w),
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(8.w),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  "${(item.matchDateStr?.length ?? 0) >= 10 ? item.matchDateStr?.substring(5) : item.matchDateStr} ${item.matchWeekStr} ${item.matchTimeStr}",
                  style: TextStyles.display12,
                ),
                const Expanded(child: SizedBox.shrink()),
                Text(
                  item.courts?.join('、') ?? "",
                  style: TextStyles.display12,
                ),
              ],
            ),
            SizedBox(
              height: 15.w,
            ),
            IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Column(
                          children: [
                            Row(
                              children: [
                                MyImage(
                                  item.leftTeamLogo ?? "",
                                  width: 22.w,
                                  height: 22.w,
                                  radius: 11.r,
                                  placeholderImage: "my_team_head4.png",
                                  errorImage: "my_team_head4.png",
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Text(
                                  item.leftTeamName ?? "",
                                  style: TextStyles.display14,
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Expanded(
                                    child: Text(
                                  '${item.leftTeamScore ?? ""}',
                                  style: TextStyles.titleMedium18,
                                  textAlign: TextAlign.right,
                                )),
                                SizedBox(
                                  width: 23.w,
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 15.w,
                            ),
                            Row(
                              children: [
                                MyImage(
                                  item.rightTeamLogo ?? "",
                                  width: 22.w,
                                  height: 22.w,
                                  radius: 11.r,
                                  placeholderImage: "my_team_head4.png",
                                  errorImage: "my_team_head4.png",
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Text(
                                  item.rightTeamName ?? "",
                                  style: TextStyles.display14,
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Expanded(
                                    child: Text(
                                  '${item.rightTeamScore ?? ""}',
                                  style: TextStyles.titleMedium18,
                                  textAlign: TextAlign.right,
                                )),
                                SizedBox(
                                  width: 23.w,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 1,
                    height: double.infinity,
                    color: Colours.color2F2F3B,
                  ),
                  Container(
                    width: 87.w,
                    height: double.infinity,
                    alignment: Alignment.center,
                    child: item.status == 0
                        ? Text(
                            status,
                            style: TextStyles.titleSemiBold16,
                          )
                        : Column(
                            children: [
                              SizedBox(
                                height: 6.w,
                              ),
                              Text(
                                status,
                                style: TextStyles.titleSemiBold16
                                    .copyWith(color: statusColor),
                              ),
                              SizedBox(
                                height: 12.w,
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 6.w),
                                width: 56.w,
                                height: 20.w,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius: BorderRadius.circular(3.w),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    markIcon.image(width: 10.w, height: 10.w),
                                    Text(
                                      mark,
                                      style: TextStyles.display12.copyWith(
                                          fontSize: 10.w, color: markColor),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
