import 'dart:io';
import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_home_model.dart';
import 'package:shoot_z/network/model/team_player_rank_model.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/ImageDotPainter.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:dio/dio.dart' as dio;
import 'package:utils_package/utils_package.dart';

class TeamInfoItemLogic1 extends GetxController {
  RefreshController refreshController1 =
      RefreshController(initialRefresh: false);
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);
  RefreshController refreshController3 =
      RefreshController(initialRefresh: false);
  var teamHomeModel = TeamHomeModel().obs;
  final ImagePicker _picker = ImagePicker();
  var isFrist = true.obs;
  var dataFag = {
    "isFrist1": true,
    "page1": 1,
    "isFrist2": true,
    "page2": 1,
    "isFrist3": true,
    "page3": 1,
  }.obs;
  var teamId = "".obs;
  var teamRankTitle = <String>[
    S.current.Player_scoring_list,
    S.current.Team_rebounding_list,
    S.current.Team_assist_list
  ];
  List<String?>? get allUrls =>
      teamHomeModel.value.teamPhotoList?.map((p) => p?.url).toList();

  var teamRankIndex = 0.obs;
  var teamMemberList1 = <TeamPlayerRankModel>[].obs;
  var teamMemberList2 = <TeamPlayerRankModel>[].obs;
  var teamMemberList3 = <TeamPlayerRankModel>[].obs;
  var lineChartData = LineChartData().obs;
  var _selectedPointIndex = 555.obs;
  @override
  void onInit() {
    super.onInit();
    teamId.value = Get.arguments["teamId"];
  }

  @override
  void onReady() {
    super.onReady();
    getTeamMembers(1);
  }

  Future<void> getImage() async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes: S.current.photo_hint2,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        File file = File(pickedFile.path);
        String path = file.path;
        cropImage(path);
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  Future<void> getCarmer() async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(const PermissionDialog(
          text: "开启相机权限",
          contentDes: "请前往系统设置开启相机权限，以设置球队合照",
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        File file = File(pickedFile.path);
        String path = file.path;
        cropImage(path);
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  Future<void> cropImage(String path2) async {
    if (path2 != "") {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: path2,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        aspectRatio: const CropAspectRatio(ratioX: 16, ratioY: 9),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '裁剪图片',
            toolbarColor: Colours.color191921, // 工具栏背景色
            toolbarWidgetColor: Colors.white, // 工具栏文字和图标颜色
            statusBarColor: Colors.black, // 状态栏背景色为黑色
            backgroundColor: Colors.black, // 裁剪区域背景色
            //    activeControlsWidgetColor: Colors.white, // 控制按钮颜色
            cropFrameColor: Colors.white, // 裁剪框颜色
            cropGridColor: Colors.white.withOpacity(0.5), // 网格线颜色
            hideBottomControls: true, // 隐藏底部控制栏，包括旋转按钮
            lockAspectRatio: true, // 必须为true
            initAspectRatio: CropAspectRatioPreset.ratio16x9, // 设置初始比例
            aspectRatioPresets: [CropAspectRatioPreset.ratio16x9], // 限制可选比例
          ),
          IOSUiSettings(
            title: '裁剪图片',
            // rectX: 0,
            // rectY: 0,
            embedInNavigationController: true, // 启用导航控制器
            hidesNavigationBar: true, // 不隐藏导航栏
            // rectWidth: ScreenUtil().screenWidth,
            // rectHeight: ScreenUtil().screenWidth * 9 / 16,
            resetButtonHidden: true, // 隐藏重置按钮
            rotateButtonsHidden: true, // 隐藏旋转按钮
            resetAspectRatioEnabled: false, // 禁止重置比例
            aspectRatioLockEnabled: true, // 锁定宽高比
            aspectRatioLockDimensionSwapEnabled: false, // 禁用维度交换
            aspectRatioPickerButtonHidden: true, // 隐藏比例选择按钮
            // 强制使用横向比例
            aspectRatioPresets: [CropAspectRatioPreset.ratio16x9],
          ),
          WebUiSettings(
            context: Get.context!,
            presentStyle: WebPresentStyle.dialog,
            size: const CropperSize(
              width: 520,
              height: 520,
            ),
          ),
        ],
      );
      if (croppedFile != null) {
        var fileName = croppedFile.path.substring(
            croppedFile.path.lastIndexOf("/") + 1, croppedFile.path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            croppedFile.path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        WxLoading.show();
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        WxLoading.dismiss();
        if (res.isSuccessful()) {
          updateTeamPhoto(res.data['path']);
        }
      }
    }
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              SizedBox(
                height: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      Get.back();
                      getCarmer();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "拍照",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                      getImage();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "相册",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      alignment: Alignment.center,
                      child: Text(
                        "取消",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 47.w,
              ),
            ],
          ),
        );
      },
    );
  }

  //上传合照
  updateTeamPhoto(var path) async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
      'path': path,
    };
    WxLoading.show();
    var url = await ApiUrl.getTeamIdPhotoUrl(teamId.value);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.modification_successful);
      BusUtils.instance.fire(EventAction(key: EventBusKey.updateTeamPhoto));
    } else {
      WxLoading.showToast(res.message);
    }
  }

  setTeamHomeModel(teamHomeModel1) {
    teamHomeModel.value = teamHomeModel1;
    teamHomeModel.value.scoreHistory;

    // 计算 Y 轴的最小值和最大值
    double minY = 0;
    double maxY = 100; // 默认值

    if (teamHomeModel.value.scoreHistory != null &&
        teamHomeModel.value.scoreHistory!.isNotEmpty) {
      // 找到最小值和最大值
      minY = teamHomeModel.value.scoreHistory!
          .map((e) => e?.score?.toDouble() ?? 0)
          .reduce(min);

      maxY = teamHomeModel.value.scoreHistory!
          .map((e) => e?.score?.toDouble() ?? 0)
          .reduce(max);

      // 添加一些边距，使图表看起来更舒适
      final padding = (maxY - minY) * 0.1;
      minY = (minY - padding).clamp(minY - padding, minY);
      maxY = (maxY + padding).clamp(maxY, maxY + padding);
      // 确保最小值和最大值不同（避免除零错误）
      if (minY == maxY) {
        maxY = minY + 10;
      }
    }
    // 计算数据范围
    final range2 = maxY - minY;
    if (range2 <= 50) {
      minY = floorToNearest(minY, 10);
      maxY = ceilToNearest(maxY, 10);
    } else if (range2 <= 100) {
      minY = floorToNearest(minY, 20);
      maxY = ceilToNearest(maxY, 20);
    } else if (range2 <= 500) {
      minY = floorToNearest(minY, 100);
      maxY = ceilToNearest(maxY, 100);
    } else if (range2 <= 1000) {
      minY = floorToNearest(minY, 200);
      maxY = ceilToNearest(maxY, 200);
    } else if (range2 <= 1500) {
      minY = floorToNearest(minY, 300);
      maxY = ceilToNearest(maxY, 300);
    } else if (range2 <= 2000) {
      minY = floorToNearest(minY, 400);
      maxY = ceilToNearest(maxY, 400);
    } else if (range2 <= 2500) {
      minY = floorToNearest(minY, 500);
      maxY = ceilToNearest(maxY, 500);
    } else if (range2 <= 5000) {
      minY = floorToNearest(minY, 1000);
      maxY = ceilToNearest(maxY, 1000);
    } else {
      minY = floorToNearest(minY, 100);
      maxY = ceilToNearest(maxY, 100);
    }
    // 计算数据范围
    final range = maxY - minY;

    // 根据数据范围设置间隔
    double yInterval;
    if (range <= 50) {
      yInterval = 10;
    } else if (range <= 100) {
      yInterval = 20;
    } else if (range <= 500) {
      yInterval = 100;
    } else if (range <= 1000) {
      yInterval = 200;
    } else if (range <= 1500) {
      yInterval = 300;
    } else if (range <= 2000) {
      yInterval = 400;
    } else if (range <= 2500) {
      yInterval = 500;
    } else if (range <= 5000) {
      yInterval = 1000;
    } else {
      // 对于大于500的范围，使用智能计算
      yInterval = _calculateNiceInterval(minY, maxY);
    }

    // 计算网格位置
    List<double> gridPositions = _calculateGridPositions(minY, maxY, yInterval);

    // 确保包含最小值和最大值
    if (!gridPositions.contains(minY)) gridPositions.insert(0, minY);
    if (!gridPositions.contains(maxY)) gridPositions.add(maxY);
    gridPositions.sort();
    // ccc.log(
    //     "LineChartData2 minY=$minY-maxY=$maxY-yInterval=$yInterval-${(teamHomeModel.value.scoreHistory?.length ?? 0) < 10 ? 1 : (teamHomeModel.value.scoreHistory?.length ?? 0) < 20 ? 2 : (teamHomeModel.value.scoreHistory?.length ?? 0) < 30 ? 3 : (teamHomeModel.value.scoreHistory?.length ?? 0) % 10}");

    lineChartData.value = LineChartData(
      minY: minY, // 设置 Y 轴最小值
      maxY: maxY, // 设置 Y 轴最大值
      gridData: FlGridData(
        show: true,
        drawHorizontalLine: true,
        drawVerticalLine: false, //画竖线
        horizontalInterval: yInterval, // 关键：设置自定义间隔
        getDrawingHorizontalLine: (value) {
          // 只在网格位置绘制网格线
          // if (gridPositions.contains(value)) {
          //   return FlLine(
          //     color: Colors.grey.withOpacity(0.3),
          //     strokeWidth: 1,
          //   );
          // }
          // return FlLine(color: Colors.transparent);
          return FlLine(
            color: Colors.grey.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: (teamHomeModel.value.scoreHistory?.length ?? 0) < 10
                ? 1
                : (teamHomeModel.value.scoreHistory?.length ?? 0) < 20
                    ? 2
                    : (teamHomeModel.value.scoreHistory?.length ?? 0) < 30
                        ? 3
                        : (teamHomeModel.value.scoreHistory?.length ?? 0) % 10,
            reservedSize: 30.w,
            getTitlesWidget: (value, meta) {
              // 将X轴的数值索引转换为日期
              final date =
                  teamHomeModel.value.scoreHistory?[value.toInt()]?.date ?? "";
              return Padding(
                padding: EdgeInsets.only(top: 12.w),
                child: Text(
                  date, //.toDotFormat()
                  style: TextStyles.regular.copyWith(fontSize: 10.sp),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 50.w,
            interval: yInterval,
            getTitlesWidget: (value, meta) {
              // 只在网格位置显示标签
              if (!gridPositions.contains(value)) {
                return SizedBox.shrink();
              }

              // 确保显示整数
              return Padding(
                padding: EdgeInsets.only(right: 5.w),
                child: Text(
                  value.toInt().toString(),
                  style: TextStyles.regular.copyWith(fontSize: 10.sp),
                  textAlign: TextAlign.right,
                ),
              );
            },
          ),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xff37434d), width: 1),
      ),
      lineTouchData: LineTouchData(
        handleBuiltInTouches: true,
        getTouchedSpotIndicator: defaultTouchedIndicators,
        touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colours.color15151D,
            tooltipBorder:
                const BorderSide(color: Colours.color2F2F3B, width: 1),
            tooltipMargin: 16,
            tooltipPadding:
                const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            getTooltipItems: defaultLineTooltipItem),
        touchCallback: (event, response) {
          if (event is FlTapUpEvent) {
            _selectedPointIndex.value =
                response?.lineBarSpots?.first.spotIndex ?? 0;
          }
        },
      ),
      lineBarsData: [
        LineChartBarData(
          spots: teamHomeModel.value.scoreHistory!.asMap().entries.map((entry) {
            // 将数据点转换为FLSpot(X索引, Y值)
            final index = entry.key;
            final point = entry.value;
            return FlSpot(index.toDouble(), (point!.score ?? 0).toDouble());
          }).toList(),
          isCurved: true, // 使用曲线
          color: Colours.color922BFF,
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true, //chart_point
            getDotPainter: (spot, percent, barData, index) {
              // 为选中点创建特殊样式
              if (index == _selectedPointIndex.value) {
                return PreloadedImageDotPainter(
                  assetPath: 'assets/images/chart_point.png',
                  sizeScale: 1.0, // 可选：缩放图像尺寸
                  fallbackColor: Colours.color922BFF, // 可选：备用颜色
                );
              }
              // 默认点样式
              return FlDotCirclePainter(
                color: Colours.color922BFF,
                radius: 4,
              );
            },
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                Colours.color922BFF.withOpacity(0.3),
                Colours.color922BFF.withOpacity(0.0)
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ],
    );

    teamHomeModel.refresh();
    if (isFrist.value) {
      isFrist.value = false;
      isFrist.refresh();
    }
  }

// 辅助函数：计算网格位置
  List<double> _calculateGridPositions(
      double minY, double maxY, double interval) {
    List<double> positions = [];

    // 找到第一个网格位置
    double firstGrid = (minY / interval).ceil() * interval;

    // 添加网格位置
    double current = firstGrid;
    while (current <= maxY) {
      positions.add(current);
      current += interval;
    }

    return positions;
  }

// 向下取整到指定基数
  double floorToNearest(double value, double base) {
    return (value / base).floor() * base;
  }

// 向上取整到指定基数
  double ceilToNearest(double value, double base) {
    return (value / base).ceil() * base;
  }

// 辅助函数：智能计算间隔（用于范围大于500的情况）
  double _calculateNiceInterval(double minY, double maxY) {
    final range = maxY - minY;
    if (range <= 0) return 1;

    // 计算理想的分段数
    int targetTicks = 5;

    // 计算初始间隔
    double interval = range / targetTicks;

    // 将间隔调整为"美观"的数字
    final double exponent = (log(interval) / ln10).floorToDouble();
    final double fraction = interval / pow(10, exponent);

    double niceFraction;
    if (fraction <= 1.0) {
      niceFraction = 1.0;
    } else if (fraction <= 2.0) {
      niceFraction = 2.0;
    } else if (fraction <= 5.0) {
      niceFraction = 5.0;
    } else {
      niceFraction = 10.0;
    }

    return niceFraction * pow(10, exponent);
  }
//   setTeamHomeModel(teamHomeModel1) {
//     teamHomeModel.value = teamHomeModel1;
//     teamHomeModel.value.scoreHistory;

// // 计算 Y 轴的最小值和最大值
//     double minY = 0;
//     double maxY = 100; // 默认值

//     if (teamHomeModel.value.scoreHistory != null &&
//         teamHomeModel.value.scoreHistory!.isNotEmpty) {
//       // 找到最小值和最大值
//       minY = teamHomeModel.value.scoreHistory!
//           .map((e) => e?.score?.toDouble() ?? 0)
//           .reduce(min);

//       maxY = teamHomeModel.value.scoreHistory!
//           .map((e) => e?.score?.toDouble() ?? 0)
//           .reduce(max);

//       // 添加一些边距，使图表看起来更舒适
//       final padding = (maxY - minY) * 0.1;
//       minY = (minY - padding).clamp(minY - padding, minY);
//       maxY = (maxY + padding).clamp(maxY, maxY + padding);

//       // 确保最小值和最大值不同（避免除零错误）
//       if (minY == maxY) {
//         maxY = minY + 10;
//       }
//     }

//     // dataPoints.addAll(teamHomeModel.value.scoreHistory!!);
//     lineChartData.value = LineChartData(
//       minY: minY, // 设置 Y 轴最小值
//       maxY: maxY, // 设置 Y 轴最大值
//       gridData: FlGridData(
//         show: true,
//         drawVerticalLine: false, //画竖线
//         getDrawingHorizontalLine: (value) => FlLine(
//           color: Colors.grey.withOpacity(0.3),
//           strokeWidth: 1,
//         ),
//             // 添加垂直网格线
//         // drawHorizontalLine: true,
//         // horizontalInterval: (maxY - minY) / 5, // 将 Y 轴分成 5 等份
//       ),

//       titlesData: FlTitlesData(
//         show: true,
//         bottomTitles: AxisTitles(
//           // axisNameSize:
//           //     (teamHomeModel.value.scoreHistory?.length ?? 0).toDouble(),
//           sideTitles: SideTitles(
//             showTitles: true,
//             interval: (teamHomeModel.value.scoreHistory?.length ?? 0) < 10
//                 ? 1
//                 : (teamHomeModel.value.scoreHistory?.length ?? 0) < 20
//                     ? 2
//                     : (teamHomeModel.value.scoreHistory?.length ?? 0) < 30
//                         ? 3
//                         : (teamHomeModel.value.scoreHistory?.length ?? 0) % 10,
//             reservedSize: 30.w,
//             getTitlesWidget: (value, meta) {
//               // 将X轴的数值索引转换为日期
//               final date =
//                   teamHomeModel.value.scoreHistory?[value.toInt()]?.date ?? "";
//               return Padding(
//                 padding: EdgeInsets.only(top: 12.w),
//                 child: Text(
//                   date, //.toDotFormat()
//                   style: TextStyles.regular.copyWith(fontSize: 10.sp),
//                 ),
//               );
//             },
//           ),
//         ),
//         leftTitles: AxisTitles(
//           sideTitles: SideTitles(
//             showTitles: true,
//             reservedSize: 50.w,
//             interval: (maxY - minY) / 4, // 将 Y 轴分成 5 等份
//             getTitlesWidget: (value, meta) {
//               // 显示 Y 轴数值，根据值的大小动态调整格式
//               String displayValue;
//               if (value.abs() >= 1000) {
//                 displayValue = '${(value / 1000).toStringAsFixed(1)}k';
//               } else if (value.abs() >= 100) {
//                 displayValue = value.toInt().toString();
//               } else {
//                 displayValue = value.toStringAsFixed(1);
//               }

//               return Padding(
//                 padding: EdgeInsets.only(right: 5.w),
//                 child: Text(
//                   displayValue,
//                   style: TextStyles.regular.copyWith(fontSize: 10.sp),
//                   textAlign: TextAlign.right,
//                 ),
//               );
//             },
//           ),
//         ),
//         // leftTitles: AxisTitles(
//         //   sideTitles: SideTitles(
//         //     showTitles: true,
//         //     reservedSize: 40.w,
//         //     getTitlesWidget: (value, meta) {
//         //       // 显示Y轴数值
//         //       return Padding(
//         //         padding: EdgeInsets.only(right: 5.w),
//         //         child: Text(
//         //           value.toInt().toString(),
//         //           style: TextStyles.regular.copyWith(fontSize: 10.sp),
//         //           textAlign: TextAlign.right,
//         //         ),
//         //       );
//         //     },
//         //   ),
//         // ),
//         topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
//         rightTitles:
//             const AxisTitles(sideTitles: SideTitles(showTitles: false)),
//       ),
//       borderData: FlBorderData(
//         show: true,
//         border: Border.all(color: const Color(0xff37434d), width: 1),
//       ),
//       lineTouchData: LineTouchData(
//         handleBuiltInTouches: true,
//         getTouchedSpotIndicator: defaultTouchedIndicators,
//         touchTooltipData: LineTouchTooltipData(
//             getTooltipColor: (touchedSpot) => Colours.color15151D,
//             tooltipBorder:
//                 const BorderSide(color: Colours.color2F2F3B, width: 1),
//             tooltipMargin: 16,
//             tooltipPadding:
//                 const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
//             getTooltipItems: defaultLineTooltipItem),
//         touchCallback: (event, response) {
//           if (event is FlTapUpEvent) {
//             _selectedPointIndex.value =
//                 response?.lineBarSpots?.first.spotIndex ?? 0;
//           }
//         },
//       ),
//       lineBarsData: [
//         LineChartBarData(
//           spots: teamHomeModel.value.scoreHistory!.asMap().entries.map((entry) {
//             // 将数据点转换为FLSpot(X索引, Y值)
//             final index = entry.key;
//             final point = entry.value;
//             return FlSpot(index.toDouble(), (point!.score ?? 0).toDouble());
//           }).toList(),
//           isCurved: true, // 使用曲线
//           color: Colours.color922BFF,
//           barWidth: 2,
//           isStrokeCapRound: true,
//           dotData: FlDotData(
//             show: true, //chart_point
//             getDotPainter: (spot, percent, barData, index) {
//               // 为选中点创建特殊样式
//               if (index == _selectedPointIndex.value) {
//                 return PreloadedImageDotPainter(
//                   assetPath: 'assets/images/chart_point.png',
//                   sizeScale: 1.0, // 可选：缩放图像尺寸
//                   fallbackColor: Colours.color922BFF, // 可选：备用颜色
//                 );
//               }
//               // 默认点样式
//               return FlDotCirclePainter(
//                 color: Colours.color922BFF,
//                 radius: 4,
//               );
//             },
//           ),
//           belowBarData: BarAreaData(
//             show: true,
//             gradient: LinearGradient(
//               colors: [
//                 Colours.color922BFF.withOpacity(0.3),
//                 Colours.color922BFF.withOpacity(0.0)
//               ],
//               begin: Alignment.topCenter,
//               end: Alignment.bottomCenter,
//             ),
//           ),
//         ),
//       ],
//     );

//     teamHomeModel.refresh();
//     if (isFrist.value) {
//       isFrist.value = false;
//       isFrist.refresh();
//     }
//   }

  //查询球队队员排行
  getTeamMembers(
    int type, {
    isLoad = false,
  }) async {
    var page = 1;

    switch (type) {
      case 1:
        if (!(dataFag["isFrist1"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page1"] as int;
        break;
      case 2:
        if (!(dataFag["isFrist2"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page2"] as int;
        break;
      case 3:
        if (!(dataFag["isFrist3"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page3"] as int;
        break;
    }
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 10,
      'page': page,
      'type': type, //1.得分 2.篮板 3.助攻
    };
    //WxLoading.show();
    var url = await ApiUrl.getTeamPlayerRank(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<TeamPlayerRankModel> modelList =
          list.map((e) => TeamPlayerRankModel.fromJson(e)).toList();
      switch (type) {
        case 1:
          dataFag["page1"] = page + 1;
          if (isLoad) {
            teamMemberList1.addAll(modelList);
            teamMemberList1.refresh();
            if (modelList.length < 10) {
              refreshController1.loadNoData();
            } else {
              refreshController1.loadComplete();
            }
          } else {
            refreshController1.resetNoData();
            teamMemberList1.assignAll(modelList);
            refreshController1.refreshCompleted();
          }
          if (dataFag["isFrist1"] as bool) {
            dataFag["isFrist1"] = false;
            dataFag.refresh();
          }
          break;
        case 2:
          dataFag["page2"] = page + 1;
          if (isLoad) {
            teamMemberList2.addAll(modelList);
            teamMemberList2.refresh();
            if (modelList.length < 10) {
              refreshController2.loadNoData();
            } else {
              refreshController2.loadComplete();
            }
          } else {
            refreshController2.resetNoData();
            teamMemberList2.assignAll(modelList);
            refreshController2.refreshCompleted();
          }
          if (dataFag["isFrist2"] as bool) {
            dataFag["isFrist2"] = false;
            dataFag.refresh();
          }
          break;
        case 3:
          dataFag["page3"] = page + 1;
          if (isLoad) {
            teamMemberList3.addAll(modelList);
            teamMemberList3.refresh();
            if (modelList.length < 10) {
              refreshController3.loadNoData();
            } else {
              refreshController3.loadComplete();
            }
          } else {
            refreshController3.resetNoData();
            teamMemberList3.assignAll(modelList);
            refreshController3.refreshCompleted();
          }
          if (dataFag["isFrist3"] as bool) {
            dataFag["isFrist3"] = false;
            dataFag.refresh();
          }
          break;
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }
}

extension DateFormatter on String {
  String toDotFormat() {
    // 支持多种格式
    final formats = [
      RegExp(r'^(\d{1,2})[/](\d{1,2})$'), // 10/05
      RegExp(r'^(\d{2})\.(\d{2})$'), // 10.05
      RegExp(r'^(\d{1,2})\-(\d{1,2})$') // 10-05
    ];

    for (final format in formats) {
      final match = format.firstMatch(this);
      if (match != null) {
        final month = int.parse(match.group(1)!);
        final day = int.parse(match.group(2)!);
        return '$month.$day';
      }
    }

    return this;
  }
}
