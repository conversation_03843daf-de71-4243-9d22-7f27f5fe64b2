import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/network/model/home_hot_record_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/src/intl/date_format.dart';

class TeamCompetitionsListItem extends StatelessWidget {
  final TeamCompetitionsModel model;
  const TeamCompetitionsListItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    String formattedStartDate = "";
    if ((model.startTime ?? '') != "") {
      DateTime parsedStartDate = DateTime.parse(model.startTime ?? '');
      formattedStartDate = DateFormat("yyyy.MM.dd").format(parsedStartDate);
    }
    String formattedEndDate = "";
    if ((model.endTime ?? '') != "") {
      DateTime parsedEndDate = DateTime.parse(model.endTime ?? '');
      formattedEndDate = DateFormat("yyyy.MM.dd").format(parsedEndDate);
    }
    return InkWell(
      onTap: () => AppPage.to(Routes.competitionDetailPage,
          arguments: {'competitionId': model.competitionId}),
      child: Container(
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.all(Radius.circular(8.r))),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                '$formattedStartDate-$formattedEndDate',
                style: TextStyles.din
                    .copyWith(color: Colours.color5C5C6E, fontSize: 12.sp),
              ),
            ),
            SizedBox(
              height: 20.w,
            ),
            Text(
              model.competitionName ?? '',
              textAlign: TextAlign.center,
              maxLines: 2,
              style: TextStyles.titleSemiBold16,
            ),
            SizedBox(
              height: 20.w,
            ),
            Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
              _getInfoWidget(model.outcomeRate ?? '0/0', '胜/负'),
              _getInfoWidget((model.averageScore ?? 0).toString(), '场均得分'),
              _getInfoWidget('${model.ShootRate ?? '0'}%', '命中率')
            ])
          ],
        ),
      ),
    );
  }

  Widget _getInfoWidget(String topStr, String bottomStr) {
    return Column(
      children: [
        Text(
          topStr,
          style: TextStyles.din.copyWith(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white),
        ),
        SizedBox(
          height: 12.w,
        ),
        Text(
          bottomStr,
          style: TextStyles.display12.copyWith(color: Colours.colorA8A8BC),
        ),
      ],
    );
  }
}
