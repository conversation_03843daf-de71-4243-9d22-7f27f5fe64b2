import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/network/model/home_hot_record_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/src/intl/date_format.dart';

class CompetitionsListItemWidget extends StatelessWidget {
  final HomeHotRecordModelCompetitions model;
  const CompetitionsListItemWidget({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    String formattedStartDate = "";
    if ((model.startTime ?? '') != "") {
      DateTime parsedStartDate = DateTime.parse(model.startTime ?? '');
      formattedStartDate = DateFormat("yyyy.MM.dd").format(parsedStartDate);
    }
    String formattedEndDate = "";
    if ((model.endTime ?? '') != "") {
      DateTime parsedEndDate = DateTime.parse(model.endTime ?? '');
      formattedEndDate = DateFormat("yyyy.MM.dd").format(parsedEndDate);
    }
    return Column(
      children: [
        InkWell(
          onTap: () => AppPage.to(Routes.competitionDetailPage,
              arguments: {'competitionId': model.competitionId}),
          child: Container(
            height: 130.w,
            margin: EdgeInsets.only(bottom: 15.w),
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            child: Row(
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(8.w),
                    child: CachedNetworkImage(
                      imageUrl: model.arenaImageUrl ?? "",
                      width: 100.w,
                      height: 100.w,
                      fit: BoxFit.fill,
                    )),
                SizedBox(
                  width: 15.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        model.competitionName ?? '',
                        style: TextStyles.semiBold14.copyWith(height: 1.5),
                        maxLines: 2,
                      ),
                      Text(
                        '报名截止：${model.registrationDeadline?.split(' ').first}',
                        style: TextStyles.display12,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            height: 20,
                            padding: EdgeInsets.symmetric(horizontal: 9.w),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              gradient: model.status == 1
                                  ? const LinearGradient(colors: [
                                      Color(0xFF7732ED),
                                      Color(0xFFA555EF),
                                    ])
                                  : null,
                              color: _getStatusColor(model.status ?? 0),
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8.r),
                                  topRight: Radius.circular(8.r)),
                            ),
                            child: Text(
                              _getStatusStr(model.status ?? 0),
                              style: TextStyles.display10.copyWith(
                                  color: model.status == 4
                                      ? Colours.color5C5C6E
                                      : Colours.white,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(
                            width: 6,
                          ),
                          Text(
                            '$formattedStartDate-$formattedEndDate',
                            style: TextStyles.display12
                                .copyWith(color: Colours.colorA8A8BC),
                          )
                        ],
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return const Color(0xFF262626);
      default:
        return const Color(0xFF262626);
    }
  }
}
