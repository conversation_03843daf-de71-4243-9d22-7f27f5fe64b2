import 'dart:convert';
import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_data_model.dart';
import 'package:shoot_z/network/model/team_player_rank_model.dart';

class TeamInfoItemLogic2 extends GetxController {
  var teamHistoryRecord = {
    "name0": S.current.team_tag1,
    "name1": S.current.team_tag2,
    "name2": S.current.team_tag3,
    "name3": S.current.team_tag4,
    "name4": S.current.team_tag5,
    "name5": S.current.team_tag6,
  };
  var teamDataName = {
    "name0": S.current.team_tag8,
    "name1": S.current.team_tag2,
    "name2": S.current.team_tag9,
    "name3": S.current.team_tag3,
    "name4": S.current.team_tag10,
    "name5": S.current.team_tag11,
    "name6": S.current.team_tag12,
    "name7": S.current.team_tag13,
    "name8": S.current.team_tag14,
    "name9": S.current.team_tag15,
  };
  RefreshController refreshController1 =
      RefreshController(initialRefresh: false);
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);
  RefreshController refreshController3 =
      RefreshController(initialRefresh: false);
  var teamDataModel = TeamDataModel().obs;
  var isFrist = true.obs;
  var dataFag = {
    "isFrist1": true,
    "page1": 1,
    "isFrist2": true,
    "page2": 2,
    "isFrist3": true,
    "page3": 3,
  }.obs;

  var teamId = "".obs;
  var teamRankTitle = <String>[
    S.current.Player_scoring_list,
    S.current.Team_rebounding_list,
    S.current.Team_assist_list
  ];
  var teamRankIndex = 0.obs;
  var teamMemberList1 = <TeamPlayerRankModel>[].obs;
  var teamMemberList2 = <TeamPlayerRankModel>[].obs;
  var teamMemberList3 = <TeamPlayerRankModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    teamId.value = Get.arguments["teamId"];
  }

  @override
  void onReady() {
    super.onReady();
    getTeamData();
    getTeamMembers(1);
  }

//获得球馆主页详情
  getTeamData() async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
    };
    var url = await ApiUrl.getTeamData(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      log("scoreHistory2=${jsonEncode(res.data)}");
      teamDataModel.value = TeamDataModel.fromJson(res.data);
      teamDataModel.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    if (isFrist.value) {
      isFrist.value = false;
      isFrist.refresh();
    }
  }

  //查询球队队员排行
  getTeamMembers(
    int type, {
    isLoad = false,
  }) async {
    var page = 1;

    switch (type) {
      case 1:
        if (!(dataFag["isFrist1"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page1"] as int;
        break;
      case 2:
        if (!(dataFag["isFrist2"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page2"] as int;
        break;
      case 3:
        if (!(dataFag["isFrist3"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page3"] as int;
        break;
    }
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 10,
      'page': page,
      'type': type, //1.得分 2.篮板 3.助攻
    };
    var url = await ApiUrl.getTeamPlayerRank(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<TeamPlayerRankModel> modelList =
          list.map((e) => TeamPlayerRankModel.fromJson(e)).toList();
      switch (type) {
        case 1:
          if (isLoad) {
            teamMemberList1.addAll(modelList);
            teamMemberList1.refresh();
            dataFag["page1"] = page + 1;
            if (modelList.length < 10) {
              refreshController1.loadNoData();
            } else {
              refreshController1.loadComplete();
            }
          } else {
            refreshController1.resetNoData();
            teamMemberList1.assignAll(modelList);
            refreshController1.refreshCompleted();
          }
          if (dataFag["isFrist1"] as bool) {
            dataFag["isFrist1"] = false;
            dataFag.refresh();
          }
          break;
        case 2:
          if (isLoad) {
            teamMemberList2.addAll(modelList);
            teamMemberList2.refresh();
            dataFag["page2"] = page + 1;
            if (modelList.length < 10) {
              refreshController2.loadNoData();
            } else {
              refreshController2.loadComplete();
            }
          } else {
            refreshController2.resetNoData();
            teamMemberList2.assignAll(modelList);
            refreshController2.refreshCompleted();
          }
          if (dataFag["isFrist2"] as bool) {
            dataFag["isFrist2"] = false;
            dataFag.refresh();
          }
          break;
        case 3:
          if (isLoad) {
            teamMemberList3.addAll(modelList);
            teamMemberList3.refresh();
            dataFag["page3"] = page + 1;
            if (modelList.length < 10) {
              refreshController3.loadNoData();
            } else {
              refreshController3.loadComplete();
            }
          } else {
            refreshController3.resetNoData();
            teamMemberList3.assignAll(modelList);
            refreshController3.refreshCompleted();
          }
          if (dataFag["isFrist3"] as bool) {
            dataFag["isFrist3"] = false;
            dataFag.refresh();
          }
          break;
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
