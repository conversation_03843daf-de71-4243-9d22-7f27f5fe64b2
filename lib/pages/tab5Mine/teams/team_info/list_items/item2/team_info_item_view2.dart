import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/team_player_rank_model.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item2/team_info_item_logic2.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的球队 球馆主页->数据列表
class TeamInfoItemPage2 extends StatelessWidget {
  TeamInfoItemPage2({super.key});
  final logic = Get.put(TeamInfoItemLogic2());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return logic.isFrist.value
            ? buildLoad()
            : logic.teamDataModel.value.teamDataItemByRecentTen == null
                ? SizedBox(
                    height: 300.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 150.w, height: 150.w),
                    ))
                : CustomScrollView(
                    slivers: [
                      _historyRecordWidget(),
                      _teamDataWidget(),
                      _teamRankWidget(context),
                      _teamDataInfoWidget(),
                    ],
                  );
      }),
    );
  }

  Widget _historyRecordWidget() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.team_history,
            rightName: S.current.all,
            margin: EdgeInsets.only(top: 6.w),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(
                left: 15.w, right: 15.w, top: 5.w, bottom: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 18.w),
            decoration: BoxDecoration(
                image: const DecorationImage(
                  image: AssetImage("assets/images/team_info_bg2.png"),
                  fit: BoxFit.fill,
                ),
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            child: Wrap(
              alignment: WrapAlignment.start,
              runSpacing: 20.w,
              children: List.generate(logic.teamHistoryRecord.length, (index) {
                return SizedBox(
                  width: 105.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          MyImage(
                            "team_data${index + 1}.png",
                            width: 18.w,
                            height: 18.w,
                            errorImage: "error_image_width.png",
                            placeholderImage: "error_image_width.png",
                            isAssetImage: true,
                          ),
                          SizedBox(
                            width: 3.w,
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 2.w),
                            child: Text(
                              logic.teamHistoryRecord["name$index"] ?? "",
                              style: TextStyles.regular.copyWith(
                                  fontSize: 14.sp, color: Colours.colorA8A8BC),
                              maxLines: 1,
                            ),
                          )
                        ],
                      ),
                      SizedBox(
                        height: 14.w,
                      ),
                      Text(
                        "${index == 0 ? (logic.teamDataModel.value.matchCount ?? 0) : index == 1 ? (logic.teamDataModel.value.winCount ?? 0) : index == 2 ? (logic.teamDataModel.value.winRate ?? 0) : index == 3 ? (logic.teamDataModel.value.totalScore ?? 0) : index == 4 ? (logic.teamDataModel.value.totalRebound ?? 0) : index == 5 ? (logic.teamDataModel.value.totalAssists ?? 0) : ""}",
                        style: TextStyles.regular.copyWith(fontSize: 20.sp),
                        maxLines: 1,
                      ),
                    ],
                  ),
                );
              }),
            ),
          )
        ],
      ),
    );
  }

  Widget _teamDataWidget() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.team_data,
            leftWidget: Padding(
              padding: EdgeInsets.only(top: 4.w),
              child: Text(
                "(${S.current.team_tag7(min((logic.teamDataModel.value.matchCount ?? 0), 10))})",
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
                maxLines: 1,
              ),
            ),
            rightName: S.current.all,
            margin: EdgeInsets.only(top: 6.w),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(
                left: 15.w, right: 15.w, top: 5.w, bottom: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 18.w),
            decoration: BoxDecoration(
                image: const DecorationImage(
                  image: AssetImage("assets/images/team_info_bg.png"),
                  fit: BoxFit.fill,
                ),
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            child: Wrap(
              alignment: WrapAlignment.start,
              runSpacing: 20.w,
              children: List.generate(logic.teamDataName.length, (index) {
                return SizedBox(
                  width: 78.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        index == 1
                            ? "${(logic.teamDataModel.value.teamDataItemByRecentTen?.win ?? 0)}/${min((logic.teamDataModel.value.matchCount ?? 0), 10)}"
                            : "${index == 0 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.avgScore ?? 0) : index == 1 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.win ?? 0) : index == 2 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.maxScore ?? 0) : index == 3 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.winRate ?? 0) : index == 4 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.shotNum ?? 0) : index == 5 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.hitNumRate ?? 0) : index == 6 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.threeNum ?? 0) : index == 7 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.threeHitRate ?? 0) : index == 8 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.freeNum ?? 0) : index == 9 ? (logic.teamDataModel.value.teamDataItemByRecentTen?.freeHitRate ?? 0) : ""}",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                        maxLines: 1,
                      ),
                      SizedBox(
                        height: 14.w,
                      ),
                      Text(
                        logic.teamDataName["name$index"] ?? "",
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color5C5C6E),
                        maxLines: 1,
                      )
                    ],
                  ),
                );
              }),
            ),
          )
        ],
      ),
    );
  }

  Widget _teamRankWidget(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.Team_ranking,
            margin: EdgeInsets.only(top: 6.w),
            rightOnTap: () {
              showBottoPlayerRankDialog(context);
            },
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
            width: double.infinity,
            height: 36.w,
            decoration: BoxDecoration(
                color: Colours.color1C1827,
                borderRadius: BorderRadius.circular(20.r)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: List.generate(3, (index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.teamRankIndex.value = index;
                    logic.getTeamMembers(index + 1);
                  },
                  child: Container(
                    width: 114.w,
                    height: 32.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: logic.teamRankIndex.value == index
                              ? [Colours.color7B35ED, Colours.colorA253EF]
                              : [Colours.color1C1827, Colours.color1C1827],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight),
                      borderRadius: BorderRadius.all(Radius.circular(20.r)),
                    ),
                    child: Text(
                      logic.teamRankTitle[index],
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.white,
                          fontWeight: index == logic.teamRankIndex.value
                              ? FontWeight.bold
                              : FontWeight.normal),
                    ),
                  ),
                );
              }),
            ),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r)),
            child: ((logic.dataFag["isFrist1"] as bool) &&
                        logic.teamRankIndex.value == 0) ||
                    ((logic.dataFag["isFrist2"] as bool) &&
                        logic.teamRankIndex.value == 1) ||
                    ((logic.dataFag["isFrist3"] as bool) &&
                        logic.teamRankIndex.value == 2)
                ? SizedBox(height: 154.w, child: buildLoad(isShowGif: false))
                : (logic.teamMemberList1.isEmpty &&
                            logic.teamRankIndex.value == 0) ||
                        (logic.teamMemberList2.isEmpty &&
                            logic.teamRankIndex.value == 1) ||
                        (logic.teamMemberList3.isEmpty &&
                            logic.teamRankIndex.value == 2)
                    ? SizedBox(
                        height: 154.w,
                        child: myNoDataView(context,
                            msg: S.current.no_data_team_rank,
                            imagewidget: WxAssets.images.teamInfoNodata
                                .image(width: 107.w, height: 72.w),
                            height: 2.w))
                    : Wrap(
                        spacing: 9.w,
                        children: List.generate(
                            min(
                                4,
                                logic.teamRankIndex.value == 0
                                    ? logic.teamMemberList1.length
                                    : logic.teamRankIndex.value == 1
                                        ? logic.teamMemberList2.length
                                        : logic.teamMemberList3.length),
                            (index) {
                          return GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              AppPage.to(Routes.careerHighlightsHomePage,
                                  arguments: {
                                    'userId':
                                        logic.teamMemberList1[index].userId
                                  });
                            },
                            child: Container(
                              width: 72.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.r)),
                              ),
                              child: Column(
                                children: [
                                  MyImage(
                                    (logic.teamRankIndex.value == 0
                                            ? logic
                                                .teamMemberList1[index].avatar
                                            : logic.teamRankIndex.value == 1
                                                ? logic.teamMemberList2[index]
                                                    .avatar
                                                : logic.teamMemberList3[index]
                                                    .avatar) ??
                                        "",
                                    width: 72.w,
                                    height: 72.w,
                                    radius: 8.r,
                                    errorImage: "error_image_width.png",
                                    placeholderImage: "error_image_width.png",
                                  ),
                                  SizedBox(
                                    height: 10.w,
                                  ),
                                  Text(
                                    (logic.teamRankIndex.value == 0
                                            ? logic
                                                .teamMemberList1[index].userName
                                            : logic.teamRankIndex.value == 1
                                                ? logic.teamMemberList2[index]
                                                    .userName
                                                : logic.teamMemberList3[index]
                                                    .userName) ??
                                        "",
                                    style: TextStyles.regular
                                        .copyWith(fontSize: 14.sp),
                                    maxLines: 1,
                                  ),
                                  SizedBox(
                                    height: 10.w,
                                  ),
                                  Text(
                                    "${(logic.teamRankIndex.value == 0 ? logic.teamMemberList1[index].score : logic.teamRankIndex.value == 1 ? logic.teamMemberList2[index].score : logic.teamMemberList3[index].score) ?? ""}",
                                    style: TextStyles.regular.copyWith(
                                        fontSize: 20.sp,
                                        fontWeight: FontWeight.w700),
                                    maxLines: 1,
                                  )
                                ],
                              ),
                            ),
                          );
                        }),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _teamDataInfoWidget() {
    return SliverToBoxAdapter(
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.only(
          left: 15.w,
          right: 15.w,
          top: 15.w,
          bottom: 50.w,
        ),
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(8.r),
            ),
            color: Colours.color191921),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 8.w,
                  height: 8.w,
                  margin: EdgeInsets.only(right: 5.w),
                  decoration: BoxDecoration(
                      color: Colours.color7732ED,
                      borderRadius: BorderRadius.circular(4.r)),
                ),
                Text(
                  S.current.team_tag16(10),
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color9393A5),
                  maxLines: 1,
                )
              ],
            ),
            SizedBox(
              height: 19.w,
            ),
            teamDataInfoItemWidget(
                S.current.team_tag19,
                (logic.teamDataModel.value.teamDataItemByRecentTen?.shotNum ??
                        0)
                    .toString(),
                logic.teamDataModel.value.teamDataItemByRecentTen?.hitNumRate ??
                    "0"),
            SizedBox(
              height: 18.w,
            ),
            teamDataInfoItemWidget(
                S.current.team_tag17,
                (logic.teamDataModel.value.teamDataItemByRecentTen?.threeNum ??
                        0)
                    .toString(),
                logic.teamDataModel.value.teamDataItemByRecentTen
                        ?.threeHitRate ??
                    "0"),
            SizedBox(
              height: 18.w,
            ),
            teamDataInfoItemWidget(
                S.current.team_tag18,
                (logic.teamDataModel.value.teamDataItemByRecentTen?.freeNum ??
                        0)
                    .toString(),
                logic.teamDataModel.value.teamDataItemByRecentTen
                        ?.freeHitRate ??
                    "0"),
            SizedBox(
              height: 18.w,
            ),
          ],
        ),
      ),
    );
  }

  Column teamDataInfoItemWidget(String title, String data, String rateStr) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyles.medium.copyWith(fontSize: 14.sp),
            ),
            Text(
              data,
              style: TextStyles.regular
                  .copyWith(fontSize: 12.sp, color: Colours.white),
            ),
            Text(
              "    ",
              style: TextStyles.medium.copyWith(fontSize: 14.sp),
            ),
          ],
        ),
        SizedBox(
          height: 12.w,
        ),
        Row(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) => Container(
                    height: 6.w,
                    decoration: BoxDecoration(
                      color: Colours.color22222D,
                      borderRadius: BorderRadius.circular(8.w),
                    ),
                    alignment: Alignment.centerLeft,
                    child: Container(
                      width: constraints.maxWidth * parseWithSign(rateStr),
                      decoration: BoxDecoration(
                        color: Colours.color7732ED,
                        borderRadius:
                            BorderRadius.horizontal(left: Radius.circular(8.w)),
                      ),
                    )),
              ),
            ),
            SizedBox(
              width: 15.w,
            ),
            Text(
              rateStr,
              style: TextStyles.regular
                  .copyWith(fontSize: 12.sp, color: Colours.white),
            ),
          ],
        ),
      ],
    );
  }

  showBottoPlayerRankDialog(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
              width: double.infinity,
              height: 682,
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color191921, //Colours.color191921
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r))),
              child: Column(
                children: [
                  SizedBox(
                    height: 8.w,
                  ),
                  Container(
                    width: 38.w,
                    height: 4.w,
                    decoration: BoxDecoration(
                        color: Colours.colorD8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                  SizedBox(
                    height: 18.w,
                  ),
                  Text(
                    logic.teamRankTitle[logic.teamRankIndex.value],
                    style: TextStyles.regular.copyWith(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colours.white),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  Text(
                    S.current.player_rank_remake,
                    style: TextStyles.regular
                        .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  logic.teamRankIndex.value == 0
                      ? Expanded(
                          child: SmartRefresher(
                              controller: logic.refreshController1,
                              footer: buildFooter(),
                              header: buildClassicHeader(),
                              enablePullDown: false,
                              enablePullUp: true,
                              // onRefresh: () {
                              //   WxLoading.showToast("onRefresh");
                              //   logic.refreshController1.refreshCompleted();
                              // },
                              onLoading: () {
                                // WxLoading.showToast("onLoading");
                                logic.getTeamMembers(
                                    logic.teamRankIndex.value + 1,
                                    isLoad: true);
                              },
                              physics: const AlwaysScrollableScrollPhysics(),
                              child: (logic.dataFag["isFrist1"] as bool)
                                  ? buildLoad(isShowGif: false)
                                  : logic.teamMemberList1.isEmpty
                                      ? myNoDataView(context,
                                          msg: S.current.no_data_team_rank,
                                          imagewidget: WxAssets
                                              .images.teamInfoNodata
                                              .image(
                                                  width: 107.w, height: 72.w),
                                          height: 2.w)
                                      : ListView.separated(
                                          scrollDirection: Axis.vertical,
                                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                          shrinkWrap: true,
                                          padding:
                                              EdgeInsets.only(bottom: 40.w),
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount:
                                              logic.teamMemberList1.length,
                                          itemBuilder: (context, position) {
                                            return _listItemWidget(
                                                logic.teamMemberList1[position],
                                                position);
                                          },
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return SizedBox(
                                              height: 15.w,
                                            );
                                          },
                                        )),
                        )
                      : logic.teamRankIndex.value == 1
                          ? Expanded(
                              child: SmartRefresher(
                                  controller: logic.refreshController2,
                                  footer: buildFooter(),
                                  header: buildClassicHeader(),
                                  enablePullDown: false,
                                  enablePullUp: true,
                                  onLoading: () {
                                    logic.getTeamMembers(
                                        logic.teamRankIndex.value + 1,
                                        isLoad: true);
                                  },
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  child: (logic.dataFag["isFrist2"] as bool)
                                      ? buildLoad(isShowGif: false)
                                      : logic.teamMemberList2.isEmpty
                                          ? myNoDataView(context,
                                              msg: S.current.no_data_team_rank,
                                              imagewidget: WxAssets
                                                  .images.teamInfoNodata
                                                  .image(
                                                      width: 107.w,
                                                      height: 72.w),
                                              height: 2.w)
                                          : ListView.separated(
                                              scrollDirection: Axis.vertical,
                                              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                              shrinkWrap: true,
                                              padding:
                                                  EdgeInsets.only(bottom: 40.w),
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  logic.teamMemberList2.length,
                                              itemBuilder: (context, position) {
                                                return _listItemWidget(
                                                    logic.teamMemberList2[
                                                        position],
                                                    position);
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return SizedBox(
                                                  height: 15.w,
                                                );
                                              },
                                            )),
                            )
                          : Expanded(
                              child: SmartRefresher(
                                  controller: logic.refreshController3,
                                  footer: buildFooter(),
                                  header: buildClassicHeader(),
                                  enablePullDown: false,
                                  enablePullUp: true,
                                  onLoading: () {
                                    logic.getTeamMembers(
                                        logic.teamRankIndex.value + 1,
                                        isLoad: true);
                                  },
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  child: (logic.dataFag["isFrist3"] as bool)
                                      ? buildLoad(isShowGif: false)
                                      : logic.teamMemberList3.isEmpty
                                          ? myNoDataView(context,
                                              msg: S.current.no_data_team_rank,
                                              imagewidget: WxAssets
                                                  .images.teamInfoNodata
                                                  .image(
                                                      width: 107.w,
                                                      height: 72.w),
                                              height: 2.w)
                                          : ListView.separated(
                                              scrollDirection: Axis.vertical,
                                              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                              shrinkWrap: true,
                                              padding:
                                                  EdgeInsets.only(bottom: 40.w),
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  logic.teamMemberList3.length,
                                              itemBuilder: (context, position) {
                                                return _listItemWidget(
                                                    logic.teamMemberList3[
                                                        position],
                                                    position);
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return SizedBox(
                                                  height: 15.w,
                                                );
                                              },
                                            )),
                            ),
                ],
              ));
        });
      },
    );
  }

  _listItemWidget(TeamPlayerRankModel teamPlayerRankModel, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        AppPage.to(Routes.careerHighlightsHomePage,
            arguments: {'userId': logic.teamMemberList1[index].userId});
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
            color: Colours.color22222D),
        child: Row(
          children: [
            Text(
              "${index + 1}",
              style: TextStyles.regular.copyWith(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w700,
                  color: Colours.white),
              maxLines: 1,
            ),
            SizedBox(
              width: 15.w,
            ),
            MyImage(
              teamPlayerRankModel.avatar ?? "",
              width: 64.w,
              height: 64.w,
              radius: 8.r,
              errorImage: "error_image_width.png",
              placeholderImage: "error_image_width.png",
            ),
            SizedBox(
              width: 12.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    (logic.teamRankIndex.value == 0
                            ? logic.teamMemberList1[index].userName
                            : logic.teamRankIndex.value == 1
                                ? logic.teamMemberList2[index].userName
                                : logic.teamMemberList3[index].userName) ??
                        "",
                    style: TextStyles.regular.copyWith(fontSize: 14.sp),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 16.w,
                  ),
                  Text(
                    S.current
                        .bind_match_num(teamPlayerRankModel.bindMatchNum ?? 0),
                    style: TextStyles.regular.copyWith(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w700,
                        color: Colours.color5C5C6E),
                    maxLines: 1,
                  )
                ],
              ),
            ),
            Text(
              "${teamPlayerRankModel.score ?? ""}",
              style: TextStyles.regular
                  .copyWith(fontSize: 20.sp, fontWeight: FontWeight.w700),
              maxLines: 1,
            )
          ],
        ),
      ),
    );
  }

// 处理可能带负号的百分比: "-39.4%" → -0.394
  double parseWithSign(String input) {
    final hasNegative = input.contains('-');
    final value = double.parse(input.replaceAll(RegExp(r'[^\d\.]'), ''));
    return (hasNegative ? -value : value) / 100;
  }
}
