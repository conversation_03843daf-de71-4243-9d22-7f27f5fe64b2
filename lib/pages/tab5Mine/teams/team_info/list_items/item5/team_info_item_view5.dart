import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item5/team_info_item_logic5.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的球队 球馆主页->集锦列表
class TeamInfoItemPage5 extends StatelessWidget {
  TeamInfoItemPage5({super.key});

  final logic = Get.put(TeamInfoItemLogic5());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _createTeamWidget(context),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: logic.dataList.isNotEmpty,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          onLoading: () {
            logic.getdataList(controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          //  physics: const NeverScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? SizedBox(
                      height: 300.w,
                      child: myNoDataView(
                        context,
                        msg: S.current.No_data_available,
                        imagewidget: WxAssets.images.icGameNo
                            .image(width: 150.w, height: 150.w),
                      ))
                  : _listIWidget());
    });
  }

  /// 构建列表项
  Widget _listIWidget() {
    return GridView.builder(
      shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
      physics: const AlwaysScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // 每行两个 item
        crossAxisSpacing: 13.w,
        mainAxisSpacing: 13.w,
        childAspectRatio: 166 / 145, // 控制每个 item 的宽高比例
      ),
      padding: EdgeInsets.only(bottom: 40.w, left: 15.w, right: 15.w),
      itemCount: logic.dataList.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () async {
            // HighlightsModel highlightsModel = HighlightsModel(
            //     logic.dataList[index].arenaId ?? "",
            //     "",
            //     logic.dataList[index].matchTime ?? "",
            //     [],
            //     "");
            // Videos video = Videos(
            //     logic.dataList[index].videoPath ?? "",
            //     logic.dataList[index].videoId ?? "",
            //     logic.dataList[index].title ?? "",
            //     0,
            //     1,
            //     1,
            //     logic.dataList[index].videoCover ?? "",
            //     logic.dataList[index].videoPath ?? "",
            //     1,
            //     true);
            // final result = await AppPage.to(Routes.highlightsVideo,
            //     arguments: {'video': video, 'group': highlightsModel});
            // if (result == true) {
            //   // 如果结果为 true，表示需要刷新列表
            // }

            AppPage.to(Routes.videos, arguments: {
              "videoId": logic.dataList[index].videoId,
              "matchId": logic.dataList[index].matchId,
              "type": "0", //0合并id  1片段id
            });
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 166.w,
                height: 95.w,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    MyImage(
                      logic.dataList[index].videoCover ?? "",
                      width: 166.w,
                      height: 95.w,
                      radius: 12.r,
                      errorImage: "error_image_width.png",
                      placeholderImage: "error_image_width.png",
                    ),
                    Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0,
                        top: 0,
                        child: WxAssets.images.videoPlay
                            .image(width: 28.w, height: 28.w)),
                  ],
                ),
              ),
              SizedBox(
                height: 12.w,
              ),
              Text(
                logic.dataList[index].title ?? "",
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: TextStyles.regular.copyWith(
                  fontSize: 12.sp,
                  color: Colours.color9393A5,
                ),
              ),
              SizedBox(
                height: 8.w,
              ),
              Text(
                logic.dataList[index].matchTime ?? "",
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: TextStyles.regular.copyWith(
                  fontSize: 12.sp,
                  color: Colours.color5C5C6E,
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
