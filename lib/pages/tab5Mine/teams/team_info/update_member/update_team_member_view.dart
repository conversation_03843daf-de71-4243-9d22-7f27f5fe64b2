import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/update_member/update_team_member_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队详情  编辑球员信息
class UpdateTeamMemberPage extends StatelessWidget {
  UpdateTeamMemberPage({super.key});

  final logic = Get.put(UpdateTeamMemberLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.update_member_title),
      ),
      body: _createTeamWidget(context),
      bottomNavigationBar: Container(
        width: double.infinity,
        padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            logic.getCompleteInfo();
          },
          child: Container(
            height: 46.w,
            width: double.infinity,
            alignment: Alignment.center,
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
              color: Colours.color282735,
              borderRadius: BorderRadius.all(Radius.circular(28.r)),
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Text(
              S.current.save,
              style: TextStyles.display16.copyWith(fontSize: 16.sp),
            ),
          ),
        ),
      ),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 15.w,
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                logic.getImage();
              },
              child: Container(
                width: 115,
                height: 152.w,
                margin: EdgeInsets.only(
                    top: 15.w, left: 20.w, right: 20.w, bottom: 20.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(16.r)),
                child: logic.headImgPath.value == ""
                    ? WxAssets.images.myTeamAdd.image(width: 36.w, height: 36.w)
                    : Stack(
                        alignment: Alignment.center,
                        children: [
                          MyImage(
                            logic.headImgPath.value,
                            width: double.infinity,
                            height: 188.w,
                            errorImage: "error_image_width.png",
                            placeholderImage: "error_image_width.png",
                            radius: 12.r,
                            fit: BoxFit.cover,
                          ),
                          Container(
                            padding: EdgeInsets.all(10.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r),
                                color: Colours.color70000000),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                WxAssets.images.teamRefresh
                                    .image(width: 18.w, height: 18.w),
                                SizedBox(
                                  width: 2.w,
                                ),
                                Text(
                                  S.current.reupload,
                                  style: TextStyles.medium.copyWith(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w600),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
              ),
            ),
            Text(
              S.current.update_member_tips1,
              style: TextStyles.regular
                  .copyWith(color: Colours.white, fontSize: 14.sp),
            ),
            SizedBox(
              height: 15.w,
            ),
            Text(
              S.current.update_member_tips2,
              style: TextStyles.regular
                  .copyWith(color: Colours.color5C5C6E, fontSize: 12.sp),
            ),
            Container(
              margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 30.w),
              width: double.infinity,
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              height: 52.w,
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(16.r)),
              child: Row(
                children: [
                  Text(
                    S.current.name,
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Expanded(
                      child: TextField(
                    controller: logic.txtController1,
                    style: TextStyles.regular,
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'[" "]')), // 不允许空格
                      LengthLimitingTextInputFormatter(5), // 限制输入长度为7
                    ],
                    decoration: InputDecoration(
                      hintText: S.current.edit_name,
                      hintStyle: TextStyles.regular
                          .copyWith(color: Colours.color5C5C6E),
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      //让文字垂直居中,
                      border: InputBorder.none,
                    ),
                    keyboardType: TextInputType.text,
                  )),
                  Text(
                    "${logic.textLength1.value}/5",
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 15.w),
              width: double.infinity,
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              height: 52.w,
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(16.r)),
              child: Row(
                children: [
                  Text(
                    S.current.update_member_number,
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Expanded(
                      child: TextField(
                    controller: logic.txtController2,
                    style: TextStyles.regular,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                          RegExp(r'[0-9]')), // 只允许输入数字
                      LengthLimitingTextInputFormatter(3), // 限制输入长度为7
                    ],
                    decoration: InputDecoration(
                      hintText: S.current.update_member_edit_num,
                      hintStyle: TextStyles.regular
                          .copyWith(color: Colours.color5C5C6E),
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      //让文字垂直居中,
                      border: InputBorder.none,
                    ),
                    keyboardType: TextInputType.text,
                  )),
                ],
              ),
            ),
            SizedBox(
              height: 18.w,
            ),
            Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: 1.w),
                    child:
                        WxAssets.images.tips.image(width: 12.w, height: 12.w),
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  Expanded(
                    child: Text(
                      S.current.update_member_tips3,
                      style: TextStyles.regular.copyWith(
                          color: Colours.color5C5C6E,
                          fontSize: 10.sp,
                          height: 1.3),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
