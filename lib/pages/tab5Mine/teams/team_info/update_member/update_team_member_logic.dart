import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_member_info_model.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:utils_package/utils_package.dart';

class UpdateTeamMemberLogic extends GetxController with WidgetsBindingObserver {
  TextEditingController txtController1 = TextEditingController();
  TextEditingController txtController2 = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  //数据列表
  RxList<Matches> dataList = <Matches>[].obs;
  var textLength1 = 0.obs;
  var textLength2 = 0.obs;
  var teamUserId = "".obs;
  var headImgPath = "".obs;
  @override
  void onInit() {
    super.onInit();
    teamUserId.value = Get.arguments["teamId"];
    txtController1.addListener(() {
      textLength1.value = txtController1.text.length;
    });
    getTeamMemberInfo();
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得球员资料
  getTeamMemberInfo() async {
    Map<String, dynamic> param = {};
    WxLoading.show();
    var res = await Api().get(ApiUrl.userDetail, queryParameters: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      TeamMemberInfoModel teamInfoModel =
          TeamMemberInfoModel.fromJson(res.data);
      headImgPath.value = teamInfoModel.photo ?? "";
      txtController1.text = teamInfoModel.realName ?? "";
      txtController2.text = teamInfoModel.number ?? "";
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //补全球员信息
  getCompleteInfo() async {
    if (txtController1.text.trim().isEmpty) {
      WxLoading.showToast(S.current.add_team_tips1);
      return;
    }
    if (txtController2.text.trim().isNotEmpty &&
        int.parse(txtController2.text.trim()) > 100) {
      WxLoading.showToast(S.current.update_member_edit_num);
      return;
    }
    Map<String, dynamic> param = {
      //   if (teamUserId.value != "") 'teamUserId': teamUserId.value,
      "number": txtController2.text.trim(),
      "photo": headImgPath.value,
      "realName": txtController1.text.trim(),
    };
    WxLoading.show();
    var res = await Api().post(ApiUrl.completeInfo, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.modification_successful);
      AppPage.back();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> getImage() async {
    try {
      var permission = await WxPermissionUtils.photo();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes: S.current.photo_hint,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        File file = File(pickedFile.path);
        String path = file.path;
        var fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        if (res.isSuccessful()) {
          headImgPath.value = res.data['path'];
        }
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
