import 'package:get/get.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item1/team_info_item_logic1.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item2/team_info_item_logic2.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item3/team_info_item_logic3.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item4/team_info_item_logic4.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item5/team_info_item_logic5.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/team_info_logic.dart';

class TeamInfoBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TeamInfoLogic());
    Get.lazyPut(() => TeamInfoItemLogic1());
    Get.lazyPut(() => TeamInfoItemLogic2());
    Get.lazyPut(() => TeamInfoItemLogic3());
    Get.lazyPut(() => TeamInfoItemLogic4());
    Get.lazyPut(() => TeamInfoItemLogic5());
  }
}
