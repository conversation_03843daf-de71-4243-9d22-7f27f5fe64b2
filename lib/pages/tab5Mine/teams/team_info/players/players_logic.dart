import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_players2_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class PlayersLogic extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <TeamPlayers2Model>[].obs;
  var teamId = "";
  var yiJiaoUserId = "".obs;
  var yiJiaoUserName = "".obs;
  bool leaderIsMe = false;
  @override
  void onInit() {
    super.onInit();
    teamId = Get.arguments["teamId"];
    getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 20,
    };
    var url = await ApiUrl.getTeamPlayers(teamId);
    var res = await Api().get(url, queryParameters: param);
    log("${param}");
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      List<TeamPlayers2Model> modelList =
          list.map((e) => TeamPlayers2Model.fromJson(e)).toList();
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

//审核入队审核
  postTeamApplyAudit(int id, int type) async {
    Map<String, dynamic> param = {
      "id": id, "status": type //2 通过 3 拒绝
    };
    WxLoading.show();
    var url = await ApiUrl.postTeamApplyAudit(teamId);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // getTeamInfo();
      getdataList(isLoad: false, controller: refreshController);
      if (type == 2) {
        BusUtils.instance.fire(EventAction(key: EventBusKey.auditApplyTeam));
      } //auditApplyTeam

      WxLoading.showToast(
          type == 2 ? S.current.Already_agreed : S.current.Rejected);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //移交队长
  void getChangeTeamLeaderDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 632,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.team_info_diolog_tips4,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Expanded(
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      SizedBox(
                        height: 580.w,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 4,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                  childAspectRatio: 75 / 130,
                                ),
                                padding:
                                    EdgeInsets.only(bottom: 70.w, top: 0.w),
                                itemCount: dataList.length,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        yiJiaoUserId.value =
                                            dataList[index].userId ?? "";
                                        yiJiaoUserName.value =
                                            dataList[index].userName ?? "";
                                        yiJiaoUserId.refresh();
                                      },
                                      child: Column(
                                        children: [
                                          Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              MyImage(
                                                dataList[index].userPhoto ?? '',
                                                //  holderImg: "home/index/df_banner_top",
                                                fit: BoxFit.fill,
                                                width: 75.w,
                                                height: 100.w,
                                                isAssetImage: false,
                                                // errorImg: "home/index/df_banner_top"
                                                radius: 4.r,
                                              ),
                                              if (yiJiaoUserId.value ==
                                                  dataList[index].userId)
                                                Container(
                                                  width: 75.w,
                                                  height: 100.w,
                                                  alignment: Alignment.center,
                                                  decoration:
                                                      const BoxDecoration(
                                                          color: Colours
                                                              .color80000000),
                                                  child: Container(
                                                    width: 20.w,
                                                    height: 20.w,
                                                    margin: EdgeInsets.only(
                                                        right: 8.w,
                                                        bottom: 3.w,
                                                        top: 8.w),
                                                    child: const Icon(
                                                      Icons.check,
                                                      color: Colours.white,
                                                      size: 20,
                                                    ),
                                                  ),
                                                )
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10.w,
                                          ),
                                          Center(
                                            child: Text(
                                              dataList[index].userName ?? "",
                                              maxLines: 1,
                                              textAlign: TextAlign.center,
                                              style: TextStyles.regular
                                                  .copyWith(
                                                      fontSize: 12.sp,
                                                      color:
                                                          Colours.color9393A5),
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  });
                                }),
                          ],
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () async {
                            if (yiJiaoUserId.value == "" ||
                                yiJiaoUserId.value ==
                                    UserManager.instance.user?.userId) {
                              WxLoading.showToast(
                                  S.current.team_info_diolog_tips6);
                              return;
                            }
                            Get.back();

                            //加入球队
                            getMyDialog(
                              S.current.Transfer_team,
                              S.current.sure,
                              content: S.current.team_info_diolog_tips12(
                                  yiJiaoUserName.value),
                              () {
                                AppPage.back();
                                putTeamTransfer(yiJiaoUserId.value);
                              },
                              isShowClose: false,
                              btnIsHorizontal: true,
                              btnText2: S.current.cancel,
                              onPressed2: () {
                                AppPage.back();
                              },
                            );
                          },
                          child: Container(
                            height: 46.w,
                            width: double.infinity,
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(left: 20.w, right: 20.w),
                            decoration: BoxDecoration(
                              color: Colours.color282735,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(28.r)),
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Text(
                              S.current.sure,
                              style: TextStyles.display16
                                  .copyWith(fontSize: 16.sp),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }

  //移交队长
  putTeamTransfer(String targetUserId) async {
    Map<String, dynamic> param = {
      'teamId': teamId,
      'targetUserId': targetUserId,
    };
    WxLoading.show();
    var url = await ApiUrl.putTeamTransfer(teamId, targetUserId);
    var res = await Api().PUT(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.modification_successful);
      getdataList(isLoad: false, controller: refreshController);
      BusUtils.instance.fire(EventAction(key: EventBusKey.changeLeader));
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //移除球队
  putTeamPlayerOut(String targetUserId) async {
    Map<String, dynamic> param = {
      'teamId': teamId,
      'memberId': targetUserId,
    };
    WxLoading.show();
    var url = await ApiUrl.getTeamPlayerOut(teamId, targetUserId);
    var res = await Api().delete(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.modification_successful);
      getdataList(isLoad: false, controller: refreshController);
      BusUtils.instance.fire(EventAction(key: EventBusKey.changeLeader));
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
