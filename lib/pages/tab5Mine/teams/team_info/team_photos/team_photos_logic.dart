import 'dart:developer';
import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dio/dio.dart' as dio;
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:mime_type/mime_type.dart';
import 'package:utils_package/utils_package.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/model/team_photos_model.dart';

class TeamPhotosLogic extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  final ImagePicker _picker = ImagePicker();
  //数据列表
  var dataList = <TeamPhotosModel>[].obs;
  // 获取所有 URL
  List<String?> get allUrls => dataList.map((p) => p.url).toList();
  var teamId = "";
  var isEdit = 0.obs; //0不编辑  1删除  2设为封面
  @override
  void onInit() {
    super.onInit();
    teamId = Get.arguments["teamId"];
  }

  @override
  void onReady() {
    super.onReady();
    getdataList(isLoad: false, controller: refreshController);
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 20,
    };
    log("getdataList2=$teamId-$param");
    var url = await ApiUrl.getTeamPhotos(teamId);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      List<TeamPhotosModel> modelList =
          list.map((e) => TeamPhotosModel.fromJson(e)).toList();
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

//删除球队合照
  deleteTeamPhotos(var ids) async {
    Map<String, dynamic> param = {
      "ids": ids,
    };
    WxLoading.show();
    var url = await ApiUrl.deleteTeamPhotos(teamId);
    var res = await Api().delete(url, data: param);
    log("deleteTeamPhotos=$teamId-$param");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast("已删除");
      getdataList(isLoad: false, controller: refreshController);
      BusUtils.instance.fire(EventAction(key: EventBusKey.updateTeamPhoto));
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> cropImage(String path2) async {
    if (path2 != "") {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: path2,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        aspectRatio: const CropAspectRatio(ratioX: 16, ratioY: 9),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '裁剪图片',
            toolbarColor: Colours.color191921, // 工具栏背景色
            toolbarWidgetColor: Colors.white, // 工具栏文字和图标颜色
            statusBarColor: Colors.black, // 状态栏背景色为黑色
            backgroundColor: Colors.black, // 裁剪区域背景色
            //    activeControlsWidgetColor: Colors.white, // 控制按钮颜色
            cropFrameColor: Colors.white, // 裁剪框颜色
            cropGridColor: Colors.white.withOpacity(0.5), // 网格线颜色
            hideBottomControls: true, // 隐藏底部控制栏，包括旋转按钮
            lockAspectRatio: true, // 必须为true
            initAspectRatio: CropAspectRatioPreset.ratio16x9, // 设置初始比例
            aspectRatioPresets: [CropAspectRatioPreset.ratio16x9], // 限制可选比例
          ),
          IOSUiSettings(
            title: '裁剪图片',
            // rectX: 0,
            // rectY: 0,
            embedInNavigationController: true, // 启用导航控制器
            hidesNavigationBar: true, // 不隐藏导航栏
            // rectWidth: ScreenUtil().screenWidth,
            // rectHeight: ScreenUtil().screenWidth * 9 / 16,
            resetButtonHidden: true, // 隐藏重置按钮
            rotateButtonsHidden: true, // 隐藏旋转按钮
            resetAspectRatioEnabled: false, // 禁止重置比例
            aspectRatioLockEnabled: true, // 锁定宽高比
            aspectRatioLockDimensionSwapEnabled: false, // 禁用维度交换
            aspectRatioPickerButtonHidden: true, // 隐藏比例选择按钮
            // 强制使用横向比例
            aspectRatioPresets: [CropAspectRatioPreset.ratio16x9],
          ),
          WebUiSettings(
            context: Get.context!,
            presentStyle: WebPresentStyle.dialog,
            size: const CropperSize(
              width: 520,
              height: 520,
            ),
          ),
        ],
      );
      if (croppedFile != null) {
        var fileName = croppedFile.path.substring(
            croppedFile.path.lastIndexOf("/") + 1, croppedFile.path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            croppedFile.path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        WxLoading.show();
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        WxLoading.dismiss();
        if (res.isSuccessful()) {
          updateTeamPhoto(res.data['path']);
        }
      }
    }
  }

  //设置封面球队合照
  postTeamPhotoCover(var id) async {
    Map<String, dynamic> param = {
      "id": id,
    };
    WxLoading.show();
    var url = await ApiUrl.postTeamPhotoCover(teamId);
    var res = await Api().post(url, data: param);
    log("postTeamPhotoCover=$param");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast("设置成功");
      getdataList(isLoad: false, controller: refreshController);
      BusUtils.instance.fire(EventAction(key: EventBusKey.updateTeamPhoto));
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> getCarmer() async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(const PermissionDialog(
          text: "开启相机权限",
          contentDes: "请前往系统设置开启相机权限，以设置球队合照",
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        File file = File(pickedFile.path);
        String path = file.path;
        cropImage(path);
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              SizedBox(
                height: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      Get.back();
                      getCarmer();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "拍照",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                      getImage();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "相册",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      alignment: Alignment.center,
                      child: Text(
                        "取消",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 47.w,
              ),
            ],
          ),
        );
      },
    );
  }

  //上传合照
  updateTeamPhoto(var path) async {
    Map<String, dynamic> param = {
      'teamId': teamId,
      'path': path,
    };
    WxLoading.show();
    var url = await ApiUrl.getTeamIdPhotoUrl(teamId);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.upload_success);
      getdataList(isLoad: false, controller: refreshController);
      BusUtils.instance.fire(EventAction(key: EventBusKey.updateTeamPhoto));
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> getImage() async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes: S.current.photo_hint2,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        File file = File(pickedFile.path);
        String path = file.path;
        cropImage(path);
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }
}

class CropAspectRatioPresetCustom implements CropAspectRatioPresetData {
  @override
  (int, int)? get data => (4, 4);

  @override
  String get name => '4x4';
}

class CropAspectRatioPresetCustom2 implements CropAspectRatioPresetData {
  @override
  (int, int)? get data => (16, 9);

  @override
  String get name => '16x9';
}
