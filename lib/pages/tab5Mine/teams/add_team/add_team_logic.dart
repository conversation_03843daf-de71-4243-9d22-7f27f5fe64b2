import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_info_model.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:utils_package/utils_package.dart';

class AddTeamLogic extends GetxController with WidgetsBindingObserver {
  TextEditingController txtController1 = TextEditingController();
  TextEditingController txtController2 = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  //数据列表
  RxList<Matches> dataList = <Matches>[].obs;
  var textLength1 = 0.obs;
  var textLength2 = 0.obs;
  var teamId = "".obs;
  var headImgPath = "".obs;
  var contentImgPath = "".obs;
  @override
  void onInit() {
    super.onInit();
    teamId.value = Get.arguments["teamId"];
    txtController1.addListener(() {
      textLength1.value = txtController1.text.length;
    });
    txtController2.addListener(() {
      textLength2.value = txtController2.text.length;
    });
    if (teamId.value != "") {
      getTeamInfo();
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得球队资料
  getTeamInfo() async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
    };
    WxLoading.show();
    var url = await ApiUrl.getTeamInfo(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      TeamInfoModel teamInfoModel = TeamInfoModel.fromJson(res.data);
      headImgPath.value = teamInfoModel.logo ?? "";
      contentImgPath.value = teamInfoModel.teamPhoto ?? "";
      txtController1.text = teamInfoModel.name ?? "";
      txtController2.text = teamInfoModel.description ?? "";
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //创建球队
  getCreateTeam() async {
    if (txtController1.text.trim().isEmpty) {
      WxLoading.showToast(S.current.add_team_tips1);
      return;
    }
    Map<String, dynamic> param = {
      if (teamId.value != "") 'teamId': teamId.value,
      "description": txtController2.text.trim(),
      "logo": headImgPath.value,
      "name": txtController1.text.trim(),
      "teamPhoto": contentImgPath.value,
    };
    WxLoading.show();
    if (teamId.value == "") {
      var res = await Api().post(ApiUrl.createTeam, data: param);
      WxLoading.dismiss();
      if (res.isSuccessful()) {
        WxLoading.showToast(S.current.create_successful);
        AppPage.back();
      } else {
        WxLoading.showToast(res.message);
      }
    } else {
      var url = await ApiUrl.putUpdateTeamInfo(teamId.value);
      var res = await Api().PUT(url, data: param);
      WxLoading.dismiss();
      if (res.isSuccessful()) {
        WxLoading.showToast(S.current.modification_successful);
        AppPage.back();
      } else {
        WxLoading.showToast(res.message);
      }
    }
  }

  Future<void> getImage(int imgType) async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes:
              imgType == 0 ? S.current.photo_hint1 : S.current.photo_hint2,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        File file = File(pickedFile.path);
        String path = file.path;
        var fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        if (res.isSuccessful()) {
          if (imgType == 0) {
            headImgPath.value = res.data['path'];
          } else {
            contentImgPath.value = res.data['path'];
          }
        }
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
