import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/teams/add_team/add_team_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class AddTeamPage extends StatelessWidget {
  AddTeamPage({super.key});

  final logic = Get.put(AddTeamLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: MyAppBar(
          title: Text(logic.teamId.value == ""
              ? S.current.create_team
              : S.current.team_update),
        ),
        body: _createTeamWidget(context),
        bottomNavigationBar: Container(
          width: double.infinity,
          padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () async {
              logic.getCreateTeam();
            },
            child: Container(
              height: 46.w,
              width: double.infinity,
              alignment: Alignment.center,
              margin: EdgeInsets.only(left: 20.w, right: 20.w),
              decoration: BoxDecoration(
                color: Colours.color282735,
                borderRadius: BorderRadius.all(Radius.circular(28.r)),
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Text(
                logic.teamId.value == ""
                    ? S.current.confirm_create
                    : S.current.update,
                style: TextStyles.display16.copyWith(fontSize: 16.sp),
              ),
            ),
          ),
        ),
      );
    });
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 15.w,
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                logic.getImage(0);
              },
              child: SizedBox(
                width: 95.w,
                height: 95.w,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    MyImage(
                      logic.headImgPath.value,
                      width: 90.w,
                      height: 90.w,
                      errorImage: "my_team_head3.png",
                      placeholderImage: "my_team_head3.png",
                      radius: 45.r,
                    ),
                    Positioned(
                        right: 0.w,
                        bottom: 0.w,
                        child: WxAssets.images.myTeamHeadAdd
                            .image(width: 30.w, height: 30.w))
                  ],
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 28.w),
              width: double.infinity,
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              height: 52.w,
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(16.r)),
              child: Row(
                children: [
                  Text(
                    S.current.Team_name,
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Expanded(
                      child: TextField(
                    controller: logic.txtController1,
                    style: TextStyles.regular,
                    inputFormatters: [
                      // FilteringTextInputFormatter.allow(
                      //     RegExp(r'[a-zA-Z0-9\u4E00-\u9FFF]')),
                      // FilteringTextInputFormatter.allow(
                      //     RegExp(r'[\u4e00-\u9fa50-9]')), // 只允许输入数字
                      CustomLengthLimitingTextInputFormatter(maxLength: 14),
                    ],
                    decoration: InputDecoration(
                      hintText: S.current.add_team_tips1,
                      hintStyle: TextStyles.regular
                          .copyWith(color: Colours.color5C5C6E),
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      //让文字垂直居中,
                      border: InputBorder.none,
                    ),
                    keyboardType: TextInputType.text,
                  )),
                  Text(
                    "${logic.textLength1.value}/7",
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 15.w),
              width: double.infinity,
              padding: EdgeInsets.all(20.w),
              height: 170.w,
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(16.r)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                      child: TextField(
                    controller: logic.txtController2,
                    style: TextStyles.regular,
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'[" "]')), // 只允许输入数字
                      LengthLimitingTextInputFormatter(150), // 限制输入长度为7
                    ],
                    decoration: InputDecoration(
                      hintText: S.current.add_team_tips2,
                      hintStyle: TextStyles.regular
                          .copyWith(color: Colours.color5C5C6E),
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      //让文字垂直居中,
                      border: InputBorder.none,
                    ),
                    keyboardType: TextInputType.text,
                    maxLines: 10,
                  )),
                  Text(
                    "${logic.textLength2.value}/150",
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  ),
                ],
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                logic.getImage(1);
              },
              child: Container(
                width: double.infinity,
                height: 188.w,
                margin: EdgeInsets.only(top: 15.w, left: 20.w, right: 20.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(16.r)),
                child: logic.contentImgPath.value == ""
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          WxAssets.images.myTeamAdd
                              .image(width: 36.w, height: 36.w),
                          SizedBox(
                            height: 22.w,
                          ),
                          Text(
                            S.current.Upload_group_photo,
                            style: TextStyles.regular.copyWith(
                                color: Colours.color5C5C6E, fontSize: 14.sp),
                          ),
                        ],
                      )
                    : Stack(
                        alignment: Alignment.center,
                        children: [
                          MyImage(
                            logic.contentImgPath.value,
                            width: double.infinity,
                            height: 188.w,
                            errorImage: "error_image_width.png",
                            placeholderImage: "error_image_width.png",
                            radius: 16.r,
                            fit: BoxFit.cover,
                          ),
                          Container(
                            padding: EdgeInsets.all(10.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r),
                                color: Colours.color70000000),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                WxAssets.images.teamRefresh
                                    .image(width: 18.w, height: 18.w),
                                SizedBox(
                                  width: 2.w,
                                ),
                                Text(
                                  S.current.reupload,
                                  style: TextStyles.medium.copyWith(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w600),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                //  MyImage(
                //   "",
                //   width: 90.w,
                //   height: 90.w,
                //   errorImage: "my_team_head3.png",
                //   placeholderImage: "my_team_head3.png",
                //   radius: 16.r,
                // ),
              ),
            ),
          ],
        ),
      );
    });
  }
}

class CustomLengthLimitingTextInputFormatter extends TextInputFormatter {
  CustomLengthLimitingTextInputFormatter({required this.maxLength});

  final int maxLength;

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 计算输入值的有效长度：英文字符计为1，中文字符计为2
    int count = 0;
    for (final rune in newValue.text.runes) {
      if (rune >= 0x4E00 && rune <= 0x9FFF) {
        // 中文字符范围
        count += 2;
      } else {
        count += 1;
      }
      // 如果超过最大长度，则不允许更新
      if (count > maxLength) {
        return oldValue;
      }
    }
    return newValue;
  }
}
