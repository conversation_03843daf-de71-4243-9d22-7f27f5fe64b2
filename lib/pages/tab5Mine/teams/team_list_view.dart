import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/team_list_model.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_list_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 二级页面->我的球队列表
class TeamListPage extends StatelessWidget {
  TeamListPage({super.key});

  final logic = Get.put(TeamListLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_team),
      ),
      body: _listWidget(context),

      floatingActionButton: Obx(() {
        return logic.dataList.isEmpty
            ? const SizedBox()
            : FloatingActionButton(
                onPressed: () {
                  // 按钮点击事件处理代码 最多只能三个球队
                  //logic.getDateInfo();
                  if (logic.dataTeamList.length >= 3) {
                    WxLoading.showToast(S.current.max_create_team);
                  } else {
                    AppPage.to(Routes.addTeamPage, arguments: {"teamId": ""})
                        .then((v) {
                      logic.getdataList(
                          controller: logic.refreshController, isLoad: false);
                    });
                  }
                },
                //tooltip: '', // 按钮内部的图标
                backgroundColor: Colors.transparent, // 提示信息（长按显示）
                child: Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: (logic.dataTeamList.length >= 3)
                            ? [Colours.color999999, Colours.color999999]
                            : [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: const Icon(
                    Icons.add,
                    size: 30,
                    color: Colours.white,
                  ),
                ), // 按钮背景颜色
              );
      }),
      floatingActionButtonLocation:
          FloatingActionButtonLocation.endFloat, // 默认位置
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 89.w),
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            AppPage.to(Routes.addTeamPage,
                                arguments: {"teamId": ""}).then((v) {
                              logic.getdataList(
                                  controller: logic.refreshController,
                                  isLoad: false);
                            });
                          },
                          child: Container(
                            margin: EdgeInsets.only(bottom: 200.w, top: 30.w),
                            padding: EdgeInsets.only(
                                left: 25.w, right: 25.w, bottom: 9.w, top: 9.w),
                            decoration: BoxDecoration(
                                color: Colours.color22222D,
                                borderRadius: BorderRadius.circular(20.r)),
                            child: Text(
                              S.current.create_team,
                              style: TextStyle(
                                  color: Colours.white,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                        )
                      ],
                    )
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(bottom: 40.w),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return _listItemWidget(logic.dataList[position]);
                      }));
    });
  }

  /// 构建列表项
  Widget _listItemWidget(TeamListModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (logic.isSelectTeam) {
          AppPage.back(result: item);
          return;
        }
        AppPage.to(Routes.teamInfoPage, arguments: {
          'teamId': item.id,
        }).then((v) {
          logic.getdataList(controller: logic.refreshController, isLoad: false);
        });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: Colours.color191921),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                MyImage(
                  item.logo ?? "",
                  width: 46.w,
                  height: 46.w,
                  radius: 23.r,
                  placeholderImage: "my_team_head4.png",
                  errorImage: "my_team_head4.png",
                ),
                if (item.defaultTeam == true)
                  Transform.translate(
                      offset: Offset(-8.w, -8.w),
                      child: WxAssets.images.teamMain
                          .image(width: 24.w, height: 18.w)),
              ],
            ),
            SizedBox(
              width: 15.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      //style: DefaultTextStyle.of(context).style,
                      children: <InlineSpan>[
                        TextSpan(
                          text: "${item.name ?? ""} ",
                          style: TextStyles.regular.copyWith(
                              fontWeight: FontWeight.w600, fontSize: 16.sp),
                        ),
                        if (item.leader == true)
                          WidgetSpan(
                            child: Container(
                              height: 20.w,
                              width: 36.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Colours.color2E1575,
                                  border: Border.all(
                                      width: 1, color: Colours.color6435E9),
                                  borderRadius: BorderRadius.circular(10.r)),
                              child: Text(
                                S.current.leader,
                                style: TextStyles.display10
                                    .copyWith(color: Colours.white),
                              ),
                            ),
                          ),
                        WidgetSpan(
                            child: SizedBox(
                          width: 6.w,
                        )),
                        WidgetSpan(
                            child: Image.asset(item.imagePath ?? '',
                                width: 24.w, height: 24.w)),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 13.w,
                  ),
                  Text(
                    "${S.current.win}${item.winCount}\t|\t${S.current.lose}${item.loseCount}\t|\t${S.current.Team_member}${item.playerCount}",
                    style: TextStyles.regular
                        .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                  ),
                ],
              ),
            ),
            Text(
              S.current.work_count(item.playedCount ?? "0"),
              style: TextStyles.regular
                  .copyWith(fontSize: 14.sp, color: Colours.color9393A5),
            ),
            MyImage("ic_arrow_right.png",
                width: 14.w,
                height: 14.w,
                isAssetImage: true,
                imageColor: Colours.color9393A5),
          ],
        ),
      ),
    );
  }
}
