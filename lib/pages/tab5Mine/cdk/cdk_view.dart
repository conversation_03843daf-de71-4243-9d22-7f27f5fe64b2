import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/cdk/cdk_logic.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../routes/app.dart';

//我的-> CDK兑换
class CDKPage extends StatelessWidget {
  CDKPage({super.key});
  final logic = Get.put(CDKLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
              child: SingleChildScrollView(
            child: Column(
              children: [
                WxAssets.images.cdkBg1
                    .image(width: 375.w, height: 370.w, fit: BoxFit.fill),
                SizedBox(
                  height: 16.w,
                ),
                Text(
                  S.current.cdk_tips,
                  style: TextStyles.regular
                      .copyWith(fontWeight: FontWeight.w600, fontSize: 16.sp),
                ),
                Container(
                  margin: EdgeInsets.only(
                      left: 30.w, right: 30.w, top: 30.w, bottom: 35.w),
                  width: double.infinity,
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  height: 50.w,
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Row(
                    children: [
                      Expanded(
                          child: TextField(
                        controller: logic.codeController,
                        style: TextStyles.regular,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(RegExp(
                              r'[" "\u4E00-\u9FFF]')), // 不允许空格RegExp(r'[a-zA-Z0-9\u4E00-\u9FFF]')
                          LengthLimitingTextInputFormatter(18), // 限制输入长度为7
                        ],
                        decoration: InputDecoration(
                          hintText: S.current.cdk_tips1,
                          hintStyle: TextStyles.regular
                              .copyWith(color: Colours.color5C5C6E),
                          contentPadding:
                              const EdgeInsets.only(top: 0, bottom: 0),
                          //让文字垂直居中,
                          border: InputBorder.none,
                        ),
                        keyboardType: TextInputType.text,
                      )),
                    ],
                  ),
                ),
                WxButton(
                  text: S.current.exchange_now,
                  textStyle: TextStyles.semiBold.copyWith(
                    color: logic.isCanExchange.value
                        ? Colours.white
                        : Colours.colorA8A8BC,
                  ),
                  margin: EdgeInsets.symmetric(horizontal: 30.w),
                  linearGradient: !logic.isCanExchange.value
                      ? null
                      : GradientUtils.mainGradient,
                  height: 55.w,
                  borderRadius: BorderRadius.circular(27.5.w),
                  backgroundColor:
                      logic.isCanExchange.value ? null : Colours.color5C5C6E,
                  onPressed: logic.getExchangeCDKey,
                ),
              ],
            ),
          )),
          Positioned(
            top: MediaQuery.of(context).padding.top,
            left: 0,
            width: ScreenUtil().screenWidth,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 60.w,
                  child: IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: Colours.white,
                    ),
                    onPressed: () {
                      AppPage.back();
                    },
                  ),
                ),
                Text(
                  "CDK兑换",
                  style: TextStyles.regular.copyWith(fontSize: 16.sp),
                ),
                SizedBox(
                  width: 60.w,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
