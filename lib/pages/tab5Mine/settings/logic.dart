import 'dart:async';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/pages/tab5Mine/settings/state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:ui_packages/ui_packages.dart';
//import 'package:xbr_gaode_navi_amap/location/xbr_location_service.dart';
import '../../../generated/l10n.dart';
import '../../../network/api_url.dart';
import '../../../widgets/update_version.dart';
import '../../login/user.dart';

class SettingLogic extends GetxController {
  StreamSubscription? subscription;
  var phoneStr = ''.obs;
  final state = SettingsState();
  @override
  void onInit() {
    super.onInit();
    phoneStr.value = UserManager.instance.userInfo.value?.phone ?? '';
    subscription = BusUtils.instance.on((action) {
      if (EventBusKey.changePhoneNumber == action.key) {
        phoneStr.value = UserManager.instance.userInfo.value?.phone ?? '';
      }
    });
  }

  @override
  void onReady() async {
    super.onReady();
    //测试时可以用下面的KEY,这是本人申请的个人账户，上线自己申请
    //initKey();
    state.isCanUpdateVersion.value = await UpdateVersion.getCanVersion();
    state.isOpenRecommendation.value =
        await WxStorage.instance.getString("isOpenRecommendation") == "1"
            ? true
            : false;
  }

  void deregisterAccount() {
    Get.dialog(CustomAlertDialog(
      title: S.current.account_cancellation,
      content: S.current.cancellation_prompt,
      onPressed: _deleteAccount,
    ));
  }

  void _deleteAccount() async {
    AppPage.back();
    WxLoading.show();
    final res = await Api().delete(ApiUrl.deleteAccount);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.account_cancellation_successful);
      final database =
          await $FloorAppDatabase.databaseBuilder('app_database.db').build();
      final optionGoalDao = database.optionGoalDao;
      await optionGoalDao
          .deleteAllUserId(UserManager.instance.userInfo.value?.userId ?? "");
      UserManager.instance.logout();
    }
  }

  Future<void> logout() async {
    Api().post(ApiUrl.logout);
    UserManager.instance.logout();
  }
}
