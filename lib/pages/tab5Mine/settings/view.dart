import 'package:flutter/material.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:utils_package/utils_package.dart';

import '../../../generated/l10n.dart';
import '../../../routes/app.dart';
import '../../../routes/route.dart';
import '../../../widgets/update_version.dart';
import '../mine_item_view.dart';
import 'logic.dart';

class SettingsPage extends StatelessWidget {
  SettingsPage({super.key});
  final logic = Get.put(SettingLogic());
  final state = Get.find<SettingLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.settings),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 2),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 15.w, right: 15.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: Colours.color191921,
                ),
                child: Column(
                  children: [
                    MineItemView(
                        height: 54.w,
                        text: S.current.about_us,
                        textStyle: TextStyles.semiBold14,
                        onTap: () => AppPage.to(Routes.aboutPage)),
                    Divider(
                      color: Colours.color99292937,
                      height: 1.w,
                    ),
                    Obx(() {
                      return MineItemView(
                          height: 54.w,
                          text: S.current.current_version,
                          textStyle: TextStyles.semiBold14,
                          rightWidget: Container(
                            height: 48.w,
                            alignment: Alignment.centerRight,
                            child: Row(
                              children: [
                                if (state.isCanUpdateVersion.value)
                                  Container(
                                    width: 6.w,
                                    height: 6.w,
                                    margin: EdgeInsets.only(right: 6.w),
                                    decoration: BoxDecoration(
                                        color: Colours.red,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(3.w))),
                                  ),
                                Text(
                                  "V ${WxAppInfoUtils.instance.version}",
                                  style: TextStyles.regular
                                      .copyWith(fontSize: 14.sp),
                                ),
                                SizedBox(
                                  width: 2.w,
                                ),
                              ],
                            ),
                          ),
                          onTap: () {
                            UpdateVersion.getVersion(context, type: 1);
                          });
                    }),
                    Divider(
                      color: Colours.color99292937,
                      height: 1.w,
                    ),
                    MineItemView(
                        height: 54.w,
                        text: S.current.deregister_account,
                        textStyle: TextStyles.semiBold14,
                        onTap: logic.deregisterAccount),
                    Divider(
                      color: Colours.color99292937,
                      height: 1.w,
                    ),
                    Obx(() {
                      return MineItemView(
                          height: 54.w,
                          textStyle: TextStyles.semiBold14,
                          text: S.current.recommendation,
                          onTap: () async {
                            logic.state.isOpenRecommendation.value =
                                !logic.state.isOpenRecommendation.value;
                            await WxStorage.instance.setString(
                                "isOpenRecommendation",
                                logic.state.isOpenRecommendation.value
                                    ? "1"
                                    : "0");
                          },
                          rightWidget: Container(
                            width: 40.w,
                            height: 30.w,
                            alignment: Alignment.centerLeft,
                            child: MyImage(
                              logic.state.isOpenRecommendation.value
                                  ? "switch_on.png"
                                  : "switch_off.png",
                              fit: BoxFit.fitWidth,
                              bgColor: Colors.transparent,
                              isAssetImage: true,
                              radius: 0.r,
                              width: 40.w,
                            ),
                          ));
                    }),
                    Divider(
                      color: Colours.color99292937,
                      height: 1.w,
                    ),
                    Obx(() {
                      return MineItemView(
                          height: 54.w,
                          text: S.current.binding_phone,
                          textStyle: TextStyles.semiBold14,
                          rightWidget: Container(
                            height: 48.w,
                            alignment: Alignment.centerRight,
                            child: Row(
                              children: [
                                Text(
                                  logic.phoneStr.value,
                                  style: TextStyles.semiBold14,
                                ),
                                SizedBox(
                                  width: 8.w,
                                ),
                                WxAssets.images.icArrowRight.image(
                                    width: 14.w,
                                    height: 14.w,
                                    color: Colors.white.withOpacity(0.5)),
                              ],
                            ),
                          ),
                          onTap: () =>
                              AppPage.to(Routes.oringinPhoneVerification));
                    }),
                  ],
                ),
              ),
              const Spacer(),
              _logout(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _logout() {
    return SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.only(bottom: 10.w),
          child: WxButton(
            backgroundColor: Colours.color0F0F16,
            borderRadius: BorderRadius.circular(22.w),
            borderSide: BorderSide(color: Colours.white, width: 1.w),
            height: 44.w,
            text: S.current.log_out,
            textStyle: TextStyles.bold
                .copyWith(color: Colours.colorFF3F3F, fontSize: 14.sp),
            onPressed: logic.logout,
          ),
        ));
  }
}
