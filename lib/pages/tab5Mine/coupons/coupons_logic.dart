import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';

class CouponsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var tabbarIndex = 0.obs;
  var tabNameList = [
    S.current.canuse_coupons,
    S.current.history_coupons,
  ].obs;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }
}
