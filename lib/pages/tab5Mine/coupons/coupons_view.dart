import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/coupons/coupons_logic.dart';
import 'package:shoot_z/pages/tab5Mine/coupons/list_items/item1/coupons_item_view1.dart';
import 'package:shoot_z/pages/tab5Mine/coupons/list_items/item2/orders_info_item_view2.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的订单
class CouponsPage extends StatelessWidget {
  CouponsPage({super.key});

  final logic = Get.put(CouponsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_coupons),
      ),
      body: Obx(() {
        return Column(
          children: [
            Container(
              width: double.infinity,
              height: 40.w,
              alignment: Alignment.centerLeft,
              color: Colours.bg_color,
              child: TabBar(
                  controller: logic.tabController,
                  unselectedLabelColor: Colours.color5C5C6E,
                  unselectedLabelStyle: TextStyle(
                      fontSize: 18.sp,
                      color: Colours.color5C5C6E,
                      fontWeight: FontWeight.w600),
                  labelColor: Colours.white,
                  labelStyle: TextStyle(
                      fontSize: 20.sp,
                      color: Colours.white,
                      fontWeight: FontWeight.w600),
                  isScrollable: false,
                  // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                  indicatorPadding: EdgeInsets.zero,
                  dividerColor: Colors.transparent,
                  dividerHeight: 0,
                  labelPadding:
                      const EdgeInsets.symmetric(horizontal: 4.0), // 调整标签间的间距
                  indicatorSize: TabBarIndicatorSize.label,
                  padding: EdgeInsets.symmetric(horizontal: 85.w),
                  indicatorColor: Colors.transparent,
                  tabs: List.generate(logic.tabNameList.length, (index) {
                    return SizedBox(
                      width: 80.w,
                      height: 40.w,
                      child: Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          if (logic.tabbarIndex.value == index)
                            WxAssets.images.imgCheckIn2
                                .image(width: 19.w, height: 9.w),
                          Positioned(
                              bottom: 10.w,
                              child: ShaderMask(
                                  shaderCallback: (bounds) =>
                                      const LinearGradient(
                                        colors: [
                                          Colours.colorFFF9DC,
                                          Colours.colorE4C8FF,
                                          Colours.colorE5F3FF,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ).createShader(bounds),
                                  child: Text(
                                    logic.tabNameList[index],
                                    style: TextStyles.regular.copyWith(
                                      fontSize: logic.tabbarIndex.value == index
                                          ? 16.sp
                                          : 14.sp,
                                      color: logic.tabbarIndex.value == index
                                          ? Colours.white
                                          : Colours.color5C5C6E,
                                      fontWeight:
                                          logic.tabbarIndex.value == index
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                    ),
                                  ))),
                        ],
                      ),
                    );
                  })),
            ),
            SizedBox(
              height: 10.w,
            ),
            Expanded(
              child: TabBarView(controller: logic.tabController, children: [
                CouponsItemPage1(
                  key: const Key("1"),
                ),
                CouponsItemPage2(
                  key: const Key("2"),
                ),
              ]),
            ),
          ],
        );
      }),
    );
  }
}
