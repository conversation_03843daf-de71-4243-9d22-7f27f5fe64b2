import 'package:get/get.dart';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/coupon_list_model.dart';

class CouponsItemLogic1 extends GetxController
    with GetSingleTickerProviderStateMixin {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <CouponListModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 20,
    };
    var res = await Api().get(ApiUrl.myCouponsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      List<CouponListModel> modelList =
          list.map((e) => CouponListModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
      if (modelList.length < 20) {
        await Future.delayed(const Duration(milliseconds: 200));
        controller.loadNoData();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
