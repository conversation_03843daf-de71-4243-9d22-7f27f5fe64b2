import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/coupon_list_model.dart';
import 'package:shoot_z/pages/tab5Mine/coupons/list_items/item1/coupons_item_logic1.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的优惠券列表
class CouponsItemPage1 extends StatelessWidget {
  CouponsItemPage1({super.key});

  final logic = Get.put(CouponsItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty && logic.dataList.length >= 20,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 480.w,
                    child: myNoDataView(
                      context,
                      height: 1,
                      msg: S.current.no_coupons,
                      imagewidget: WxAssets.images.couponsNodata
                          .image(width: 180.w, height: 120.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(CouponListModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      height: 120.w,
      padding: EdgeInsets.only(left: 20.w, right: 18.w, top: 15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colours.color191921,
          image: const DecorationImage(
              image: AssetImage("assets/images/coupons_bg.png"),
              fit: BoxFit.fill)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 16.sp,
                          color: Colours.color191921,
                          fontWeight: FontWeight.w600),
                    ),
                    SizedBox(
                      height: 10.w,
                    ),
                    Text(
                      item.description ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.colorA8A8BC,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: item.type == 3
                        ? (double.parse(item.discount ?? "0") * 10)
                            .toStringAsFixed(1)
                        : item.type == 2
                            ? "￥"
                            : item.type == 1
                                ? S.current.Free_admission
                                : "", //type	integer1 次数券；2 金额抵扣券；3 折扣券
                    style: TextStyle(
                        color: Colours.colorA44EFF,
                        fontSize: item.type == 2 ? 12.sp : 30.sp,
                        height: 2,
                        fontWeight: FontWeight.w600),
                    children: <InlineSpan>[
                      TextSpan(
                          text: item.type == 3
                              ? S.current.fold
                              : item.type == 2
                                  ? item.price
                                  : "",
                          style: TextStyle(
                              color: Colours.colorA44EFF,
                              fontSize: item.type == 2 ? 30.sp : 12.sp,
                              height: 2,
                              fontWeight: FontWeight.w600)),
                    ]),
              ),
            ],
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            height: 40.w,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                WxAssets.images.couponsTime.image(width: 14.w, height: 14.w),
                SizedBox(
                  width: 5.w,
                ),
                Expanded(
                  child: Text(
                    ((item.remainingDays ?? 0) == -1)
                        ? S.current.long_effective
                        : (item.effectiveTime ?? "") == ""
                            ? "${(item.expireTime ?? "").length > 16 ? (item.expireTime ?? "").substring(0, 16) : (item.expireTime ?? "")}到期"
                            : "${(item.effectiveTime ?? "").length > 16 ? (item.effectiveTime ?? "").substring(0, 16) : (item.effectiveTime ?? "")}\t至\t${(item.effectiveTime ?? "").length > 16 ? (item.expireTime ?? "").substring(0, 16) : (item.expireTime ?? "")}",
                    style: TextStyles.regular.copyWith(
                      fontSize: 10.sp,
                      color: Colours.color5C5C6E,
                    ),
                    maxLines: 1,
                  ),
                ),
                if ((item.remainingDays ?? 0) != 0)
                  Text(
                    S.current.coupons_have_days(item.remainingDays ?? 0),
                    style: TextStyles.regular.copyWith(
                      fontSize: 10.sp,
                      color: Colours.color5C5C6E,
                    ),
                    maxLines: 1,
                  ),
                SizedBox(
                  width: 2.w,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
