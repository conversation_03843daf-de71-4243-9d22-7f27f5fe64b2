import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/vip/model/vip_price_model.dart';
import 'package:shoot_z/pages/tab5Mine/vip/state.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/utils/pay/pay_utils.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:ui_packages/generated/l10n.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../gen/assets.gen.dart';
import '../../../generated/l10n.dart';
import '../../../inappwebview/router.dart';
import '../../../routes/route.dart';
import '../../../utils/event_bus.dart';
import '../../../widgets/MyImage.dart';
import 'serach/view.dart';

class VipLogic extends GetxController {
  final state = VipState();

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      state.fromReport = Get.arguments;
    }
    state.paySubscription = BusUtils.instance.on((event) async {
      if (event.key == EventBusKey.payResult) {
        if (event.action == true) {
          paySuccess();
        }
      }
    });
    getPriceList();
  }

  @override
  void onClose() {
    super.onClose();
    state.paySubscription?.cancel();
  }

  void switchVip(bool value) {
    state.switchVip.value = value;
    if (value) {
      if (state.fromReport) {
        Get.dialog(const CustomAlertDialog(
          title: '温馨提示',
          content: 'VIP不包含赛事权益',
          hideCancel: true,
          sureText: '确认',
          hideAllCancel: false,
        ));
      }
      state.fromReport = false;
    }
//value  false svip  true vip
    //int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
    UserManager.instance.postApmTracking(
      1,
      nowPage: Routes.vipPage,
      subPage: value ? "vipPageVip" : "vipPageSvip",
      remark: "vip页面切换",
      content: value ? "vip页面vip点击切换vip开通页面" : "vip页面Svip点击切换Svip开通页面",
    );
  }

  Future<void> getPriceList() async {
    WxLoading.show();
    final res = await Api().get(ApiUrl.vipPriceList,
        queryParameters: {'channel': Platform.isIOS ? "iOS" : "Android"});
    if (res.isSuccessful()) {
      state.vipPriceModel.value = VipPriceModel.fromJson(res.data);
      state.vipPriceModel.refresh();
    }
    WxLoading.dismiss();
  }

  void didVipPolicy() {
    WebviewRouter router = WebviewRouter(
        url: ApiUrl.vipPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: '会员服务协议');
    AppPage.to(Routes.webview, arguments: router);
  }

  void didPrivacyPolicy() {
    WebviewRouter router = WebviewRouter(
        url: ApiUrl.privacyPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.privacy_policy);
    AppPage.to(Routes.webview, arguments: router);
  }

  Vip? get price {
    final vipPriceModel = state.vipPriceModel.value;
    return state.switchVip.value
        ? vipPriceModel?.vip[state.selVipIndex.value]
        : vipPriceModel?.svip[state.selSVipIndex.value];
  }

  void pay() async {
    if (price == null) return;
    if (state.switchVip.value && state.place.value == null) {
      WxLoading.showToast(S.current.please_select_stadium);
      return;
    }

    WxLoading.show();
    final arenaId =
        state.switchVip.value ? state.place.value!.arenaID.toString() : "0";
    final clientType = GetPlatform.isIOS ? "2" : "1";
    final orderRes = await Api().post(ApiUrl.createOrder, data: {
      'arenaId': arenaId,
      'clientType': clientType,
      'vipSkuId': price!.id
    });
    if (!orderRes.isSuccessful()) {
      WxLoading.dismiss();
      return;
    }
    final orderId = orderRes.data['orderId'];
    if (GetPlatform.isIOS) {
      WxLoading.dismiss();
      final appProductId = orderRes.data['appProductId'];
      PayUtils.instance.applePay(appProductId, orderId);
    } else {
      final res = await Api()
          .post(ApiUrl.pay, data: {'orderId': orderId, 'channel': 1});
      WxLoading.dismiss();
      if (res.isSuccessful()) {
        final channelPayParams = res.data['channelPayParams'];
        if (channelPayParams != null) {
          PayUtils.instance.wxPay(channelPayParams);
        } else {
          WxLoading.showToast('获取支付参数出错');
        }
      }
    }
    //int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
    UserManager.instance.postApmTracking(
      1,
      nowPage: Routes.vipPage,
      subPage: state.switchVip.value ? "vipPageVip" : "vipPageSvip",
      remark: state.switchVip.value ? "vip付费" : "Svip付费",
      content: state.switchVip.value ? "点击支付开通vip" : "点击支付开通Svip",
    );
  }

  void showPlaceSheet(BuildContext context1) {
    showModalBottomSheet(
      context: context1,
      backgroundColor: Colours.bg_color,
      isScrollControlled: true,
      // shape: const RoundedRectangleBorder(
      //   borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      // ),
      builder: (context) {
        return ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: SizedBox(
              height: Get.height - 98.w - MediaQuery.of(context1).padding.top,
              width: Get.width,
              child: Column(children: [
                SizedBox(
                  height: 9.w,
                ),
                Container(
                  width: 38.w,
                  height: 4.w,
                  decoration: BoxDecoration(
                    color: Colours.colorD8D8D8.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(2.w),
                  ),
                ),
                SizedBox(
                  height: 17.w,
                ),
                const Expanded(child: SearchPlacePage()),
              ])),
        );
      },
    );
  }

  void selPlace(PlaceModel model) {
    state.place.value = model;
    AppPage.back();
  }

  void paySuccess() {
    Get.dialog(
      Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Material(
          type: MaterialType.transparency,
          color: Colors.transparent,
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    color: Colors.transparent,
                    child: Column(
                      children: <Widget>[
                        //upload_top_img
                        SizedBox(
                          height: 60.w,
                        ),
                        SizedBox(
                          height: 100.w,
                          width: double.infinity,
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Container(
                                height: 65.w,
                                width: double.infinity,
                                margin: EdgeInsets.only(top: 35.w),
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(25.r),
                                    topRight: Radius.circular(25.r),
                                  ),
                                ),
                              ),
                              Transform.translate(
                                offset: Offset(0, -30.w),
                                child: MyImage(
                                  "daka3.png",
                                  width: 78.w,
                                  height: 78.w,
                                  isAssetImage: true,
                                  fit: BoxFit.fitWidth,
                                  bgColor: Colors.transparent,
                                ),
                              ),
                            ],
                          ),
                        ),

                        Transform.translate(
                          offset: const Offset(0, -10),
                          child: Container(
                            alignment: Alignment.topLeft,
                            decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(25.r),
                                bottomRight: Radius.circular(25.r),
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            width: double.infinity,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  S.current.merge_videos_dialog_tips8(
                                      price?.point as Object),
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 18, color: Colours.colorA44EFF),
                                ),
                                SizedBox(
                                  height: 25.w,
                                ),
                                Text(
                                    '您已成功开通${price?.name}球秀${state.switchVip.value ? 'VIP' : 'SVIP'}',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colours.color9393A5,
                                      fontWeight: AppFontWeight.regular(),
                                      height: 1,
                                    )),
                                SizedBox(
                                  height: 35.w,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () => AppPage.back(),
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      UiS.current.ok,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  height: 30,
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(
                          height: 25.w,
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            AppPage.back();
                          },
                          child: WxAssets.images.icCloseDialog
                              .image(width: 30.w, height: 30.w),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierColor: Colors.black.withOpacity(0.85),
    );
  }
}
