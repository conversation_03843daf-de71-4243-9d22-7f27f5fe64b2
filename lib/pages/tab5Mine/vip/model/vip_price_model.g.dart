// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_price_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VipPriceModel _$VipPriceModelFromJson(Map<String, dynamic> json) =>
    VipPriceModel(
      (json['svip'] as List<dynamic>)
          .map((e) => Vip.fromJson(e as Map<String, dynamic>))
          .toList(),
      (json['vip'] as List<dynamic>)
          .map((e) => Vip.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$VipPriceModelToJson(VipPriceModel instance) =>
    <String, dynamic>{
      'svip': instance.svip,
      'vip': instance.vip,
    };

Vip _$VipFromJson(Map<String, dynamic> json) => Vip(
      json['appleProductId'] as String,
      json['day'] as int,
      json['description'] as String,
      json['discountPrice'] as String,
      json['id'] as String,
      json['name'] as String,
      json['price'] as String,
      json['userRecommend'] as bool,
      json['point'] as int,
    );

Map<String, dynamic> _$VipToJson(Vip instance) => <String, dynamic>{
      'appleProductId': instance.appleProductId,
      'day': instance.day,
      'description': instance.description,
      'discountPrice': instance.discountPrice,
      'id': instance.id,
      'name': instance.name,
      'price': instance.price,
      'userRecommend': instance.userRecommend,
      'point': instance.point,
    };
