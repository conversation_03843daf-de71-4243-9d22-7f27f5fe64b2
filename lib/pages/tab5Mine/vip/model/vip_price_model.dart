import 'package:json_annotation/json_annotation.dart';

part 'vip_price_model.g.dart';


@JsonSerializable()
class VipPriceModel extends Object {

  @Json<PERSON>ey(name: 'svip')
  List<Vip> svip;

  @Json<PERSON>ey(name: 'vip')
  List<Vip> vip;

  VipPriceModel(this.svip,this.vip,);

  factory VipPriceModel.fromJson(Map<String, dynamic> srcJson) => _$VipPriceModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$VipPriceModelToJson(this);

}


@JsonSerializable()
class Vip extends Object {

  @Json<PERSON>ey(name: 'appleProductId')
  String appleProductId;

  @JsonKey(name: 'day')
  int day;

  @JsonKey(name: 'description')
  String description;

  @JsonKey(name: 'discountPrice')
  String discountPrice;

  @JsonKey(name: 'id')
  String id;

  @<PERSON>son<PERSON>ey(name: 'name')
  String name;

  @<PERSON><PERSON><PERSON>ey(name: 'price')
  String price;

  @Json<PERSON>ey(name: 'userRecommend')
  bool userRecommend;

  @Json<PERSON>ey(name: 'point')
  int point;

  Vip(this.appleProductId,this.day,this.description,this.discountPrice,this.id,this.name,this.price,this.userRecommend,this.point);

  factory Vip.fromJson(Map<String, dynamic> srcJson) => _$VipFromJson(srcJson);

  Map<String, dynamic> toJson() => _$VipToJson(this);

}


