import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import '../../../../generated/l10n.dart';
import '../../../../network/api_url.dart';
import '../../../../utils/location_utils.dart';
import '../../../tab3Create/place/models/place_model.dart';
import 'state.dart';

class SearchPlaceLogic extends GetxController {
  final state = SearchPlaceState();
  @override
  void onInit() {
    super.onInit();
    requestPlace();
  }

  @override
  void onClose() {
    super.onClose();
    state.controller.dispose();
  }

  Future<void> requestPlace() async {
    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    WxLoading.show();
    var map = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}'
    };
    var url = ApiUrl.vipRecommendList;
    if (state.controller.text.isNotEmpty) {
      map['arenaName'] = state.controller.text;
      url = ApiUrl.arenasSearchModel;
    }
    final res = await Api().get(url, queryParameters: map);
    state.init.value = true;
    if (res.isSuccessful()) {
      state.list.value =
          (res.data as List).map((e) => PlaceModel.fromJson(e)).toList();
    } else {
      state.list.value = [];
      WxLoading.showToast(res.message);
    }
    WxLoading.dismiss();
  }
}
