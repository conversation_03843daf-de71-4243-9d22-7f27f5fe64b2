import 'dart:async';

import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';

import 'model/vip_price_model.dart';

class VipState {
  StreamSubscription? paySubscription;
  var switchVip = false.obs;
  var selSVipIndex = 0.obs;
  var selVipIndex = 0.obs;
  var vipPriceModel = Rx<VipPriceModel?>(null);
  var place = Rx<PlaceModel?>(null);
  static List<String> iconList = [
    'ssq',
    'free',
    'gp',
    'fm',
    'hy',
    'xc',
    '10s',
    'yj'
  ];
  static List<String> textList = [
    '个人赛事券',
    '免费下载',
    '跟拍效果',
    '慢放效果',
    '会员标识',
    '消除原声',
    '10秒片段',
    '一键成片'
  ];

  static List<String> iconList2 = [
    'yj',
    'free',
    'gp',
    'fm',
    'hy',
    'xc',
    '10s',
    'ssq',
  ];
  static List<String> textList2 = [
    '一键成片',
    '免费下载',
    '跟拍效果',
    '慢放效果',
    '会员标识',
    '消除原声',
    '10秒片段',
    '个人赛事券'
  ];
  var fromReport = false;
}
