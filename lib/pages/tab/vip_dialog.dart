import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';

import '../../routes/app.dart';

class VipDialog extends StatelessWidget {
  const VipDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          WxAssets.images.icVipPop.image(width: 305.w,height: 390.w,fit: BoxFit.fill),
          const SizedBox(height: 20,),
          GestureDetector(onTap: ()=> AppPage.back(),child: WxAssets.images.icCloseDialog.image()),
        ],
      ),
    );
  }
}
