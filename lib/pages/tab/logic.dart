import 'dart:convert';
import 'dart:developer';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/my_points_model.dart';
import 'package:shoot_z/network/model/obtain_notify_model.dart';
import 'package:shoot_z/pages/tab/vip_dialog.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/update_version.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../main.dart';
import '../../utils/event_bus.dart';
import '../../utils/location_utils.dart';
import '../login/user.dart';
import 'state.dart';

class TabLogic extends GetxController with WidgetsBindingObserver, RouteAware {
  final TabState state = TabState();

  @override
  void onInit() {
    super.onInit();
    getPointsList();
    WidgetsBinding.instance.addObserver(this);
    startConnectivity();
    //token过期event_bus通知处理
    state.allSubscription = BusUtils.instance.on((p0) {
      if (p0.key == Api.onUnauthorized) {
        // WxLoading.showToast("登录已过期,请重新登录");
        UserManager.instance.logout();
        // AppPage.back(page: Routes.tab);
        // state.currentIndex.value = 0;
        // state.pageController.jumpToPage(0);
      } else if (p0.key == EventBusKey.toCollectionHighlights) {
        AppPage.back(page: Routes.tab);
        state.currentIndex.value = 3;
        state.pageController.jumpToPage(3);
      } else if (p0.key == EventBusKey.toArenaDetails) {
        AppPage.back(page: Routes.arenaDetailsPage);
      }
    });
  }

  Future<void> getPointsList() async {
    Map<String, dynamic>? param = {};
    final res = await Api().get(ApiUrl.pointsAllTask, queryParameters: param);
    if (res.isSuccessful()) {
      var allTaskModel = MyPointsModel.fromJson(res.data);
      try {
        var item = (allTaskModel.taskList ?? [])
            .firstWhere((element) => (element?.id ?? 0) == 13);
        await WxStorage.instance
            .setString("createSitePoint", item?.point ?? '0');
      } catch (e) {
        log('未找到 ID 为 13 的元素');
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
    UpdateVersion.getVersion(Get.context!, type: 0);
    getNoticeList(0);
    if (UserManager.instance.isLogin) {
      UserManager.instance.postChannelSubmit(0);
    }
  }

  void subscribe(BuildContext context) {
    final ModalRoute? route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  /// 网络监听
  void startConnectivity() async {
    state.connectivityStreamSubscription ??=
        Connectivity().onConnectivityChanged.listen((event) async {
      debug("ConnectivityResult=$event");
      bool connectivity = event != ConnectivityResult.none;
      if (state.connectivity == connectivity) {
        return;
      }
      state.connectivity = event != ConnectivityResult.none;
      if (connectivity) {
        //从无网到有网
      }
    });
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
    WidgetsBinding.instance.removeObserver(this);
    state.connectivityStreamSubscription?.cancel();
    state.allSubscription?.cancel();
    routeObserver.unsubscribe(this);
  }

  Future<void> barOnTap(int index) async {
    switch (index) {
      case 0:
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(1,
            subPage: "tab1",
            remark: "点击tab切换",
            nowPage: Routes.main,
            content: "切换tab首页");
        break;
      case 1:
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(1,
            subPage: "tab2",
            remark: "点击tab切换",
            nowPage: Routes.main,
            content: "切换tab场地");
        break;
      case 2:
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(1,
            subPage: "tab3",
            remark: "点击tab切换",
            nowPage: Routes.main,
            content: "切换tab创作");
        break;
      case 3:
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(1,
            subPage: "tab4",
            remark: "点击tab切换",
            nowPage: Routes.main,
            content: "切换tab商城");
        break;
      case 4:
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(
          1,
          subPage: "tab5",
          remark: "点击tab切换",
          nowPage: Routes.main,
          content: "切换tab我的",
        );
        break;
    }
    if (index == 0) {
      getNoticeList(0);
    } else if (index == 1 && state.currentIndex.value != index) {
      BusUtils.instance.fire(EventAction(key: EventBusKey.bottomBarArenaClick));
    }

    if (index == 2) {
      return;
    }
    if ((index == 3 || index == 4) && !UserManager.instance.isLogin) {
      notLogged(index);
      return;
    } else if (index == 4) {
      UserManager.instance.pullUserInfo();
      UserManager.instance.getMySummary();
      UserManager.instance.getMessageHasUnread();
    }
    state.currentIndex.value = index;
    state.pageController.jumpToPage(index);
  }

  //获得公告弹窗
  Future<void> getNoticeList(int i) async {
    if (!UserManager.instance.isLogin) {
      return;
    }
    var param = {
      'shareType': "1",
    };

    final res = await Api().get(ApiUrl.obtainNotify, queryParameters: param);
    if (res.isSuccessful()) {
      if (res.data == null) {
        return;
      }
      log("getNoticeList=${jsonEncode(res.data)}-user=${UserManager.instance.user?.userId}");
      ObtainNotifyModel obtainNotifyModel =
          ObtainNotifyModel.fromJson(res.data);
      if (obtainNotifyModel.notify == true) {
        if (obtainNotifyModel.expiringSoon == true) {
          //即将过期
          getInvitationDialog4(obtainNotifyModel);
        } else {
          //获得优惠券
          getInvitationDialog3(obtainNotifyModel);
        }
      }
    }
  }

  //邀请成功弹窗公告
  void getInvitationDialog3(ObtainNotifyModel obtainNotifyModel) {
    return getMyDialog3(
      "",
      S.current.reclock,
      () {
        AppPage.back();
      },
      isShowClose: false,
      imageAsset: "Invitation_dialog1.png",
      imgHeight: 113.w,
      imgWidth: 113.w,
      padHorizontal: 60,
      btnIsHorizontal: true,
      bottomBtnWidget: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          AppPage.back();
        },
        child: Container(
          height: 46.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
            left: 50.w,
            top: 15.w,
            right: 50.w,
          ),
          decoration: BoxDecoration(
            color: Colours.color7F38ED,
            borderRadius: BorderRadius.all(Radius.circular(28.r)),
          ),
          child: Text(
            S.current.invitation_dialog_text7,
            style: TextStyles.regular.copyWith(
                fontSize: 15.sp,
                fontWeight: FontWeight.w600,
                color: Colours.white),
          ),
        ),
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.only(left: 25.w, right: 25.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 10.w,
              ),
              MyImage(
                "Invitation_dialog2.png",
                width: 89.w,
                fit: BoxFit.fitWidth,
                isAssetImage: true,
              ),
              SizedBox(
                height: 20.w,
              ),
              RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                    text: S.current.invitation_dialog_text1,
                    style: TextStyle(
                        color: Colours.color333333,
                        fontSize: 14.sp,
                        //  height: 1.2,
                        fontWeight: FontWeight.w400),
                    children: <InlineSpan>[
                      TextSpan(
                          text: S.current.invitation_dialog_text2(
                              obtainNotifyModel.count ?? ""),
                          style: TextStyle(
                              color: Colours.colorA44EFF,
                              fontSize: 14.sp,
                              // height: 1.2,
                              fontWeight: FontWeight.w400)),
                      TextSpan(
                          text: S.current.invitation_dialog_text3,
                          style: TextStyle(
                              color: Colours.color333333,
                              fontSize: 14.sp,
                              //  height: 1.2,
                              fontWeight: FontWeight.w400)),
                    ]),
              ),
              SizedBox(
                height: 15.w,
              ),
              Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                        S.current.invitation_dialog_text4(
                            obtainNotifyModel.expireTime ?? ""),
                        style: TextStyle(
                            color: Colours.color333333,
                            fontSize: 12.sp,
                            //  height: 1.2,
                            fontWeight: FontWeight.w400)),
                    SizedBox(
                      height: 5.w,
                    ),
                    RichText(
                      textAlign: TextAlign.start,
                      text: TextSpan(
                          text: S.current.invitation_dialog_text5,
                          style: TextStyle(
                              color: Colours.color333333,
                              fontSize: 12.sp,
                              //  height: 1.2,
                              fontWeight: FontWeight.w400),
                          children: <InlineSpan>[
                            TextSpan(
                                text: S.current.invitation_dialog_text6,
                                style: TextStyle(
                                    color: Colours.colorA44EFF,
                                    fontSize: 12.sp,
                                    // height: 1.2,
                                    fontWeight: FontWeight.w400)),
                          ]),
                    ),
                  ],
                ),
              ),
            ],
          )),
    );
  }

//邀请成功弹窗公告 优惠券即将过期
  void getInvitationDialog4(ObtainNotifyModel obtainNotifyModel) {
    return getMyDialog3(
      "",
      S.current.reclock,
      () {
        AppPage.back();
      },
      isShowClose: false,
      imageAsset: "Invitation_dialog3.png",
      imgHeight: 86.w,
      imgWidth: 86.w,
      padHorizontal: 60,
      btnIsHorizontal: true,
      bottomBtnWidget: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          AppPage.back();
        },
        child: Container(
          height: 46.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
            left: 50.w,
            top: 15.w,
            right: 50.w,
          ),
          decoration: BoxDecoration(
            color: Colours.color7F38ED,
            borderRadius: BorderRadius.all(Radius.circular(28.r)),
          ),
          child: Text(
            S.current.invitation_dialog_text7,
            style: TextStyles.regular.copyWith(
                fontSize: 15.sp,
                fontWeight: FontWeight.w600,
                color: Colours.white),
          ),
        ),
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.only(left: 25.w, right: 25.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 10.w,
              ),
              MyImage(
                "Invitation_dialog4.png",
                width: 177.w,
                height: 31.w,
                fit: BoxFit.fitWidth,
                isAssetImage: true,
              ),
              SizedBox(
                height: 20.w,
              ),
              RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                    text: S.current.invitation_dialog_text8,
                    style: TextStyle(
                        color: Colours.color333333,
                        fontSize: 14.sp,
                        //  height: 1.2,
                        fontWeight: FontWeight.w400),
                    children: <InlineSpan>[
                      TextSpan(
                          text: S.current.invitation_dialog_text2(
                              obtainNotifyModel.count ?? ""),
                          style: TextStyle(
                              color: Colours.colorA44EFF,
                              fontSize: 14.sp,
                              // height: 1.2,
                              fontWeight: FontWeight.w400)),
                      TextSpan(
                          text: S.current.invitation_dialog_text9,
                          style: TextStyle(
                              color: Colours.color333333,
                              fontSize: 14.sp,
                              //  height: 1.2,
                              fontWeight: FontWeight.w400)),
                    ]),
              ),
              SizedBox(
                height: 15.w,
              ),
              Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                        S.current.invitation_dialog_text4(
                            obtainNotifyModel.expireTime ?? ""),
                        style: TextStyle(
                            color: Colours.color333333,
                            fontSize: 12.sp,
                            //  height: 1.2,
                            fontWeight: FontWeight.w400)),
                    SizedBox(
                      height: 5.w,
                    ),
                    RichText(
                      textAlign: TextAlign.start,
                      text: TextSpan(
                          text: S.current.invitation_dialog_text5,
                          style: TextStyle(
                              color: Colours.color333333,
                              fontSize: 12.sp,
                              //  height: 1.2,
                              fontWeight: FontWeight.w400),
                          children: <InlineSpan>[
                            TextSpan(
                                text: S.current.invitation_dialog_text6,
                                style: TextStyle(
                                    color: Colours.colorA44EFF,
                                    fontSize: 12.sp,
                                    // height: 1.2,
                                    fontWeight: FontWeight.w400)),
                          ]),
                    ),
                  ],
                ),
              ),
            ],
          )),
    );
  }

  void notLogged(int index) {
    state.loginSubscription = BusUtils.instance.on((p0) async {
      if (p0.key == EventBusKey.loginSuccessful) {
        if (index == 4) {
          UserManager.instance.getMySummary();
          UserManager.instance.getMessageHasUnread();
        }
        state.currentIndex.value = index;
        state.pageController.jumpToPage(index);
        state.loginSubscription?.cancel();
        state.loginSubscription = null;
      } else if (p0.key == EventBusKey.loginCanceled) {
        state.loginSubscription?.cancel();
        state.loginSubscription = null;
      }
    });
    AppPage.to(Routes.login);
  }

  var paused = false;
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    // TODO: implement didChangeAppLifecycleState
    super.didChangeAppLifecycleState(state);
    debug("didChangeAppLifecycleState---$state");
    if (state == AppLifecycleState.resumed) {
      if (!LocationUtils.instance.havePermission.value) {
        final location = await LocationUtils.instance.checkPermission();
        if (location) {
          final position = await LocationUtils.instance.getCurrentPosition();
          if (position != null) {
            BusUtils.instance.fire(EventAction(key: EventBusKey.getLocation));
          }
        }
      }
      if (paused) {
        paused = false;
        if (UserManager.instance.isLogin) UserManager.instance.pullUserInfo();
      }
    } else if (state == AppLifecycleState.paused) {
      paused = true;
    }
  }

  void showVipPop() {
    if (!UserManager.instance.isFirstLogin) return;
    Get.dialog(const VipDialog());
    UserManager.instance.isFirstLogin = false;
  }

  @override
  void didPopNext() {
    // TODO: implement didPopNext
    super.didPopNext();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showVipPop();
    });
  }

  @override
  void didPush() {
    super.didPush();
    showVipPop();
  }
}
