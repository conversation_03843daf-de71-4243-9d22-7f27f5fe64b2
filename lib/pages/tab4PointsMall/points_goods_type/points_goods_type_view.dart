import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/inappwebview/router.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_goods_type/points_goods_type_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的->积分页面
class PointsGoodsTypePage extends StatefulWidget {
  const PointsGoodsTypePage({super.key});

  @override
  State<PointsGoodsTypePage> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<PointsGoodsTypePage> {
  final logic = Get.put(PointsGoodsTypeLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: MyAppBar(
          title: Text(S.current.points_shopping_Mall),
        ),
        body: Obx(() {
          return Center(
            child: (logic.isFrist.value)
                ? buildLoad()
                : Column(
                    children: [
                      Container(
                        height: 44.w,
                        decoration: BoxDecoration(
                          color: Colours.color191921,
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        margin: EdgeInsets.symmetric(
                            horizontal: 15.w, vertical: 15.w),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: logic.textController,
                                style: TextStyles.regular.copyWith(
                                    color: Colours.white, fontSize: 14.sp),
                                inputFormatters: [
                                  FilteringTextInputFormatter.deny(
                                      RegExp(r'[" "]')), // 只允许输入数字
                                  LengthLimitingTextInputFormatter(
                                      20), // 限制输入长度为7
                                ],
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: '请输入商品关键词',
                                  contentPadding: EdgeInsets.only(bottom: 3.w),
                                  hintStyle: TextStyle(
                                    color: Colours.color5C5C6E,
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                textInputAction: TextInputAction.search,
                                onSubmitted: (value) {
                                  // BusUtils.instance.fire(EventAction(
                                  //     key: EventBusKey.searchGoodName,
                                  //     action: value));
                                  if (value.trim() == "") {
                                    logic.isShowSearch.value = false;
                                  } else {
                                    logic.isShowSearch.value = true;
                                    logic.getdataList(isLoad: false);
                                  }
                                },
                              ),
                            ),
                            if (logic.isShowSearch.value)
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  logic.textController.text = "";
                                  logic.isShowSearch.value = false;
                                },
                                child: Container(
                                  width: 40.w,
                                  height: 40.w,
                                  padding: EdgeInsets.only(right: 10.w),
                                  alignment: Alignment.centerRight,
                                  child: WxAssets.images.close2.image(
                                      width: 15.w,
                                      height: 15.w,
                                      color: Colors.white),
                                ),
                              )
                          ],
                        ),
                      ),
                      if (logic.isShowSearch.value)
                        (logic.dataFag["isFrist"] as bool)
                            ? buildLoad()
                            : logic.dataList.isEmpty
                                ? SizedBox(
                                    height: 480.w,
                                    child: myNoDataView(
                                      context,
                                      msg: S.current.No_data_available,
                                      imagewidget: WxAssets.images.icGameNo
                                          .image(width: 150.w, height: 150.w),
                                    ))
                                : Expanded(
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                          left: 15.w, right: 15.w),
                                      child: GridView.builder(
                                          scrollDirection: Axis.vertical,
                                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                          shrinkWrap: true,
                                          physics:
                                              const AlwaysScrollableScrollPhysics(),
                                          gridDelegate:
                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 2,
                                            crossAxisSpacing: 15,
                                            mainAxisSpacing: 15,
                                            childAspectRatio: 165 / 222,
                                          ),
                                          itemCount: logic.dataList.length,
                                          itemBuilder: (context, position) {
                                            // String base64 =  ImageUtils.netImageToBase64(images[position]);
                                            // myLog(base64, StackTrace.current);
                                            return GestureDetector(
                                              behavior:
                                                  HitTestBehavior.translucent,
                                              onTap: () {
                                                var url = "";
                                                if (const String
                                                        .fromEnvironment('env',
                                                        defaultValue: 'dev') !=
                                                    'pro') {
                                                  url =
                                                      "https://idev.shootz.tech/product/goods-detail?id=${logic.dataList[position].spuId}&userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                                                } else {
                                                  url =
                                                      "https://i.shootz.tech/product/goods-detail?id=${logic.dataList[position].spuId}&userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                                                }

                                                //测试 https://idev.shootz.tech/kf 正式 https://i.shootz.tech/kf
                                                WebviewRouter router =
                                                    WebviewRouter(
                                                        url: url,
                                                        showNavigationBar: true,
                                                        needBaseHttp: false,
                                                        resizeToAvoidBottomInset:
                                                            true,
                                                        title: "商品详情");
                                                AppPage.to(Routes.webviewh5,
                                                    arguments: router,
                                                    needLogin: true);

                                                // AppPage.to(Routes.goodsInfoPage,
                                                //     arguments: {
                                                //       "goodsId":
                                                //           "${logic.dataList[position].spuId}",
                                                //     },
                                                //     needLogin: true);
                                              },
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: Colours.color0F0F16,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10.r),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    MyImage(
                                                      logic.dataList[position]
                                                              .imageUrl ??
                                                          '',
                                                      fit: BoxFit.fill,
                                                      width: double.infinity,
                                                      height: 165.w,
                                                      radius: 10.r,
                                                      bgColor:
                                                          Colours.color000000,
                                                      errorImage:
                                                          "error_img_white.png",
                                                      placeholderImage:
                                                          "error_img_white.png",
                                                    ),
                                                    SizedBox(
                                                      height: 12.w,
                                                    ),
                                                    Expanded(
                                                      child: Text(
                                                        logic.dataList[position]
                                                                .goodsName ??
                                                            '',
                                                        maxLines: 1,
                                                        style: TextStyles
                                                            .semiBold14,
                                                      ),
                                                    ),
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        WxAssets.images.points2
                                                            .image(
                                                                width: 16.w,
                                                                height: 16.w),
                                                        SizedBox(
                                                          width: 3.w,
                                                        ),
                                                        Expanded(
                                                          child: ShaderMask(
                                                            shaderCallback:
                                                                (bounds) =>
                                                                    const LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: [
                                                                Colours
                                                                    .color7732ED,
                                                                Colours
                                                                    .colorA555EF,
                                                              ],
                                                            ).createShader(
                                                                        bounds),
                                                            child: Text(
                                                              "${logic.dataList[position].pointPrice ?? ''}",
                                                              style: TextStyles.din.copyWith(
                                                                  color: Colours
                                                                      .white,
                                                                  fontSize:
                                                                      14.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold),
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              maxLines: 1,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }),
                                    ),
                                  ),
                      if (!logic.isShowSearch.value)
                        Expanded(
                          child: Row(
                            children: [
                              Container(
                                width: 100.w,
                                color: Colours.color191921,
                                child: ListView.builder(
                                  itemCount: logic.tabs.length,
                                  itemBuilder: (context, index) {
                                    return Obx(() {
                                      return GestureDetector(
                                        onTap: () {
                                          logic.changeIndex(index);
                                        },
                                        child: Container(
                                          width: 100.w,
                                          height: 44.w,
                                          alignment: Alignment.center,
                                          color:
                                              logic.tabbarIndex.value == index
                                                  ? Colours.color0F0F16
                                                  : Colors.transparent,
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 4.w,
                                                height: 20.w,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(8.r)),
                                                  gradient: logic.tabbarIndex
                                                              .value ==
                                                          index
                                                      ? const LinearGradient(
                                                          colors: [
                                                            Colours.color7732ED,
                                                            Colours.colorA555EF
                                                          ],
                                                          begin: Alignment
                                                              .bottomLeft,
                                                          end: Alignment
                                                              .bottomRight,
                                                        )
                                                      : null,
                                                ),
                                              ),
                                              Expanded(
                                                  child: Center(
                                                      child: Text(
                                                logic.tabs[index].title,
                                                style: TextStyle(
                                                    color: logic.tabbarIndex
                                                                .value ==
                                                            index
                                                        ? Colours.white
                                                        : Colours.color5C5C6E,
                                                    fontSize: 14.sp,
                                                    fontWeight: logic
                                                                .tabbarIndex
                                                                .value ==
                                                            index
                                                        ? FontWeight.bold
                                                        : FontWeight.normal),
                                              ))),
                                            ],
                                          ),
                                        ),
                                      );
                                    });
                                  },
                                ),
                              ),
                              Expanded(
                                  child: logic.isFristGoods.value
                                      ? buildLoad()
                                      : TabBarView(
                                          controller: logic.tabController,
                                          physics:
                                              const NeverScrollableScrollPhysics(), // 禁止所有滚动,
                                          children: logic.tabs
                                              .map((tab) => tab.page)
                                              .toList(),
                                        )),
                            ],
                          ),
                        ),
                    ],
                  ),
          );
        }));
  }
}
