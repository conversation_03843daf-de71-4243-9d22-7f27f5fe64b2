// ignore_for_file: unnecessary_null_comparison

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/goods_detail_model.dart';
import 'package:shoot_z/pages/tab4PointsMall/goods_info/goods_info_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/RectIndicator.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///积分商城 商品详情
///
class GoodsInfoPage extends StatefulWidget {
  const GoodsInfoPage({super.key});

  @override
  State<GoodsInfoPage> createState() => _InputInviteCodePageState();
}

class _InputInviteCodePageState extends State<GoodsInfoPage> {
  final logic = Get.put(GoodsInfoLogic());
  @override
  void initState() {
    super.initState();
    // 设置当前页面的状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark, // Android
        statusBarBrightness: Brightness.light, // iOS
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // // 设置当前页面的状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark, // Android
        statusBarBrightness: Brightness.light, // iOS
      ),
    );

    return Scaffold(
      backgroundColor: Colours.color191921,
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : _goodsInfoWidget(context);
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? const SizedBox()
            : barWidget(context);
      }),
    );
  }

  InkWell barWidget(BuildContext context) {
    return InkWell(
      onTap: () {
        if (logic.goodsDetailModel.value.goodType == 2) {
          // for (var item in logic.goodsDetailModel.value.specList ?? []) {
          //   if (!(colorNameList.contains(item?.colorName) == true &&
          //       item?.colorName != "")) {
          //     colorNameList.add(item?.colorName ?? "");
          //   }
          //   if (!(sizeNameList.contains(item?.sizeName) == true &&
          //       item?.sizeName != "")) {
          //     sizeNameList.add(item?.sizeName ?? "");
          //   }
          // }
          getExchangeGoods(context);
        } else {
          getMyDialog(
            S.current.Exchange_confirmation,
            S.current.sure,
            () {
              AppPage.back();
              _getPointsExchangeGoods();
            },
            btnText2: S.current.cancel,
            onPressed2: () {
              AppPage.back();
            },
            isShowClose: false,
            btnIsHorizontal: true,
            contentWidget: Column(
              children: [
                Container(
                  padding: EdgeInsets.only(
                      left: 30.w, right: 30.w, bottom: 30.w, top: 10.w),
                  child: MyImage(
                    logic.goodsDetailModel.value.thumbUrl ?? '',
                    //  holderImg: "home/index/df_banner_top",
                    fit: BoxFit.fill,
                    width: 174.w,
                    height: 116.w,
                    // errorImg: "home/index/df_banner_top"
                    radius: 5.r,
                    bgColor: Colours.white,
                    errorImage: "error_img_white.png",
                    placeholderImage: "error_img_white.png",
                  ),
                ),
                RichText(
                  text: TextSpan(
                      text: S.current.Confirmed_use,
                      style: TextStyle(
                          color: Colours.color9393A5,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.normal),
                      children: <InlineSpan>[
                        TextSpan(
                            text:
                                "\t${logic.goodsDetailModel.value.pointPrice ?? 0}\t",
                            style: TextStyle(
                                color: Colours.colorA44EFF,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w600)),
                        TextSpan(
                            text: S.current.Point_exchange,
                            style: TextStyle(
                                color: Colours.color9393A5,
                                fontSize: 14.sp,
                                fontWeight: FontWeight.normal)),
                      ]),
                ),
              ],
            ),
          );
        }
      },
      child: Container(
        width: double.infinity,
        height: 50.w,
        alignment: Alignment.center,
        margin: EdgeInsets.only(
            left: 15.w,
            right: 15.w,
            bottom: ScreenUtil().bottomBarHeight + 10.w),
        decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colours.color7732ED, Colours.colorA555EF],
              begin: Alignment.bottomLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(25.r)),
        child: Text(
          '立即兑换',
          style: TextStyles.semiBold14,
        ),
      ),
    );
  }

  Widget _goodsInfoWidget(BuildContext context) {
    return Obx(() {
      return Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SizedBox(height: ScreenUtil().statusBarHeight,),
                if ((logic.goodsDetailModel.value.bannerUrl?.length ?? 0) > 0)
                  SizedBox(
                    height: 375.w,
                    child: Swiper(
                      itemBuilder: (context, position) {
                        return MyImage(
                            logic.goodsDetailModel.value.bannerUrl?[position] ??
                                '',
                            fit: BoxFit.fill,
                            width: double.infinity,
                            height: 375.w,
                            radius: 0.r,
                            bgColor: Colors.white,
                            errorImage: "error_image_width.png");
                      },
                      itemCount:
                          logic.goodsDetailModel.value.bannerUrl?.length ?? 0,
                      autoplay: true,
                      loop: true,
                      controller: logic.swiperController,
                      // pagination: SwiperPagination( alignment: Alignment.bottomRight),
                      pagination: SwiperPagination(
                        alignment: Alignment.bottomRight,
                        builder: SwiperCustomPagination(builder:
                            (BuildContext context, SwiperPluginConfig config) {
                          return RectIndicator(
                              alignment: Alignment.bottomRight,
                              position: config.activeIndex,
                              count: logic.goodsDetailModel.value.bannerUrl
                                      ?.length ??
                                  0,
                              // color: Colors.white.withOpacity(1),
                              // activeColor: const Color(0xFFD8D8D8),
                              color: Colours.color964AEE,
                              activeColor: Colours.color666666,
                              //未选中 指示器颜色，选中的颜色key为Color
                              width: 7.0,
                              //指示器宽度
                              activeWidth: 6.0,
                              //选中的指示器宽度
                              radius: 2,
                              //指示器圆角角度
                              height: 4.0);
                        }),
                      ),
                      onTap: (position) async {
                        // if (await canLaunch(ads[position].url.toString())) {
                        //   launch(ads[position].url.toString());
                        // }
                      },
                      onIndexChanged: (value) {
                        // logic.changeSwiper(value);
                      },
                    ),
                  ),
                if ((logic.goodsDetailModel.value.bannerUrl?.length ?? 0) == 0)
                  Container(
                    width: double.infinity,
                    height: 375.w,
                    alignment: Alignment.center,
                    color: Colours.white,
                    child: MyImage(
                      logic.goodsDetailModel.value.thumbUrl ?? '',
                      fit: BoxFit.contain,
                      width: 304.w,
                      height: 304.w,
                      bgColor: Colours.white,
                      errorImage: "error_img_white.png",
                      radius: 0.r,
                      placeholderImage: "error_img_white.png",
                    ),
                  ),

                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        (logic.goodsDetailModel.value.goodsName ?? ""),
                        maxLines: 2,
                        style: TextStyles.titleSemiBold16,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(
                        height: 20.w,
                      ),
                      Row(
                        children: [
                          WxAssets.images.points2
                              .image(width: 18.w, height: 18.w),
                          SizedBox(
                            width: 6.w,
                          ),
                          ShaderMask(
                            shaderCallback: (bounds) => const LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF,
                              ],
                            ).createShader(bounds),
                            child: Text(
                              "${logic.goodsDetailModel.value.pointPrice ?? ""}",
                              maxLines: 2,
                              style: TextStyles.din.copyWith(
                                  fontSize: 20.sp, fontWeight: FontWeight.bold),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 20.w,
                      ),
                      Divider(
                        height: 0.5,
                        color: Colours.white.withOpacity(0.2),
                      ),
                      buildRowTitleWidget("商品介绍",
                          padding: EdgeInsets.zero, height: 51.w),
                      Text(
                        logic.goodsDetailModel.value.goodsDese ?? "",
                        maxLines: 50,
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp,
                            height: 1.2,
                            color: Colours.colorA8A8BC),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Column(
                        children: List.generate(
                            logic.goodsDetailModel.value.imageUrl?.length ?? 0,
                            (position) {
                          return MyImage(
                              logic.goodsDetailModel.value
                                      .imageUrl?[position] ??
                                  '',
                              fit: BoxFit.fill,
                              width: double.infinity,
                              height: 375.w,
                              radius: 0.r,
                              errorImage: "error_image_width.png");
                        }),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.back();
            },
            child: Container(
                margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
                padding: EdgeInsets.only(
                    left: 15.w, right: 15.w, top: 6.w, bottom: 6.w),
                child: WxAssets.images.arrowBackRound
                    .image(width: 32.w, height: 32.w)),
          ),
        ],
      );
    });
  }

  void getExchangeGoods(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 24.w),
              decoration: BoxDecoration(
                  color: Colours.color0F0F16,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16.r),
                      topRight: Radius.circular(16.r))),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.end,
                    //   children: [
                    //     GestureDetector(
                    //       behavior: HitTestBehavior.translucent,
                    //       onTap: () {
                    //         AppPage.back();
                    //       },
                    //       child: Container(
                    //         width: 50.w,
                    //         height: 30.w,
                    //         padding: EdgeInsets.only(bottom: 5.w),
                    //         alignment: Alignment.bottomRight,
                    //         child: WxAssets.images.icCloseDialog.image(
                    //             width: 16.w,
                    //             height: 16.w,
                    //             color: Colours.color999999),
                    //       ),
                    //     )
                    //   ],
                    // ),
                    Container(
                      width: double.infinity,
                      constraints:
                          BoxConstraints(minHeight: 80.w, maxHeight: 100.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MyImage(
                              logic.goodsDetailModelSpecList.value.imageUrl ??
                                  logic.goodsDetailModel.value.thumbUrl ??
                                  "",
                              width: 80.w,
                              height: 80.w,
                              radius: 8.r,
                              isAssetImage: false,
                              borderColor: Colours.colorD6D6D6,
                              borderWidth: 0.8,
                              errorImage: "error_img_white.png",
                              placeholderImage: "error_img_white.png",
                              hasBorder: true
                              // bgColor: Colours.color000000,
                              ),
                          SizedBox(
                            width: 15.w,
                          ),
                          Expanded(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 40.w,
                                child: Text(
                                  logic.goodsDetailModel.value.goodsName ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyles.regular.copyWith(
                                      height: 1.2,
                                      color: Colours.white,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                              Row(
                                children: [
                                  WxAssets.images.points2
                                      .image(width: 18.w, height: 18.w),
                                  SizedBox(
                                    width: 6.w,
                                  ),
                                  ShaderMask(
                                    shaderCallback: (bounds) =>
                                        const LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    ).createShader(bounds),
                                    child: Text(
                                      "${logic.goodsDetailModelSpecList.value.pointPrice ?? ""}",
                                      maxLines: 2,
                                      style: TextStyles.bold
                                          .copyWith(fontSize: 20.sp),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 10.w,
                              ),
                              Text(
                                "库存：${logic.goodsDetailModelSpecList.value.stock}",
                                style: TextStyles.medium.copyWith(
                                    fontSize: 13.sp,
                                    color: Colours.color333333),
                              ),
                            ],
                          ))
                        ],
                      ),
                    ),
                    ...logic.attrValueMap.entries.map((entry) {
                      var list2 = logic.goodsDetailModelSpecList.value == null
                          ? []
                          : logic.goodsDetailModelSpecList.value.attrValueItem
                              ?.where((value) {
                              return (entry.key == value?.attrName);
                            }).toList();

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "${entry.key}：",
                              style: TextStyles.medium.copyWith(
                                  fontSize: 12.sp, color: Colours.color5C5C6E),
                            ),
                            //  WxAssets.images.pointsColor
                            //     .image(width: 34.w, height: 14.w)
                          ),
                          Wrap(
                            spacing: 15.w,
                            runSpacing: 12.w,
                            children:
                                List.generate(entry.value.length, (index) {
                              return GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  logic.getCheckGoods(
                                    entry.key.toString(),
                                    entry.value.toList()[index].toString(),
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsetsDirectional.symmetric(
                                      vertical: 11.w, horizontal: 20.w),
                                  decoration: BoxDecoration(
                                      color: (list2?.length ?? 0) > 0 &&
                                              list2?.first?.attrValueName ==
                                                  entry.value.toList()[index]
                                          ? Colours.color813AEE
                                          : null,
                                      borderRadius: BorderRadius.circular(20.r),
                                      border: (list2?.length ?? 0) > 0 &&
                                              list2?.first?.attrValueName ==
                                                  entry.value.toList()[index]
                                          ? null
                                          : Border.all(
                                              width: 1.w,
                                              color: Colours.white)),
                                  child: Text(
                                    entry.value.toList()[index],
                                    textAlign: TextAlign.center,
                                    style: TextStyles.display14.copyWith(
                                        color: Colours.white,
                                        fontWeight:
                                            list2?.first?.attrValueName ==
                                                    entry.value.toList()[index]
                                                ? FontWeight.bold
                                                : FontWeight.normal),
                                  ),
                                ),
                              );
                            }),
                          ),
                        ],
                      );
                    }),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.only(top: 20.w, bottom: 15.w),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "收货地址：",
                        style: TextStyles.medium.copyWith(
                            fontSize: 12.sp, color: Colours.color5C5C6E),
                      ),
                      //  WxAssets.images.pointsColor
                      //     .image(width: 34.w, height: 14.w)
                    ),
                    (logic.addressModel.value.id == null)
                        ? GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              AppPage.to(Routes.addressPage, arguments: {
                                "id": (logic.addressModel.value.id ?? "")
                                    .toString()
                              }).then((onValue) {
                                if (onValue != null) {
                                  logic.addressModel.value = onValue;
                                }
                              });
                            },
                            child: Container(
                              width: double.infinity,
                              height: 40.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: 1.w, color: Colours.white),
                                  borderRadius: BorderRadius.circular(20.r)),
                              child: RichText(
                                text: TextSpan(
                                    text: "暂未添加收货地址，",
                                    style: TextStyle(
                                        color: Colours.white,
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.bold),
                                    children: <InlineSpan>[
                                      TextSpan(
                                          text: "点击去添加",
                                          style: TextStyle(
                                              color: Colours.colorA44EFF,
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w600)),
                                    ]),
                              ),
                            ),
                          )
                        : GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              AppPage.to(Routes.addressPage, arguments: {
                                "id": (logic.addressModel.value.id ?? "")
                                    .toString()
                              }).then((onValue) {
                                if (onValue != null) {
                                  logic.addressModel.value = onValue;
                                }
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.only(
                                  left: 15.w, right: 15.w, top: 15.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.r),
                                  color: Colours.color191921,
                                  border: Border.all(
                                      width: 1.w, color: Colours.white)),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${logic.addressModel.value.province}${logic.addressModel.value.city}${logic.addressModel.value.district}",
                                    style: TextStyles.regular.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.color5C5C6E),
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${logic.addressModel.value.address}",
                                          style: TextStyles.regular.copyWith(
                                              fontSize: 14.sp,
                                              color: Colours.white,
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ),
                                      WxAssets.images.arrowRight.image(
                                          width: 7.w,
                                          height: 15.w,
                                          color: Colours.white),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Text(
                                    "${logic.addressModel.value.name ?? ""}\t\t\t\t${logic.addressModel.value.phone ?? ""}",
                                    style: TextStyles.regular.copyWith(
                                      fontSize: 14.sp,
                                      color: Colours.white,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                ],
                              ),
                            ),
                          ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        if (logic.addressModel.value.id == null) {
                          WxLoading.showToast("请选择收货地址");
                          return;
                        }
                        if (logic.goodsDetailModelSpecList.value.skuId ==
                            null) {
                          WxLoading.showToast("请选择要兑换的物品");
                          return;
                        }
                        Get.back();
                        if (logic.goodsDetailModel.value.specList!.isNotEmpty) {
                          _getPointsExchangeGoods();
                        } else {
                          WxLoading.showToast("暂无商品数据");
                        }
                      },
                      child: Container(
                        height: 46.w,
                        width: double.infinity,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(
                          top: 20.w,
                          bottom: 15.w,
                        ),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [
                              Colours.color7732ED,
                              Colours.colorA555EF,
                            ],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          S.current.Exchange_immediately,
                          style: TextStyles.display16.copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 35.w,
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  //积分兑换商品
  Future<void> _getPointsExchangeGoods() async {
    WxLoading.show();
    Map<String, dynamic>? param = {
      "skuId": logic.goodsDetailModelSpecList.value.skuId,
      if (logic.goodsDetailModel.value.goodType == 2)
        "address": logic.addressModel.value.address,
      if (logic.goodsDetailModel.value.goodType == 2)
        "phone": logic.addressModel.value.phone,
      if (logic.goodsDetailModel.value.goodType == 2)
        "name": logic.addressModel.value.name,
    };

    final res = await Api()
        .post(ApiUrl.pointsExchangeGoods, data: param, showError: false);
    log(jsonEncode(res.data));
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      BusUtils.instance.fire(EventAction(key: EventBusKey.changePoints));
      getMyDialog(
        S.current.Successful_exchange,
        S.current.sure,
        () {
          AppPage.back();
        },
        isShowClose: false,
        btnIsHorizontal: false,
        contentWidget: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: RichText(
            text: TextSpan(
                text: S.current.successfully_exchanged,
                style: TextStyle(
                    color: Colours.color9393A5,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.normal),
                children: <InlineSpan>[
                  TextSpan(
                      text: "\t${logic.goodsDetailModel.value.goodsName}\t",
                      style: TextStyle(
                          color: Colours.colorA44EFF,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.normal)),
                ]),
          ),
        ),
      );
    } else {
      getMyDialog(S.current.failure_exchanged, S.current.sure, () {
        AppPage.back();
      },
          isShowClose: false,
          btnIsHorizontal: false,
          contentWidget: Padding(
            padding: EdgeInsets.only(left: 50.w, right: 50.w),
            child: Text(res.message,
                maxLines: 5,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Colours.color9393A5,
                    height: 2,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.normal)),
          ));
    }
  }

  //兑换商品弹窗
  void getExchangeGoodsDialog(String title, String sureText,
      GoodsDetailModel goodsDetailModel, void Function()? onPressed) {
    Get.dialog(BaseDialog(
      title: title,
      onPressed: onPressed,
      isShowCannel: true,
      sureText: sureText,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(30),
              child: MyImage(
                goodsDetailModel.thumbUrl ?? '',
                //  holderImg: "home/index/df_banner_top",
                fit: BoxFit.fill,
                width: 174.w,
                height: 116.w,
                bgColor: Colours.white,
                errorImage: "error_img_white.png",
                placeholderImage: "error_img_white.png",
                radius: 5.r,
              ),
            ),
            SizedBox(
              height: 5.w,
            ),
            RichText(
              text: TextSpan(
                  text: S.current.Confirmed_use,
                  style: TextStyle(
                      color: Colours.color5C5C6E,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold),
                  children: <InlineSpan>[
                    TextSpan(
                        text: "${goodsDetailModel.pointPrice ?? "-"}",
                        style: TextStyle(
                            color: Colours.colorA44EFF,
                            fontSize: 26.sp,
                            fontWeight: FontWeight.normal)),
                    TextSpan(
                        text: S.current.Point_exchange,
                        style: TextStyle(
                            color: Colours.color5C5C6E,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.normal)),
                  ]),
            ),
          ],
        ),
      ),
    ));
  }
}
