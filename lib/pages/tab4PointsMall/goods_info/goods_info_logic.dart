import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/address_model.dart';
import 'package:shoot_z/network/model/goods_detail_model.dart';
import 'package:shoot_z/utils/event_bus.dart';

class GoodsInfoLogic extends GetxController {
  var dataFag = {
    "isFrist": true,
  }.obs;
  StreamSubscription? subscription;
  var goodsDetailModel = GoodsDetailModel().obs;
  SwiperController swiperController = SwiperController();
  var goodId = "0";
  var addressModel = AddressModel().obs;
  var attrValueMap = <String, Set<String>>{}.obs;
  var attrValueCheckList = <String>[].obs;
  var goodsDetailModelSpecList = GoodsDetailModelSpecList().obs; //选中的sku
  @override
  void onInit() {
    super.onInit();
    goodId = Get.arguments["goodsId"];
    log("goodsDetail111=${Get.arguments["goodsId"]}");
    getGoodsInfo();
  }

  @override
  void onReady() {
    super.onReady();
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.auditApplyTeam) {
        getGoodsInfo();
      }
    });
    getAddressList();
  }

  //获得最新列表
  getAddressList() async {
    Map<String, dynamic> param = {
      // 'page': dataFag["page"] ?? 1,
      'isDefault': 1,
    };
    var res = await Api().get(ApiUrl.getAddressList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<AddressModel> modelList =
          list.map((e) => AddressModel.fromJson(e)).toList();
      log("getAddressList22-${res.data}");
      if (modelList.isNotEmpty) {
        addressModel.value = modelList.first;
        addressModel.refresh();
      }
    }
  }

  //获得球馆主页详情
  getGoodsInfo() async {
    Map<String, dynamic> param = {
      'spuId': goodId,
    };
    log("goodsDetail111=${param} ");
    var res = await Api().get(ApiUrl.goodsDetail, queryParameters: param);
    if (res.isSuccessful()) {
      log("goodsDetail111=${param} ${jsonEncode(res.data)}");
      goodsDetailModel.value = GoodsDetailModel.fromJson(res.data);
      goodsDetailModel.refresh();

      attrValueMap.clear();
      for (GoodsDetailModelSpecList? sku
          in goodsDetailModel.value.specList ?? []) {
        if (sku != null) {
          if (goodsDetailModelSpecList.value.skuId == null) {
            goodsDetailModelSpecList.value = sku;
            goodsDetailModelSpecList.refresh();
          }
          for (GoodsDetailModelSpecListAttrValueItem? attrItem
              in sku.attrValueItem ?? []) {
            if (attrItem != null) {
              final attrName = attrItem.attrName ?? "";
              final attrValue = attrItem.attrValueName ?? "";
              attrValueMap.putIfAbsent(attrName, () => <String>{});
              attrValueMap[attrName]!.add(attrValue);
            }
          }
        }
      }
      log("goodsDetail1113=${attrValueMap}");
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

//选中商品
  void getCheckGoods(String key2, String value2) {
    log("getCheckGoods2=${key2}-${value2}");
    //  attrValueMap.clear();
    for (GoodsDetailModelSpecList? sku
        in goodsDetailModel.value.specList ?? []) {
      if (sku != null) {
        if (goodsDetailModelSpecList.value.skuId == null) {
          goodsDetailModelSpecList.value = sku;
          goodsDetailModelSpecList.refresh();
        }
        var isHave = false;
        for (GoodsDetailModelSpecListAttrValueItem? attrItem
            in sku.attrValueItem ?? []) {
          if (attrItem != null) {
            final attrName = attrItem.attrName ?? "";
            final attrValue = attrItem.attrValueName ?? "";
            // if (attrName == key2 && value2 != attrValue) {
            //   isHave = false;
            // }
            if (attrName == key2 && value2 == attrValue) {
              isHave = true;
            } else if (attrName == key2 && value2 != attrValue) {
              attrValueMap.putIfAbsent(attrName, () => <String>{});
              attrValueMap[attrName]!.add(attrValue);
            }
          }
        }

        if (isHave) {
          for (GoodsDetailModelSpecListAttrValueItem? attrItem
              in sku.attrValueItem ?? []) {
            if (attrItem != null) {
              final attrName = attrItem.attrName ?? "";
              final attrValue = attrItem.attrValueName ?? "";
              attrValueMap.putIfAbsent(attrName, () => <String>{});
              attrValueMap[attrName]!.add(attrValue);
            }
          }
        }
      }
    }
    //goodsDetailModelSpecList.value
    GoodsDetailModelSpecList goodsDetailModelSpecList2 =
        GoodsDetailModelSpecList();
    for (GoodsDetailModelSpecList? sku
        in goodsDetailModel.value.specList ?? []) {
      if (sku != null) {
        var isThisGoods = true;
        for (GoodsDetailModelSpecListAttrValueItem? attrItem
            in sku.attrValueItem ?? []) {
          log("getCheckGoods20=${jsonEncode(sku)}");
          if (attrItem != null) {
            final attrName = attrItem.attrName ?? "";
            final attrValue = attrItem.attrValueName ?? "";
            if (goodsDetailModelSpecList2.skuId == null &&
                attrName == key2 &&
                attrValue == value2) {
              goodsDetailModelSpecList2 = sku;
              log("getCheckGoods200=${jsonEncode(goodsDetailModelSpecList2)}");
            }
            log("getCheckGoods21=${attrName}-${attrValue}-${jsonEncode(goodsDetailModelSpecList.value)}");
            for (GoodsDetailModelSpecListAttrValueItem? attrItem2
                in goodsDetailModelSpecList.value.attrValueItem ?? []) {
              final attrName2 = attrItem2?.attrName ?? "";
              final attrValue2 = attrItem2?.attrValueName ?? "";
              log("getCheckGoods22=${attrName2}-${attrValue2}");
              if (attrItem2 != null && attrName == attrName2) {
                if (attrName2 == key2) {
                  log("getCheckGoods23=${attrName}-${attrValue}-${attrName2}-${attrValue2}-${value2}-${(value2 != attrValue2)}");
                  if (value2 != attrValue) {
                    isThisGoods = false;
                  }
                } else {
                  log("getCheckGoods24=${attrName}-${attrValue}-${attrName2}-${attrValue2}-${(attrValue != attrValue2)}");
                  if (attrValue != attrValue2) {
                    isThisGoods = false;
                  }
                }
              }
            }
          }
        }
        log("getCheckGoods25=${isThisGoods}");
        if (isThisGoods) {
          goodsDetailModelSpecList2 = sku;
        }
      }
    }
    log("getCheckGoods26=${jsonEncode(goodsDetailModelSpecList2)}");

    goodsDetailModelSpecList.value = goodsDetailModelSpecList2;
    goodsDetailModelSpecList.refresh();
    // if (indexColor != null) {
    //   indexColorName.value = indexColor;
    //   var myColor = colorNameList[indexColorName.value];
    //   var mySize = sizeNameList[indexSizeName.value];
    //   var list = <String>[];
    //   var index2 = 0;
    //   var indexSizeName2 = 0;
    //   log("getCheckGoods1-myColor=$myColor-mySize=$mySize");
    //   for (int i = 0; i < logic.goodsDetailModel.value.specList!.length; i++) {
    //     var item = logic.goodsDetailModel.value.specList?[i];
    //     if (!(list.contains(item?.sizeName) == true && item?.sizeName != "") &&
    //         myColor == item?.colorName) {
    //       list.add(item?.sizeName ?? "");
    //       if (list.length == 1) {
    //         index2 = i;
    //         indexSizeName2 = 0;
    //         log("getCheckGoods10-myColor=$myColor-mySize=$mySize-index2=$index2-indexSizeName2=$indexSizeName2");
    //       }
    //       if (mySize == item?.sizeName) {
    //         indexSizeName2 = list.length - 1;
    //         index2 = i;
    //         log("getCheckGoods11-myColor=$myColor-mySize=$mySize-index2=$index2-indexSizeName2=$indexSizeName2");
    //       }
    //     }
    //   }
    //   sizeNameList.assignAll(list);
    //   indexSizeName.value = indexSizeName2;
    //   indexGoodsList.value = index2;
    // }
    // if (indexSize != null) {
    //   indexSizeName.value = indexSize;
    //   var myColor = colorNameList[indexColorName.value];
    //   var mySize = sizeNameList[indexSizeName.value];
    //   var list = <String>[];
    //   var index2 = 0;
    //   var indexColorName2 = 0;
    //   log("getCheckGoods2-myColor=$myColor-mySize=$mySize");
    //   for (int i = 0; i < logic.goodsDetailModel.value.specList!.length; i++) {
    //     var item = logic.goodsDetailModel.value.specList?[i];
    //     if (!(list.contains(item?.colorName) == true &&
    //             item?.colorName != "") &&
    //         mySize == item?.sizeName) {
    //       list.add(item?.colorName ?? "");
    //       if (list.length == 1) {
    //         indexColorName2 = 0;
    //         index2 = i;
    //         log("getCheckGoods21-myColor=$myColor-mySize=$mySize-index2=$index2-indexColorName2=$indexColorName2");
    //       }

    //       if (myColor == item?.colorName) {
    //         indexColorName2 = list.length - 1;
    //         index2 = i;
    //         log("getCheckGoods22-myColor=$myColor-mySize=$mySize-index2=$index2-indexColorName2=$indexColorName2");
    //       }
    //     }
    //   }
    //   colorNameList.assignAll(list);
    //   indexColorName.value = indexColorName2;
    //   indexGoodsList.value = index2;
    // }
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }
}
