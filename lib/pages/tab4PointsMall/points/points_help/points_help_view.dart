import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_help/points_help_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:ui_packages/ui_packages.dart';

///我的->积分页面->积分帮助
class PointsHelpPage extends StatelessWidget {
  PointsHelpPage({super.key});
  final logic = Get.find<PointsHelpLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MyAppBar(
          title: Text(S.current.points_rule),
        ),
        body: KeepAliveWidget(
          child: SingleChildScrollView(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(
                  left: 20.w, right: 20.w, top: 20.w, bottom: 20.w),
              child: SafeArea(
                bottom: false,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 6.w,
                          height: 6.w,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight),
                              borderRadius: BorderRadius.circular(3.w)),
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          S.current.points_help_title1,
                          style: TextStyles.semiBold
                              .copyWith(fontSize: 14.sp, height: 1),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Text(
                      S.current.points_help_tips1,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          height: 1.8,
                          color: Colours.color9393A5),
                    ),
                    SizedBox(
                      height: 30.w,
                    ),
                    Row(
                      children: [
                        Container(
                          width: 6.w,
                          height: 6.w,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight),
                              borderRadius: BorderRadius.circular(3.w)),
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          S.current.points_help_title2,
                          style: TextStyles.semiBold
                              .copyWith(fontSize: 14.sp, height: 1),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 2.w),
                          child: Text(
                            "1.",
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp,
                                height: 1.8,
                                color: Colours.color9393A5),
                          ),
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Expanded(
                            child: RichText(
                          text: TextSpan(
                              text: S.current.points_help_tips2,
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp,
                                  height: 1.8,
                                  color: Colours.color9393A5),
                              children: <InlineSpan>[
                                TextSpan(
                                  text: S.current.points_help_tips3,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.colorA44EFF),
                                ),
                                TextSpan(
                                  text: S.current.points_help_tips4,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.color9393A5),
                                ),
                              ]),
                        ))
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 2.w),
                          child: Text(
                            "2.",
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp,
                                height: 1.8,
                                color: Colours.color9393A5),
                          ),
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Expanded(
                            child: RichText(
                          text: TextSpan(
                              text: S.current.points_help_tips5,
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp,
                                  height: 1.8,
                                  color: Colours.color9393A5),
                              children: <InlineSpan>[
                                TextSpan(
                                  text: S.current.points_help_tips6,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.colorA44EFF),
                                ),
                                TextSpan(
                                  text: S.current.points_help_tips7,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.color9393A5),
                                ),
                              ]),
                        ))
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 2.w),
                          child: Text(
                            "3.",
                            style: TextStyles.regular.copyWith(
                                fontSize: 12.sp,
                                height: 1.8,
                                color: Colours.color9393A5),
                          ),
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Expanded(
                            child: RichText(
                          text: TextSpan(
                              text: S.current.points_help_tips8,
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp,
                                  height: 1.8,
                                  color: Colours.color9393A5),
                              children: <InlineSpan>[
                                TextSpan(
                                  text: S.current.points_help_tips9,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.colorA44EFF),
                                ),
                                TextSpan(
                                  text: S.current.points_help_tips10,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.color9393A5),
                                ),
                                TextSpan(
                                  text: S.current.points_help_tips11,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.colorA44EFF),
                                ),
                                TextSpan(
                                  text: S.current.points_help_tips12,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      height: 1.8,
                                      color: Colours.color9393A5),
                                ),
                              ]),
                        ))
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ));
  }
}
