import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/points_details_model.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///积分明细 积分商城列表

class PointsGoodsItemPage1 extends StatefulWidget {
  const PointsGoodsItemPage1({super.key, required this.tabParameter});
  final String tabParameter; // 接收传入的参数
  @override
  State<PointsGoodsItemPage1> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<PointsGoodsItemPage1> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag1 = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList1 = <PointsDetailsModel>[].obs;
  @override
  void initState() {
    super.initState();
    getdataList1(isLoad: false, controller: refreshController);
  }

  //获得最新列表
  getdataList1({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag1["page"] = (dataFag1["page"] as int) + 1;
    } else {
      dataFag1["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag1["page"] ?? 1,
      'limit': 20,
      'type': widget.tabParameter, //1.积分获取 2.积分消耗Available values : 1, 2
    };
    var res = await Api().get(ApiUrl.pointList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<PointsDetailsModel> modelList =
          list.map((e) => PointsDetailsModel.fromJson(e)).toList();
      //log("zzzzzz12removeAt-${jsonEncode(res.data)}");
      if (isLoad) {
        dataList1.addAll(modelList);
        dataList1.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList1.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag1["isFrist"] as bool) {
      dataFag1["isFrist"] = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: dataList1.isNotEmpty,
        onRefresh: () {
          getdataList1(isLoad: false, controller: refreshController);
        },
        onLoading: () {
          getdataList1(controller: refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (dataFag1["isFrist"] as bool)
            ? buildLoad()
            : dataList1.isEmpty
                ? SizedBox(
                    height: 480.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.no_order,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 105.w, height: 89.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: dataList1.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(dataList1[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(PointsDetailsModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 12.w),
      padding:
          EdgeInsets.only(left: 12.w, right: 12.w, top: 10.w, bottom: 10.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r), color: Colours.white),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name ?? "",
                  style: TextStyles.regular.copyWith(
                      fontSize: 14.sp,
                      color: Colours.color333333,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  item.date ?? "",
                  style: TextStyles.regular.copyWith(
                      fontSize: 12.sp,
                      color: Colours.color999999,
                      fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
          RichText(
            textAlign: TextAlign.end,
            text: TextSpan(
                text: "+",
                style: TextStyle(
                    //   color: Colours.white,
                    fontSize: 24.sp,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    foreground: Paint()
                      ..shader = const LinearGradient(colors: [
                        Colours.color7B35ED,
                        Colours.colorA253EF
                      ]).createShader(Rect.fromLTWH(0, 0, 300, 0))),
                children: [TextSpan(text: "${item.point ?? ""}")]),
          ),
        ],
      ),
    );
  }
}
