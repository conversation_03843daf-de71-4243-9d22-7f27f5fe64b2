// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item1/points_details_item_logic1.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item1/points_details_item_view1.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item2/points_details_item_logic2.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item2/points_details_item_view2.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/points_details_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:ui_packages/ui_packages.dart';
import 'dart:ui' as ui;

///我的 积分明细
///
class PointsDetailsPage extends StatefulWidget {
  const PointsDetailsPage({super.key});

  @override
  State<PointsDetailsPage> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<PointsDetailsPage> {
  final logic = Get.put(PointsDetailsLogic());
  final logic1 = Get.put(PointsDetailsItemLogic1());
  final logic2 = Get.put(PointsDetailsItemLogic2());
  final _playerTabIndicatorKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // 预加载指示器图片
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _CustomPainter.preloadImage(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.bg_color,
      // appBar:  MyAppBar(
      //   title: Text(S.current.integral_info1),
      // ),
      body: _teamInfoWidget(context),
    );
  }

  Widget _teamInfoWidget(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 215.w, // 282.w,
          decoration: BoxDecoration(
              image: DecorationImage(
                  image: WxAssets.images.pointsDetailsBg.provider(),
                  fit: BoxFit.fill)),
        ),
        SizedBox(
          width: double.infinity,
          height: double.infinity, // 282.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 50.w,
                alignment: Alignment.center,
                margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 60.w,
                      padding:
                          EdgeInsets.only(left: 8.w, right: 10.w, top: 8.w),
                      child: IconButton(
                          onPressed: () {
                            AppPage.back();
                          },
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 20,
                          )),
                    ),
                    Text(
                      S.current.integral_info1,
                      style: TextStyles.textBold16.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: Colours.white),
                    ),
                    Container(
                      width: 60.w,
                      padding: EdgeInsets.only(left: 3.w, right: 10.w),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20.w,
              ),
              Obx(() {
                return Container(
                  height: 50.w,
                  width: double.infinity,
                  padding: EdgeInsets.only(left: 16.w, top: 1.w, bottom: 10.w),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      RichText(
                        textAlign: TextAlign.start,
                        text: TextSpan(
                          text: logic1.points.value == ""
                              ? logic2.points.value
                              : logic1.points.value,

                          ///  "${logic.teamHomeModel.value.avgScore == "" ? "0" : logic.teamHomeModel.value.avgScore}",
                          style: TextStyle(
                              //   color: Colours.white,
                              fontSize: 36.sp,
                              height: 1,
                              fontWeight: FontWeight.w500,
                              foreground: Paint()
                                ..shader = LinearGradient(colors: [
                                  Colours.colorC5FE94,
                                  Colours.colorF3FC5C
                                ]).createShader(Rect.fromLTWH(0, 0, 300, 0))),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(bottom: 3.w),
                        child: Text("\t${S.current.integral}",
                            style: TextStyle(
                                color: Colours.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w400)),
                      )
                    ],
                  ),
                );
              }),
              SizedBox(
                height: 10.w,
              ),
              Container(
                width: double.infinity,
                height: 40.w,
                margin: EdgeInsets.only(left: 16.w, right: 16.w),
                decoration: BoxDecoration(
                    color: Colours.white,
                    borderRadius: BorderRadius.circular(8.r)),
                child: TabBar(
                    controller: logic.tabController,
                    unselectedLabelStyle: TextStyle(
                        fontSize: 14.sp,
                        color: Colours.colorB88DFF,
                        fontWeight: FontWeight.w600),
                    labelStyle: TextStyle(
                      //   color: Colours.white,
                      fontSize: 14.sp,
                      height: 1,
                      fontWeight: FontWeight.w500,
                      // foreground: Paint()
                      //   ..shader = const LinearGradient(colors: [
                      //     Colours.color7B35ED,
                      //     Colours.colorA253EF
                      //   ]).createShader(Rect.fromLTWH(0, 0, 300, 50))
                    ),
                    isScrollable: false,
                    // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                    indicatorPadding:
                        EdgeInsets.only(bottom: 6.w, left: 19.w, right: 19.w),
                    dividerHeight: 0,
                    padding: EdgeInsets.only(left: 40.w, right: 40.w),
                    indicatorColor: Colors.transparent,
                    dividerColor: Colors.transparent,
                    indicatorSize: TabBarIndicatorSize.label,
                    // dragStartBehavior: DragStartBehavior.start,
                    //indicatorWeight: 1,
                    indicator: CustomTabIndicator(
                      repaintKey: _playerTabIndicatorKey,
                    ),
                    tabs: [S.current.integral_info6, S.current.integral_info7]
                        .map((e) => gradientTab(
                            e,
                            [S.current.integral_info6, S.current.integral_info7]
                                .indexOf(e),
                            logic.tabController))
                        .toList()),
              ),
              SizedBox(
                height: 20.w,
              ),
              Expanded(
                child: TabBarView(controller: logic.tabController, children: [
                  PointsDetailsItemPage1(
                    key: const Key("1"),
                  ),
                  PointsDetailsItemPage2(
                    key: const Key("2"),
                  ),
                ]),
              ),
            ],
          ),
        )
      ],
    );
  }
}

class CustomTabIndicator extends Decoration {
  final GlobalKey? repaintKey;

  const CustomTabIndicator({this.repaintKey});

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomPainter(onChanged);
  }
}

/// 自定义 Tab，选中时渐变色，未选中时普通颜色
Widget gradientTab(String text, int index, TabController controller) {
  return AnimatedBuilder(
    animation: controller.animation!,
    builder: (context, child) {
      bool isSelected = controller.index == index;
      return Container(
        height: 40.w,
        width: 130.w,
        padding: EdgeInsets.only(
          bottom: 4.w,
        ),
        alignment: Alignment.center,
        child: ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: isSelected
                  ? [Colours.color7B35ED, Colours.colorA253EF]
                  : [Colours.colorB88DFF, Colours.colorB88DFF],
            ).createShader(bounds);
          },
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: Colors.white,
            ),
          ),
        ),
      );
    },
  );
}

class _CustomPainter extends BoxPainter {
  final ui.Image? _cachedImage;
  static ui.Image? _sharedCachedImage;
  static bool _isLoading = false;

  _CustomPainter(VoidCallback? onChanged)
      : _cachedImage = _sharedCachedImage,
        super(onChanged);

  static void preloadImage(BuildContext context) {
    if (_sharedCachedImage != null || _isLoading) return;

    _isLoading = true;
    final provider = WxAssets.images.icRankingIndicator.provider();

    provider.resolve(ImageConfiguration.empty).addListener(
      ImageStreamListener((ImageInfo info, bool synchronous) {
        _sharedCachedImage = info.image;
        // 通知 StatefulWidget 进行重建
        if (context.findAncestorStateOfType<_RankingsPageState>() != null) {
          context
              .findAncestorStateOfType<_RankingsPageState>()
              ?.setState(() {});
        }
      }),
    );
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = offset & configuration.size!;
    final indicatorWidth = 16.w;
    final indicatorHeight = 3.w;

    final double indicatorX = rect.left + (rect.width - indicatorWidth) / 2;
    final double indicatorY = rect.bottom - indicatorHeight;

    if (_cachedImage != null) {
      canvas.drawImageRect(
        _cachedImage,
        Rect.fromLTWH(
          0,
          0,
          _cachedImage.width.toDouble(),
          _cachedImage.height.toDouble(),
        ),
        Rect.fromLTWH(indicatorX, indicatorY, indicatorWidth, indicatorHeight),
        Paint(),
      );
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
