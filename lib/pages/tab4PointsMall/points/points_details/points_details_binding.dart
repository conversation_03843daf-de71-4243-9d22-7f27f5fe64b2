import 'package:get/get.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item1/points_details_item_logic1.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item2/points_details_item_logic2.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/points_details_logic.dart';

class PointsDetailsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => PointsDetailsLogic());
    Get.lazyPut(() => PointsDetailsItemLogic1());
    Get.lazyPut(() => PointsDetailsItemLogic2());
  }
}
