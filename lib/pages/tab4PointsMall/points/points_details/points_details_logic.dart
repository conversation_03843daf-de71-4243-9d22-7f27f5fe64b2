import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PointsDetailsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  late final TabController tabController;
  var tabbarIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(
      () {
        tabbarIndex.value = tabController.index;
      },
    );
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }
}
