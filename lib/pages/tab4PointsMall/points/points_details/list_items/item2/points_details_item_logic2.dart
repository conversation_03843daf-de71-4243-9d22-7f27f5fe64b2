import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/points_details_model.dart';

class PointsDetailsItemLogic2 extends GetxController
    with GetSingleTickerProviderStateMixin {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var points = "".obs;
  //数据列表
  var dataList = <PointsDetailsModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
    getdataList(isLoad: false, controller: refreshController);
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 20,
      'type': "2", //1.积分获取 2.积分消耗Available values : 1, 2
    };
    var res = await Api().get(ApiUrl.pointList, queryParameters: param);
    if (res.isSuccessful()) {
      points.value = "${res.data["myPoint"] ?? ""}";
      List list = res.data["list"] ?? [];
      List<PointsDetailsModel> modelList =
          list.map((e) => PointsDetailsModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
