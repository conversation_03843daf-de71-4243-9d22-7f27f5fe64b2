import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/points_details_model.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/list_items/item1/points_details_item_logic1.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///积分明细 积分获取
class PointsDetailsItemPage1 extends StatelessWidget {
  PointsDetailsItemPage1({super.key});

  final logic = Get.put(PointsDetailsItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 480.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.no_order,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 105.w, height: 89.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(PointsDetailsModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 12.w),
      padding:
          EdgeInsets.only(left: 12.w, right: 12.w, top: 10.w, bottom: 10.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r), color: Colours.white),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name ?? "",
                  style: TextStyles.regular.copyWith(
                      fontSize: 14.sp,
                      color: Colours.color333333,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  item.date ?? "",
                  style: TextStyles.regular.copyWith(
                      fontSize: 12.sp,
                      color: Colours.color999999,
                      fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
          RichText(
            textAlign: TextAlign.end,
            text: TextSpan(
                text: "+",
                style: TextStyle(
                    //   color: Colours.white,
                    fontSize: 24.sp,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    foreground: Paint()
                      ..shader = const LinearGradient(colors: [
                        Colours.color7B35ED,
                        Colours.colorA253EF
                      ]).createShader(Rect.fromLTWH(0, 0, 300, 0))),
                children: [TextSpan(text: "${item.point ?? ""}")]),
          ),
        ],
      ),
    );
  }
}
