import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/my_points_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的->积分页面
class PointsPage extends StatelessWidget {
  PointsPage({super.key});

  final logic = Get.find<PointsLogic>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      // appBar: MyAppBar(
      //   title: Text(S.current.sign_in),
      // ),
      body: Stack(
        children: [
          Container(
            child: WxAssets.images.pageTopBg
                .image(width: ScreenUtil().screenWidth, height: 260.w),
          ),
          Column(
            children: [
              _topBar(context),
              Obx(() {
                return Expanded(
                  child: KeepAliveWidget(
                    child: ListView(
                      padding: EdgeInsets.only(top: 15.w),
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: SafeArea(
                            bottom: false,
                            top: false,
                            child: Column(
                              children: [
                                //积分头部签到页面
                                pointsTitleWidget(
                                    (logic.myPointsModel.value.continuousDays ??
                                            0) -
                                        1),
                                //积分任务
                                pointsTask(),
                                const SizedBox(
                                  height: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              })
            ],
          )
        ],
      ),
    );
  }

  Widget _topBar(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 4.w),
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(
              left: 8.w,
              right: 0.w,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: Colors.white,
              ),
              onPressed: () {
                AppPage.back();
              },
            ),
          ),
          Text(
            S.current.sign_in,
            style: TextStyles.titleSemiBold16,
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 0.w, right: 8.w),
          ),
        ],
      ),
    );
  }

  Widget pointsTask() {
    return Column(
      children: [
        //每日任务
        Visibility(
          visible: logic.dataList1.isNotEmpty,
          child: pointsTodayTask(1, logic.dataList1),
        ),
        //成长任务
        Visibility(
          visible: logic.dataList2.isNotEmpty,
          child: pointsTodayTask(2, logic.dataList2),
        ),
        //新手任务
        Visibility(
          visible: logic.dataList3.isNotEmpty,
          child: pointsTodayTask(3, logic.dataList3),
        ),
        const SizedBox(
          height: 30,
        ),
      ],
    );
  }

  Widget pointsTodayTask(int type, RxList<MyPointsModelTaskList> dataList) {
    return Column(
      children: [
        SizedBox(
          height: 5.w,
        ),
        buildRowTitleWidget(
            type == 1
                ? S.current.Daily_task
                : type == 2
                    ? S.current.Growth_task
                    : S.current.Novice_task,
            margin: EdgeInsets.only(top: 6.w),
            fontSize: 16.sp,
            height: 45.w),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 15.w),
          padding: EdgeInsets.symmetric(vertical: 14.w, horizontal: 15.w),
          decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(16.r)),
          child: Column(
            children: List.generate(dataList.length, (index) {
              return Container(
                margin: EdgeInsets.only(top: 9.w, bottom: 9.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              (dataList[index].id ?? 0) == 2
                                  ? "${dataList[index].taskName ?? ""} (${dataList[index].count}/${dataList[index].totalCount})"
                                  : "${dataList[index].taskName ?? ""}", //id=${dataList[index].id ?? ""}
                              style: TextStyles.textWhite14,
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            WxAssets.images.points2
                                .image(width: 16.w, height: 16.w),
                            SizedBox(
                              width: 3.w,
                            ),
                            Text(
                              (dataList[index].id ?? 0) == 2
                                  ? "+${int.tryParse((dataList[index].point ?? "0"))! * dataList[index].totalCount!}"
                                  : "+${dataList[index].point ?? ""}",
                              style: TextStyle(
                                  color: Colours.colorA44EFF,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 6.w,
                        ),
                        Text(
                          "${dataList[index].taskDesc ?? ""}",
                          style: TextStyle(
                              color: Colours.color5C5C6E, fontSize: 12.sp),
                        ),
                      ],
                    )),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        if (dataList[index].isCompleted ?? false) {
                          WxLoading.showToast(S.current.points_task_tips1);
                        } else {
                          logic.getTaskWithId(dataList[index].id ?? 0);
                        }
                      },
                      child: Container(
                        height: 30.w,
                        width: 70.w,
                        margin: EdgeInsets.only(left: 10.w),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            // color: !(dataList[index].isCompleted ?? false)
                            //     ? null
                            //     : Colours.color000000,
                            border: (dataList[index].isCompleted ?? false)
                                ? Border.all(
                                    width: 1.w,
                                    color: Colours.color5C5C6E,
                                  )
                                : null,
                            gradient: !(dataList[index].isCompleted ?? false)
                                ? const LinearGradient(
                                    colors: [
                                        Colours.colorFFECC1,
                                        Colours.colorE7CEFF,
                                        Colours.colorD1EAFF
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight)
                                : null,
                            borderRadius: BorderRadius.circular(15.w)),
                        child: Text(
                          !(dataList[index].isCompleted ?? false)
                              ? S.current.To_complete
                              : S.current.completed,
                          style: !(dataList[index].isCompleted ?? false)
                              ? TextStyles.regular.copyWith(
                                  color: Colours.color191921, fontSize: 12.sp)
                              : TextStyles.regular.copyWith(
                                  color: Colours.color5C5C6E, fontSize: 12.sp),
                        ),
                      ),
                    )
                  ],
                ),
              );
            }),
          ),
        )
      ],
    );
  }

  Widget pointsTitleWidget(int cindex) {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      child: Stack(
        children: [
          Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          WxAssets.images.points2
                              .image(width: 14.w, height: 14.w),
                          Padding(
                            padding: EdgeInsets.only(left: 4.w),
                            child: Text(
                              S.current.My_integral,
                              style: TextStyle(
                                  color: Colours.color9393A5, fontSize: 12.sp),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          SizedBox(
                            width: 3.w,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5.w,
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.to(Routes.pointsDetailsPage);
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              "${logic.myPointsModel.value.point ?? "0"}",
                              style: TextStyle(
                                  color: Colours.white,
                                  fontSize: 30.sp,
                                  fontFamily: 'DIN',
                                  fontWeight: FontWeight.w700),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                            SizedBox(
                              width: 13.w,
                            ),
                            WxAssets.images.icArrowRight.image(
                                width: 14.w,
                                height: 14.w,
                                color: Colours.white),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    AppPage.to(Routes.pointsMallPage, arguments: {"type": "1"});
                    //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                    UserManager.instance.postApmTracking(1,
                        remark: "点击积分兑换按钮",
                        nowPage: Routes.pointsPage,
                        toPage: Routes.pointsMallPage,
                        content: "进入积分商城页面");
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        gradient: const LinearGradient(
                            colors: [
                              Colours.colorFFECC1,
                              Colours.colorE7CEFF,
                              Colours.colorD1EAFF
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight)),
                    child: Row(
                      children: [
                        WxAssets.images.ponitsExchange
                            .image(width: 16.w, height: 16.w),
                        SizedBox(
                          width: 4.w,
                        ),
                        Text(
                          S.current.Point_exchange1,
                          style: TextStyles.semiBold14
                              .copyWith(color: Colours.color191921),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(top: 15.w),
              padding: EdgeInsets.only(
                  left: 12.w, right: 12.w, bottom: 12.w, top: 15.w),
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: WxAssets.images.ponitsBg.provider(),
                  fit: BoxFit.fill,
                  alignment: Alignment.topCenter,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                  text: S.current.Have_been_signed,
                                  style: TextStyle(
                                      color: Colours.white,
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600),
                                  children: <InlineSpan>[
                                    TextSpan(
                                        text:
                                            "\t${logic.myPointsModel.value.continuousDays ?? "0"}\t",
                                        style: TextStyle(
                                            color: Colours.colorFFCC00,
                                            fontSize: 20.sp,
                                            fontWeight: FontWeight.bold)),
                                    TextSpan(
                                        text: S.current.day,
                                        style: TextStyle(
                                            color: Colours.white,
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w600)),
                                  ]),
                            ),
                            SizedBox(
                              height: 5.w,
                            ),
                            Text(
                              S.current.Sign_in_tomorrow(
                                  logic.myPointsModel.value.nextDayPoints ??
                                      "0"),
                              style: TextStyle(
                                color: Colours.color9393A5,
                                fontSize: 12.sp,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.to(Routes.pointsHelpPage);
                        },
                        child: Container(
                          height: 40.w,
                          padding: EdgeInsets.only(top: 5.w),
                          alignment: Alignment.topCenter,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              WxAssets.images.pointsHelpWhite.image(
                                  color: Colours.white,
                                  width: 12.w,
                                  height: 12.w),
                              SizedBox(
                                width: 3.w,
                              ),
                              Text(
                                "积分获取方式",
                                style: TextStyle(
                                    color: Colours.white, fontSize: 12.sp),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                            ],
                          ),
                        ),
                      ),

                      //   Container(
                      //     height: 30.w,
                      //     padding: EdgeInsets.only(left: 10.w, right: 10.w),
                      //     decoration: BoxDecoration(
                      //         borderRadius: BorderRadius.circular(15.w),
                      //         gradient: const LinearGradient(
                      //             colors: [
                      //               Color(0x03FFF9DC),
                      //               Color(0x46E4C8FF),
                      //               Color(0x97E5F3FF)
                      //             ],
                      //             begin: Alignment.centerLeft,
                      //             end: Alignment.centerRight)),
                      //     child: Row(
                      //       mainAxisSize: MainAxisSize.min,
                      //       children: [
                      //         WxAssets.images.pointsNotification.image(
                      //             color: Colours.color000000,
                      //             width: 18.w,
                      //             height: 18.w),
                      //         SizedBox(
                      //           width: 3.w,
                      //         ),
                      //         Text(
                      //           "签到提醒",
                      //           style: TextStyle(
                      //               color: Colours.color000000,
                      //               fontSize: 12.sp),
                      //           overflow: TextOverflow.ellipsis,
                      //           maxLines: 1,
                      //         ),
                      //       ],
                      //     ),
                      //   )
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(7, (index) {
                      return GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          logic.getPointsSignIn();
                        },
                        child: Container(
                          width: 37.w,
                          padding: EdgeInsets.only(bottom: 6.w, top: 8.w),
                          decoration: BoxDecoration(
                              color: index > cindex
                                  ? Colours.color33A8A8BC
                                  : Colours.colorEDDBFF,
                              borderRadius: BorderRadius.circular(8.r),
                              gradient: index <= cindex
                                  ? const LinearGradient(
                                      colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight)
                                  : null,
                              border: index > cindex
                                  ? null
                                  : Border.all(
                                      width: 1, color: Colours.color9D4FEF)),
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                    bottom: 5.w, left: 8.w, right: 8.w),
                                child: index > cindex
                                    ? WxAssets.images.points2Hui
                                        .image(width: 20.w, height: 20.w)
                                    : WxAssets.images.points2
                                        .image(width: 20.w, height: 20.w),
                              ),
                              Text(
                                index == 6
                                    ? "+${2 * (index * 2)}"
                                    : "+${2 * (1 + index)}",
                                style: TextStyle(
                                    color: index > cindex
                                        ? Colours.colorA8A8BC
                                        : Colours.white,
                                    fontSize: 12.sp),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                  SizedBox(
                    height: 8.w,
                  ),
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.only(left: 13.w, right: 7.w),
                    child: Row(
                      children: [
                        0 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                        Container(
                          width: 33.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: 1 > cindex ? Colours.colorA8A8BC : null,
                            gradient: 1 > cindex
                                ? null
                                : const LinearGradient(
                                    colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        1 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                        Container(
                          width: 33.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: 2 > cindex ? Colours.colorA8A8BC : null,
                            gradient: 2 > cindex
                                ? null
                                : const LinearGradient(
                                    colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        2 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                        Container(
                          width: 33.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: 3 > cindex ? Colours.colorA8A8BC : null,
                            gradient: 3 > cindex
                                ? null
                                : const LinearGradient(
                                    colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        3 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                        Container(
                          width: 33.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: 4 > cindex ? Colours.colorA8A8BC : null,
                            gradient: 4 > cindex
                                ? null
                                : const LinearGradient(
                                    colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        4 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                        Container(
                          width: 33.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: 5 > cindex ? Colours.colorA8A8BC : null,
                            gradient: 5 > cindex
                                ? null
                                : const LinearGradient(
                                    colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        5 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                        Container(
                          width: 33.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: 6 > cindex ? Colours.colorA8A8BC : null,
                            gradient: 6 > cindex
                                ? null
                                : const LinearGradient(
                                    colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF,
                                      ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        6 > cindex
                            ? WxAssets.images.pointsSignNo
                                .image(width: 14.w, height: 14.w)
                            : WxAssets.images.pointsSign
                                .image(width: 14.w, height: 14.w),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(7, (index) {
                        return Container(
                          width: 37.w,
                          padding: EdgeInsets.only(top: 8.w),
                          alignment: Alignment.center,
                          child: Text(
                            index > cindex
                                ? S.current.the_day(index + 1)
                                : S.current.signed,
                            style: TextStyle(
                                color: Colours.white, fontSize: 10.sp),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
          ]),
        ],
      ),
    );
  }
}

/// 自定义 Tab，选中时渐变色，未选中时普通颜色
Widget gradientTab(String text, int index, TabController controller) {
  return AnimatedBuilder(
    animation: controller.animation!,
    builder: (context, child) {
      bool isSelected = controller.index == index;
      return Container(
        height: 40.w,
        width: 130.w,
        padding: EdgeInsets.only(
          bottom: 4.w,
        ),
        alignment: Alignment.center,
        child: Column(
          children: [
            ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  colors: isSelected
                      ? [Colours.colorFFFC4E, Colours.colorBBFFA0]
                      : [Colours.color9393A5, Colours.color9393A5],
                ).createShader(bounds);
              },
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                  color: Colors.white,
                ),
              ),
            ),
            if (isSelected)
              Container(
                width: 18.w,
                height: 3.w,
                margin: EdgeInsets.only(top: 5.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2.r),
                    gradient: LinearGradient(
                        colors: isSelected
                            ? [Colours.colorFFFC4E, Colours.colorBBFFA0]
                            : [],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight)),
              )
          ],
        ),
      );
    },
  );
}
