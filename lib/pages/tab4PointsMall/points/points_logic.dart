import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/my_points_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

class PointsLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  var myPointsModel = MyPointsModel().obs;
  var isFrist2 = true;
  var colorNameList = <String>[].obs;
  var sizeNameList = <String>[].obs;
  //数据列表 每日任务
  var dataList1 = <MyPointsModelTaskList>[].obs;
  //数据列表 成长任务
  var dataList2 = <MyPointsModelTaskList>[].obs;
  //数据列表 新手任务
  var dataList3 = <MyPointsModelTaskList>[].obs;
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    getPointsInfo();
    getPointsList();
    //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
    UserManager.instance.postApmTracking(0,
        remark: "签到页面",
        nowPage: Routes.minePage,
        toPage: Routes.pointsPage,
        content: "进入签到页面");
  }

  Future<void> getPointsList() async {
    // WxLoading.show();
    Map<String, dynamic>? param = {};
    final res = await Api().get(ApiUrl.pointsAllTask, queryParameters: param);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      var allTaskModel = MyPointsModel.fromJson(res.data);
      dataList1.clear();
      dataList2.clear();
      dataList3.clear();
      for (var item in allTaskModel.taskList ?? []) {
        if (item is MyPointsModelTaskList) {
          if (item.id == 13) {
            await WxStorage.instance
                .setString("createSitePoint", item.point ?? '0');
          }
          switch (item.taskType) {
            //任务类型 //1.每日任务 2.成长任务 3.新手任务
            case 1:
              dataList1.add(item);
              break;
            case 2:
              dataList2.add(item);
              break;
            default:
              dataList3.add(item);
              break;
          }
        }
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //获得积分数据
  Future<void> getPointsInfo() async {
    // WxLoading.show();
    Map<String, dynamic>? param = {};
    final res = await Api().get(ApiUrl.pointsMyPoint, queryParameters: param);
    log("MineLogicgetPointsSignIn0=${jsonEncode(res.data)}");
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      myPointsModel.value = MyPointsModel.fromJson(res.data);
      myPointsModel.refresh();
      if (isFrist2 && !(myPointsModel.value.isSignToday ?? false)) {
        isFrist2 = false;
        getPointsSignIn();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //每日积分签到
  Future<void> getPointsSignIn() async {
    log("MineLogicgetPointsSignIn1");
    // WxLoading.show();
    Map<String, dynamic>? param = {"taskID": "4"};
    final res = await Api().post(ApiUrl.pointsSignIn, data: param);
    log(jsonEncode(res.data));
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      //  WxLoading.showToast(S.current.Sign_in_successfully);
      getDakaDialog3(
        S.current.Sign_in_successfully,
        "+${res.data["point"]}",
        (myPointsModel.value.nextDayPoints ?? "0").toString(),
        S.current.sure,
        () {
          AppPage.back();
        },
      );
      getPointsInfo();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
    WidgetsBinding.instance.removeObserver(this);
  }

  //打卡成功
  void getDakaDialog3(
    String titltPoint,
    String ponitCount,
    String ponitTomorrow,
    String sureText,
    void Function()? onPressed,
  ) {
    Get.dialog(
      Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Material(
          type: MaterialType.transparency,
          color: Colors.transparent,
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    color: Colors.transparent,
                    child: Column(
                      children: <Widget>[
                        //upload_top_img
                        SizedBox(
                          height: 60.w,
                        ),
                        Container(
                          height: 100.w,
                          width: double.infinity,
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Container(
                                height: 65.w,
                                width: double.infinity,
                                margin: EdgeInsets.only(top: 35.w),
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(25.r),
                                    topRight: Radius.circular(25.r),
                                  ),
                                ),
                              ),
                              Transform.translate(
                                offset: Offset(0, -30.w),
                                child: MyImage(
                                  "daka3.png",
                                  width: 78.w,
                                  height: 78.w,
                                  isAssetImage: true,
                                  fit: BoxFit.fitWidth,
                                  bgColor: Colors.transparent,
                                ),
                              ),
                            ],
                          ),
                        ),

                        Transform.translate(
                          offset: const Offset(0, -10),
                          child: Container(
                            alignment: Alignment.topLeft,
                            decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(25.r),
                                bottomRight: Radius.circular(25.r),
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            width: double.infinity,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                if (titltPoint != "")
                                  Text(titltPoint,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 18.sp,
                                        color: Colours.white,
                                        fontWeight: AppFontWeight.regular(),
                                        height: 1,
                                      )),
                                if (titltPoint != "")
                                  SizedBox(
                                    height: 25.w,
                                  ),
                                if (ponitCount != "")
                                  RichText(
                                    textAlign: TextAlign.end,
                                    text: TextSpan(
                                        text: ponitCount,
                                        style: TextStyle(
                                            color: Colours.colorA44EFF,
                                            fontSize: 26.sp,
                                            fontWeight: FontWeight.bold),
                                        children: <InlineSpan>[
                                          TextSpan(
                                              text: " ${S.current.integral}",
                                              style: TextStyle(
                                                  color: Colours.colorA44EFF,
                                                  fontSize: 12.sp,
                                                  fontWeight:
                                                      FontWeight.normal)),
                                        ]),
                                  ),
                                if (ponitCount != "")
                                  SizedBox(
                                    height: 25.w,
                                  ),
                                Text(S.current.Sign_in_tomorrow2(ponitTomorrow),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colours.color9393A5,
                                      fontWeight: AppFontWeight.regular(),
                                      height: 1,
                                    )),
                                SizedBox(
                                  height: 20.w,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      sureText,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(
                          height: 25.w,
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            AppPage.back();
                          },
                          child: WxAssets.images.icCloseDialog
                              .image(width: 30.w, height: 30.w),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierColor: Colors.black.withOpacity(0.85),
    );
  }

  //处理任务跳转逻辑
  void getTaskWithId(int id) {
    log("getTaskWithId$id");
    switch (id) {
      case 1: //合成集锦
        AppPage.to(Routes.place).then((value) {
          getPointsInfo();
        });
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(0,
            remark: "点击每日任务中的合成集锦按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.place,
            subPage: "pointsTodayTask1",
            content: "进入球馆列表页面");
        break;
      case 2: //分享
        AppPage.to(Routes.place).then((value) {
          getPointsInfo();
        });
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(0,
            remark: "点击每日任务中的合成分享按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.place,
            subPage: "pointsTodayTask2",
            content: "进入球馆列表页面");
        break;
      case 3: //球馆打卡
        AppPage.to(Routes.place).then((value) {
          getPointsInfo();
        });
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(0,
            remark: "点击每日任务中的合成打卡按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.place,
            subPage: "pointsTodayTask3",
            content: "进入球馆列表页面");
        break;
      case 4: //签到
        getPointsSignIn();
        //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
        UserManager.instance.postApmTracking(1,
            remark: "点击每日任务中的合成签到按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.place,
            subPage: "pointsTodayTask4",
            content: "调用签到接口");
        break;
      case 5: //开通续约会员
        AppPage.to(Routes.vipPage).then((value) {
          getPointsInfo();
        });
        UserManager.instance.postApmTracking(0,
            remark: "点击开通续约会员按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.vipPage,
            subPage: "pointsUpTask1",
            content: "跳转开通会员");
        break;
      case 6: //解锁比赛报告
        AppPage.to(Routes.scheduleHomePage).then((value) {
          getPointsInfo();
        });
        UserManager.instance.postApmTracking(0,
            remark: "点击解锁比赛报告按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.scheduleHomePage,
            subPage: "pointsUpTask2",
            content: "跳转解锁比赛报告");
        break;
      case 7: //邀请新用户
        AppPage.to(Routes.inviteCodePage).then((value) {
          getPointsInfo();
        });
        UserManager.instance.postApmTracking(0,
            remark: "点击邀请新用户按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.inviteCodePage,
            subPage: "pointsUpTask3",
            content: "跳转邀请新用户");
        break;
      case 8: //完善基础信息
        AppPage.to(Routes.modifyInfo).then((value) {
          getPointsInfo();
        });
        UserManager.instance.postApmTracking(0,
            remark: "点击完善基础信息按钮",
            nowPage: Routes.pointsPage,
            toPage: Routes.modifyInfo,
            subPage: "pointsNewUserTask1",
            content: "跳转完善基础信息");
        break;
      case 9: //下载球秀APP并登录
        WxLoading.showToast(S.current.app_market_to_download);
        UserManager.instance.postApmTracking(1,
            remark: "点击下载球秀APP并登录按钮",
            nowPage: Routes.pointsPage,
            subPage: "pointsNewUserTask2",
            content: "下载球秀APP并登录");
        break;
      case 10:
        AppPage.to(Routes.place).then((value) {
          getPointsInfo();
        });
        UserManager.instance.postApmTracking(0,
            remark: "点击每日任务中的合成参与赛事按钮",
            nowPage: Routes.place,
            toPage: Routes.pointsPage,
            subPage: "pointsTodayTask5",
            content: "进入球馆列表页面");
        break;
      case 13: //创建场地
        AppPage.to(Routes.createArenaPage, arguments: {"from": "points"});
        break;
      default:
        AppPage.to(Routes.place).then((value) {
          getPointsInfo();
        });
        break;
    }
  }
}
