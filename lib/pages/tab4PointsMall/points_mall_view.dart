import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_mall_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的->积分页面
class PointsMallPage extends StatelessWidget {
  final int type;
  PointsMallPage({super.key, required this.type});

  final logic = Get.put(PointsMallLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: MyAppBar(
            title: Text(S.current.points_shopping_Mall),
            leading: (type == 0)
                ? const SizedBox()
                : IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                    ),
                    onPressed: () {
                      AppPage.back();
                    },
                  )),
        body: Obx(() {
          return Center(
            child: (logic.isFrist.value)
                ? buildLoad()
                : DefaultTabController(
                    length: 4,
                    child: NestedScrollView(
                      controller: logic.scrollController,
                      headerSliverBuilder: (context, innerBoxIsScrolled) {
                        return [
                          SliverToBoxAdapter(
                            child: pointsInfoWidget(context),
                          ),
                          if (!logic.isFristGoods.value)
                            SliverAppBar(
                              // 关键高度设置
                              toolbarHeight: 30, // 设置主高度
                              expandedHeight: 0, // 禁用扩展
                              // 禁用所有可能增加高度的部分
                              title: const SizedBox.shrink(), // 隐藏标题
                              leading: const SizedBox.shrink(), // 隐藏返回按钮
                              actions: const [], // 隐藏操作按钮
                              pinned: true,
                              floating: true,
                              snap: false,
                              backgroundColor: Colours.color0F0F16,
                              automaticallyImplyLeading: false,
                              bottom: PreferredSize(
                                preferredSize: Size.fromHeight(66.w), // 固定高度30
                                child: Container(
                                  width: double.infinity,
                                  color: Colours.color0F0F16,
                                  alignment: Alignment.topLeft,
                                  padding: EdgeInsets.only(bottom: 15.w),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: TabBar(
                                            controller: logic.tabController,
                                            unselectedLabelColor:
                                                Colours.color5C5C6E,
                                            unselectedLabelStyle: TextStyle(
                                                fontSize: 14.sp,
                                                color: Colours.color5C5C6E,
                                                fontWeight: FontWeight.w600),
                                            labelColor: Colours.white,
                                            labelStyle: TextStyle(
                                                fontSize: 16.sp,
                                                color: Colours.white,
                                                fontWeight: FontWeight.w600),
                                            isScrollable: true,
                                            // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                                            indicatorPadding: EdgeInsets.zero,
                                            dividerColor: Colors.transparent,
                                            dividerHeight: 0,
                                            labelPadding: const EdgeInsets
                                                .symmetric(
                                                horizontal: 4.0), // 调整标签间的间距
                                            indicatorSize:
                                                TabBarIndicatorSize.label,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 15.w),
                                            indicatorColor: Colors.transparent,
                                            physics:
                                                const NeverScrollableScrollPhysics(),
                                            tabAlignment: TabAlignment.start,
                                            tabs: List.generate(
                                                logic.tabs.length, (index) {
                                              return Obx(() {
                                                return SizedBox(
                                                  width: 75.w,
                                                  height: 40.w,
                                                  child: Stack(
                                                    alignment:
                                                        Alignment.bottomCenter,
                                                    children: [
                                                      if (logic.tabbarIndex
                                                              .value ==
                                                          index)
                                                        WxAssets
                                                            .images.imgCheckIn2
                                                            .image(
                                                                width: 19.w,
                                                                height: 9.w),
                                                      Positioned(
                                                          bottom: 10.w,
                                                          child: Text(
                                                            logic.tabs[index]
                                                                .title,
                                                            style: TextStyles
                                                                .regular
                                                                .copyWith(
                                                              fontWeight: logic
                                                                          .tabbarIndex
                                                                          .value ==
                                                                      index
                                                                  ? FontWeight
                                                                      .bold
                                                                  : FontWeight
                                                                      .normal,
                                                              fontSize: logic
                                                                          .tabbarIndex
                                                                          .value ==
                                                                      index
                                                                  ? 16.sp
                                                                  : 14.sp,
                                                              color: logic.tabbarIndex
                                                                          .value ==
                                                                      index
                                                                  ? Colours
                                                                      .white
                                                                  : Colours
                                                                      .color5C5C6E,
                                                            ),
                                                          )),
                                                    ],
                                                  ),
                                                );
                                              });
                                            })),
                                      ),
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          AppPage.to(Routes.pointsGoodsTypePage,
                                              needLogin: true);
                                          //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                                          UserManager.instance.postApmTracking(
                                              0,
                                              remark: "积分商城点击更多分类按钮",
                                              nowPage: Routes.pointsMallPage,
                                              toPage:
                                                  Routes.pointsGoodsTypePage,
                                              content: "跳转商品更多分类页面");
                                        },
                                        child: SizedBox(
                                          height: 35.w,
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                S.current.more_type,
                                                style: TextStyle(
                                                    color: Colours.white,
                                                    fontSize: 14.sp),
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 1,
                                              ),
                                              SizedBox(
                                                width: 3.w,
                                              ),
                                              Padding(
                                                padding: EdgeInsets.only(
                                                    bottom: 1.w),
                                                child: WxAssets
                                                    .images.icArrowRight
                                                    .image(
                                                        width: 14.w,
                                                        height: 14.w,
                                                        color: Colours.white),
                                              ),
                                              SizedBox(
                                                width: 12.w,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ];
                      },
                      body: logic.isFristGoods.value
                          ? buildLoad()
                          : TabBarView(
                              controller: logic.tabController,
                              children:
                                  logic.tabs.map((tab) => tab.page).toList(),
                            ),
                    )),
          );
        }));
  }

  Container pointsInfoWidget(context) {
    return Container(
      width: double.infinity,
      height: 86.w,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      decoration: BoxDecoration(
          gradient: const LinearGradient(
              colors: [Colours.color217732ED, Colours.color890AFF]),
          borderRadius: BorderRadius.circular(8.r)),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                S.current.integral,
                style: TextStyles.regular
                    .copyWith(fontSize: 12.sp, color: Colours.colorA8A8BC),
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  AppPage.to(Routes.pointsExchangePage, needLogin: true);
                },
                child: SizedBox(
                  height: 32.w,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        S.current.integral_info2,
                        style: TextStyle(color: Colours.white, fontSize: 12.sp),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      SizedBox(
                        width: 3.w,
                      ),
                      Padding(
                        padding: EdgeInsets.only(bottom: 1.w),
                        child: WxAssets.images.icArrowRight.image(
                            width: 14.w, height: 14.w, color: Colours.white),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Obx(
                () => Text(
                  "${UserManager.instance.userInfo.value?.point}",
                  style: TextStyles.regular.copyWith(
                      fontSize: 30.sp,
                      fontFamily: 'DIN',
                      fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              WxAssets.images.points2.image(width: 22.w, height: 22.w),
              const Spacer(),
              if (logic.type.value == 0)
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    AppPage.to(Routes.pointsPage, needLogin: true)
                        .then((onValue) {
                      logic.getPointsInfo();
                    });
                    //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                    UserManager.instance.postApmTracking(0,
                        remark: "积分商城获取更多积分按钮",
                        nowPage: Routes.pointsMallPage,
                        toPage: Routes.pointsPage,
                        content: "跳转积分页面");
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 6.w),
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                            colors: [
                              Colours.colorFFECC1,
                              Colours.colorE7CEFF,
                              Colours.colorD1EAFF
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight),
                        borderRadius: BorderRadius.all(Radius.circular(22.r))),
                    child: Text(
                      S.current.integral_info8,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.color191921,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                )
            ],
          ),
          SizedBox(
            height: 12.w,
          )
        ],
      ),
    );
  }
}
