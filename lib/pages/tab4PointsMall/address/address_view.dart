import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/address_model.dart';
import 'package:shoot_z/pages/tab4PointsMall/address/address_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///商城地址列表
class AddressPage extends StatelessWidget {
  AddressPage({super.key});

  final logic = Get.put(AddressLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MyAppBar(
        title: Text("收货地址"),
      ),
      body: _listWidget(context),
      bottomNavigationBar: barWidget(context),
    );
  }

  InkWell barWidget(BuildContext context) {
    return InkWell(
      onTap: () {
        getAddressDialog(context);
      },
      child: Container(
        width: double.infinity,
        height: 50.w,
        alignment: Alignment.center,
        margin: EdgeInsets.only(
            left: 15.w,
            right: 15.w,
            bottom: ScreenUtil().bottomBarHeight + 10.w),
        decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colours.color7732ED, Colours.colorA555EF],
              begin: Alignment.bottomLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(25.r)),
        child: Text(
          '新增地址',
          style: TextStyles.semiBold14,
        ),
      ),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? myNoDataView(
                      context,
                      msg: "暂无收货地址",
                      imagewidget: WxAssets.images.addressNodata
                          .image(width: 180.w, height: 120.w),
                    )
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(bottom: 40.w),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return _listItemWidget(
                            logic.dataList[position], context);
                      }));
    });
  }

  /// 构建列表项
  Widget _listItemWidget(AddressModel item, BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (logic.isSelectTeam) {
          AppPage.back(result: item);
          return;
        }
        // AppPage.to(Routes.teamInfoPage, arguments: {
        //   'teamId': item.id,
        // }).then((v) {
        //   logic.getdataList(controller: logic.refreshController, isLoad: false);
        // });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: const Color.fromRGBO(25, 25, 33, 1)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${item.province}${item.city}${item.district}",
              style: TextStyles.regular
                  .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
            ),
            SizedBox(
              height: 15.w,
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    "${item.address}${logic.isSelectId.value}",
                    style: TextStyles.regular.copyWith(
                        fontSize: 14.sp,
                        color: Colours.white,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                logic.isSelectId.value == item.id.toString()
                    ? WxAssets.images.addressOk.image(width: 16.w, height: 16.w)
                    : SizedBox(
                        height: 16.w,
                      ),
              ],
            ),
            SizedBox(
              height: 15.w,
            ),
            Text(
              "${item.name ?? ""}\t\t\t\t${item.phone ?? ""}",
              style: TextStyles.regular.copyWith(
                fontSize: 14.sp,
                color: Colours.white,
              ),
            ),
            SizedBox(
              height: 10.w,
            ),
            const Divider(
              color: Colours.color99292937,
              height: 10,
            ),
            Row(
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.updateAddAddress2(
                        item.id.toString(), item.isDefault == 1 ? 0 : 1);
                  },
                  child: Container(
                      height: 40.w,
                      width: 100.w,
                      alignment: Alignment.centerLeft,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          item.isDefault == 1
                              ? WxAssets.images.addressCheck
                                  .image(width: 16.w, height: 16.w)
                              : WxAssets.images.addressUncheck
                                  .image(width: 16.w, height: 16.w),
                          SizedBox(
                            width: 6.w,
                          ),
                          Text(
                            "设为默认",
                            style: TextStyles.regular.copyWith(
                              fontSize: 12.sp,
                              color: Colours.white,
                            ),
                          ),
                        ],
                      )),
                ),
                const Spacer(),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    getMyDialog(
                      S.current.dialog_title,
                      S.current.sure,
                      content: S.current.address_tips,
                      () {
                        AppPage.back();
                        logic.deleteAddress(item.id.toString());
                      },
                      isShowClose: false,
                      btnIsHorizontal: true,
                      btnText2: S.current.cancel,
                      onPressed2: () {
                        AppPage.back();
                      },
                    );
                  },
                  child: Container(
                      height: 40.w,
                      padding: EdgeInsets.symmetric(horizontal: 5.w),
                      alignment: Alignment.centerLeft,
                      child: Row(
                        children: [
                          WxAssets.images.addressDelete
                              .image(width: 16.w, height: 16.w),
                          SizedBox(
                            width: 6.w,
                          ),
                          Text(
                            "删除",
                            style: TextStyles.regular.copyWith(
                              fontSize: 12.sp,
                              color: Colours.colorFF3F3F,
                            ),
                          ),
                        ],
                      )),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.nameController.text = item.name ?? "";
                    logic.address2Controller.text = item.address ?? "";
                    logic.phoneController.text = item.phone ?? "";
                    logic.addressP.value =
                        "${item.province}${item.city}${item.district}";
                    logic.province = item.province ?? "";
                    logic.city = item.city ?? "";
                    logic.district = item.district ?? "";
                    //编辑
                    getAddressDialog(context, isUpdate: true, item: item);
                  },
                  child: Container(
                      height: 40.w,
                      padding: EdgeInsets.only(left: 5.w),
                      alignment: Alignment.centerLeft,
                      child: Row(
                        children: [
                          WxAssets.images.addressEdit
                              .image(width: 16.w, height: 16.w),
                          SizedBox(
                            width: 6.w,
                          ),
                          Text(
                            "编辑",
                            style: TextStyles.regular.copyWith(
                              fontSize: 12.sp,
                              color: Colours.white,
                            ),
                          ),
                        ],
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void getAddressDialog(BuildContext context,
      {bool isUpdate = false, AddressModel? item}) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colours.color191921,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.only(
              left: 20.w,
              right: 20.w,
            ),
            decoration: BoxDecoration(
                color: Colours.color0F0F16,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.r),
                    topRight: Radius.circular(16.r))),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Center(
                    child: Container(
                      width: 38.w,
                      margin: EdgeInsets.only(top: 5.w, bottom: 24.w),
                      height: 2.5.w,
                      decoration: BoxDecoration(
                          color: Colours.color10D8D8D8,
                          borderRadius: BorderRadius.circular(2.r)),
                    ),
                  ),
                  // 地址输入框
                  Container(
                    height: 44.w,
                    decoration: BoxDecoration(
                      color: Colours.color000000,
                      borderRadius: BorderRadius.circular(22.r),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    child: TextField(
                      controller: logic.nameController,
                      style: TextStyles.bold
                          .copyWith(color: Colours.white, fontSize: 14.sp),
                      inputFormatters: [
                        FilteringTextInputFormatter.deny(
                            RegExp(r'[" "]')), // 只允许输入数字
                        LengthLimitingTextInputFormatter(15), // 限制输入长度为7
                      ],
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: '请输入收货人姓名',
                        contentPadding: EdgeInsets.only(bottom: 3.w),
                        hintStyle: TextStyle(
                          color: Colours.color5C5C6E,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 15.w),
                  Container(
                    height: 44.w,
                    decoration: BoxDecoration(
                      color: Colours.color000000,
                      borderRadius: BorderRadius.circular(22.r),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    child: TextField(
                      controller: logic.phoneController,
                      style: TextStyles.medium
                          .copyWith(color: Colours.white, fontSize: 14.sp),
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'[0-9]')), // 只允许输入数字
                        LengthLimitingTextInputFormatter(11), // 限制输入长度为7
                      ],
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: '请输入手机号',
                        contentPadding: EdgeInsets.only(bottom: 3.w),
                        hintStyle: TextStyle(
                          color: Colours.color5C5C6E,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 15.w),
                  // 地址输入框
                  Obx(() {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.showCityPicker();
                      },
                      child: Container(
                        height: 44.w,
                        decoration: BoxDecoration(
                          color: Colours.color000000,
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        padding: EdgeInsets.only(left: 20.w),
                        margin: EdgeInsets.symmetric(horizontal: 5.w),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                logic.addressP.value == ""
                                    ? "请选择省市区县"
                                    : logic.addressP.value,
                                style: TextStyle(
                                  color: logic.addressP.value == ""
                                      ? Colours.color5C5C6E
                                      : Colours.white,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                //定位获取省市区
                                logic.getLocationAddress();
                              },
                              child: Container(
                                  height: 44.w,
                                  padding:
                                      EdgeInsets.only(right: 15.w, left: 10.w),
                                  child: WxAssets.images.addressLocation
                                      .image(width: 16.w, height: 16.w)),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                  SizedBox(height: 15.w),
                  // 地址输入框
                  Container(
                    height: 44.w,
                    decoration: BoxDecoration(
                      color: Colours.color000000,
                      borderRadius: BorderRadius.circular(22.r),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    child: TextField(
                      controller: logic.address2Controller,
                      style: TextStyles.bold
                          .copyWith(color: Colours.white, fontSize: 14.sp),
                      inputFormatters: [
                        FilteringTextInputFormatter.deny(
                            RegExp(r'[" "]')), // 只允许输入数字
                        LengthLimitingTextInputFormatter(40), // 限制输入长度为7
                      ],
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: '请输入街道、小区楼栋、门牌号、村等',
                        contentPadding: EdgeInsets.only(bottom: 3.w),
                        hintStyle: TextStyle(
                          color: Colours.color5C5C6E,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      if (logic.nameController.text.trim().isEmpty) {
                        WxLoading.showToast("请输入收货人姓名");
                        return;
                      }
                      var phone = logic.phoneController.text.trim();
                      if (!Utils.isPhoneNumber(phone)) {
                        WxLoading.showToast("请输入正确手机号码");
                        return;
                      }
                      if (logic.addressP.value.trim().isEmpty ||
                          logic.addressP.value == "") {
                        WxLoading.showToast("请选择省市区");
                        return;
                      }
                      if (logic.address2Controller.text.trim().isEmpty) {
                        WxLoading.showToast("请输入街道、小区楼栋、门牌号、村等");
                        return;
                      }
                      Get.back();
                      if (!isUpdate) {
                        logic.postAddAddress();
                      } else {
                        logic.updateAddAddress((item?.id ?? 0).toString());
                      }
                    },
                    child: Container(
                      height: 46.w,
                      width: double.infinity,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                        top: 25.w,
                        bottom: 35.w,
                      ),
                      padding: EdgeInsets.only(
                          left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                      decoration: BoxDecoration(
                        color: Colours.color282735,
                        borderRadius: BorderRadius.all(Radius.circular(28.r)),
                        gradient: const LinearGradient(
                          colors: [
                            Colours.color7732ED,
                            Colours.colorA555EF,
                          ],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        S.current.save,
                        style: TextStyles.display16.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 25.w,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
