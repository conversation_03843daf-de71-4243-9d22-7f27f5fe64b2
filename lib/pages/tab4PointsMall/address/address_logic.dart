import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:city_pickers/city_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/address_model.dart';
import 'package:shoot_z/network/model/location_address_model.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:ui_packages/ui_packages.dart';

class AddressLogic extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  final nameController = TextEditingController();
  final phoneController = TextEditingController();
  final address2Controller = TextEditingController();
  var addressP = "".obs;
  var city = ""; //市
  var province = ""; //省
  var district = ""; //区
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <AddressModel>[].obs;
  //是否是选择球队
  var isSelectTeam = true;
  var locationAddressModel = LocationAddressModel().obs;
  StreamSubscription? subscription;
  var isUpdate = false;
  var isSelectId = "".obs;
  //数据列表
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('id')) {
      isSelectId.value = Get.arguments['id'];
    }
    subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        getLocationAddress();
      }
    });
    getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      // 'page': dataFag["page"] ?? 1,
      // 'limit': 20,
    };
    var res = await Api().get(ApiUrl.getAddressList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<AddressModel> modelList =
          list.map((e) => AddressModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  getLocationAddress() async {
    if (await LocationUtils.instance.checkPermission()) {
      await LocationUtils.instance.getCurrentPosition();
    }

    final position = LocationUtils.instance.position;
    if (position == null) {
      return;
    }
    Map<String, dynamic> param = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
    };
    log("getLocationAddress=${param} ");
    var res = await Api().get(ApiUrl.getCommonLocation, queryParameters: param);
    if (res.isSuccessful()) {
      log("getLocationAddress=${param} ${jsonEncode(res.data)}");
      locationAddressModel.value = LocationAddressModel.fromJson(res.data);
      locationAddressModel.refresh();
      province = locationAddressModel.value.name ?? "";
      city = locationAddressModel.value.nodes?.first?.name ?? "";
      district =
          locationAddressModel.value.nodes?.first?.nodes?.first?.name ?? "";
      addressP.value = "$province$city$district";
      addressP.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  postAddAddress() async {
    Map<String, dynamic> param = {
      'province': province,
      'city': city,
      'district': district,
      'address': address2Controller.text.trim(),
      'isDefault': dataList.isNotEmpty ? 0 : 1, //1：默认
      'name': nameController.text.trim(),
      'phone': phoneController.text.trim(),
    };
    log("getLocationAddress=${param} ");
    var res = await Api().post(ApiUrl.getAddressList, data: param);
    if (res.isSuccessful()) {
      log("postAddAddress=${param} ${jsonEncode(res.data)}");
      nameController.text = "";
      address2Controller.text = "";
      phoneController.text = "";
      getdataList(isLoad: false, controller: refreshController);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  updateAddAddress(
    String id,
  ) async {
    Map<String, dynamic> param = {
      'id': id,
      'province': province,
      'city': city,
      'district': district,
      'address': address2Controller.text.trim(),
      // 'isDefault': dataList.isNotEmpty ? 0 : 1, //1：默认
      'name': nameController.text.trim(),
      'phone': phoneController.text.trim(),
    };
    log("getLocationAddress=${param} ");
    var url = await ApiUrl.putAddress(id);
    var res = await Api().PUT(url, data: param);
    if (res.isSuccessful()) {
      log("postAddAddress=${param} ${jsonEncode(res.data)}");
      nameController.text = "";
      address2Controller.text = "";
      phoneController.text = "";
      WxLoading.showToast(S.current.modification_successful);
      getdataList(isLoad: false, controller: refreshController);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  updateAddAddress2(String id, int isDefault) async {
    Map<String, dynamic> param = {
      'id': id,
      'isDefault': isDefault, //1：默认
    };
    log("getLocationAddress=${param} ");
    var url = await ApiUrl.putAddress(id);
    var res = await Api().PUT(url, data: param);
    if (res.isSuccessful()) {
      log("postAddAddress=${param} ${jsonEncode(res.data)}");
      nameController.text = "";
      address2Controller.text = "";
      phoneController.text = "";
      WxLoading.showToast(S.current.modification_successful);
      getdataList(isLoad: false, controller: refreshController);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  deleteAddress(String id) async {
    Map<String, dynamic> param = {
      'id': id,
    };
    log("getLocationAddress=${param} ");
    var url = await ApiUrl.deleteAddress(id);
    var res = await Api().delete(url, data: param);
    if (res.isSuccessful()) {
      log("deleteAddress=${jsonEncode(res.data)}");
      WxLoading.showToast(S.current.Operation_successful);
      getdataList(isLoad: false, controller: refreshController);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void openSettings() async {
    final result = await WxStorage.instance.getBool('requestPermission');
    if (Platform.isAndroid && result == null) {
      WxStorage.instance.setBool('requestPermission', true);
      if (await LocationUtils.instance
          .requestPermission(Get.context!, "我们需要您的位置信息以提供位置，获得收货地址的省市区县")) {
        getLocationAddress()();
      }
    } else {
      LocationUtils.instance.openSettings("我们需要您的位置信息以提供位置，获得收货地址的省市区县");
    }
  }

  void showCityPicker() async {
    Result? result = await CityPickers.showCityPicker(
      context: Get.context!,
      theme: ThemeData.light(),
      showType: ShowType.pca,

      locationCode:
          locationAddressModel.value.nodes?.first?.nodes?.first?.code ??
              '110000',
      itemExtent: 34.w,
      // citiesData: citiesData,
      // provincesData: provincesData,
      itemBuilder: (item, list, index) {
        return Text(
          item,
          style: TextStyles.regular.copyWith(color: Colors.black),
        ).paddingOnly(top: 10.w);
      },
    );
    log("showCityPicker2=${result}");
    if (result != null) {
      province = result.provinceName ?? "";
      city = result.cityName ?? "";
      district = result.areaName ?? "";
      addressP.value = "$province$city$district";
      addressP.refresh();
    }
    // if (result != null && result.cityId != state.cityId) {
    //   state.cityName.value = result.cityName ?? '';
    //   state.cityId = result.cityId;
    //   BusUtils.instance.fire(EventAction(key: EventBusKey.rankingsCitySwitch));
    //   // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   //   pageController.jumpToPage(state.isPlayer.value ? 0 : 1); // 跳转到第二页
    //   // });
    // }
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }
}
