// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/points_exchange_model.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_exchange/points_exchange_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 积分兑换记录
///
class PointsExchangePage extends StatelessWidget {
  PointsExchangePage({super.key});
  final logic = Get.put(PointsExchangeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colours.bg_color,
        appBar: MyAppBar(
          title: Text(S.current.integral_info2),
        ),
        body: _listWidget1(context));
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 480.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 105.w, height: 89.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(PointsExchangeModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 12.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r), color: Colours.color191921),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            (item.goodAttrs ?? "") != ""
                ? "${item.name ?? ""} (${item.goodAttrs ?? ""})"
                : item.name ?? "",
            style: TextStyles.regular.copyWith(fontWeight: FontWeight.w600),
          ),
          SizedBox(
            height: 15.w,
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  item.date ?? "",
                  style: TextStyles.regular
                      .copyWith(color: Colours.color5C5C6E, fontSize: 12.sp),
                ),
              ),
              WxAssets.images.points2.image(width: 16.w, height: 16.w),
              Text(
                "\t-${item.pointCost ?? ""}",
                style: TextStyles.regular
                    .copyWith(color: Colours.white, fontSize: 14.sp),
              )
            ],
          ),
          if (item.type == 2)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(
                  height: 20,
                  color: Colours.c10ffffff,
                ),
                Text(
                  item.phone ?? "",
                  style: TextStyles.regular
                      .copyWith(color: Colours.color5C5C6E, fontSize: 12.sp),
                ),
                SizedBox(
                  height: 15.w,
                ),
                Text(
                  item.address ?? "",
                  style: TextStyles.regular,
                ),
              ],
            ),
        ],
      ),
    );
  }
}
