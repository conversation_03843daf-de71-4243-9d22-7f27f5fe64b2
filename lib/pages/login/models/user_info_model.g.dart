// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserInfoModel _$UserInfoModelFromJson(Map<String, dynamic> json) =>
    UserInfoModel(
      json['age'] as int,
      json['avatar'] as String,
      json['birthday'] as String,
      json['gender'] as int,
      json['isSignToday'] as bool,
      json['point'] as int,
      json['registered'] as bool,
      json['betaUser'] as bool,
      json['signInPoints'] as int,
      json['userId'] as String,
      json['userName'] as String,
      (json['phone'] ?? '') as String,
      json['vipLevel'] as int,
      json['vipArenaId'] as int?,
      json['nextDayPoints'] as int,
      json['vipArenaName'] as String?,
      vipExpiredFlag: json['vipExpiredFlag'] as int?,
      vipExpiredDays: json['vipExpiredDays'] as int?,
      vipExpireDate: json['vipExpireDate'] as String?,
      inviteCode: json['inviteCode'] as String?,
    );

Map<String, dynamic> _$UserInfoModelToJson(UserInfoModel instance) =>
    <String, dynamic>{
      'age': instance.age,
      'avatar': instance.avatar,
      'birthday': instance.birthday,
      'gender': instance.gender,
      'isSignToday': instance.isSignToday,
      'point': instance.point,
      'registered': instance.registered,
      'betaUser': instance.betaUser,
      'signInPoints': instance.signInPoints,
      'userId': instance.userId,
      'userName': instance.userName,
      'phone': instance.phone,
      'vipLevel': instance.vipLevel,
      'nextDayPoints': instance.nextDayPoints,
      'vipExpiredFlag': instance.vipExpiredFlag,
      'vipExpiredDays': instance.vipExpiredDays,
      'vipExpireDate': instance.vipExpireDate,
      'inviteCode': instance.inviteCode,
      'vipArenaId': instance.vipArenaId,
      'vipArenaName': instance.vipArenaName,
    };
