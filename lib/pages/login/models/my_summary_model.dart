import 'package:json_annotation/json_annotation.dart';

part 'my_summary_model.g.dart';

@JsonSerializable()
class MySummaryModel extends Object {
  @Json<PERSON>ey(name: 'assist')
  int assist;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rebound')
  int rebound;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'score')
  int score;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'shotRate')
  String shotRate;

  @Json<PERSON>ey(name: 'totalMatch')
  int totalMatch;

  MySummaryModel(
      this.assist, this.rebound, this.score, this.shotRate, this.totalMatch);

  factory MySummaryModel.fromJson(Map<String, dynamic> srcJson) =>
      _$MySummaryModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MySummaryModelToJson(this);
}
