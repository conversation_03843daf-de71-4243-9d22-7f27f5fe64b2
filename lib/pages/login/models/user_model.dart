import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';


@JsonSerializable()
class UserModel extends Object {
  @J<PERSON><PERSON><PERSON>(name: 'userId')
  String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'token')
  String token;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'refreshToken')
  String refreshToken;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'expireTime')
  String expireTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'isFirstRegister')
  bool? isFirstRegister;

  UserModel(this.userId, this.token, this.refreshToken, this.expireTime,{this.isFirstRegister});

  factory UserModel.fromJson(Map<String, dynamic> srcJson) => _$UserModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

}


