import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import '../../../generated/l10n.dart';
import 'logic.dart';
import 'package:ui_packages/ui_packages.dart';

class LoginCodePage extends StatefulWidget {
  const LoginCodePage({super.key});

  @override
  _LoginCodePageState createState() => _LoginCodePageState();
}

class _LoginCodePageState extends State<LoginCodePage> {
  final logic = Get.put(LoginCodeLogic());
  final state = Get.find<LoginCodeLogic>().state;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    logic.startCountdown();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: const Text(""),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          SizedBox(
            height: 48.w,
          ),
          Text(
            S.current.enter_code,
            style: TextStyles.titleMedium18.copyWith(fontSize: 24.sp),
          ),
          SizedBox(
            height: 20.w,
          ),
          Text(
            S.current.send_code_to('${state.region} ${state.photo}'),
            style: TextStyles.display14.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 40.w,
          ),
          PinCodeTextField(
            controller: state.codeController,
            appContext: context,
            length: 4,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly, // 限制只能输入数字
            ],
            onChanged: (value) => print("onchange $value"),
            onCompleted: (value) => logic.login(),
            //变化回调
            enableActiveFill: true,
            // Enable fill color
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            textStyle: TextStyles.titleSemiBold16.copyWith(fontSize: 34.sp),
            keyboardType: TextInputType.number,
            animationType: AnimationType.fade,
            autoFocus: true,
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              borderRadius: BorderRadius.circular(5),
              // fieldOuterPadding: EdgeInsets.symmetric(horizontal: 30.w),
              fieldHeight: 60.w,
              fieldWidth: 60.w,
              borderWidth: 1,
              activeColor: Colors.transparent,
              activeFillColor: Colours.color191921,
              selectedColor: Colours.app_main,
              selectedFillColor: Colours.color191921,
              inactiveColor: Colors.transparent,
              inactiveFillColor: Colours.color191921,
            ),
          ),
          SizedBox(
            height: 45.w,
          ),
          WxButton(
            height: 54,
            text: S.current.login,
            borderRadius: BorderRadius.circular(27),
            linearGradient: const LinearGradient(
              colors: [Colours.color7732ED, Colours.colorA555EF],
              begin: Alignment.bottomLeft,
              end: Alignment.bottomRight,
            ),
            onPressed: () => logic.login(),
          ),
          SizedBox(
            height: 20.w,
          ),
          GestureDetector(
              onTap: logic.requestCode,
              child: Obx(() => Center(
                      child: Text(
                    state.countdownTime.value == 0
                        ? S.current.re_acquisition
                        : "${S.current.re_acquisition}（${state.countdownTime.value}s）",
                    style:
                        TextStyles.display14.copyWith(color: Colours.app_main),
                  )))),
        ]),
      ),
    );
  }
}
