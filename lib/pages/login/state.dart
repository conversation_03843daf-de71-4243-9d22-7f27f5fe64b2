import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class LoginState {
  var areaCode = "+86".obs;
  var isAgree = false.obs;
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();

  // 倒计时相关状态
  var countdown = 0.obs;
  var isCountdownActive = false.obs;
  Timer? countdownTimer;
}
