import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'logic.dart';

class LoadingPage extends StatefulWidget {
  const LoadingPage({Key? key}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return _LoadingPage();
  }
}

class _LoadingPage extends State<LoadingPage> {
  final logic = Get.put(LoadingLogic());
  // final state = Get.find<LoadingLogic>().state;

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}
