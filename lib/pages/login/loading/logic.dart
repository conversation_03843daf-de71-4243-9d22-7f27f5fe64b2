import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../../routes/app.dart';
import '../../../routes/route.dart';
import '../../../utils/event_bus.dart';
import 'state.dart';

class LoadingLogic extends GetxController {
  final LoadingState state = LoadingState();

  @override
  void onInit() {
    super.onInit();
    state.subscription = BusUtils.instance.on((p0) async {
      if (p0.key == EventBusKey.toHome) {
        FlutterNativeSplash.remove();
        // if (UserManager.instance.isLogin) {
        //   AppPage.resetRootPageName(Routes.tab);
        // } else {
        //   AppPage.resetRootPageName(Routes.login);
        // }
        AppPage.resetRootPageName(Routes.tab);
      }
      // else if (p0.key == EventBusKey.networkErr) {
      //   startConnectivity();
      // }
    });
  }

  /// 网络监听
  void startConnectivity() {
    state.streamSubscription ??=
        Connectivity().onConnectivityChanged.listen((event) {
      if (event != ConnectivityResult.none) {
        BusUtils.instance.fire(
          EventAction(key: EventBusKey.networkOk),
        );
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    state.streamSubscription?.cancel();
    state.subscription?.cancel();
  }
}
