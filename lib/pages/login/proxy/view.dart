import 'package:flutter/material.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../main.dart';
import '../../../utils/utils.dart';

class ProxyPage extends StatelessWidget {
  ProxyPage({super.key});
  final textController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    WxStorage.instance.getString(proxyKey).then((value) {
      textController.text = value ?? '';
    });
    return Scaffold(
      appBar: MyAppBar(
        title: const Text('设置代理'),
      ),
      body: Column(
        children: [
          SizedBox(
            height: 20.w,
          ),
          _textField(context),
          SizedBox(
            height: 10.w,
          ),
          _save(context),
        ],
      ),
    );
  }

  Widget _textField(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      height: 54,
      decoration: BoxDecoration(
          color: Colours.color191921, borderRadius: BorderRadius.circular(27)),
      child: TextField(
        controller: textController,
        style: TextStyles.display16,
        decoration: InputDecoration(
          hintText: 'eg:192.168.101.61:9090',
          hintStyle: TextStyles.display16.copyWith(color: Colours.color5C5C6E),
          contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
          //让文字垂直居中,
          border: InputBorder.none,
        ),
        keyboardType: TextInputType.url,
      ),
    );
  }

  Widget _save(BuildContext context) {
    return WxButton(
      height: 54,
      text: '保存设置',
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      borderRadius: BorderRadius.circular(27),
      linearGradient: GradientUtils.mainGradient,
      onPressed: () {
        onKeyDismiss();
        if (textController.text.isEmpty) {
          WxEnv.instance.httpProxy = null;
          WxStorage.instance.setString(proxyKey, '');
          AppPage.back();
          return;
        }
        if (!Utils.isValidIpPort(textController.text)) {
          WxLoading.showToast('格式不对');
          return;
        }

        WxEnv.instance.httpProxy = "PROXY ${textController.text}";
        WxStorage.instance.setString(proxyKey, textController.text);
        AppPage.back();
      },
    );
  }
}
