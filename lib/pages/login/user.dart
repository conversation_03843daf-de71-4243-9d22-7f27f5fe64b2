import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shoot_z/network/model/message_has_unread_model.dart';
import 'package:shoot_z/pages/login/models/my_summary_model.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../generated/l10n.dart';
import '../../network/api_url.dart';
import '../../routes/app.dart';
import '../../routes/route.dart';
import '../../utils/event_bus.dart';
import 'models/user_info_model.dart';
import 'models/user_model.dart';

class UserManager {
  UserManager._privateConstructor();
  static final UserManager _instance = UserManager._privateConstructor();
  static UserManager get instance {
    return _instance;
  }

  /// 响应式 - 是否登录
  var isLoginObs = false.obs;

  /// 普通 - 是否登录
  bool get isLogin => isLoginObs.value;

  /// 本地缓存用户信息key
  final String _key = "userModel";

  /// 本地缓存用户信息key
  final String _keyUserInfo = "userInfoModelKey";

  final String _keySummaryInfo = "SummaryInfo";
  final String _keyMessageHasUnreadModel = "messageHasUnreadModel";

  /// 本地缓存用户信息key
  var _traceId = "";

  UserModel? user;
  var userInfo = Rx<UserInfoModel?>(null);
  var summaryModel = Rx<MySummaryModel?>(null);
  var messageHasUnreadModel = Rx<MessageHasUnreadModel?>(null);

  var isFirstLogin = false;
  var showPrivacyDialog = false.obs;
  var channel = "".obs; // 渠道
  var source = "".obs; // 来源标识 applinks
  var campaign = "".obs; // 活动标识
  var medium = "".obs; // 媒介类型
  var term = "".obs; // 关键词
  var content = "".obs; // 内容标识
  var versionCode = "".obs; // 内容标识

  /// 初始化本地用户信息
  Future<void> setup() async {
    String? str = await WxStorage.instance.getString(_key);

    if (str != null) {
      user = UserModel.fromJson(json.decode(str));
      isLoginObs.value = true;
      WxStorage.instance.exchangeUser(user!.userId);
      String? userInfoStr = await WxStorage.instance.getString(_keyUserInfo);
      if (userInfoStr != null) {
        userInfo.value = UserInfoModel.fromJson(json.decode(userInfoStr));
        userInfo.refresh();
      }
      if (_traceId == "" && ((userInfo.value?.userId ?? "") != "")) {
        _traceId =
            "${userInfo.value?.userId}${userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
      }
      String? summaryInfoStr =
          await WxStorage.instance.getString(_keySummaryInfo);
      if (summaryInfoStr != null) {
        summaryModel.value =
            MySummaryModel.fromJson(json.decode(summaryInfoStr));
        summaryModel.refresh();
      }

      String? messageHasUnreadModelStr =
          await WxStorage.instance.getString(_keyMessageHasUnreadModel);
      if (messageHasUnreadModelStr != null) {
        messageHasUnreadModel.value = MessageHasUnreadModel.fromJson(
            json.decode(messageHasUnreadModelStr));
        messageHasUnreadModel.refresh();
      }
    }
  }

  /// 获取我的信息
  Future<bool> pullUserInfo() async {
    var res = await Api().get(ApiUrl.userInfo);
    log("userInfo=${res.data}");
    if (res.isSuccessful()) {
      setUserInfo(UserInfoModel.fromJson(res.data));
      return true;
    } else {
      // WxLoading.showToast(res.message);
      return false;
    }
  }

  Future postChannelSubmit(int channelType,
      {String channelParam = 'launch'}) async {
    if (versionCode.value == "") {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      versionCode.value = packageInfo.version;
    }
    var params = {
      "versionCode": versionCode.value, // 版本号
      "channelCode": channel.value, // 渠道码
      "channelParam":
          channelParam, // 渠道参数 如二维码上的场地参数 启动 launch   半场单人shot  半场多人 shots
      "userId": user?.userId, // 必填
      "userFor": "", // 备用字段
      "channelType": channelType, //'0 通用， 1 注册，2 登陆，3 跳转'
      "source": source.value, // h5 小程序之类的
      "campaign": campaign.value, // 活动id
      "medium": medium.value, // 媒介类型
      "term": term.value, // 关键词
      "content": content.value // 内容标识
    };
    log("postChannelSubmit1=$params");
    log("postChannelSubmit1=${WxEnv.instance.apiUrl}");
    var res =
        await Api().post(ApiUrl.channelSubmit, showError: false, data: params);
    log("postChannelSubmit=${res.data}");
  }

  Future<bool> getMySummary() async {
    var res = await Api().get(ApiUrl.mySummary, showError: false);
    log("mySummary333=${res.data}");
    if (res.isSuccessful()) {
      summaryModel.value = MySummaryModel.fromJson(res.data);
      summaryModel.refresh();
      WxStorage.instance.setString(
          _keySummaryInfo, json.encode(summaryModel.value!.toJson()));
      return true;
    } else {
      // WxLoading.showToast(res.message);
      return false;
    }
  }

  Future<bool> getMessageHasUnread() async {
    var res = await Api().get(ApiUrl.getMessagehasUnread, showError: false);
    log("messageHasUnreadModel=${res.data}");
    if (res.isSuccessful()) {
      messageHasUnreadModel.value = MessageHasUnreadModel.fromJson(res.data);
      messageHasUnreadModel.refresh();
      WxStorage.instance.setString(_keyMessageHasUnreadModel,
          json.encode(messageHasUnreadModel.value!.toJson()));
      return true;
    } else {
      // WxLoading.showToast(res.message);
      return false;
    }
  }

  setUserInfo([UserInfoModel? model]) {
    userInfo.value = model ?? userInfo.value;
    userInfo.refresh();
    if (_traceId == "" && ((userInfo.value?.userId ?? "") != "")) {
      _traceId =
          "${userInfo.value?.userId}${userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
    }
    WxStorage.instance
        .setString(_keyUserInfo, json.encode(userInfo.value!.toJson()));
  }

  Future<bool> refreshToken() async {
    var res = await Api.refreshToken().post(ApiUrl.refreshToken,
        data: {"refreshToken": user?.refreshToken},
        headers: {"Authorization": null},
        showError: false);
    if (res.isSuccessful()) {
      UserModel userModel = UserManager.instance.user!;
      userModel.refreshToken = res.data["refreshToken"];
      userModel.token = res.data["token"];
      WxEnv.instance.setToken(userModel.token, userModel.refreshToken);
      UserManager.instance.login(userModel);
      return true;
    } else if (res.code == 401) {
      // WxLoading.showToast(res.message);
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      //   Get.dialog(CustomAlertDialog(title: S.current.you_are_offline,content: S.current.offline_hint,hideCancel: true,));
      // });
      Future.delayed(const Duration(milliseconds: 500)).then((_) {
        Get.dialog(CustomAlertDialog(
          title: S.current.you_are_offline,
          content: S.current.offline_hint,
          hideCancel: true,
        ));
      });
      return false;
    }
    return false;
  }

  void login(UserModel user, {bool isSave = true}) {
    this.user = user;
    if (isSave) {
      BusUtils.instance.fire(EventAction(key: EventBusKey.loginSuccessful));
      isLoginObs.value = true;
      WxStorage.instance.setString(_key, json.encode(user.toJson()));
      WxStorage.instance.exchangeUser(user.userId);
    }
  }

  void logout({bool isKickOut = true}) {
    WxStorage.instance.remove(_key);
    WxStorage.instance.remove(_keyUserInfo);
    WxEnv.instance.clearToken();
    WxStorage.instance.exchangeUser('');
    isLoginObs.value = false;
    user = null;
    userInfo.value = null;
    userInfo.refresh();
    if (isKickOut) {
      Get.delete<TabVenueLogic>();
      AppPage.resetRootPageName(Routes.login);
    }
  }

  void returnTabVenue() {
    AppPage.resetRootPageName(Routes.tab);
  }

  getPageName(String pageName) {
    var page = 0;
    switch (pageName) {
      case "/main":
        page = 0;
        break;
      case "/HomePage": //mainTab首页
        page = 1;
        break;
      case "CenuePage": //mainTab场馆
        page = 2;
        break;
      case "CreatPage": //mainTab创作
        page = 4;
        break;
      case "PointsMall": //mainTab积分商城
        page = 5;
        break;
      case "Mine": ////mainTab我的
        page = 6;
        break;
      case "/HighlightsPage": //我的集锦
        page = 7;
        break;
      case "/RankingsPage": //球秀天梯
        page = 8;
        break;
      case "/messageTypePage": //消息分类列表页面
        page = 9;
        break;

      case "/messageListPage": //消息列表页面
        page = 10;
        break;
      case "/GameDetailsView": //比赛概况
        page = 11;
        break;
      case "/ScheduleHomePage": ////赛事赛程中的赛程列表
        page = 12;
        break;
      case "/competitionCalendarPage": //赛事赛程中的赛事列表
        page = 13;
        break;
      case "/matchListPage": //老的比赛日历页面
        page = 14;
        break;
      case "/BattleDetailTeamPage": //约战详情
        page = 15;
        break;
      case "/competitionDetailPage": //赛事赛程主页
        page = 16;
        break;
      case "/unlockDataPage": //解锁报告
        page = 17;
        break;

      case "/TeamReportPage": //单队报告
        page = 18;
        break;
      case "/moreHighlightsPage": //更多集锦
        page = 19;
        break;
      case "/arenaDetailsPage": //球馆详情
        page = 20;
        break;
      case "/compositePlayerVideoPage": //球员报告  去剪辑 合成页面
        page = 21;
        break;
      case "/minePage": //我的
        page = 22;
        break;

      case "/compositeVideoPage": //场馆->子页面合成页面
        page = 23;
        break;
      case "/ShootGoalPage": //自由半场页面
        page = 24;
        break;
      case "/VipPage": //vip页面
        page = 25;
        break;
      case "/selfieShotPage": //自由半场页面
        page = 26;
        break;
      case "/teamInfoPage": //球队主页页面
        page = 27;
        break;
      case "/TeamPhotosPage": //合照设置页面
        page = 28;
        break;
      case "/playersPage": //球队成员页面
        page = 29;
        break;
      case "/teamListPage": //我的球队列表页面
        page = 30;
        break;
      case "/ordersPage": //我的订单页面
        page = 31;
        break;
      case "/couponsPage": //我的优惠券页面
        page = 32;
        break;
      case "/webviewh5": //问题反馈h5页面
        page = 33;
        break;
      case "/inviteCodePage": //将球秀推荐给朋友页面
        page = 34;
        break;
      case "/KfPage": //加入我们页面
        page = 35;
        break;
      case "/SettingsPage": //设置页面
        page = 36;
        break;
      case "/careerHighlightsHomePage": //生涯与集锦页面
        page = 37;
        break;
      case "/pointsPage": //签到页面
        page = 38;
        break;
      case "/pointsMallPage": //积分商城
        page = 39;
        break;
      case "/place": //场地页面（积分签到页面->每日任务 合成集锦按钮跳转）
        page = 40;
        break;
      case "/pointsGoodsTypePage": //积分商城 更多分类页面
        page = 41;
        break;
      case "/CDKPage": //CDKey兑换页面
        page = 42;
        break;
    }
    return page;
  }

  //子界面名字
  getSubPageName(String pageName) {
    var subPage = 0;
    switch (pageName) {
      case "Getmyvideo": //场馆主页 获取我的视频
        subPage = 10001;
        break;
      case "Recentcompetitions": //场馆主页 近期比赛
        subPage = 10002;
        break;
      case "Recentschedule": //场馆主页 近期赛程
        subPage = 10003;
        break;
      case "UnlockCount0": //解锁数据 整场解锁
        subPage = 10004;
        break;
      case "UnlockCount1": //解锁数据 整队解锁
        subPage = 10005;
        break;
      case "UnlockCount2": //解锁数据 单人解锁
        subPage = 10006;
        break;
      case "tabhome1": //首页tab1 精选
        subPage = 10007;
        break;
      case "tabhome2": //首页tab1 赛事
        subPage = 10008;
        break;
      case "tabhome3": //首页tab1 约战
        subPage = 10009;
        break;
      case "tabhome4": //首页tab1 球队
        subPage = 10010;
        break;
      case "tabhome5": //首页tab1 教学
        subPage = 10011;
        break;
      case "tab1": //主页面tab1 首页
        subPage = 10012;
        break;
      case "tab2": //主页面tab2 场地
        subPage = 10013;
        break;
      case "tab3": //主页面tab3 创作
        subPage = 10014;
        break;
      case "tab4": //主页面tab4 积分商城
        subPage = 10015;
        break;
      case "tab5": //主页面tab5 我的
        subPage = 10016;
        break;
      case "AppointmentReport": //比赛日历->预约报告按钮
        subPage = 10017;
        break;
      case "ReportGetitnow": // //比赛日历->立即获取按钮
        subPage = 10018;
        break;
      case "Goregister": //赛程详情->去报名按钮
        subPage = 10019;
        break;
      case "SharethecompetitionSchedule": //赛程详情->分享按钮
        subPage = 10020;
        break;
      case "vipPageSvip": //vip页面->tab子页面 sivp
        subPage = 10021;
        break;
      case "vipPageVip": //vip页面->tab子页面 ivp
        subPage = 10022;
        break;
      case "tabSelfieShot1": //自由半场->tab子页面 自由投篮
        subPage = 10023;
        break;
      case "tabSelfieShot2": //自由半场->tab子页面 单人训练
        subPage = 10024;
        break;
      case "SelfieShot1StartShooting": //自由半场->自由投篮 开始录制按钮
        subPage = 10025;
        break;
      case "SelfieShot2StartShooting": //自由半场->单人训练 开始录制按钮
        subPage = 10026;
        break;
      case "TeamInfoTab1": //球队主页->tab子页面 首页
        subPage = 10027;
        break;
      case "TeamInfoTab2": //球队主页->tab子页面 数据
        subPage = 10028;
        break;
      case "TeamInfoTab3": //球队主页->tab子页面 赛事
        subPage = 10029;
        break;
      case "TeamInfoTab4": //球队主页->tab子页面 赛程
        subPage = 10030;
        break;
      case "TeamInfoTab5": //球队主页->tab子页面 集锦
        subPage = 10031;
        break;
      case "pointsTodayTask1": //积分签到->合成集锦按钮
        subPage = 10032;
        break;
      case "pointsTodayTask2": //积分签到->分享按钮
        subPage = 10033;
        break;
      case "pointsTodayTask3": //积分签到->球馆打卡按钮
        subPage = 10034;
        break;
      case "pointsTodayTask4": //积分签到->签到按钮
        subPage = 10035;
        break;
      case "pointsTodayTask5": //积分签到->参与赛事按钮
        subPage = 10036;
        break;
      case "pointsUpTask1": //积分签到->成长任务 开通续约会有有按钮
        subPage = 10037;
        break;
      case "pointsUpTask2": //积分签到->成长任务 解锁比赛报告按钮
        subPage = 10038;
        break;
      case "pointsUpTask3": //积分签到->成长任务 邀请新用户按钮
        subPage = 10039;
        break;
      case "pointsNewUserTask1": //积分签到->成长任务 完善基础信息按钮
        subPage = 10040;
        break;
      case "pointsNewUserTask2": //积分签到->新手任务 下载球秀APP并登录按钮
        subPage = 10041;
        break;
    }
    return subPage;
  }

  // 存储最后一次成功调用的时间戳
  int _lastCallTimestamp = 0;

  // 最小调用间隔（毫秒）
  static const int MIN_INTERVAL = 2000; // 3秒
  //埋点相关接口
  Future postApmTracking(int action,
      {String nowPage = '',
      String remark = '',
      String toPage = '',
      String nextPage = '',
      String content = '',
      String subPage = '',
      String arenaId = '0'}) async {
    if (_traceId == "") {
      if (_traceId == "" && ((userInfo.value?.userId ?? "") != "")) {
        _traceId =
            "${userInfo.value?.userId}${userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
      }
      return;
    }
    final currentTime = DateTime.now().millisecondsSinceEpoch;

    // 如果距离上次调用不足3秒，忽略本次调用
    if (currentTime - _lastCallTimestamp < MIN_INTERVAL) {
      log('postApmTracking1=调用过于频繁，已忽略');
      return;
    }
    // 更新最后调用时间戳
    _lastCallTimestamp = currentTime;
    var nowPage2 = await getPageName(nowPage); //现在页面
    var toPage2 = await getPageName(toPage); //下个页面
    var nextPage2 = await getPageName(nextPage); //上个页面
    var subPage2 = await getSubPageName(subPage); //上个页面

    log("postApmTracking2=nowPage$nowPage-nextPage$nextPage-toPage$toPage");
    if (versionCode.value == "") {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      versionCode.value = packageInfo.version;
    }
    var vername = Platform.isAndroid ? "android" : "ios";
    var params = {
      "traceId": _traceId, //string 档次会话跟踪ID，打开小程序时前端生成，后续每次请求都带上同一个traceId
      "action": action, //int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
      "arenaId": arenaId, //string 场馆ID 默认 0
      "content":
          content, //string 内容 业务上的操作内容，如：输入了什么内容，分享了哪个内容，下载了哪个内容, 二维码参数， 分享用户id 等, 前端自定义
      "elem": remark, //string 页面元素（可空） 按钮名称，输入框名称，滑动元素，banner名称，广告名称 等, 前端自定义
      "page": nowPage2, //int 页面id 如：10000 自己定义写在文档
      "referer":
          nextPage2, //int 来源页面 action 0 且路由跳转进入时，referer 为上一页面id；如使用是其他方式进入页面，为微信小程序场景值
      "subPage": subPage2, //int 子页面 如：10001
      "to": toPage2, //页面id 如：11000
      "timestamp": DateTime.now().millisecondsSinceEpoch, //时间戳
      "userId": user?.userId ?? "0", // 必填 //string 用户ID Example : 0
      "ver": "$vername-$versionCode", // 版本号, //string 小程序版本号
      "platformType": 1, // 埋点类型:0:小程序 1:app
      "deviceOs": Platform.isAndroid ? 1 : 2, //操作系统:1=Android，2=i0S
      "vip": !isLoginObs.value
          ? -1
          : (userInfo.value?.isVip ?? false)
              ? 1
              : 0, //int  是否是vip -1:未知 0:不是 1:是
    };

    log("postApmTracking=${jsonEncode(params)}");
    // return;
    var res =
        await Api().post(ApiUrl.getApmTracking, showError: false, data: params);
    log("postApmTracking =${res.data}");
  }
}
