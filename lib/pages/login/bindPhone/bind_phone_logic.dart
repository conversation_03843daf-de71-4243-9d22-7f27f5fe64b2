import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/encrypt.dart';
import 'package:shoot_z/pages/login/models/user_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';

class BindPhoneLogic extends GetxController {
  var areaCode = "+86".obs;
  var isAgree = false.obs;
  var loginParams = {};
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();

  // 倒计时相关状态
  var countdown = 0.obs;
  var isCountdownActive = false.obs;
  Timer? countdownTimer;
  @override
  void onInit() {
    super.onInit();
    loginParams = Get.arguments;
  }

//  什么时候调用 onClose：
//  •	控制器的依赖被释放时触发，例如：
//  •	页面销毁且没有其他地方依赖该控制器。
//  •	显式调用 Get.delete 删除控制器。
  @override
  void onClose() {
    // 清理定时器
    countdownTimer?.cancel();
    super.onClose();
  }

  /// 开始倒计时
  void startCountdown() {
    countdown.value = 60;
    isCountdownActive.value = true;

    countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        timer.cancel();
        isCountdownActive.value = false;
      }
    });
  }

  void wxLogin() async {
    WxLoading.show();
    loginParams['phone'] = phoneController.text;
    loginParams['region'] = areaCode.value;
    loginParams['phoneCode'] = codeController.text;
    var res = await Api().post(ApiUrl.wxLogin, data: loginParams);
    if (!res.isSuccessful()) {
      WxLoading.showToast(res.message);
      return;
    }
    log("wxLogin!!!!${res.data}$loginParams");
    loginSuccess(res);
  }

  void getCode(BuildContext context) async {
    if (phoneController.text.isEmpty ||
        !Utils.isValidPhoneNumber(phoneController.text)) {
      WxLoading.showToast(S.current.mobile_number_hint);
      return;
    }
    onKeyDismiss();

    WxLoading.show();
    final phone = PhoneEncryption.encryptPhone(phoneController.text);
    var data = {'phone': phone, 'region': areaCode.value};
    var res = await Api().post(ApiUrl.getCode, data: data);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // 启动一分钟倒计时
      startCountdown();

      // AppPage.to(Routes.loginCode,
      //     arguments: {...data, 'original': state.phoneController.text});
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void loginSuccess(ApiResp res) async {
    UserModel userModel = UserModel.fromJson(res.data);
    WxEnv.instance.setToken(userModel.token, userModel.refreshToken);
    await UserManager.instance.pullUserInfo();
    WxLoading.dismiss();
    UserManager.instance.login(userModel);
    if (userModel.isFirstRegister ?? false) {
      AppPage.to(Routes.inputInviteCodePage);
    } else {
      final tabVenue = Get.isRegistered<TabVenueLogic>();
      if (tabVenue) {
        // AppPage.back(page:Routes.tab);
        AppPage.back(result: true);
      } else {
        AppPage.resetRootPageName(Routes.tab);
      }
    }
  }
}
