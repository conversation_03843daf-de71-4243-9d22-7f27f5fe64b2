import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encrypt;

class PhoneEncryption {
  // 这个key必须跟后端一致
  static const String _encryptKey = "aX9p9Km5L6n2V8q8R3w3Y6h6F8cB4tZa";

  /// 加密手机号
  // static String? encryptPhone(String phone) {
  //   if (phone.isEmpty) {
  //     return null;
  //   }
  //
  //   try {
  //     final key = encrypt.Key(utf8.encode(_encryptKey));
  //     final iv = encrypt.IV.fromSecureRandom(16);
  //     final encrypter = encrypt.Encrypter(
  //         encrypt.AES(key, mode: encrypt.AESMode.cfb64)
  //     );
  //     final encrypted = encrypter.encrypt(phone, iv: iv);
  //
  //     // 合并IV和加密内容
  //     final mergedBytes = Uint8List(iv.bytes.length + encrypted.bytes.length);
  //     mergedBytes.setAll(0, iv.bytes);
  //     mergedBytes.setAll(iv.bytes.length, encrypted.bytes);
  //
  //     return base64.encode(mergedBytes);
  //
  //   } catch (e) {
  //     throw Exception("创建加密块失败了，草: $e");
  //   }
  // }

  /// 加密手机号
  static String? encryptPhone(String phone) {
    return phone;
    // if (phone.isEmpty) {
    //   return null;
    // }

    // try {
    //   final key = encrypt.Key(utf8.encode(_encryptKey));
    //   final iv = encrypt.IV.fromSecureRandom(16);
    //   final encrypter = encrypt.Encrypter(
    //       encrypt.AES(key, mode: encrypt.AESMode.cbc, padding: 'PKCS7'));

    //   // 加密手机号
    //   final encrypted = encrypter.encrypt(phone, iv: iv);

    //   // 合并IV和加密内容
    //   final mergedBytes = Uint8List(iv.bytes.length + encrypted.bytes.length);
    //   mergedBytes.setAll(0, iv.bytes);
    //   mergedBytes.setAll(iv.bytes.length, encrypted.bytes);

    //   return base64.encode(mergedBytes);
    // } catch (e) {
    //   throw Exception("创建加密块失败了，草: $e");
    // }
  }

  /// 解密手机号
  static String? decryptPhone(String encryptedPhone) {
    try {
      final encryptedData = base64.decode(encryptedPhone);

      // 提取IV和加密数据
      final iv = encrypt.IV(encryptedData.sublist(0, 16));
      final encryptedBytes = encryptedData.sublist(16);

      final key = encrypt.Key(utf8.encode(_encryptKey));
      final encrypter = encrypt.Encrypter(
          encrypt.AES(key, mode: encrypt.AESMode.cbc, padding: 'PKCS7'));

      // 解密数据
      final decrypted = encrypter.decrypt(
        encrypt.Encrypted(encryptedBytes),
        iv: iv,
      );

      return decrypted;
    } catch (e) {
      throw Exception("解密失败: $e");
    }
  }
}
