import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';

import '../../../routes/app.dart';
import '../../../routes/route.dart';
import '../../tab2Venue/tab_venue_logic.dart';

class InputInviteCodeLogic extends GetxController {
  TextEditingController textController = TextEditingController();
  void inviteCode() async {
    if (textController.text.isEmpty) {
      WxLoading.showToast("请填写邀请码");
      return;
    }
    WxLoading.show();
    final res = await Api()
        .post(ApiUrl.inviteCode, data: {'inviteCode': textController.text});
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      UserManager.instance.isFirstLogin = true;
      goTab();
    }
  }

  void goTab() {
    final TabVenue = Get.isRegistered<TabVenueLogic>();
    if (TabVenue) {
      // AppPage.back(page:Routes.tab);
      AppPage.back();
      AppPage.back();
      AppPage.back(result: true);
    } else {
      AppPage.resetRootPageName(Routes.tab);
    }
  }

  void paste() {
    Clipboard.getData(Clipboard.kTextPlain).then((value) {
      textController.text = value?.text ?? '';
    });
  }
}
