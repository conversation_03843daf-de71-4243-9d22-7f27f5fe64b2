import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/login/inviteCode/logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:ui_packages/ui_packages.dart';

class InputInviteCodePage extends StatefulWidget {
  const InputInviteCodePage({super.key});

  @override
  State<InputInviteCodePage> createState() => _InputInviteCodePageState();
}

class _InputInviteCodePageState extends State<InputInviteCodePage> {
  final logic = Get.put(InputInviteCodeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(children: [
          Positioned(
            top: 84.w,
            left: 0,
            right: 0,
            bottom: 0,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  WxAssets.images.icInviteCodeTitle
                      .image(width: 234.w, height: 101.w, fit: BoxFit.fill),
                  SizedBox(
                    height: 50.w,
                  ),
                  WxAssets.images.icInviteCodeCard
                      .image(width: 314.w, height: 179.w, fit: BoxFit.fill),
                  SizedBox(
                    height: 50.w,
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 30.w),
                    padding: EdgeInsets.only(left: 15.w, right: 10.w),
                    height: 54.w,
                    decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: logic.textController,
                            style: TextStyles.regular.copyWith(fontSize: 16.sp),
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.only(
                                  top: 0, bottom: 0), //让文字垂直居中
                              border: InputBorder.none,
                              hintText: "请输入推荐码",
                              hintStyle: TextStyles.regular.copyWith(
                                  color: Colours.color5C5C6E, fontSize: 16.sp),
                            ),
                            keyboardType: TextInputType.text,
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        WxButton(
                          text: '粘贴',
                          textStyle: TextStyles.regular
                              .copyWith(color: Colours.color9A46FF),
                          width: 48.w,
                          height: 34.w,
                          backgroundColor: Colors.transparent,
                          onPressed: logic.paste,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 30.w,
                  ),
                  WxButton(
                    margin: EdgeInsets.symmetric(horizontal: 30.w),
                    height: 55.w,
                    text: '确认',
                    textStyle: TextStyles.semiBold,
                    linearGradient: GradientUtils.mainGradient,
                    borderRadius: BorderRadius.circular(27.5.w),
                    onPressed: logic.inviteCode,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
              top: 0,
              left: 0,
              child: GestureDetector(
                onTap: () => AppPage.back(),
                child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 18),
                    child:
                        WxAssets.images.icClose.image(width: 20, height: 20)),
              )),
          Positioned(
            bottom: -MediaQuery.of(context).viewInsets.bottom,
            left: 0,
            right: 0,
            child: Center(
                child: Padding(
                    padding: EdgeInsets.only(bottom: 30.w),
                    child: GestureDetector(
                        onTap: logic.goTab,
                        child: WxAssets.images.icInviteCodeSkip
                            .image(width: 186.w, height: 17.w)))),
          ),
        ]),
      ),
    );
  }
}
