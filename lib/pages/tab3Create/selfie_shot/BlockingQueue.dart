import 'dart:async';
import 'dart:collection';

class BlockingQueue<E> {
  final Queue<E> _queue = Queue<E>();
  final _waitTime;
  BlockingQueue(this._waitTime);

  void enqueue(E item) {
    _queue.add(item);
  }

  Future<E> dequeue() async {
    while (_queue.isEmpty) {
      await Future<void>.delayed(Duration(seconds: _waitTime));
    }
    return _queue.removeFirst();
  }

  Future<int> getLength() async {
    return _queue.length;
  }

  void clearAll() async {
    _queue.clear();
  }
}
