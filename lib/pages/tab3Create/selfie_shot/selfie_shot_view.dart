// ignore_for_file: avoid_print, unused_element

import 'dart:async';
import 'dart:convert';
import 'dart:developer' as cc;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/list_items/item2/selfie_shot_item_view2.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/list_items/item1/selfie_shot_item_view1.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮
class SelfieShotPage extends StatefulWidget {
  const SelfieShotPage({super.key});

  @override
  State<SelfieShotPage> createState() => _SelfieShotPage();
}

class _SelfieShotPage extends State<SelfieShotPage> {
  final EventChannel eventChannel =
      const EventChannel('com.shootz.video_recorder_envent');
  StreamSubscription<dynamic>? eventStreamSubscription;
  final logic = Get.put(SelfieShotLogic());
  static const platform =
      MethodChannel('com.example.my_flutter_app/native_method');

  Future<void> _getShootEventsByTrainingId(String trainingId) async {
    try {
      var result = await platform
          .invokeMethod('getRecordList', {'trainingId': trainingId});
      cc.log("getShootEventsByTrainingId!!!!!!!$result");
    } on PlatformException catch (e) {
      print("native_method：Failed to invoke native method: '${e.message}'.");
    }
  }

  Future<void> _updateEventById() async {
    try {
      await platform.invokeMethod(
          'updateRecordStatus', {'id': "1", "playImgUpload": true});
    } on PlatformException catch (e) {
      print("native_method：Failed to invoke native method: '${e.message}'.");
    }
  }

  @override
  void initState() {
    super.initState();
    eventStreamSubscription =
        eventChannel.receiveBroadcastStream().listen((event) {
      cc.log(
          "postHalfShootingEvents eventChannel:$event ${event is Map && event['type'] == 'channel_closed'}");
      // 处理接收到的消息
      cc.log('Received event: $event');
      if (event is Map && event['type'] == 'channel_closed') {
        cc.log('Received channel_closed: ${event['trainingId']}');
        logic.getEndShooting(event['trainingId'], 1);
        //  logic.blockingQueue.clearAll();
      } else if (event is Map && event['type'] == 'channel_closed1') {
        cc.log('Received channel_closed: ${event['trainingId']}');
        logic.getEndShooting(event['trainingId'], 2);
        //  logic.blockingQueue.clearAll();
      } else {
        Map<String, dynamic> jsonMap = jsonDecode(event);
        ShotRecordModel shotRecordModel = ShotRecordModel.fromJson(jsonMap);
        cc.log('Received postHalfShootingEvents: ${shotRecordModel.filePath}');
        //tabbarIndex=0 多人  1单人 单人不传身形
        //  Received event: {"player_image_uploaded":false,"player_confidence":0,"file_path":
        //"file:\/\/\/private\/var\/mobile\/Containers\/Data\/Application\/F9F6A010-24E4-44FC-A2A8-212C0E1C9F
        //BB\/tmp\/cache_20250515_144850.mp4","training_id":"5","start_time":"2025-05-15 14:44:01",
        //"is_goal":true,"goal_time":1747291729.2113008,"file_path_uploaded":false,
        //"created_at":768984531.00349796}
        // Received shotRecordModel: file:///private/var/mobile/Containers/Data/Application/F9F6A010-24E4-44FC-A2A8-212C0E1C9FBB/tmp/cache_20250515_144850.mp4
        // shotRecordModel.eventId = "${DateTime.now().microsecond}";
        logic.postHalfShootingEvents(shotRecordModel);
      }
    }, onError: (error) {
      // 处理错误情况
      cc.log('Error: $error');
    }, onDone: () {
      // 完成监听后的操作，例如清理资源等
      cc.log('Stream closed');
    });
    // 延迟发送握手信号
    Future.delayed(const Duration(milliseconds: 500), _sendHandshake);
  }

// Flutter 端代码
  Future<void> _sendHandshake() async {
    try {
      await platform.invokeMethod('confirmEventChannelReady');
      print('halfShootingRecording 事件通道握手确认已发送');
    } catch (e) {
      print('halfShootingRecording 发送握手信号失败: $e');
    }
  }

  @override
  void dispose() {
    eventStreamSubscription?.cancel(); // 清理资源，避免内存泄漏
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: const Text("即刻创作"),
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.SelfieShotLocalPage);
            },
            child: Container(
                padding: EdgeInsets.only(right: 15.w),
                child:
                    WxAssets.images.localFile.image(width: 32.w, height: 32.w)),
          )
        ],
      ),
      body: _tabWidget2(context),
    );
  }

  Widget _tabWidget2(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.centerLeft,
            color: Colours.bg_color,
            child: Row(
              children: [
                Container(
                  width: 60.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                ),
                Expanded(
                  child: TabBar(
                      controller: logic.tabController,
                      unselectedLabelColor: Colours.color5C5C6E,
                      unselectedLabelStyle: TextStyle(
                          fontSize: 18.sp,
                          color: Colours.color5C5C6E,
                          fontWeight: FontWeight.w600),
                      labelColor: Colours.white,
                      labelStyle: TextStyle(
                          fontSize: 20.sp,
                          color: Colours.white,
                          fontWeight: FontWeight.w600),
                      isScrollable: true,
                      tabAlignment: TabAlignment.center,
                      // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                      indicatorPadding: EdgeInsets.zero,
                      dividerColor: Colors.transparent,
                      indicatorColor: Colors.transparent, // 设置指示器颜色为透明
                      indicator: const BoxDecoration(
                          color: Colors.transparent), // 使用空装饰完全移除指示器
                      dividerHeight: 0,
                      labelPadding: const EdgeInsets.symmetric(
                          horizontal: 4.0), // 调整标签间的间距
                      indicatorSize: TabBarIndicatorSize.label,
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      tabs: List.generate(logic.tabNameList.length, (index) {
                        return SizedBox(
                          width: 70.w,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 20.w,
                              ),
                              ShaderMask(
                                shaderCallback: (bounds) =>
                                    const LinearGradient(
                                  colors: [
                                    Colours.colorFFF9DC,
                                    Colours.colorE4C8FF,
                                    Colours.colorE5F3FF,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ).createShader(bounds),
                                child: Text(
                                  logic.tabNameList[index],
                                  style: TextStyles.regular.copyWith(
                                    fontWeight: logic.tabbarIndex.value == index
                                        ? FontWeight.w600
                                        : FontWeight.w400,
                                    fontSize: logic.tabbarIndex.value == index
                                        ? 16.sp
                                        : 14.sp,
                                    color: logic.tabbarIndex.value == index
                                        ? Colours.white
                                        : Colours.color5C5C6E,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 5.w,
                              ),
                              WxAssets.images.imgCheckIn2.image(
                                  width: 19.w,
                                  height: 9.w,
                                  color: (logic.tabbarIndex.value == index)
                                      ? null
                                      : Colors.transparent),
                            ],
                          ),
                        );
                      })),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    AppPage.to(Routes.SelfieShotTipsPage);
                  },
                  child: Container(
                      width: 60.w,
                      alignment: Alignment.centerRight,
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: WxAssets.images.tips1
                          .image(width: 20.w, height: 20.w)),
                )
              ],
            ),
          ),
          SizedBox(
            height: 10.w,
          ),
          Expanded(
            child: TabBarView(controller: logic.tabController, children: [
              SelfieShotItemPage1(
                key: const Key("1"),
              ),
              SelfieShotItemPage2(
                key: const Key("2"),
              ),
            ]),
          ),
        ],
      );
    });
  }
}
