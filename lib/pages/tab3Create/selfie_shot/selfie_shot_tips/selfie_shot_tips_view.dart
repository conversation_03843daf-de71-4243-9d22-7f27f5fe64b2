import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_tips/selfie_shot_tips_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 温馨提示
class SelfieShotTipsPage extends StatelessWidget {
  SelfieShotTipsPage({super.key});
  final logic = Get.put(SelfieShotTipsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MyAppBar(
          title: Text(S.current.dialog_title),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(15.w),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.circular(8.r)),
                  padding: EdgeInsets.all(15.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Colours.colorFFECC1,
                            Colours.colorE7CEFF,
                            Colours.colorD1EAFF,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds),
                        child: Text(
                          S.current.selfile_tips1,
                          style:
                              TextStyle(color: Colours.white, fontSize: 14.sp),
                        ),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips2,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips3,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips4,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips5,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips6,
                        style: TextStyle(color: Colours.white, fontSize: 14.sp),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 5.w,
                ),
                buildRowTitleWidget(S.current.selfile_tips7,
                    height: 55, padding: EdgeInsets.zero),
                Container(
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.circular(8.r)),
                  padding: EdgeInsets.all(15.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.current.selfile_tips8,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips9,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips10,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips11,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 18.w,
                      ),
                      Text(
                        S.current.selfile_tips12,
                        style: TextStyle(
                            color: Colours.colorA8A8BC, fontSize: 14.sp),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 30.w,
                ),
              ],
            ),
          ),
        ));
  }
}
