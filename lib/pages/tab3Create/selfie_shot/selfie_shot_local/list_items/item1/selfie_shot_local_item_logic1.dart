import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/dao/TrainingGroup.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';

class SelfieShotLocalItemLogic1 extends GetxController {
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 0,
    "isFrist": true,
    "pageSize": 10,
  }.obs;
  var type = "1"; //1是单人  2多人  “”全部
  var dataList = <TrainingGroup>[].obs;
  @override
  void onInit() {
    super.onInit();
    getdataList(controller: refreshController2, isLoad: false);
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 0;
    }
    int page = (dataFag["page"] ?? 0) as int;
    int pageSize = (dataFag["pageSize"] ?? 10) as int;
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers =
        await selfieShotDao.findPagedTrainingRecords(
            UserManager.instance.userInfo.value?.userId ?? "",
            type,
            4,
            pageSize,
            page);
    // 按 training_id 分组
    final groupedRecords = <String, List<ShotRecordModel>>{};
    for (final record in filteredNumbers) {
      if (!groupedRecords.containsKey(record.trainingId)) {
        groupedRecords[record.trainingId ?? "0"] = [];
      }
      groupedRecords[record.trainingId]!.add(record);
    }
    // 转换为 TrainingGroup 对象
    var group = <TrainingGroup>[].obs;
    for (final entry in groupedRecords.entries) {
      final trainingId = entry.key;
      final records = entry.value;

      // 计算统计信息
      final latestDate =
          records.map((r) => r.createdAt).reduce((a, b) => a! > b! ? a : b);
      final recordCount = records.length;
      group.add(TrainingGroup(
        trainingId: trainingId,
        records: records,
        latestDate: DateTime.fromMillisecondsSinceEpoch(latestDate!.toInt()),
        recordCount: recordCount,
        siteId: '',
        siteName: '',
      ));
    }
    // 按最新日期排序
    group.sort((a, b) => b.latestDate.compareTo(a.latestDate));

    if (isLoad) {
      dataList.addAll(group);
      dataList.refresh();
      if (group.length < 10) {
        controller.loadNoData();
        //  controller.loadComplete();
      } else {
        controller.loadComplete();
      }
    } else {
      controller.resetNoData();
      dataList.assignAll(group);
      controller.refreshCompleted();
    }

    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
