// ignore_for_file: avoid_print, unused_element

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_local/list_items/item1/selfie_shot_local_item_view1.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_local/list_items/item2/selfie_shot_local_item_view2.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮  本地视频

class SelfieShotLocalPage extends StatelessWidget {
  SelfieShotLocalPage({super.key});
  final logic = Get.put(SelfieShotLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.selfile_shot_local_title),
      ),
      body: _tabWidget2(context),
    );
  }

  Widget _tabWidget2(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.center,
            color: Colours.bg_color,
            child: TabBar(
                controller: logic.tabController,
                unselectedLabelColor: Colours.color5C5C6E,
                unselectedLabelStyle: TextStyle(
                    fontSize: 18.sp,
                    color: Colours.color5C5C6E,
                    fontWeight: FontWeight.w600),
                labelColor: Colours.white,
                labelStyle: TextStyle(
                    fontSize: 20.sp,
                    color: Colours.white,
                    fontWeight: FontWeight.w600),
                isScrollable: true,
                tabAlignment: TabAlignment.center,
                // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                indicatorPadding: EdgeInsets.zero,
                dividerColor: Colors.transparent,
                indicatorColor: Colors.transparent, // 设置指示器颜色为透明
                indicator: const BoxDecoration(
                    color: Colors.transparent), // 使用空装饰完全移除指示器
                dividerHeight: 0,
                labelPadding:
                    const EdgeInsets.symmetric(horizontal: 4.0), // 调整标签间的间距
                indicatorSize: TabBarIndicatorSize.label,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                tabs: List.generate(logic.tabNameList.length, (index) {
                  return SizedBox(
                    width: 70.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 20.w,
                        ),
                        ShaderMask(
                          shaderCallback: (bounds) => const LinearGradient(
                            colors: [
                              Colours.colorFFF9DC,
                              Colours.colorE4C8FF,
                              Colours.colorE5F3FF,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ).createShader(bounds),
                          child: Text(
                            logic.tabNameList[index],
                            style: TextStyles.regular.copyWith(
                              fontWeight: logic.tabbarIndex.value == index
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                              fontSize: logic.tabbarIndex.value == index
                                  ? 16.sp
                                  : 14.sp,
                              color: logic.tabbarIndex.value == index
                                  ? Colours.white
                                  : Colours.color5C5C6E,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 5.w,
                        ),
                        WxAssets.images.imgCheckIn2.image(
                            width: 19.w,
                            height: 9.w,
                            color: (logic.tabbarIndex.value == index)
                                ? null
                                : Colors.transparent),
                      ],
                    ),
                  );
                })),
          ),
          SizedBox(
            height: 10.w,
          ),
          Expanded(
            child: TabBarView(controller: logic.tabController, children: [
              SelfieShotLocalItemPage1(
                key: const Key("1"),
              ),
              SelfieShotLocalItemPage2(
                key: const Key("2"),
              ),
            ]),
          ),
        ],
      );
    });
  }
}
