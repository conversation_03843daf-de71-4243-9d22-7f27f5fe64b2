// ignore_for_file: avoid_print, deprecated_member_use

import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_local/list_items/item1/selfie_shot_local_item_logic1.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_local/list_items/item2/selfie_shot_local_item_logic2.dart';
import 'package:flutter/material.dart';

class SelfieShotLocalLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var tabbarIndex = 0.obs;
  final logic1 = Get.put(SelfieShotLocalItemLogic1());
  final logic2 = Get.put(SelfieShotLocalItemLogic2());
  get tabNameList =>
      [S.current.selfile_shot_title2, S.current.selfile_shot_title1]; // 最大延迟1分钟

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(() {
      tabbarIndex.value = tabController?.index ?? 0;
      // if (tabController?.indexIsChanging ?? false) {
      //   switch (tabbarIndex.value) {
      //     case 1:
      //       //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
      //       UserManager.instance.postApmTracking(1,
      //           remark: "点击tab切换",
      //           nowPage: Routes.SelfieShotLocalPage,
      //           subPage: "tabSelfieShotLocal1",
      //           content: "切换tab自由投篮");
      //       break;
      //     case 0:
      //       //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
      //       UserManager.instance.postApmTracking(1,
      //           remark: "点击tab切换",
      //           nowPage: Routes.SelfieShotLocalPage,
      //           subPage: "tabSelfieShotLocal2",
      //           content: "切换tab单人训练");
      //       break;
      //   }
      //}
    });
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }
}
