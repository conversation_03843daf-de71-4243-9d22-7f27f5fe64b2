import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';

class SelfieShotLocalInfoLogic extends GetxController {
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);

  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <ShotRecordModel>[].obs;
  var latestDate = "".obs;
  var siteId = "".obs;
  var siteName = "".obs;
  var trainingId = "".obs;
  var type = "1".obs; //1单人  2多人
  var allCheck = false.obs;
  @override
  void onInit() {
    super.onInit();
    //自由半场 本地视频 单个比赛详情
    if (Get.arguments != null && Get.arguments.containsKey('latestDate')) {
      latestDate.value = Get.arguments['latestDate'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('siteId')) {
      siteId.value = Get.arguments['siteId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('siteName')) {
      siteName.value = Get.arguments['siteName'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('trainingId')) {
      trainingId.value = Get.arguments['trainingId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('type')) {
      type.value = Get.arguments['type'];
    }
    getdataList(controller: refreshController2, isLoad: false);
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        trainingId.value,
        UserManager.instance.userInfo.value?.userId ?? "",
        type.value);
    dataList.addAll(filteredNumbers);
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //全选和取消全选
  Future<void> checkAllVideo() async {
    allCheck.value = !allCheck.value;
    for (int i = 0; i < dataList.length; i++) {
      dataList[i].isCheck = (allCheck.value) ? "1" : "0";
    }
    dataList.refresh();
  }

  deleteVideo() async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    for (int i = 0; i < dataList.length; i++) {
      if (dataList[i].isCheck == "1") {
        selfieShotDao.deleteShot1(
            dataList[i].trainingId ?? "0",
            UserManager.instance.userInfo.value?.userId ?? "",
            dataList[i].eventId ?? "0");
        //删除本地文件 未实现
      }
    }
  }

  void uploadVideo() {}

  void loadVideo() {}
}
