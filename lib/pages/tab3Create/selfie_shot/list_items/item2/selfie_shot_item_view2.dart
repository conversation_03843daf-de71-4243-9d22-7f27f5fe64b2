import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/list_items/item2/selfie_shot_item_logic2.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场自由投篮 多人模式
class SelfieShotItemPage2 extends StatelessWidget {
  SelfieShotItemPage2({super.key});

  final logic = Get.put(SelfieShotItemLogic2());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : _infoWidget(context);
      }),
    );
  }

  Widget _infoWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    WxAssets.images.selfieShotTips
                        .image(width: 14.w, height: 14.w),
                    SizedBox(
                      width: 3.w,
                    ),
                    Expanded(
                      child: Text(
                        S.current.selfile_shot2,
                        style: TextStyles.regular.copyWith(
                            color: Colours.color5C5C6E, fontSize: 12.sp),
                      ),
                    )
                  ],
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    if ((logic.videoPath.value) != "") {
                      AppPage.to(Routes.videoPath, arguments: {
                        "videoPath": logic.videoPath.value,
                        "teamName": "投篮视频示例",
                        "isShowShareUpdate": "0",
                      });
                    } else {
                      WxLoading.showToast(S.current.No_data_available);
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 15.w, bottom: 15.w),
                    width: double.infinity,
                    height: 194.w,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8.r)),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        MyImage(
                          logic.imgPath.value,
                          width: double.infinity,
                          height: 194.w,
                          radius: 8.r,
                          errorImage: "error_image_width.png",
                          placeholderImage: "error_image_width.png",
                        ),
                        WxAssets.images.selfieShotPlay
                            .image(width: 51.w, height: 51.w)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Text(
            S.current.selfile_shot10,
            style: TextStyles.regular
                .copyWith(color: Colours.color922BFF, fontSize: 12.sp),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () async {
                  //logic.getNextPage2("104");
                  // logic.getHalfShootingRecordingEnd("7");
                  //半场投篮 开始录制
                  // if (Platform.isAndroid) {
                  //   await WxPermissionUtils.microphoneAndCameraStorage(
                  //       handle: () {
                  //     logic.getHalfShootingRecording();
                  //   });
                  // } else {
                  //   logic.getHalfShootingRecording();
                  // }

                  final database = await $FloorAppDatabase
                      .databaseBuilder('app_database.db')
                      .build();
                  final selfieShotDao = database.selfieShotDao;
                  // List<ShotRecordModel> filteredNumbers =
                  //     await selfieShotDao.findAllShot("673",
                  //         UserManager.instance.userInfo.value?.userId ?? "");
                  // List<ShotRecordModel> filteredNumbers =
                  //     await selfieShotDao.findPagedTrainingRecords(
                  //         UserManager.instance.userInfo.value?.userId ?? "",
                  //         null);

                  //   log("filteredNumbers2222:${jsonEncode(filteredNumbers)}");
                  return;
                  logic.getHalfShootingRecording();
                  //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                  UserManager.instance.postApmTracking(1,
                      remark: "开始录制按钮",
                      nowPage: Routes.selfieShotPage,
                      subPage: "SelfieShot2StartShooting",
                      content: "点击自由投篮，开始录制");
                },
                child: Container(
                  width: double.infinity,
                  height: 44.w,
                  alignment: Alignment.center,
                  margin:
                      EdgeInsets.symmetric(vertical: 15.w, horizontal: 15.w),
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20.r)),
                  child: Text(
                    S.current.selfile_shot3,
                    style: TextStyles.regular.copyWith(color: Colours.white),
                  ),
                ),
              ),
              buildRowTitleWidget(
                S.current.selfile_shot4,
                margin: EdgeInsets.only(bottom: 15.w),
                rightWidget: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    //投篮记录{
                    AppPage.to(Routes.selfieShotReportPage,
                        arguments: {"type": "2"}).then((onValue) {
                      logic.getVideoList();
                    });
                    //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                    UserManager.instance.postApmTracking(1,
                        remark: "投篮记录按钮",
                        nowPage: Routes.selfieShotPage,
                        toPage: Routes.selfieShotReportPage,
                        subPage: "SelfieShot1StartShooting",
                        content: "投篮记录按钮跳转");
                  },
                  child: Container(
                    height: 30.w,
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    decoration: BoxDecoration(
                        image: DecorationImage(
                      image: WxAssets.images.moreHiglightsBg.provider(),
                      fit: BoxFit.fill,
                    )),
                    child: Row(
                      children: [
                        Text(
                          S.current.view_more,
                          style:
                              TextStyle(color: Colours.white, fontSize: 12.sp),
                        ),
                        WxAssets.images.icArrowRight.image(
                            width: 12.w,
                            height: 12.w,
                            color: Colours.white,
                            fit: BoxFit.fill),
                      ],
                    ),
                  ),
                ),
              ),
              ((logic.halfShootingRecordsModel.value.result?.length ?? 0) <= 0)
                  ? SizedBox(
                      height: 180.w,
                      child: myNoDataView(
                        context,
                        msg: S.current.No_data_available,
                        textColor: Colours.white,
                        height: 0,
                        imagewidget: WxAssets.images.teamInfoNodata
                            .image(width: 140.w, height: 120.w),
                      ))
                  : GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        AppPage.to(Routes.selfieShotInfoPage, arguments: {
                          "halfShootingRecordsModel": logic
                              .halfShootingRecordsModel.value.result?.first,
                          "type": "2",
                          "trainingId":
                              "${logic.halfShootingRecordsModel.value.result?.first?.trainingId}",
                        }).then((onValue) {
                          logic.getHalfShootingRecords();
                          logic.getVideoList();
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: 15.w, right: 15.w),
                        padding: EdgeInsets.symmetric(
                            horizontal: 15.w, vertical: 15.w),
                        decoration: BoxDecoration(
                            color: Colours.color191921,
                            borderRadius: BorderRadius.circular(8.r)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _myColumnWidget(S.current.selfile_shot6,
                                "${logic.halfShootingRecordsModel.value.result?.first?.duration ?? ""}分"),
                            _myColumnWidget(S.current.selfile_shot7,
                                "${logic.halfShootingRecordsModel.value.result?.first?.shotCount ?? ""}"),
                            _myColumnWidget(S.current.selfile_shot8,
                                "${(logic.halfShootingRecordsModel.value.result?.first?.hitCount ?? "")}"),
                            _myColumnWidget(S.current.selfile_shot9,
                                "${logic.halfShootingRecordsModel.value.result?.first?.rate ?? ""}%"),
                          ],
                        ),
                      ),
                    )
            ],
          ),
          buildRowTitleWidget(
            S.current.my_highlights,
            margin: EdgeInsets.only(top: 15.w, bottom: 15.w),
            rightWidget: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                //集锦查看更多按钮
                AppPage.to(Routes.selfieShotVideosPage,
                    arguments: {"type": "2"}).then((onValue) {
                  logic.getVideoList();
                });
                //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                UserManager.instance.postApmTracking(1,
                    remark: "集锦查看更多按钮",
                    nowPage: Routes.selfieShotPage,
                    toPage: Routes.selfieShotVideosPage,
                    subPage: "SelfieShot1StartShooting",
                    content: "更多按钮按钮跳转");
              },
              child: Container(
                height: 30.w,
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: WxAssets.images.moreHiglightsBg.provider(),
                  fit: BoxFit.fill,
                )),
                child: Row(
                  children: [
                    Text(
                      S.current.view_more,
                      style: TextStyle(color: Colours.white, fontSize: 12.sp),
                    ),
                    WxAssets.images.icArrowRight.image(
                        width: 12.w,
                        height: 12.w,
                        color: Colours.white,
                        fit: BoxFit.fill),
                  ],
                ),
              ),
            ),
          ),
          logic.videosDataList.isEmpty
              ? Container(
                  margin: EdgeInsets.only(top: 15.w, bottom: 70.w),
                  child: myNoDataView(
                    context,
                    msg: "暂无视频",
                    textColor: Colours.color5C5C6E,
                    height: 10.w,
                    imagewidget: Container(
                      margin: EdgeInsets.only(left: 10.w),
                      child: WxAssets.images.noVideos
                          .image(width: 96.w, height: 60.w, fit: BoxFit.fill),
                    ),
                  ),
                )
              : Container(
                  // height: 55.w,
                  margin: EdgeInsets.only(left: 15.w, right: 15.w),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                  child: GridView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      physics:
                          const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 11,
                        mainAxisSpacing: 11,
                        childAspectRatio: 152 / 110,
                      ),
                      padding: EdgeInsets.only(bottom: 60.w),
                      itemCount: logic.videosDataList.length,
                      itemBuilder: (context, index) {
                        return Obx(() {
                          return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                              switch (logic.videosDataList[index].status) {
                                case 0:
                                case 1:
                                  WxLoading.showToast("视频合成中，请稍后再试");
                                  break;
                                case 2:
                                  AppPage.to(Routes.videoPath, arguments: {
                                    "videoPath":
                                        logic.videosDataList[index].videoPath,
                                    "teamName": "投篮视频",
                                    "isShowShareUpdate": "1",
                                    "videoId": logic.videosDataList[index].id,
                                  }).then((onValue) {
                                    if (onValue == true) {
                                      logic.getVideoList();
                                    }
                                  });
                                  break;
                                case 3:
                                  getMyDialog(
                                    S.current.selfile_shot_info7,
                                    S.current.delete,
                                    content: S.current.selfile_shot_info8,
                                    () {
                                      AppPage.back();
                                      logic.getDeleteVideo(
                                          logic.videosDataList[index]);
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                  break;
                              }
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                    logic.videosDataList[index].status == 3
                                        ? Container(
                                            width: double.infinity,
                                            height: 93.w,
                                            decoration: BoxDecoration(
                                                color: Colours.color191921,
                                                borderRadius:
                                                    BorderRadius.circular(8.r)),
                                            alignment: Alignment.center,
                                            child: Center(
                                              child: Image.asset(
                                                  'assets/images/composition_failure.png'),
                                            ),
                                          )
                                        : (logic.videosDataList[index].cover ??
                                                    "") !=
                                                ""
                                            ? MyImage(
                                                logic.videosDataList[index]
                                                        .cover ??
                                                    "",
                                                width: double.infinity,
                                                height: 93.w,
                                                radius: 8.r,
                                                fit: BoxFit.fill,
                                                errorImage: "error_image.png",
                                                placeholderImage:
                                                    "error_image.png",
                                              )
                                            : Container(
                                                width: double.infinity,
                                                height: 93.w,
                                                decoration: BoxDecoration(
                                                    color: Colours.color191921,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.r)),
                                                alignment: Alignment.center,
                                                child: MyImage(
                                                  "no_videos.png",
                                                  width: 50.w,
                                                  isAssetImage: true,
                                                  height: 36.w,
                                                  fit: BoxFit.fill,
                                                  errorImage: "error_image.png",
                                                  placeholderImage:
                                                      "error_image.png",
                                                ),
                                              ),
                                    Positioned(
                                      right: 0.w,
                                      top: 0.w,
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            left: 10.w,
                                            right: 10.w,
                                            top: 5.w,
                                            bottom: 5.w),
                                        decoration: BoxDecoration(
                                            color: logic.videosDataList[index]
                                                        .status ==
                                                    0
                                                ? Colours.colorA44EFF
                                                : logic.videosDataList[index]
                                                            .status ==
                                                        1
                                                    ? Colours.colorA44EFF
                                                    : logic
                                                                .videosDataList[
                                                                    index]
                                                                .status ==
                                                            2
                                                        ? Colors.green
                                                        : const Color(
                                                            0xFFFF3F3F),
                                            borderRadius: BorderRadius.only(
                                                bottomLeft:
                                                    Radius.circular(8.r),
                                                topRight:
                                                    Radius.circular(8.r))),
                                        child: Text(
                                          // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                          logic.videosDataList[index].status ==
                                                  0
                                              ? "待合成"
                                              : logic.videosDataList[index]
                                                          .status ==
                                                      1
                                                  ? "合成中"
                                                  : logic.videosDataList[index]
                                                              .status ==
                                                          2
                                                      ? "合成完成"
                                                      : "合成失败",
                                          textAlign: TextAlign.right,
                                          style: TextStyles.medium.copyWith(
                                              fontSize: 10.sp,
                                              color: Colours.white),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 5.w,
                                      right: 5.w,
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            left: 5.w,
                                            right: 5.w,
                                            top: 2.w,
                                            bottom: 2.w),
                                        decoration: BoxDecoration(
                                            color: Colours.color65000000,
                                            borderRadius:
                                                BorderRadius.circular(2.r)),
                                        child: Text(
                                          // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                          logic.videosDataList[index]
                                                  .completedTime ??
                                              "",
                                          textAlign: TextAlign.right,
                                          style: TextStyles.medium.copyWith(
                                              fontSize: 10.sp,
                                              color: Colours.white),
                                        ),
                                      ),
                                    ),

                                    if (logic.videosDataList[index].status == 2)
                                      WxAssets.images.selfieShotPlay
                                          .image(width: 25.w, height: 25.w)
                                  ],
                                ),
                                Expanded(
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      logic.videosDataList[index].name ?? "",
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      style: TextStyles.medium.copyWith(
                                          fontSize: 14.sp,
                                          color: Colours.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        });
                      }),
                ),
        ],
      ),
    );
  }

  Column _myColumnWidget(var title, var data) {
    return Column(
      children: [
        Text(
          data,
          style: TextStyle(
              fontFamily: "DIN",
              color: Colours.white,
              height: 1,
              fontSize: 20.sp,
              fontWeight: FontWeight.w700),
        ),
        SizedBox(
          height: 15.w,
        ),
        Text(
          title,
          style:
              TextStyle(color: Colours.color9393A5, height: 1, fontSize: 12.sp),
        ),
      ],
    );
  }
}
