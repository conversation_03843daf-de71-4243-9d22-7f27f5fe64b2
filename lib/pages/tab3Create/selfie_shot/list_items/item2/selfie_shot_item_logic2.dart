// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/half_shooting_records_model.dart';
import 'package:shoot_z/network/model/shooting_videos_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';

class SelfieShotItemLogic2 extends GetxController {
  static const platform =
      MethodChannel('com.example.my_flutter_app/native_method');
  Future<void> _showNativeView(String trainingId, String sampleVideoUrl) async {
    try {
      await platform.invokeMethod('showNativeView', {
        "trainingId": trainingId,
        "type": 2,
        "sampleVideoUrl": sampleVideoUrl
      });
    } on PlatformException catch (e) {
      print("native_method：Failed to invoke native method: '${e.message}'.");
    }
  }

  var dataFag = {
    "isFrist": true,
  }.obs;
  var halfShootingRecordsModel = HalfShootingRecordsModel().obs;
  var sampleVideoUrl = "";
  var videoPath = "".obs;
  var imgPath = "".obs;
  var videosDataList = <ShootingVideosModel>[].obs;
  var trainingId = "".obs;
  @override
  void onInit() {
    super.onInit();
    getHalfShootingRecords();
    getVideoList();
  }

  setVideos(var videoPath2, var imgPath2) {
    imgPath.value = imgPath2;
    videoPath.value = videoPath2;
  }

  //开始录制
  getHalfShootingRecording() async {
    Map<String, dynamic> param = {
      'trainType': 2,
    };
    var res = await Api().post(ApiUrl.halfShootingRecording, data: param);
    if (sampleVideoUrl == "") {
      var videoRes = await Api().get(ApiUrl.sampleVideo, queryParameters: {});
      if (videoRes.isSuccessful()) {
        sampleVideoUrl = videoRes.data["path"];
        log("videoRes!!!!!!!!:$sampleVideoUrl");
      }
    }
    if (res.isSuccessful()) {
      UserManager.instance.postChannelSubmit(0,
          channelParam: "shots"); //埋点'0 通用， 1 注册，2 登陆，3 跳转'
      trainingId.value = res.data["trainingId"].toString();
      log("halfShootingRecording:${jsonEncode(res.data)}");
      _showNativeView(trainingId.value, sampleVideoUrl);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //结束录制
  getHalfShootingRecordingEnd(String trainingId) async {
    Map<String, dynamic> param = {
      'trainingId': trainingId,
    };
    var url = await ApiUrl.halfShootingRecordingEnd(trainingId);
    log("halfShootingRecordingEnd:$param-$url");

    try {
      var res = await Api().PUT(url, data: param);
      log("halfShootingRecordingEnd:${jsonEncode(res.data)}");
      if (res.isSuccessful()) {
      } else {
        WxLoading.showToast(res.message);
      }
    } catch (e) {
      log("halfShootingRecordingEnd:e=$e");
    }
  }

  //获得投篮记录列表
  getHalfShootingRecords() async {
    Map<String, dynamic> param = {
      'pageIndex': 1,
      'pageSize': 1,
      'trainType': 2, //1单人 2多人
      'reportId': 0, //reportId 报告id，0时查训练所有； 不为0时查制定报告的合成
    };
    var res =
        await Api().get(ApiUrl.halfShootingRecords, queryParameters: param);
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      halfShootingRecordsModel.value =
          HalfShootingRecordsModel.fromJson(res.data);
      halfShootingRecordsModel.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //获得集锦列表
  getVideoList() async {
    Map<String, dynamic> param = {
      'userId': UserManager.instance.user?.userId,
      'pageIndex': 1,
      'pageSize': 4,
      'trainType': 2, //1单人 2多人
      'reportId': 0, //reportId 报告id，0时查训练所有； 不为0时查制定报告的合成
    };
    log("param3=$param");
    var res =
        await Api().get(ApiUrl.halfShootingVideos, queryParameters: param);
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      // halfShootingRecordsModel.value =ShootingVideosModel
      List list = res.data["result"] ?? [];
      List<ShootingVideosModel> modelList =
          list.map((e) => ShootingVideosModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      videosDataList.assignAll(modelList);
      videosDataList.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void getNextPage2(trainingId1) {
    AppPage.to(Routes.selfieShotInfoPage, arguments: {
      "type": "4",
      "trainingId": trainingId1,
    }).then((onValue) {
      getHalfShootingRecords();
      getVideoList();
    });
  }

  Future<void> getDeleteVideo(ShootingVideosModel data) async {
    Map<String, dynamic> param2 = {"id": data.id};
    var url = await ApiUrl.getDeleteVideo(data.id ?? "");
    var res = await Api().delete(url, data: param2);
    log("$param2");
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      getVideoList();
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }
}
