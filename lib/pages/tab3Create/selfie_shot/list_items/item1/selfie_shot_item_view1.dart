import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/list_items/item1/selfie_shot_item_logic1.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场自由投篮 多人模式
class SelfieShotItemPage1 extends StatelessWidget {
  SelfieShotItemPage1({super.key});

  final logic = Get.put(SelfieShotItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : _infoWidget(context);
      }),
    );
  }

  Widget _infoWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SizedBox(
                //   height: 14.w,
                // ),
                // Center(
                //   child: Text(
                //     S.current.selfile_shot_title3,
                //     style: TextStyles.regular
                //         .copyWith(color: Colours.white, fontSize: 16.sp),
                //   ),
                // ),
                SizedBox(
                  height: 20.w,
                ),
                // WxAssets.images.selfieShot2.image(width: 113.w, height: 28.w),

                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    WxAssets.images.selfieShotTips
                        .image(width: 14.w, height: 14.w),
                    SizedBox(
                      width: 3.w,
                    ),
                    Expanded(
                      child: Text(
                        S.current.selfile_shot2,
                        style: TextStyles.regular.copyWith(
                            color: Colours.color9393A5, fontSize: 12.sp),
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: 10.w,
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    if ((logic.videoPath.value) != "") {
                      AppPage.to(Routes.videoPath, arguments: {
                        "videoPath": logic.videoPath.value,
                        "teamName": "投篮视频示例",
                        "isShowShareUpdate": "0",
                      });
                    } else {
                      WxLoading.showToast(S.current.No_data_available);
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 10.w, bottom: 25.w),
                    width: double.infinity,
                    height: 178.w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r)),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        MyImage(
                          logic.imgPath.value,
                          width: double.infinity,
                          height: 178.w,
                          radius: 12.r,
                          errorImage: "error_image_width.png",
                          placeholderImage: "error_image_width.png",
                        ),
                        WxAssets.images.selfieShotPlay
                            .image(width: 51.w, height: 51.w)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            thickness: 1,
            height: 2,
            color: Colours.color2F2F3B,
            indent: 15.w,
            endIndent: 15.w,
          ),
          Padding(
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {
                    //logic.getNextPage2("104");
                    // logic.getHalfShootingRecordingEnd("7");
                    //半场投篮 开始投篮
                    // if (Platform.isAndroid) {
                    //   await WxPermissionUtils.microphoneAndCameraStorage(
                    //       handle: () {
                    //     logic.getHalfShootingRecording();
                    //   });
                    // } else {
                    //   logic.getHalfShootingRecording();
                    // }
                    logic.getHalfShootingRecording();
                    //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                    UserManager.instance.postApmTracking(1,
                        remark: "开始投篮按钮",
                        nowPage: Routes.selfieShotPage,
                        subPage: "SelfieShot2StartShooting",
                        content: "点击自由投篮，开始投篮");
                  },
                  child: Container(
                    width: double.infinity,
                    height: 40.w,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(
                        left: 94.w, right: 94.w, top: 25.w, bottom: 25.w),
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20.r)),
                    child: Text(
                      S.current.selfile_shot3,
                      style: TextStyles.regular.copyWith(color: Colours.white),
                    ),
                  ),
                ),
                Container(
                  height: 40.w,
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(top: 0.w, bottom: 6.w),
                  child: Row(
                    children: [
                      WxAssets.images.selfieShot1
                          .image(height: 10.w, width: 10.w),
                      SizedBox(
                        width: 3.w,
                      ),
                      Text(
                        S.current.selfile_shot4,
                        style: TextStyle(
                            color: Colours.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const Spacer(),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          //投篮记录{
                          AppPage.to(Routes.selfieShotReportPage,
                              arguments: {"type": "2"}).then((onValue) {
                            logic.getVideoList();
                          });
                          //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                          UserManager.instance.postApmTracking(1,
                              remark: "投篮记录按钮",
                              nowPage: Routes.selfieShotPage,
                              toPage: Routes.selfieShotReportPage,
                              subPage: "SelfieShot1StartShooting",
                              content: "投篮记录按钮跳转");
                        },
                        child: SizedBox(
                          height: 40.w,
                          child: Row(
                            children: [
                              Text(
                                S.current.selfile_shot5,
                                style: TextStyle(
                                    color: Colours.color9393A5,
                                    fontSize: 12.sp),
                              ),
                              SizedBox(
                                width: 3.w,
                              ),
                              WxAssets.images.icArrowRight.image(
                                  width: 14.w,
                                  height: 14.w,
                                  color: Colours.white),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                ((logic.halfShootingRecordsModel.value.result?.length ?? 0) <=
                        0)
                    ? SizedBox(
                        height: 180.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          textColor: Colours.white,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 105.w, height: 89.w),
                        ))
                    : GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.to(Routes.selfieShotInfoPage, arguments: {
                            "halfShootingRecordsModel": logic
                                .halfShootingRecordsModel.value.result?.first,
                            "type": "2",
                            "trainingId":
                                "${logic.halfShootingRecordsModel.value.result?.first?.trainingId}",
                          }).then((onValue) {
                            logic.getHalfShootingRecords();
                            logic.getVideoList();
                          });
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              children: [
                                Text(
                                  S.current.selfile_shot6,
                                  style: TextStyle(
                                      color: Colours.color9393A5,
                                      fontSize: 14.sp),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Text(
                                  "${logic.halfShootingRecordsModel.value.result?.first?.duration ?? ""}分",
                                  style: TextStyle(
                                      color: Colours.white, fontSize: 16.sp),
                                ),
                              ],
                            ),
                            SizedBox(
                              width: 80.w,
                              child: Column(
                                children: [
                                  Text(
                                    S.current.selfile_shot7,
                                    style: TextStyle(
                                        color: Colours.color9393A5,
                                        fontSize: 14.sp),
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Text(
                                    "${logic.halfShootingRecordsModel.value.result?.first?.shotCount ?? ""}",
                                    style: TextStyle(
                                        color: Colours.white, fontSize: 16.sp),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 80.w,
                              child: Column(
                                children: [
                                  Text(
                                    S.current.selfile_shot8,
                                    style: TextStyle(
                                        color: Colours.color9393A5,
                                        fontSize: 14.sp),
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Text(
                                    "${(logic.halfShootingRecordsModel.value.result?.first?.hitCount ?? "")}",
                                    style: TextStyle(
                                        color: Colours.white, fontSize: 16.sp),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              children: [
                                Text(
                                  S.current.selfile_shot9,
                                  style: TextStyle(
                                      color: Colours.color9393A5,
                                      fontSize: 14.sp),
                                ),
                                SizedBox(
                                  height: 15.w,
                                ),
                                Text(
                                  "${logic.halfShootingRecordsModel.value.result?.first?.rate ?? ""}%",
                                  style: TextStyle(
                                      color: Colours.white, fontSize: 16.sp),
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
              ],
            ),
          ),
          Container(
            height: 40.w,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            margin: EdgeInsets.only(top: 35.w, bottom: 15.w),
            child: Row(
              children: [
                WxAssets.images.selfieShot1.image(height: 10.w, width: 10.w),
                SizedBox(
                  width: 3.w,
                ),
                Text(
                  S.current.my_highlights,
                  style: TextStyle(
                      color: Colours.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                const Spacer(),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    //集锦查看更多按钮
                    AppPage.to(Routes.selfieShotVideosPage,
                        arguments: {"type": "2"}).then((onValue) {
                      logic.getVideoList();
                    });
                    //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                    UserManager.instance.postApmTracking(1,
                        remark: "集锦查看更多按钮",
                        nowPage: Routes.selfieShotPage,
                        toPage: Routes.selfieShotVideosPage,
                        subPage: "SelfieShot1StartShooting",
                        content: "更多按钮按钮跳转");
                  },
                  child: SizedBox(
                    height: 40.w,
                    child: Row(
                      children: [
                        Text(
                          S.current.view_more,
                          style: TextStyle(
                              color: Colours.color9393A5, fontSize: 12.sp),
                        ),
                        SizedBox(
                          width: 3.w,
                        ),
                        WxAssets.images.icArrowRight.image(
                            width: 14.w, height: 14.w, color: Colours.white),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          logic.videosDataList.isEmpty
              ? Container(
                  margin: EdgeInsets.only(top: 15.w, bottom: 70.w),
                  child: myNoDataView(
                    context,
                    msg: "暂无视频",
                    textColor: Colours.white,
                    imagewidget: Container(
                      margin: EdgeInsets.only(left: 10.w),
                      child: WxAssets.images.noVideos
                          .image(width: 105.w, height: 60.w),
                    ),
                  ),
                )
              : Container(
                  // height: 55.w,
                  margin: EdgeInsets.only(left: 15.w, right: 15.w),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                  child: GridView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      physics:
                          const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 11,
                        mainAxisSpacing: 11,
                        childAspectRatio: 152 / 103,
                      ),
                      padding: EdgeInsets.only(bottom: 60.w),
                      itemCount: logic.videosDataList.length,
                      itemBuilder: (context, index) {
                        return Obx(() {
                          return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                              switch (logic.videosDataList[index].status) {
                                case 0:
                                case 1:
                                  WxLoading.showToast("视频合成中，请稍后再试");
                                  break;
                                case 2:
                                  AppPage.to(Routes.videoPath, arguments: {
                                    "videoPath":
                                        logic.videosDataList[index].videoPath,
                                    "teamName": "投篮视频",
                                    "isShowShareUpdate": "1",
                                    "videoId": logic.videosDataList[index].id,
                                  }).then((onValue) {
                                    if (onValue == true) {
                                      logic.getVideoList();
                                    }
                                  });
                                  ;
                                  break;
                                case 3:
                                  getMyDialog(
                                    S.current.selfile_shot_info7,
                                    S.current.delete,
                                    content: S.current.selfile_shot_info8,
                                    () {
                                      AppPage.back();
                                      logic.getDeleteVideo(
                                          logic.videosDataList[index]);
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                  break;
                              }
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                    logic.videosDataList[index].status == 3
                                        ? Container(
                                            width: double.infinity,
                                            height: 93.w,
                                            decoration: BoxDecoration(
                                                color: Colours.color191921,
                                                borderRadius:
                                                    BorderRadius.circular(8.r)),
                                            alignment: Alignment.center,
                                            child: Center(
                                              child: Image.asset(
                                                  'assets/images/composition_failure.png'),
                                            ),
                                          )
                                        : (logic.videosDataList[index].cover ??
                                                    "") !=
                                                ""
                                            ? MyImage(
                                                logic.videosDataList[index]
                                                        .cover ??
                                                    "",
                                                width: double.infinity,
                                                height: 93.w,
                                                radius: 8.r,
                                                fit: BoxFit.fill,
                                                errorImage: "error_image.png",
                                                placeholderImage:
                                                    "error_image.png",
                                              )
                                            : Container(
                                                width: double.infinity,
                                                height: 93.w,
                                                decoration: BoxDecoration(
                                                    color: Colours.color191921,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.r)),
                                                alignment: Alignment.center,
                                                child: MyImage(
                                                  "no_videos.png",
                                                  width: 50.w,
                                                  isAssetImage: true,
                                                  height: 36.w,
                                                  fit: BoxFit.fill,
                                                  errorImage: "error_image.png",
                                                  placeholderImage:
                                                      "error_image.png",
                                                ),
                                              ),
                                    Positioned(
                                      right: 0.w,
                                      top: 0.w,
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            left: 10.w,
                                            right: 10.w,
                                            top: 5.w,
                                            bottom: 5.w),
                                        decoration: BoxDecoration(
                                            color: logic.videosDataList[index]
                                                        .status ==
                                                    0
                                                ? Colours.colorA44EFF
                                                : logic.videosDataList[index]
                                                            .status ==
                                                        1
                                                    ? Colours.colorA44EFF
                                                    : logic
                                                                .videosDataList[
                                                                    index]
                                                                .status ==
                                                            2
                                                        ? Colors.green
                                                        : const Color(
                                                            0xFFFF3F3F),
                                            borderRadius: BorderRadius.only(
                                                bottomLeft:
                                                    Radius.circular(8.r),
                                                topRight:
                                                    Radius.circular(8.r))),
                                        child: Text(
                                          // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                          logic.videosDataList[index].status ==
                                                  0
                                              ? "待合成"
                                              : logic.videosDataList[index]
                                                          .status ==
                                                      1
                                                  ? "合成中"
                                                  : logic.videosDataList[index]
                                                              .status ==
                                                          2
                                                      ? "合成完成"
                                                      : "合成失败",
                                          textAlign: TextAlign.right,
                                          style: TextStyles.medium.copyWith(
                                              fontSize: 10.sp,
                                              color: Colours.white),
                                        ),
                                      ),
                                    ),
                                    if (logic.videosDataList[index].status == 2)
                                      WxAssets.images.selfieShotPlay
                                          .image(width: 25.w, height: 25.w)
                                  ],
                                ),
                                Container(
                                  alignment: Alignment.centerLeft,
                                  margin: EdgeInsets.only(top: 7.w),
                                  child: Text(
                                    logic.videosDataList[index].completedTime ??
                                        "",
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    style: TextStyles.medium.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.color5C5C6E),
                                  ),
                                ),
                              ],
                            ),
                          );
                        });
                      }),
                ),
        ],
      ),
    );
  }
}
