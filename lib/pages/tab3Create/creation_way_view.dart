import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/creation_way_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:ui_packages/ui_packages.dart';

class CreationWayPage extends StatefulWidget {
  const CreationWayPage({super.key});

  @override
  State<CreationWayPage> createState() => _PlacePageState();
}

class _PlacePageState extends State<CreationWayPage> {
  final logic = Get.put(CreationWayLogic());

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        top: false,
        child: Stack(children: [
          Container(
            child: WxAssets.images.pageTopBg
                .image(width: ScreenUtil().screenWidth, height: 260.w),
          ),
          Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
            SizedBox(
              height: ScreenUtil().statusBarHeight,
            ),
            Row(
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => AppPage.back(),
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 20.w, top: 12.w, bottom: 10.w, right: 20.w),
                    child: WxAssets.images.icClose
                        .image(width: 26.w, height: 26.w),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 25,
            ),
            WxAssets.images.aiImg1.image(width: 215.w, height: 128.w),
            SizedBox(
              height: 120.w,
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => AppPage.to(Routes.selectSitePage),
              child: Container(
                decoration: BoxDecoration(
                    image: DecorationImage(
                        image: WxAssets.images.aiBg.provider(),
                        fit: BoxFit.fill)),
                width: double.infinity,
                margin: EdgeInsets.symmetric(horizontal: 15.w),
                padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ShaderMask(
                            shaderCallback: (bounds) => const LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Colours.colorFFECC1,
                                Colours.colorE7CEFF,
                                Colours.colorD1EAFF,
                              ],
                            ).createShader(bounds),
                            child: Text(
                              '自由半场投篮',
                              style: TextStyles.display12.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16.sp),
                            ),
                          ),
                          SizedBox(
                            height: 10.w,
                          ),
                          Text(
                            '手机半场投篮拍摄',
                            style: TextStyles.display12.copyWith(
                                color: Colours.colorA8A8BC, fontSize: 12.sp),
                          ),
                        ],
                      ),
                    ),
                    WxAssets.images.ai3.image(width: 78.w, height: 60.w),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ]),
        ]),
      ),
    );
  }
}
