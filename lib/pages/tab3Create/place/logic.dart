import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/place/state.dart';
import '../../../network/api_url.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/location_utils.dart';
import '../../../widgets/permission_dialog.dart';
import 'models/place_model.dart';

class PlaceLogic extends GetxController {
  final PlaceState state = PlaceState();

  @override
  void onInit() {
    super.onInit();
    // onRefresh();
    state.locationSubscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        // onRefresh();
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    state.controller.dispose();
    state.locationSubscription?.cancel();
  }

  bool hasMore() {
    return state.list.length < state.total;
  }

  Future<void> loadMore() async {
    if (state.isLoading.value) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await request(false);
  }

  Future<void> onRefresh() async {
    state.page = 1;
    await request(true);
    state.init.value = true;
  }

  Future<void> request(bool isRefresh) async {
    state.isLoading.value = true;
    final location = await LocationUtils.instance.checkPermission();
    if (isRefresh && location) {
      await LocationUtils.instance.getCurrentPosition();
    }
    final position = LocationUtils.instance.position;
    if (position == null) {
      state.list.value = [];
      state.total = 0;
      // WxLoading.showToast(S.current.failed_location);
      if (!location) {
        showLocationDialog();
      }
      state.isLoading.value = false;
      return;
    }
    // WxLoading.show();
    List<int> filterOptions = [];
    if (state.isFree.value) filterOptions.add(1);
    if (state.isGame.value) filterOptions.add(2);
    if (state.isIndoor.value) filterOptions.add(3);
    final url =
        filterOptions.isEmpty ? ApiUrl.recommendedStadium : ApiUrl.arenasList;
    var param = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
      'limit': state.pageSize,
      'page': state.page
    };
    if (filterOptions.isNotEmpty) {
      param['filterOptions'] = filterOptions;
    }
    final res = await Api().get(url, queryParameters: param);
    // WxLoading.dismiss();
    state.isLoading.value = false;
    if (res.isSuccessful()) {
      state.page += 1;
      state.total = res.data['total'];
      final list = (res.data['results'] as List)
          .map((e) => PlaceModel.fromJson(e))
          .toList();
      if (isRefresh) {
        state.list.value = list;
      } else {
        state.list.addAll(list);
      }
    } else {
      if (isRefresh) {
        state.list.value = [];
        state.total = 0;
      }
      WxLoading.showToast(res.message);
    }
  }

  void switchSel() async {
    state.controller.jumpTo(0);
    // state.controller.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    WxLoading.show();
    await onRefresh();
    WxLoading.dismiss();
  }
}
