import 'package:flutter/material.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../widgets/gradient_border_painter.dart';

class PlaceSelView extends StatelessWidget {
  final bool isSel;
  final String text;
  final GestureTapCallback callback;
  const PlaceSelView(
      {super.key,
      required this.isSel,
      required this.text,
      required this.callback});

  @override
  Widget build(BuildContext context) {
    final gradient = isSel
        ? null
        : const LinearGradient(
            colors: [Colours.color2F2F3B, Colours.color2F2F3B]);
    final color = isSel ? Colours.color9D4FEF : Colors.white;
    return GestureDetector(
      onTap: callback,
      child: CustomPaint(
        painter: GradientBorderPainter(
            strokeWidth: 2, gradient: gradient, radius: 15),
        child: Container(
          width: 60,
          height: 30,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(15)),
          child: Text(
            text,
            style: TextStyles.display12.copyWith(color: color),
          ),
        ),
      ),
    );
  }
}
