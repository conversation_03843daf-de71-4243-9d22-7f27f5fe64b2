import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/place/logic.dart';
import 'package:shoot_z/pages/tab3Create/place/place_sel_view.dart';

import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../generated/l10n.dart';
import '../../../widgets/more_widget.dart';
import '../../../widgets/view.dart';
import '../../tab2Venue/place_list_item.dart';

class PlacePage extends StatefulWidget {
  const PlacePage({super.key});

  @override
  State<PlacePage> createState() => _PlacePageState();
}

class _PlacePageState extends State<PlacePage> {
  final logic = Get.put(PlaceLogic());
  final state = Get.find<PlaceLogic>().state;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: Stack(children: [
          Column(children: [
            _topWidget(context),
            const SizedBox(
              height: 28,
            ),
            _selWidget(context),
            const SizedBox(
              height: 20,
            ),
            Expanded(child: _list(context)),
          ]),
          Obx(
            () => opaqueVisibility(state.isLoading.value),
          ),
        ]),
      ),
    );
  }

  Widget _topWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => AppPage.back(),
          child: Padding(
            padding: const EdgeInsets.only(left: 20, top: 12),
            child: WxAssets.images.icClose.image(width: 26.w, height: 26.w),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 23, left: 20, right: 20),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    S.current.please_select_stadium,
                    style: TextStyles.titleSemiBold16.copyWith(fontSize: 24.sp),
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  Text(
                    S.current.get_your_highlights_video,
                    style: TextStyles.titleSemiBold16.copyWith(fontSize: 22.sp),
                  ),
                ],
              ),
              const Spacer(),
              GestureDetector(
                  onTap: () => AppPage.to(Routes.placeSearch),
                  child: WxAssets.images.icPlaceSearch
                      .image(width: 56, height: 56)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _selWidget(BuildContext context) {
    return Row(
      children: [
        const SizedBox(
          width: 15,
        ),
        Obx(() => PlaceSelView(
            isSel: state.isFree.value,
            text: S.current.free,
            callback: () {
              state.isFree.value = !state.isFree.value;
              logic.switchSel();
            })),
        const SizedBox(
          width: 10,
        ),
        Obx(() => PlaceSelView(
            isSel: state.isGame.value,
            text: S.current.competitions,
            callback: () {
              state.isGame.value = !state.isGame.value;
              logic.switchSel();
            })),
        const SizedBox(
          width: 10,
        ),
        Obx(() => PlaceSelView(
            isSel: state.isIndoor.value,
            text: S.current.indoor,
            callback: () {
              state.isIndoor.value = !state.isIndoor.value;
              logic.switchSel();
            })),
      ],
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => state.init.value
          ? NotificationListener(
              onNotification: (ScrollNotification note) {
                if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                  logic.loadMore();
                }
                return true;
              },
              child: RefreshIndicator(
                onRefresh: logic.onRefresh,
                child: Obx(
                  () => ListView.builder(
                    controller: state.controller,
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).padding.bottom),
                    itemCount: state.list.length + 1,
                    itemBuilder: (context, index) {
                      return index < state.list.length
                          ? PlaceListItem(model: state.list[index])
                          : MoreWidget(state.list.length, logic.hasMore(),
                              state.pageSize);
                    },
                  ),
                ),
              ),
            )
          : indicatorLoading(),
    );
  }
}
