///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PlaceModel {
/*
{
  "address": "string",
  "arenaID": 0,
  "arenaName": "string",
  "beginTime": "string",
  "cityID": 0,
  "createBy": 0,
  "createdTime": "string",
  "deleted": 0,
  "description": "string",
  "distance": 0,
  "enabled": 0,
  "endTime": "string",
  "floorCondition": "string",
  "initial": "string",
  "isBuy": true,
  "isPremium": 0,
  "isReport": true,
  "latitude": 0,
  "logo": "string",
  "longitude": 0,
  "pedestrianFlowStatus": 0,
  "provinceID": "string",
  "stage": 0,
  "tags": "string",
  "tel": "string",
  "type": 0,
  "updateBy": 0,
  "updatedTime": "string"
} 
*/

  String? address;
  int? arenaID;
  String? arenaName;
  String? beginTime;
  int? cityID;
  int? createBy;
  String? createdTime;
  int? deleted;
  String? description;
  int? distance;
  int? enabled;
  String? endTime;
  String? floorCondition;
  String? initial;
  bool? isBuy;
  int? isPremium;
  bool? isReport;
  int? latitude;
  String? logo;
  int? longitude;
  int? pedestrianFlowStatus;
  String? provinceID;
  int? stage;
  String? tags;
  String? tel;
  int? type;
  int? updateBy;
  String? updatedTime;

  PlaceModel({
    this.address,
    this.arenaID,
    this.arenaName,
    this.beginTime,
    this.cityID,
    this.createBy,
    this.createdTime,
    this.deleted,
    this.description,
    this.distance,
    this.enabled,
    this.endTime,
    this.floorCondition,
    this.initial,
    this.isBuy,
    this.isPremium,
    this.isReport,
    this.latitude,
    this.logo,
    this.longitude,
    this.pedestrianFlowStatus,
    this.provinceID,
    this.stage,
    this.tags,
    this.tel,
    this.type,
    this.updateBy,
    this.updatedTime,
  });
  PlaceModel.fromJson(Map<String, dynamic> json) {
    address = json['address']?.toString();
    arenaID = json['arenaID']?.toInt();
    arenaName = json['arenaName']?.toString();
    beginTime = json['beginTime']?.toString();
    cityID = json['cityID']?.toInt();
    createBy = json['createBy']?.toInt();
    createdTime = json['createdTime']?.toString();
    deleted = json['deleted']?.toInt();
    description = json['description']?.toString();
    distance = json['distance']?.toInt();
    enabled = json['enabled']?.toInt();
    endTime = json['endTime']?.toString();
    floorCondition = json['floorCondition']?.toString();
    initial = json['initial']?.toString();
    isBuy = json['isBuy'];
    isPremium = json['isPremium']?.toInt();
    isReport = json['isReport'];
    latitude = json['latitude']?.toInt();
    logo = json['logo']?.toString();
    longitude = json['longitude']?.toInt();
    pedestrianFlowStatus = json['pedestrianFlowStatus']?.toInt();
    provinceID = json['provinceID']?.toString();
    stage = json['stage']?.toInt();
    tags = json['tags']?.toString();
    tel = json['tel']?.toString();
    type = json['type']?.toInt();
    updateBy = json['updateBy']?.toInt();
    updatedTime = json['updatedTime']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['arenaID'] = arenaID;
    data['arenaName'] = arenaName;
    data['beginTime'] = beginTime;
    data['cityID'] = cityID;
    data['createBy'] = createBy;
    data['createdTime'] = createdTime;
    data['deleted'] = deleted;
    data['description'] = description;
    data['distance'] = distance;
    data['enabled'] = enabled;
    data['endTime'] = endTime;
    data['floorCondition'] = floorCondition;
    data['initial'] = initial;
    data['isBuy'] = isBuy;
    data['isPremium'] = isPremium;
    data['isReport'] = isReport;
    data['latitude'] = latitude;
    data['logo'] = logo;
    data['longitude'] = longitude;
    data['pedestrianFlowStatus'] = pedestrianFlowStatus;
    data['provinceID'] = provinceID;
    data['stage'] = stage;
    data['tags'] = tags;
    data['tel'] = tel;
    data['type'] = type;
    data['updateBy'] = updateBy;
    data['updatedTime'] = updatedTime;
    return data;
  }
}
