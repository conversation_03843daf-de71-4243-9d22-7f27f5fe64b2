import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';

class PlaceState {
  StreamSubscription? locationSubscription;

  /// 是否正在加载数据
  var isLoading = false.obs;
  ScrollController controller = ScrollController();
  var list = <PlaceModel>[].obs;
  int total = 0;
  var page = 1;
  var pageSize = 20;
  var init = false.obs;

  var isFree = false.obs;
  var isGame = false.obs;
  var isIndoor = false.obs;
}
