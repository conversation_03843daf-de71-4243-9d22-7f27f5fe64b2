// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/pages/tab3Create/place/search/state.dart';

import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../../generated/l10n.dart';
import '../../../../routes/app.dart';
import '../../../../utils/location_utils.dart';

//巨坑
//controller.addListener方式监听文本输入
//ios手机，如果输入拼音后没有选择文字，然后直接点击返回，会触发controller.addListener回调，所以返回的时候需要处理removeListener
//iOS手机，按删除键回退addListener会回调2次，比如输入框文案是‘12’，点击删除回调第一次controller.text还是‘12’，第二次才是‘1’
//iOS手机PopScope不能阻止返回
//TextEditingController.addListener添加的监听器会立即执行回调。其行为与 TextField 的 onChanged 不同，onChanged是加入事件循环
//用onChanged监听没有上面问题但是composingRange.isValid又不对

class SearchLogic extends GetxController {
  final state = SearchState();
  Timer? _debounce;
  static const String deleteHistoryKey = "deleteHistoryKey";
  CancelToken _cancelToken = CancelToken();
  void back({bool isSlide = false}) {
    //ios手机，如果输入拼音后没有选择文字，然后直接点击返回，会触发controller.addListener回调，所以返回的时候需要处理removeListener
    //PopScope的回调不是同步的有延迟，所以需要_cancelSearch中断
    print('backbackback');
    state.controller.removeListener(onTextChanged);
    onKeyDismiss();
    _cancelSearch();
    if (Platform.isIOS && isSlide) {
      return;
    }
    AppPage.back();
  }

  @override
  void onInit() async {
    super.onInit();
    state.controller.addListener(onTextChanged);
    // 初始化响应式变量
    state.isTextEmpty.value = state.controller.text.isEmpty;
    final historyList =
        await WxStorage.instance.getStringList(deleteHistoryKey, token: true);
    if (historyList != null) {
      state.historyList.value = historyList;
    }
  }

  var _preText = '';
  void onTextChanged() {
    // print('_onTextChanged');
    final composingRange = state.controller.value.composing;
    //TextEditingController 的 value.composing 是用来表示当前输入法正在编辑（例如输入拼音）的范围
    //当 composing 的范围有效（start 和 end 不为 -1）时，表示用户正在输入拼音，内容尚未确认。
    // 如果用户正在输入拼音，则不更新内容
    if (composingRange.isValid) {
      // print("Composing: ${state.controller.text.substring(composingRange.start, composingRange.end)}");
    } else {
      // 用户已完成输入，更新内容
      print("Final input: ${state.controller.text}");
      // 更新响应式变量
      state.isTextEmpty.value = state.controller.text.isEmpty;
      if (state.controller.text == _preText) {
        print('input =====');
        return;
      }
      _preText = state.controller.text;
      onSearchChanged(state.controller.text);
    }
  }

  void searchModel(String str) async {
    _cancelSearch();
    onKeyDismiss();
    _preText = str;
    state.controller.text = str;
    // 更新响应式变量
    state.isTextEmpty.value = str.isEmpty;
    state.models.clear();
    WxStorage.instance
        .getStringList(deleteHistoryKey, token: true)
        .then((value) {
      value?.remove(str);
      return value;
    }).then((value) {
      var list = [str];
      if (value != null) {
        list.addAll(value);
      }
      // 只保留最后10条
      if (list.length > 10) {
        list = list.sublist(0, 10);
      }
      state.historyList.value = list;
      WxStorage.instance.setStringList(deleteHistoryKey, list, token: true);
    });
    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    WxLoading.show();
    final res = await Api().get(ApiUrl.arenasSearchModel, queryParameters: {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
      'arenaName': str
    });
    if (res.isSuccessful()) {
      state.models.value =
          (res.data as List).map((e) => PlaceModel.fromJson(e)).toList();
    } else {
      state.models.value = [];
      WxLoading.showToast(res.message);
    }
    state.type.value = 2;
    WxLoading.dismiss();
  }

  /// 方法：匹配字符串并改变匹配字符的颜色
  TextSpan highlightWord(
      String text, String target, Color color, Color defaultColor) {
    List<TextSpan> spans = [];
    int start = 0;

    // 遍历整个字符串，找到所有匹配的目标字符
    while (true) {
      int index = text.indexOf(target, start);
      if (index == -1) {
        // 如果没有更多的匹配，添加剩余的文字
        spans.add(TextSpan(text: text.substring(start)));
        break;
      }
      // 添加目标字符前的普通文本
      if (index > start) {
        spans.add(TextSpan(text: text.substring(start, index)));
      }
      // 添加高亮的目标字符
      spans.add(TextSpan(
        text: target,
        style: TextStyle(color: color),
      ));
      // 更新起始位置
      start = index + target.length;
    }

    return TextSpan(
      style: TextStyles.semiBold14.copyWith(color: defaultColor), // 默认样式
      children: spans,
    );
  }

  void onSearchChanged(String query) {
    _cancelSearch();
    if (query.isEmpty) {
      state.type.value = 0;
      return;
    }
    // 创建新的 Timer
    _debounce = Timer(const Duration(milliseconds: 300), () {
      // 在延迟结束后执行搜索逻辑
      _performSearch(query);
    });
  }

  void _cancelSearch() {
    //取消网络请求
    _cancelToken.cancel();
    _cancelToken = CancelToken();
    // 如果已有 Timer，取消它
    if (_debounce?.isActive ?? false) {
      debug("如果已有 Timer，取消它");
      _debounce!.cancel();
    }
  }

  void _performSearch(String query) async {
    print("Searching for: $query");
    if (state.controller.text != query) {
      //开始搜索的时候如果文案已经改变return
      print("输入框文案已经改变，停止搜索 $query != ${state.controller.text}");
      return;
    }
    state.type.value = 1;
    state.texts.value = [S.current.click_to_search(query)];

    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    final res = await Api().get(ApiUrl.arenasSearchName, queryParameters: {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
      'arenaName': query
    });
    if (state.controller.text != query) {
      //搜索结果出来的时候如果输入框文案已经改变，不更新结果
      print("输入框文案已经改变，不更新结果 $query != ${state.controller.text}");
      return;
    }
    state.type.value = 1;
    var list = [S.current.click_to_search(query)];
    if (res.isSuccessful()) {
      list.addAll(res.data['results'].cast<String>());
    }
    state.texts.value = list;
  }

  void deleteHistory() {
    state.historyList.clear();
    WxStorage.instance.remove(deleteHistoryKey, token: true);
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
    print('onClose----');
    state.controller.dispose();
  }
}
