import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';

class SearchState {
  final TextEditingController controller = TextEditingController();
  var type = 0.obs; // 0 history 1 search 2 list
  var historyList = <String>[].obs;
  var texts = <String>[].obs;
  var models = <PlaceModel>[].obs;
  var isTextEmpty = true.obs; // 响应式变量跟踪输入框是否为空
}
