import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';

class SearchSiteState {
  final TextEditingController controller = TextEditingController();
  var type = 0.obs; // 0 history 1 search 2 list
  var historyList = <String>[].obs;
  var texts = <String>[].obs;
  var models = <VenueModel>[].obs;
  var isTextEmpty = true.obs; // 响应式变量跟踪输入框是否为空
}
