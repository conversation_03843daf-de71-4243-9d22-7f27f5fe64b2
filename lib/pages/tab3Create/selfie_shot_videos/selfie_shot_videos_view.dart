import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/shooting_videos_model.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_videos/selfie_shot_videos_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 我的集锦列表
class SelfieShotVideosPage extends StatelessWidget {
  SelfieShotVideosPage({super.key});
  final logic = Get.put(SelfieShotVideosLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_highlights),
      ),
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController2,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(
              isLoad: false, controller: logic.refreshController2);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController2);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? Center(
                    child: SizedBox(
                        height: 500.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 150.w),
                        )),
                  )
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding:
                        EdgeInsets.only(bottom: 40.w, left: 15.w, right: 15.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(ShootingVideosModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // 0 待合成；1 合成中 ； 2 合成完成；3 失败
        switch (item.status) {
          case 0:
          case 1:
            WxLoading.showToast("视频合成中，请稍后再试");
            break;
          case 2:
            AppPage.to(Routes.videoPath, arguments: {
              "videoPath": item.videoPath,
              "videoId": item.id,
              "teamName": "投篮视频",
              "isShowShareUpdate": "1",
            }).then((onValue) {
              if (onValue == true) {
                logic.getdataList(
                    isLoad: false, controller: logic.refreshController2);
              }
            });

            break;
          case 3:
            getMyDialog(
              S.current.selfile_shot_info7,
              S.current.delete,
              content: S.current.selfile_shot_info8,
              () {
                AppPage.back();
                logic.getDeleteVideo(item);
              },
              isShowClose: false,
              btnIsHorizontal: true,
              btnText2: S.current.cancel,
              onPressed2: () {
                AppPage.back();
              },
            );
            break;
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerRight,
            margin: EdgeInsets.only(top: 15.w, bottom: 15.w),
            child: Text(
              (item.completedTime ?? "")
                  .substring(0, (item.completedTime?.length.clamp(0, 10))),
              textAlign: TextAlign.right,
              style: TextStyles.regular
                  .copyWith(fontSize: 14.sp, color: Colours.white),
            ),
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              // 0 待合成；1 合成中 ； 2 合成完成；3 失败
              //item.status == 2
              ClipRRect(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.r),
                    topRight: Radius.circular(8.r)),
                child: (item.cover ?? "") != ""
                    ? MyImage(
                        item.cover ?? "",
                        width: double.infinity,
                        height: 197.w,
                        fit: BoxFit.fill,
                        errorImage: "error_image.png",
                        placeholderImage: "error_image.png",
                      )
                    : Container(
                        width: double.infinity,
                        height: 197.w,
                        alignment: Alignment.center,
                        color: Colours.color1C1C24,
                        child: MyImage(
                          "no_videos.png",
                          width: 50.w,
                          isAssetImage: true,
                          height: 36.w,
                          fit: BoxFit.fill,
                          errorImage: "error_image.png",
                          placeholderImage: "error_image.png",
                        ),
                      ),
              ),
              Positioned(
                right: 0.w,
                top: 0.w,
                child: Container(
                  padding: EdgeInsets.only(
                      left: 10.w, right: 10.w, top: 5.w, bottom: 5.w),
                  decoration: BoxDecoration(
                      color: item.status == 0
                          ? Colours.colorA44EFF
                          : item.status == 1
                              ? Colours.colorA44EFF
                              : item.status == 2
                                  ? Colors.green
                                  : Colours.colorA54040,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8.r),
                          topRight: Radius.circular(8.r))),
                  child: Text(
                    // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                    item.status == 0
                        ? "待合成"
                        : item.status == 1
                            ? "合成中"
                            : item.status == 2
                                ? "合成完成"
                                : "失败",
                    textAlign: TextAlign.right,
                    style: TextStyles.medium
                        .copyWith(fontSize: 10.sp, color: Colours.white),
                  ),
                ),
              ),
              if (item.status == 2)
                WxAssets.images.selfieShotPlay.image(width: 48.w, height: 48.w)
            ],
          ),
          Container(
            alignment: Alignment.centerLeft,
            height: 48.w,
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r)),
            ),
            child: Text(
              (item.name ?? "暂无"),
              maxLines: 1,
              style: TextStyles.regular
                  .copyWith(fontSize: 14.sp, color: Colours.white),
            ),
          ),
        ],
      ),
    );
  }
}
