import 'dart:developer';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/half_shooting_records_model.dart';

class SelfieShotReportLogic extends GetxController {
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);

  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var type = "";
  //数据列表
  var dataList = <HalfShootingRecordsModelResult>[].obs;
  @override
  void onInit() {
    super.onInit();
    type = Get.arguments["type"];
    getdataList(controller: refreshController2, isLoad: false);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 20,
      'trainType': type, //1单人 2多人
    };
    var res =
        await Api().get(ApiUrl.halfShootingRecords, queryParameters: param);
    log("zzzzzz12removeAt2-${res.data}");
    if (res.isSuccessful()) {
      List list = res.data == null ? [] : res.data["result"] ?? [];
      List<HalfShootingRecordsModelResult> modelList =
          list.map((e) => HalfShootingRecordsModelResult.fromJson(e)).toList();

      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
