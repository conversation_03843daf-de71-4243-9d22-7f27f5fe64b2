import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/half_shooting_records_model.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_report/selfie_shot_report_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 投篮记录
class SelfieShotReportPage extends StatelessWidget {
  SelfieShotReportPage({super.key});
  final logic = Get.put(SelfieShotReportLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _createTeamWidget(context),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 50.w,
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(
                top: ScreenUtil().statusBarHeight, bottom: 30.w),
            child: Container(
              width: 60.w,
              padding: EdgeInsets.only(left: 8.w, right: 10.w, top: 8.w),
              child: IconButton(
                  onPressed: () {
                    AppPage.back();
                  },
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 20,
                  )),
            ),
          ),
          Container(
            height: 40.w,
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(left: 29.w, bottom: 5.w),
            child: Row(
              children: [
                WxAssets.images.selfieShot1.image(height: 10.w, width: 10.w),
                SizedBox(
                  width: 3.w,
                ),
                Text(
                  S.current.selfile_shot5,
                  style: TextStyle(
                      color: Colours.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ],
            ),
          ),
          // _listItemWidget(MatchesModel()),
          // _listItemWidget(MatchesModel()),
          Expanded(child: _listWidget1(context)),
        ]);
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController2,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(
              isLoad: false, controller: logic.refreshController2);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController2);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? Center(
                    child: SizedBox(
                        height: 500.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 150.w),
                        )),
                  )
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(HalfShootingRecordsModelResult item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        //投篮详情
        AppPage.to(Routes.selfieShotInfoPage, arguments: {
          "halfShootingRecordsModel": item,
          "type": logic.type,
          "trainingId": "${item.trainingId}",
        });
      },
      child: Container(
        margin:
            EdgeInsets.only(left: 29.w, right: 29.w, bottom: 15.w, top: 30.w),
        child: Column(
          children: [
            Container(
              height: 40.w,
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(top: 0.w, bottom: 6.w),
              child: Row(
                children: [
                  Text(
                    item.startTime ?? "",
                    style: TextStyle(
                        color: Colours.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  SizedBox(
                    width: 3.w,
                  ),
                  const Spacer(),
                  SizedBox(
                    height: 40.w,
                    child: Row(
                      children: [
                        Text(
                          S.current.select_details,
                          style: TextStyle(
                              color: Colours.color9393A5, fontSize: 12.sp),
                        ),
                        SizedBox(
                          width: 3.w,
                        ),
                        WxAssets.images.icArrowRight.image(
                            width: 14.w, height: 14.w, color: Colours.white),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    Text(
                      S.current.selfile_shot6,
                      style: TextStyle(
                          color: Colours.color9393A5, fontSize: 14.sp),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Text(
                      "${item.duration ?? ""}分",
                      style: TextStyle(color: Colours.white, fontSize: 16.sp),
                    ),
                  ],
                ),
                SizedBox(
                  width: 80.w,
                  child: Column(
                    children: [
                      Text(
                        S.current.selfile_shot7,
                        style: TextStyle(
                            color: Colours.color9393A5, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      Text(
                        "${item.shotCount ?? ""}",
                        style: TextStyle(color: Colours.white, fontSize: 16.sp),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 80.w,
                  child: Column(
                    children: [
                      Text(
                        S.current.selfile_shot8,
                        style: TextStyle(
                            color: Colours.color9393A5, fontSize: 14.sp),
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      Text(
                        "${item.hitCount ?? ""}",
                        style: TextStyle(color: Colours.white, fontSize: 16.sp),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Text(
                      S.current.selfile_shot9,
                      style: TextStyle(
                          color: Colours.color9393A5, fontSize: 14.sp),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Text(
                      "${item.rate ?? ""}%",
                      style: TextStyle(color: Colours.white, fontSize: 16.sp),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
