import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/half_shooting_records_model.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_report/selfie_shot_report_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 投篮记录
class SelfieShotReportPage extends StatelessWidget {
  SelfieShotReportPage({super.key});
  final logic = Get.put(SelfieShotReportLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Obx(() {
          return Text(logic.type.value == "1"
              ? S.current.selfile_shot11
              : S.current.selfile_shot_title1);
        }),
      ),
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController2,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(
              isLoad: false, controller: logic.refreshController2);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController2);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? Center(
                    child: SizedBox(
                        height: 500.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 150.w),
                        )),
                  )
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(HalfShootingRecordsModelResult item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        //投篮详情
        AppPage.to(Routes.selfieShotInfoPage, arguments: {
          "halfShootingRecordsModel": item,
          "type": logic.type.value,
          "trainingId": "${item.trainingId}",
        });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.startTime ?? "",
                        style: TextStyle(
                          color: Colours.color5C5C6E,
                          height: 1,
                          fontSize: 14.sp,
                        ),
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      Text(
                        item.startTime ?? "",
                        style: TextStyle(
                          color: Colours.colorA8A8BC,
                          height: 1,
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {},
                  child: Container(
                    height: 30.w,
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    decoration: BoxDecoration(
                        image: DecorationImage(
                      image: WxAssets.images.moreHiglightsBg.provider(),
                      fit: BoxFit.fill,
                    )),
                    child: Row(
                      children: [
                        Text(
                          S.current.view_more,
                          style:
                              TextStyle(color: Colours.white, fontSize: 12.sp),
                        ),
                        WxAssets.images.icArrowRight.image(
                            width: 12.w,
                            height: 12.w,
                            color: Colours.white,
                            fit: BoxFit.fill),
                      ],
                    ),
                  ),
                )
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.w),
              margin: EdgeInsets.only(top: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(8.r)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _myColumnWidget(
                      logic.type.value == "1"
                          ? S.current.selfile_shot12
                          : S.current.selfile_shot6,
                      "${item.duration ?? ""}分"),
                  _myColumnWidget(
                      S.current.selfile_shot7, "${item.shotCount ?? ""}"),
                  _myColumnWidget(
                      S.current.selfile_shot8, "${(item.hitCount ?? "")}"),
                  _myColumnWidget(
                      S.current.selfile_shot9, "${item.rate ?? ""}%"),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Column _myColumnWidget(var title, var data) {
    return Column(
      children: [
        Text(
          data,
          style: TextStyle(
              fontFamily: "DIN",
              color: Colours.white,
              height: 1,
              fontSize: 20.sp,
              fontWeight: FontWeight.w700),
        ),
        SizedBox(
          height: 15.w,
        ),
        Text(
          title,
          style:
              TextStyle(color: Colours.color9393A5, height: 1, fontSize: 12.sp),
        ),
      ],
    );
  }
}
