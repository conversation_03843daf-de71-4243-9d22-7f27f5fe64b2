///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VenueModelHalves {
/*
{
  "id": 0,
  "images": [
    "string"
  ]
} 
*/

  int? id;
  String? name;
  List<String?>? images;

  VenueModelHalves({
    this.id,
    this.name,
    this.images,
  });
  VenueModelHalves.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    name = json['name']?.toString();
    if (json['images'] != null) {
      final v = json['images'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      images = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (images != null) {
      final v = images;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['images'] = arr0;
    }
    return data;
  }
}

class VenueModel {
/*
{
  "auditStatus": 0,
  "coverUrl": "string",
  "createdByType": 0,
  "environmentImages": [
    "string"
  ],
  "floorMaterial": 0,
  "halves": [
    {
      "id": 0,
      "images": [
        "string"
      ]
    }
  ],
  "hasLight": 0,
  "id": 0,
  "isFree": 0,
  "latitude": 0,
  "longitude": 0,
  "name": "string",
  "openTime": 0,
  "remark": "string",
  "type": 0
} 
*/

  int? auditStatus;
  String? coverUrl;
  int? createdByType;
  List<String?>? environmentImages;
  int? floorMaterial;
  List<VenueModelHalves?>? halves;
  int? hasLight;
  int? id;
  int? isFree;
  double? latitude;
  double? longitude;
  String? name;
  String? createdByName;
  String? address;
  int? openTime;
  String? remark;
  int? type;
  int? distance;
  VenueModel({
    this.auditStatus,
    this.coverUrl,
    this.createdByType,
    this.environmentImages,
    this.floorMaterial,
    this.halves,
    this.hasLight,
    this.id,
    this.isFree,
    this.latitude,
    this.longitude,
    this.name,
    this.createdByName,
    this.address,
    this.openTime,
    this.remark,
    this.type,
    this.distance,
  });
  VenueModel.fromJson(Map<String, dynamic> json) {
    auditStatus = json['auditStatus']?.toInt();
    coverUrl = json['coverUrl']?.toString();
    createdByType = json['createdByType']?.toInt();
    if (json['environmentImages'] != null) {
      final v = json['environmentImages'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      environmentImages = arr0;
    }
    floorMaterial = json['floorMaterial']?.toInt();
    if (json['halves'] != null) {
      final v = json['halves'];
      final arr0 = <VenueModelHalves>[];
      v.forEach((v) {
        arr0.add(VenueModelHalves.fromJson(v));
      });
      halves = arr0;
    }
    hasLight = json['hasLight']?.toInt();
    id = json['id']?.toInt();
    isFree = json['isFree']?.toInt();
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    name = json['name']?.toString();
    createdByName = json['createdByName']?.toString();
    address = json['address']?.toString();
    openTime = json['openTime']?.toInt();
    remark = json['remark']?.toString();
    type = json['type']?.toInt();
    distance = json['distance']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['auditStatus'] = auditStatus;
    data['coverUrl'] = coverUrl;
    data['createdByType'] = createdByType;
    if (environmentImages != null) {
      final v = environmentImages;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v);
      }
      data['environmentImages'] = arr0;
    }
    data['floorMaterial'] = floorMaterial;
    if (halves != null) {
      final v = halves;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['halves'] = arr0;
    }
    data['hasLight'] = hasLight;
    data['id'] = id;
    data['isFree'] = isFree;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['name'] = name;
    data['createdByName'] = createdByName;
    data['address'] = address;
    data['openTime'] = openTime;
    data['remark'] = remark;
    data['type'] = type;
    data['distance'] = distance;
    return data;
  }
}
