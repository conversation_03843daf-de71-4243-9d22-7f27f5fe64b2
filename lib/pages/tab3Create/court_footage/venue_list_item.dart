import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

// import '../../generated/l10n.dart';

class VenueListItem extends StatelessWidget {
  final VenueModel model;
  const VenueListItem({super.key, required this.model});
  // static final desList = [
  //   "#${S.current.video_free}",
  //   S.current.match_reports_available
  // ];
  @override
  Widget build(BuildContext context) {
    // List<String> des = [];
    // if (model.isBuy) {
    //   des.add(desList.first);
    // }
    // if (model.isReport) {
    //   des.add(desList.last);
    // }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => AppPage.to(Routes.siteDetailPage, arguments: model.id),
      child: Padding(
        padding: EdgeInsets.only(bottom: 15.w, left: 15.w, right: 15.w),
        child: Stack(children: [
          Container(
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: CachedNetworkImage(
                        imageUrl: model.coverUrl ?? '',
                        width: 96.w,
                        height: 96.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 15.w),
                        child: SizedBox(
                          height: 96.w,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                      child: Text(
                                    maxLines: 2,
                                    model.name ?? '',
                                    style: TextStyles.semiBold14
                                        .copyWith(height: 1.71),
                                    overflow: TextOverflow.ellipsis,
                                  )),
                                  // if (model.isPremium == 1)
                                  //   WxAssets.images.advancedArena.image()
                                ],
                              ),
                              SizedBox(
                                height: 2.w,
                              ),
                              Expanded(
                                child: Text(
                                  _getTagsStr(model)
                                      .whereType<String>()
                                      .join(' | '),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyles.display12.copyWith(
                                    color: Colours.color5C5C6E,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  WxAssets.images.icLocation
                                      .image(width: 14, height: 14),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text(
                                    "距离您${model.distance}km",
                                    style: TextStyles.display12,
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (model.createdByType == 1)
            Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 60.w,
                  height: 20.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10.r),
                          topRight: Radius.circular(10.r))),
                  child: Text('官方创建',
                      style: TextStyles.semiBold.copyWith(fontSize: 10.sp)),
                ))
        ]),
      ),
    );
  }

  String removeDotIfLast(String input) {
    if (input.endsWith('·')) {
      return input.substring(0, input.length - 1);
    }
    return input;
  }

  List<String> _getTagsStr(VenueModel model) {
    final floorMaterial = ['木地板', '塑胶', '悬浮拼接地板', '水泥地'];
    final hasLight = ['有灯光', '无灯光'];
    // final isFree = ['免费', '收费'];
    final openTime = ['不对外开放', '全天开放', '白天开放', '晚上开放'];
    final type = ['室内', '室外'];
    var result = <String>[];
    // final tagList = tags.split(',');
    result.add(type[(model.type ?? 1) - 1]);
    result.add(openTime[(model.openTime ?? 1) - 1]);
    result.add(floorMaterial[(model.floorMaterial ?? 1) - 1]);
    result.add(hasLight[(model.hasLight ?? 1) - 1]);

    return result;
  }
}
