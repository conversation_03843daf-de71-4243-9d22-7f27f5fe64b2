import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/half_shooting_records_model.dart';
import 'package:shoot_z/network/model/shoot_ai_model.dart';
import 'package:shoot_z/network/model/shoot_goal_model.dart';
import 'package:shoot_z/network/model/shooting_info_model.dart';
import 'package:shoot_z/network/model/shooting_videos_model.dart';
import 'package:shoot_z/network/model/tencent_cos_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/tencentcos/FetchCredentials.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart';
import 'package:ui_packages/ui_packages.dart';

class SelfieShotInfoLogic extends GetxController {
  // 控制滚动位置的控制器
  final ScrollController scrollController = ScrollController();

  // 当前选中的头像索引
  var selectedIndex = 0.obs;

  var dataFag = {
    "isFrist": true,
  }.obs;
  TextEditingController nickNameController = TextEditingController();
  var shootingInfoModel = ShootingInfoModel().obs;
  // var halfShootingRecordsModel = HalfShootingRecordsModelResult().obs;
  var tencentCosModel = TencentCosModel();
  var type = "".obs; //1单人 2多人
  var trainingId = "".obs; //1单人 2多人
  var reportId = "".obs; //报告id
  //数据列表 Ai选身型
  RxList<ShootAiModel> aiDataList = <ShootAiModel>[].obs;
  var videosDataList = <ShootGoalModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    type.value = Get.arguments["type"];
    trainingId.value = Get.arguments["trainingId"];
    //getUpLoadVideoCos();
    // UserModel userModel = UserManager.instance.user!;
    log("SelfieShotInfoLogic ßtype=${type.value}");
  }

  @override
  void onReady() {
    super.onReady();
    getData();
  }

  getData() async {
    if (type.value == "4") {
      //多人投篮拍摄结束跳转到报告页面
      type.value = "2";
      log(type.value);
      getCreateReport([], "多人报告", trainingId.value);
      //录制的多人模式
      getAiOptionGoal(Get.context!);
      // if (aiDataList.isNotEmpty) {
      //   getAiOptionGoal(Get.context!);
      // } else {
      //   var isOk = await getAiOptionGoal(Get.context!);
      //   if (isOk) {
      //     getAiOptionGoalDialog(Get.context!);
      //   } else {
      //     getCreateReport([], "多人报告", trainingId.value);
      //   }
      // }
    } else if (type.value == "3") {
      type.value = "1";
      //录制的单人模式
      getCreateReport([], "单人报告", trainingId.value);
    } else {
      //投篮记录页面跳转到报告页面
      var halfShootingRecordsModel = Get.arguments["halfShootingRecordsModel"]
          as HalfShootingRecordsModelResult;
      trainingId.value = "${halfShootingRecordsModel.trainingId}";
      reportId.value = "${halfShootingRecordsModel.id}";
      getDataInfo(reportId.value);
      getVideoList(reportId.value);
    }
  }

  //获得自由投篮详情
  getDataInfo(String reportId) async {
    Map<String, dynamic> param = {
      'reportId': reportId,
    };
    WxLoading.show();
    var url = ApiUrl.halfShootingRecordsId(reportId);
    var res = await Api().get(url, queryParameters: param);
    log("getDataInfo2=${WxEnv.instance.apiUrl}\n${jsonEncode(res.data)}");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      shootingInfoModel.value = ShootingInfoModel.fromJson(res.data);
      shootingInfoModel.refresh();
      for (int i = 0;
          i < (shootingInfoModel.value.trainingReports?.length ?? 0);
          i++) {
        if (shootingInfoModel.value.trainingReports?[i]?.reportId.toString() ==
            reportId) {
          selectedIndex.refresh();
          // 计算滚动位置
          double position = i * 75.w; // 头像宽度加间距

          if (scrollController.hasClients) {
            // 关键检查
            // 执行平滑滚动
            scrollController.animateTo(
              position,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeOut,
            );
          }
        }
      }
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  // 滚动到指定头像
  void scrollToAvatar(int index, ShootingInfoModelTrainingReports item) {
    selectedIndex.value = index;
    selectedIndex.refresh();
    // 计算滚动位置
    double position = index * 75.w; // 头像宽度加间距
    // 执行平滑滚动
    scrollController.animateTo(
      position,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOut,
    );
    reportId.value = "${item.reportId}";
    getDataInfo(reportId.value);
    getVideoList(reportId.value);
  }

  //获得集锦列表
  getVideoList(String reportId) async {
    var param = {
      'id': trainingId.value,
      'reportId': reportId,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }

    log("getVideoList=$param");
    final res = await Api().get(
        ApiUrl.getShootingFragments(trainingId.value, reportId),
        queryParameters: param);
    // Map<String, dynamic> param = {
    //   'userId': UserManager.instance.user?.userId,
    //   'pageIndex': 1,
    //   'pageSize': 20,
    //   'trainType': type.value, //1单人 2多人
    //   'reportId': reportId, //reportId 报告id，0时查训练所有； 不为0时查制定报告的合成
    // };
    //     log("message!!!!!!$param");
    // var res =
    //     await Api().get(ApiUrl.halfShootingVideos, queryParameters: param);
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      WxLoading.dismiss();
      List list = res.data ?? [];
      List<ShootGoalModel> modelList =
          list.map((e) => ShootGoalModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      if (modelList.length > 2) {
        videosDataList.assignAll(modelList.sublist(0, 2));
      } else {
        videosDataList.assignAll(modelList);
      }
      videosDataList.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //ai选球弹窗
  Future<bool> getAiOptionGoal(BuildContext context) async {
    var param = {
      'id': trainingId.value,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    var url = await ApiUrl.getShootingClassifications(trainingId.value);
    final res = await Api().get(url, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
    if (res.isSuccessful()) {
      if (res.data == null) {
        WxLoading.showToast(S.current.no_ai_data);
        return false;
      }

      List list = res.data;
      List<ShootAiModel> modelList =
          list.map((e) => ShootAiModel.fromJson(e)).toList();
      log("getAiOptionGoal=${res.data}");
      aiDataList.assignAll(modelList);
      if (modelList.isEmpty) {
        WxLoading.showToast(S.current.no_ai_data);
      } else {
        return true;
      }
      return false;
    } else {
      WxLoading.showToast(res.message);
      return false;
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> getUpLoadVideoCos() async {
    TencentCosModel tencentCosModel = await fetchSessionCredentials2();
    await Cos().forceInvalidationCredential(); //强制使本地保存的临时密钥失效
    await Cos().initWithSessionCredential(FetchCredentials());
// 存储桶所在地域简称，例如广州地区是 ap-guangzhou
    String region = tencentCosModel.region ?? "ap-guangzhou";
// 创建 CosXmlServiceConfig 对象，根据需要修改默认的配置参数
    CosXmlServiceConfig serviceConfig = CosXmlServiceConfig(
      region: region,
      isDebuggable: true,
      isHttps: true,
    );
// 注册默认 COS Service
    Cos().registerDefaultService(serviceConfig);

// 创建 TransferConfig 对象，根据需要修改默认的配置参数
// TransferConfig 可以设置智能分块阈值 默认对大于或等于2M的文件自动进行分块上传，可以通过如下代码修改分块阈值
    TransferConfig transferConfig = TransferConfig(
      forceSimpleUpload: false,
      enableVerification: true,
      divisionForUpload: 2097152, // 设置大于等于 2M 的文件进行分块上传
      sliceSizeForUpload: 1048576, //设置默认分块大小为 1M
    );
// 注册默认 COS TransferManger
    await Cos().registerDefaultTransferManger(serviceConfig, transferConfig);
    await Future.delayed(const Duration(milliseconds: 300));
// 也可以通过 registerService 和 registerTransferManger 注册其他实例， 用于后续调用
// 一般用 region 作为注册的key
    // String newRegion = tencentCosModel.region ?? "ap-guangzhou";
    // Cos().registerService(newRegion, serviceConfig..region = newRegion);
    // Cos().registerTransferManger(
    //     newRegion, serviceConfig..region = newRegion, transferConfig);

    final remoteKey =
        "mobile/videos/${DateTime.now().millisecondsSinceEpoch}.mp4";
    var filePath2 = "/storage/emulated/0/Movies/1747040142000.mp4";
    var ishave = await checkIfFileExists(filePath2);
    log("uploadVideo0ishave=${ishave}-${filePath2}");
    uploadVideo(filePath2, remoteKey, tencentCosModel.bucketName ?? "");
  }

  Future<bool> checkIfFileExists(String filePath) async {
    // 构建目标文件路径
    String fullPath = filePath;

    // 检查文件是否存在
    final file = File(fullPath);
    bool exists = await file.exists();

    if (exists) {
      print("文件存在: $fullPath");
    } else {
      print("文件不存在: $fullPath");
    }
    return await file.exists();
  }

  Future<void> uploadVideo(
      String localFilePath, String remoteKey, String bucketName) async {
    // 获取 TransferManager
    CosTransferManger transferManager = Cos().getDefaultTransferManger();
    //CosTransferManger transferManager = Cos().getTransferManger("newRegion");
    // 存储桶名称，由 bucketname-appid 组成，appid 必须填入，可以在 COS 控制台查看存储桶名称。 https://console.cloud.tencent.com/cos5/bucket
    String bucket = bucketName;
    String cosPath = remoteKey; //对象在存储桶中的位置标识符，即称对象键
    String srcPath = localFilePath; //本地文件的绝对路径
    //若存在初始化分块上传的 UploadId，则赋值对应的 uploadId 值用于续传；否则，赋值 null
    String? _uploadId;

    // 上传成功回调
    successCallBack(Map<String?, String?>? header, CosXmlResult? result) {
      // todo 上传成功后的逻辑
      log("uploadVideo1=${result?.accessUrl}");
    }

    //上传失败回调
    failCallBack(clientException, serviceException) {
      // todo 上传失败后的逻辑
      log("uploadVideo2=clientException=${clientException.message}-${clientException.errorCode}-${clientException.details}\nserviceException=${serviceException}");
    }

    //上传状态回调, 可以查看任务过程
    stateCallback(state) {
      // todo notify transfer state
      log("uploadVideo3=${state}");
    }

    //上传进度回调
    progressCallBack(complete, target) {
      // todo Do something to update progress...
      //log("uploadVideo4=${complete}-${target}");
    }

    //初始化分块完成回调
    initMultipleUploadCallback(String bucket, String cosKey, String uploadId) {
      //用于下次续传上传的 uploadId
      _uploadId = uploadId;
      //log("uploadVideo5=${bucket}-${cosKey}-${uploadId}");
    }

    //开始上传
    // TransferTask transferTask =
    await transferManager.upload(bucket, cosPath,
        filePath: srcPath,
        uploadId: _uploadId,
        resultListener: ResultListener(successCallBack, failCallBack),
        stateCallback: stateCallback,
        progressCallBack: progressCallBack,
        initMultipleUploadCallback: initMultipleUploadCallback);
    //暂停任务
    //transferTask.pause();
    //恢复任务
    //transferTask.resume();
    //取消任务
    //transferTask.cancel();
  }

  Future<TencentCosModel> fetchSessionCredentials2() async {
    // 首先从您的临时密钥服务器获取包含了密钥信息的响应，例如：
    var httpClient = HttpClient();
    try {
      // 临时密钥服务器 url，临时密钥生成服务请参考 https://cloud.tencent.com/document/product/436/14048
      var stsUrl = "https://i.shootz.tech/mgr-api/common/sts";
      var request = await httpClient.getUrl(Uri.parse(stsUrl));
      var response = await request.close();
      if (response.statusCode == HttpStatus.OK) {
        var json = await response.transform(utf8.decoder).join();
        print(jsonEncode(json));
        // 然后解析响应，获取临时密钥信息
        var data = jsonDecode(json);
        // 最后返回临时密钥信息对象
        TencentCosModel tencentCosModel = TencentCosModel.fromJson(data);
        return tencentCosModel;
      } else {
        throw ArgumentError();
      }
    } catch (exception) {
      throw ArgumentError();
    }
  }

  //创建报告
  Future<void> getCreateReport(
      List<String> ids, String name, String trainingId) async {
    Map<String, dynamic> param2 = {"classifications": ids, "name": name};
    // Map<String, dynamic> param = {
    //   'trainingId': halfShootingRecordsModel.value.trainingId ?? 0,
    //   'req': param2,
    // };

    var url = await ApiUrl.getCreateReport(trainingId);
    log("getCreateReport=${param2}=${url}");

    var res = await Api().post(url, data: param2);
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      shootingInfoModel.value = ShootingInfoModel.fromJson(res.data);
      shootingInfoModel.refresh();
      reportId.value = shootingInfoModel.value.id ?? "";
      for (int i = 0;
          i < (shootingInfoModel.value.trainingReports?.length ?? 0);
          i++) {
        if ((shootingInfoModel.value.trainingReports?[i]?.reportId)
                .toString()
                .trim() ==
            reportId.value) {
          scrollToAvatar(i, shootingInfoModel.value.trainingReports![i]!);
        }
      }

      getVideoList(reportId.value);
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  Future<void> getDeleteVideo(ShootingVideosModel data) async {
    Map<String, dynamic> param2 = {"id": data.id};
    var url = await ApiUrl.getDeleteVideo(data.id ?? "");
    var res = await Api().delete(url, data: param2);
    log("$param2");
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      getVideoList(reportId.value);
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //ai选球弹窗
  void getAiOptionGoalDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 632,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.Please_select_your_photo,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      GridView.builder(
                          scrollDirection: Axis.vertical,
                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                          shrinkWrap: true,
                          physics:
                              const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 15,
                            mainAxisSpacing: 15,
                            childAspectRatio: 102 / 179,
                          ),
                          padding: EdgeInsets.only(bottom: 60.w, top: 0.w),
                          itemCount: aiDataList.length,
                          itemBuilder: (context, index) {
                            return Obx(() {
                              return GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  aiDataList[index].isSelect =
                                      !(aiDataList[index].isSelect ?? false);
                                  aiDataList.refresh();
                                },
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    MyImage(
                                      aiDataList[index].photo ?? '',
                                      fit: BoxFit.fill,
                                      width: 102.w,
                                      height: 179.w,
                                      isAssetImage: false,
                                      // errorImg: "home/index/df_banner_top"
                                      radius: 8.r,
                                    ),
                                    Container(
                                      width: 102.w,
                                      height: 179.w,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          color: !(aiDataList[index].isSelect ??
                                                  false)
                                              ? null
                                              : Colours.color50000000),
                                      child:
                                          !(aiDataList[index].isSelect ?? false)
                                              ? null
                                              : Container(
                                                  width: 20.w,
                                                  height: 20.w,
                                                  margin: EdgeInsets.only(
                                                      right: 8.w,
                                                      bottom: 3.w,
                                                      top: 8.w),
                                                  child: const Icon(
                                                    Icons.check,
                                                    color: Colours.white,
                                                    size: 20,
                                                  ),
                                                ),
                                    )
                                  ],
                                ),
                              );
                            });
                          }),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 46.w,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: Colours.colorEBEBEB,
                        borderRadius: BorderRadius.circular(23.r),
                      ),
                      margin: EdgeInsets.only(bottom: 20.w),
                      padding: EdgeInsets.only(
                          left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                      child: TextField(
                        controller: nickNameController,
                        style: TextStyle(
                            color: Colours.color333333, fontSize: 14.sp),
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                              RegExp(r'[" "]')), // 只允许输入数字
                          LengthLimitingTextInputFormatter(15), // 限制输入长度为7
                        ],
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.current.selfile_shot_info3,
                          contentPadding:
                              EdgeInsets.only(left: 10.w, bottom: 10.w),
                          hintStyle: TextStyle(
                            color: Colours.color999999,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        List<ShootAiModel> filteredNumbers =
                            aiDataList.where((value) {
                          bool result = value.isSelect ?? false;
                          return result;
                        }).toList();
                        if (filteredNumbers.isEmpty) {
                          WxLoading.showToast(S.current.select_your_photo_tips);
                          return;
                        }
                        if (nickNameController.text.trim().isEmpty) {
                          WxLoading.showToast(S.current.selfile_shot_info3);
                          return;
                        }
                        AppPage.back();
                        List<String> ids = filteredNumbers
                            .map((item) => item.id ?? "")
                            .toList();

                        getCreateReport(ids, nickNameController.text.trim(),
                            trainingId.value);
                      },
                      child: Container(
                        height: 46.w,
                        width: double.infinity,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(bottom: 10.w),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          S.current.select_your_photo_tips2,
                          style: TextStyles.titleMedium18
                              .copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }

  void showDeleteDialog(var index, var reportId) {
    Get.dialog(CustomAlertDialog(
      title: S.current.dialog_title,
      content: S.current.confirm_deletion,
      onPressed: () async {
        AppPage.back();

        var url = await ApiUrl.recordsDelete(reportId);
        log("showDeleteDialog=$url");
        final res = await Api().delete(url);
        log("showDeleteDialog=$res");
        if (res.isSuccessful()) {
          if ((shootingInfoModel.value.trainingReports?.length ?? 0) > 1) {
            if (index == 0) {
              scrollToAvatar(
                  1, shootingInfoModel.value.trainingReports![index]!);
            } else {
              scrollToAvatar(
                  0, shootingInfoModel.value.trainingReports![index]!);
            }
          } else {
            AppPage.back(result: true);
          }
        }
      },
    ));
  }
}
