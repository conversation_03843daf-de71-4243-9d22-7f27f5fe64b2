import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/shoot_ai_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_info/selfie_shot_info_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 入口
class SelfieShotInfoPage extends StatelessWidget {
  SelfieShotInfoPage({super.key});
  final logic = Get.put(SelfieShotInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.color0F0F16,
      body: _createTeamWidget(context),
      // bottomNavigationBar: Obx(() {
      //   return logic.reportId.value == ""
      //       ? const SizedBox()
      //       : GestureDetector(
      //           behavior: HitTestBehavior.translucent,
      //           onTap: () {
      //             if (logic.reportId.value == "") {
      //               WxLoading.showToast("请先点击切换球员数据，生成报告");
      //             } else {
      //               //去剪辑
      //               AppPage.to(Routes.shootGoalPage, arguments: {
      //                 "trainingId": logic.trainingId.value,
      //                 "reportId": logic.reportId.value,
      //                 "type": logic.type.value, //1单人 2多人
      //               });
      //             }
      //           },
      //           child: Container(
      //             height: 50.w,
      //             width: double.infinity,
      //             alignment: Alignment.center,
      //             margin: EdgeInsets.only(
      //                 left: 20.w, right: 20.w, bottom: 25.w, top: 10.w),
      //             decoration: BoxDecoration(
      //               color: Colours.color282735,
      //               borderRadius: BorderRadius.all(Radius.circular(28.r)),
      //               gradient: const LinearGradient(
      //                 colors: [Colours.color7732ED, Colours.colorA555EF],
      //                 begin: Alignment.bottomLeft,
      //                 end: Alignment.bottomRight,
      //               ),
      //             ),
      //             child: Text(
      //               S.current.player_report_tips18,
      //               style: TextStyles.display16.copyWith(
      //                   fontSize: 14.sp,
      //                   color: Colours.white,
      //                   fontWeight: FontWeight.w600),
      //             ),
      //           ),
      //         );
      // }),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _myAppBarWidget(context),
              _infoWidget(context),
            ]),
      );
    });
  }

  Widget _myAppBarWidget(BuildContext context) {
    return Obx(() {
      return Stack(
        children: [
          Container(
            width: double.infinity,
            // height: 260.w, // 282.w,
            constraints: BoxConstraints(
              minHeight: 260.w,
              maxHeight: 302.w,
            ),
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF])),
          ),
          Container(
            width: double.infinity,
            // height: 260.w, // 282.w,
            constraints: BoxConstraints(
              minHeight: 260.w,
              maxHeight: 302.w,
            ),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  height: 50.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 3.w,
                      ),
                      IconButton(
                          onPressed: () {
                            AppPage.back();
                          },
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 20,
                          )),
                      const Spacer(),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () => MyShareH5.getShareH5(
                            ShareHalfShootingReport(
                                userId: UserManager.instance.user?.userId ?? "",
                                reportId: logic.reportId.value,
                                trainType: logic.type.value)),
                        child: Container(
                          width: 40.w,
                          height: 40.w,
                          margin: EdgeInsets.only(right: 5.w),
                          alignment: Alignment.center,
                          child: WxAssets.images.share3.image(
                              color: Colors.white, width: 22.w, height: 22.w),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 15.w,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    ((logic.shootingInfoModel.value.trainingReports?.length ??
                                0) ==
                            0)
                        ? Container(
                            height: 95.w,
                            margin: EdgeInsets.only(left: 15.w),
                            child: Container(
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 10),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  // 头像边框样式处理
                                  Container(
                                    padding: const EdgeInsets.all(3),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 3,
                                      ),
                                    ),
                                    // 头像
                                    child: WxAssets.images.icEidtHead
                                        .image(width: 55.w, height: 55.w),
                                  ),
                                  SizedBox(height: 10.w),
                                  Text(
                                    logic.shootingInfoModel.value.title ?? "",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ))
                        : Expanded(
                            child: Container(
                              height: 95.w,
                              child: ListView.builder(
                                shrinkWrap: true,
                                controller: logic.scrollController,
                                scrollDirection: Axis.horizontal,
                                itemCount: logic.shootingInfoModel.value
                                        .trainingReports?.length ??
                                    0,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () => logic.scrollToAvatar(
                                          index,
                                          logic.shootingInfoModel.value
                                              .trainingReports![index]!),
                                      child: Container(
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Stack(
                                          alignment: Alignment.topRight,
                                          children: [
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                // 头像边框样式处理
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(3),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color: logic.selectedIndex
                                                                  .value ==
                                                              index
                                                          ? Colors.white
                                                          : Colors.transparent,
                                                      width: 3,
                                                    ),
                                                  ),
                                                  // 头像
                                                  child: WxAssets
                                                      .images.icEidtHead
                                                      .image(
                                                          width: logic.selectedIndex
                                                                      .value ==
                                                                  index
                                                              ? 55.w
                                                              : 40.w,
                                                          height: logic
                                                                      .selectedIndex
                                                                      .value ==
                                                                  index
                                                              ? 55.w
                                                              : 40.w),
                                                ),
                                                SizedBox(height: 10.w),
                                                Text(
                                                  logic
                                                          .shootingInfoModel
                                                          .value
                                                          .trainingReports?[
                                                              index]
                                                          ?.title ??
                                                      "",
                                                  style: TextStyle(
                                                    fontSize: 12.sp,
                                                    color: logic.selectedIndex
                                                                .value ==
                                                            index
                                                        ? Colors.white
                                                        : Colours.colorA8A8BC,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            if (logic.selectedIndex.value ==
                                                index)
                                              GestureDetector(
                                                behavior:
                                                    HitTestBehavior.translucent,
                                                onTap: () {
                                                  logic.showDeleteDialog(
                                                      index,
                                                      logic
                                                          .shootingInfoModel
                                                          .value
                                                          .trainingReports![
                                                              index]
                                                          ?.reportId!);
                                                },
                                                child: WxAssets.images.close2
                                                    .image(
                                                  width: 17.w,
                                                  height: 17.w,
                                                ),
                                              )
                                          ],
                                        ),
                                      ),
                                    );
                                  });
                                },
                              ),
                            ),
                          ),
                    SizedBox(
                      width: 15.w,
                    ),
                    if (logic.type.value == "2")
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          //切换身形
                          if (logic.aiDataList.isNotEmpty) {
                            getAiOptionGoalDialog(context);
                          } else {
                            var isOk = await logic.getAiOptionGoal(context);
                            if (isOk) {
                              getAiOptionGoalDialog(context);
                            }
                          }
                        },
                        child: Column(
                          children: [
                            Container(
                              width: 40.w,
                              height: 40.w,
                              decoration: BoxDecoration(
                                  color: Colours.color7F21E2,
                                  borderRadius: BorderRadius.circular(20.r)),
                              child: WxAssets.images.halfAdd
                                  .image(width: 13.w, height: 13.w),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              "切换球友数据",
                              style: TextStyles.regular.copyWith(
                                  fontWeight: FontWeight.w400, fontSize: 12.sp),
                            ),
                          ],
                        ),
                      ),
                    SizedBox(
                      width: 15.w,
                    ),
                  ],
                ),
                SizedBox(
                  height: 7.w,
                ),
                Container(
                  width: double.infinity,
                  height: 55.w,
                  margin: EdgeInsets.only(left: 5.w, right: 5.w),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: List.generate(7, (index) {
                        return index == 1 || index == 3 || index == 5
                            ? Container(
                                width: 1.w,
                                height: 35.w,
                                color: Colours.colorD5B6F8,
                              )
                            : Expanded(
                                child: Column(
                                  children: [
                                    Text(
                                      index == 0
                                          ? "${logic.shootingInfoModel.value.hitCount ?? "0"}"
                                          : index == 2
                                              ? "${logic.shootingInfoModel.value.shotCount ?? "0"}"
                                              : index == 4
                                                  ? "${logic.shootingInfoModel.value.rate ?? "0"}%"
                                                  : "${logic.shootingInfoModel.value.duration ?? "0"}分",
                                      style: TextStyle(
                                          color: Colours.white,
                                          fontSize: 20.sp),
                                    ),
                                    SizedBox(
                                      height: 5.w,
                                    ),
                                    Text(
                                      index == 0
                                          ? S.current.selfile_shot8
                                          : index == 2
                                              ? S.current.selfile_shot7
                                              : index == 4
                                                  ? S.current.selfile_shot9
                                                  : S.current.selfile_shot6,
                                      style: TextStyle(
                                          color: Colours.colorC396F5,
                                          fontSize: 12.sp),
                                    ),
                                  ],
                                ),
                              );
                      })),
                ),
                SizedBox(
                  height: 15.w,
                ),
                const Spacer(),
                Container(
                  width: double.infinity,
                  height: 20.w,
                  decoration: BoxDecoration(
                      color: Colours.bg_color,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20.r),
                          topRight: Radius.circular(20.r))),
                ),
              ],
            ),
          )
        ],
      );
    });
  }

  Widget _infoWidget(BuildContext context) {
    return (logic.dataFag["isFrist"] as bool)
        ? buildLoad()
        :
        // logic.teamHomeModel.value.teamId == ""
        //     ? myNoDataView(
        //         context,
        //         msg: S.current.No_data_available,
        //         imagewidget: WxAssets.images.icGameNo
        //             .image(width: 150.w, height: 150.w),
        //       )
        //     :
        Column(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 15.w, right: 15.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 40.w,
                      alignment: Alignment.centerLeft,
                      margin: EdgeInsets.only(top: 5.w, bottom: 10.w),
                      child: Row(
                        children: [
                          WxAssets.images.selfieShot1
                              .image(height: 10.w, width: 10.w),
                          SizedBox(
                            width: 3.w,
                          ),
                          Text(
                            S.current.selfile_shot_info1,
                            style: TextStyle(
                                color: Colours.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: ScreenUtil().screenWidth - 30.w,
                      height: (ScreenUtil().screenWidth - 30.w) / 15 * 14,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                            image: AssetImage("assets/images/half_shoot1.png"),
                            fit: BoxFit.fill),
                      ),
                      child: Stack(
                        children: List.generate(
                            (logic.shootingInfoModel.value.points?.length ?? 0)
                                .clamp(0, 100), (index) {
                          return Positioned(
                              left: ((logic.shootingInfoModel.value
                                                  .points?[index]?.shootX ??
                                              0) *
                                          (ScreenUtil().screenWidth - 30.w)) /
                                      1500 -
                                  5.w,
                              top: (((logic.shootingInfoModel.value
                                                  .points?[index]?.shootY ??
                                              0) *
                                          ((ScreenUtil().screenWidth - 30.w) /
                                              15 *
                                              14)) /
                                      1400) -
                                  5.w,
                              child: (logic.shootingInfoModel.value
                                          .points?[index]?.hit ??
                                      false)
                                  ? Container(
                                      width: 10.w,
                                      height: 10.w,
                                      decoration: BoxDecoration(
                                          //  color: Colours.color9045EE,
                                          border: Border.all(
                                            width: 3.w,
                                            color: Colours.color9045EE,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(5.r)),
                                    )
                                  : WxAssets.images.halfShoot3 //halfShoot2
                                      .image(width: 10.w, height: 10.w));
                        }),
                      ),
                    ),
                    Container(
                      height: 40.w,
                      alignment: Alignment.centerLeft,
                      margin: EdgeInsets.only(top: 30.w, bottom: 20.w),
                      child: Row(
                        children: [
                          WxAssets.images.selfieShot1
                              .image(height: 10.w, width: 10.w),
                          SizedBox(
                            width: 3.w,
                          ),
                          Text(
                            S.current.selfile_shot_info2,
                            style: TextStyle(
                                color: Colours.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                          const Spacer(),
                          if (logic.videosDataList.isNotEmpty)
                            GestureDetector(
                              onTap: () {
                                //去剪辑
                                if (logic.reportId.value == "") {
                                  WxLoading.showToast("请先点击切换球员数据，生成报告");
                                } else {
                                  //去剪辑
                                  AppPage.to(Routes.shootGoalPage, arguments: {
                                    "trainingId": logic.trainingId.value,
                                    "reportId": logic.reportId.value,
                                    "type": logic.type.value, //1单人 2多人
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.only(
                                    left: 17.w,
                                    top: 8.w,
                                    bottom: 8.w,
                                    right: 10.w),
                                decoration: BoxDecoration(
                                    border: Border.all(
                                      width: 1.w,
                                      color: const Color(0xffFF7C4CCE),
                                    ),
                                    borderRadius: BorderRadius.circular(30.r)),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      S.current.player_report_tips18,
                                      style: TextStyles.display12
                                          .copyWith(color: Colours.white),
                                    ),
                                    const SizedBox(
                                      width: 2,
                                    ),
                                    WxAssets.images.icArrowRight
                                        .image(width: 14, height: 14)
                                  ],
                                ),
                              ),
                            )
                        ],
                      ),
                    ),
                    logic.videosDataList.isEmpty
                        ? Container(
                            margin: EdgeInsets.only(top: 30.w),
                            child: Column(
                              children: [
                                WxAssets.images.noVideos
                                    .image(width: 120.w, height: 60.w),
                                SizedBox(
                                  height: 20.w,
                                ),
                                MyText(
                                  S.current.selfile_shot_info4,
                                  size: 14,
                                  color: Colors.white,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    if (logic.reportId.value == "") {
                                      WxLoading.showToast("请先点击切换球员数据，生成报告");
                                    } else {
                                      //去剪辑
                                      AppPage.to(Routes.shootGoalPage,
                                          arguments: {
                                            "trainingId":
                                                logic.trainingId.value,
                                            "reportId": logic.reportId.value,
                                            "type": logic.type.value, //1单人 2多人
                                          });
                                    }
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    height: 40.w,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                        left: 94.w,
                                        right: 94.w,
                                        top: 30.w,
                                        bottom: 25.w),
                                    decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          colors: [
                                            Colours.color7732ED,
                                            Colours.colorA555EF
                                          ],
                                          begin: Alignment.bottomLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(20.r)),
                                    child: Text(
                                      S.current.player_report_tips18,
                                      style: TextStyles.regular
                                          .copyWith(color: Colours.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Container(
                            // height: 55.w,
                            margin: EdgeInsets.only(left: 0.w, right: 0.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r)),
                            child: GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 11,
                                  mainAxisSpacing: 11,
                                  childAspectRatio: 152 / 116,
                                ),
                                padding: EdgeInsets.only(bottom: 60.w),
                                itemCount: logic.videosDataList.length,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        //去剪辑
                                        AppPage.to(Routes.shootGoalPage,
                                            arguments: {
                                              "trainingId":
                                                  logic.trainingId.value,
                                              "reportId": logic.reportId.value,
                                              "type":
                                                  logic.type.value, //1单人 2多人
                                            });
                                        // // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                        // switch (logic
                                        //     .videosDataList[index].status) {
                                        //   case 0:
                                        //   case 1:
                                        //     WxLoading.showToast(
                                        //       S.current.selfile_shot_info5,
                                        //     );
                                        //     break;
                                        //   case 2:
                                        //     AppPage.to(Routes.videoPath,
                                        //         arguments: {
                                        //           "videoPath": logic
                                        //               .videosDataList[index]
                                        //               .videoPath,
                                        //           "teamName": S.current
                                        //               .selfile_shot_info6,
                                        //           "isShowShareUpdate": "1",
                                        //           "videoId": logic
                                        //               .videosDataList[index].id,
                                        //         }).then((onValue) {
                                        //       if (onValue == true) {
                                        //         logic.getVideoList(
                                        //             logic.reportId.value);
                                        //       }
                                        //     });
                                        //     ;
                                        //     break;
                                        //   case 3:
                                        //     // WxLoading.showToast(
                                        //     //     S.current.selfile_shot_info7);
                                        //     getMyDialog(
                                        //       S.current.selfile_shot_info7,
                                        //       S.current.delete,
                                        //       content:
                                        //           S.current.selfile_shot_info8,
                                        //       () {
                                        //         AppPage.back();
                                        //         logic.getDeleteVideo(logic
                                        //             .videosDataList[index]);
                                        //       },
                                        //       isShowClose: false,
                                        //       btnIsHorizontal: true,
                                        //       btnText2: S.current.cancel,
                                        //       onPressed2: () {
                                        //         AppPage.back();
                                        //       },
                                        //     );
                                        //     break;
                                        // }
                                      },
                                      child: Column(
                                        children: [
                                          Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              MyImage(
                                                logic.videosDataList[index]
                                                        .cover ??
                                                    "",
                                                width: double.infinity,
                                                height: 94.w,
                                                radius: 8.r,
                                                fit: BoxFit.fill,
                                                errorImage: index == 0
                                                    ? "shoot_place_holder_img1.png"
                                                    : 'shoot_place_holder_img2.png',
                                                placeholderImage: index == 0
                                                    ? "shoot_place_holder_img1.png"
                                                    : 'shoot_place_holder_img2.png',
                                              ),
                                              Positioned(
                                                  left: 0,
                                                  top: 0,
                                                  child: Container(
                                                    padding: EdgeInsets.only(
                                                        left: 10.w,
                                                        right: 10.w,
                                                        top: 6.w,
                                                        bottom: 6.w),
                                                    decoration: BoxDecoration(
                                                        color: (logic
                                                                    .videosDataList[
                                                                        index]
                                                                    .hit ??
                                                                false)
                                                            ? Colours
                                                                .colorA44EFF
                                                            : Colours
                                                                .color999999,
                                                        borderRadius:
                                                            BorderRadius.only(
                                                                topLeft: Radius
                                                                    .circular(
                                                                        8.r),
                                                                bottomRight: Radius
                                                                    .circular(
                                                                        8.r))),
                                                    child: Text(
                                                      logic
                                                                  .videosDataList[
                                                                      index]
                                                                  .shootType ==
                                                              1
                                                          ? "两分"
                                                          : logic
                                                                      .videosDataList[
                                                                          index]
                                                                      .shootType ==
                                                                  2
                                                              ? "三分"
                                                              : logic.videosDataList[index]
                                                                          .shootType ==
                                                                      4
                                                                  ? "罚球"
                                                                  : "",
                                                      style: TextStyles.semiBold
                                                          .copyWith(
                                                              fontSize: 10.sp),
                                                    ),
                                                  )),
                                              Positioned(
                                                  bottom: 5.w,
                                                  right: 5.w,
                                                  child: Container(
                                                    height: 13.w,
                                                    decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius.all(
                                                                Radius.circular(
                                                                    2.r)),
                                                        color: const Color(
                                                            0xA6000000)),
                                                    padding: EdgeInsets.only(
                                                        left: 5.w,
                                                        right: 5.w,
                                                        top: 2,
                                                        bottom: 2),
                                                    child: Text(
                                                      logic
                                                              .videosDataList[
                                                                  index]
                                                              .time ??
                                                          "",
                                                      textAlign:
                                                          TextAlign.right,
                                                      style: TextStyles.regular
                                                          .copyWith(
                                                        fontSize: 10.sp,
                                                      ),
                                                    ),
                                                  )),
                                              // Positioned(
                                              //   right: 0.w,
                                              //   top: 0.w,
                                              //   child: Container(
                                              //     padding: EdgeInsets.only(
                                              //         left: 10.w,
                                              //         right: 10.w,
                                              //         top: 5.w,
                                              //         bottom: 5.w),
                                              //     decoration: BoxDecoration(
                                              //         color: logic
                                              //                     .videosDataList[
                                              //                         index]
                                              //                     .status ==
                                              //                 0
                                              //             ? Colours.colorA44EFF
                                              //             : logic
                                              //                         .videosDataList[
                                              //                             index]
                                              //                         .status ==
                                              //                     1
                                              //                 ? Colours
                                              //                     .colorA44EFF
                                              //                 : logic.videosDataList[index].status ==
                                              //                         2
                                              //                     ? Colors.green
                                              //                     : Colours
                                              //                         .colorA54040,
                                              //         borderRadius:
                                              //             BorderRadius.only(
                                              //                 bottomLeft: Radius
                                              //                     .circular(
                                              //                         8.r),
                                              //                 topRight: Radius
                                              //                     .circular(
                                              //                         8.r))),
                                              //     child: Text(
                                              //       // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                              //       logic.videosDataList[index]
                                              //                   .status ==
                                              //               0
                                              //           ? "待合成"
                                              //           : logic
                                              //                       .videosDataList[
                                              //                           index]
                                              //                       .status ==
                                              //                   1
                                              //               ? "合成中"
                                              //               : logic.videosDataList[index]
                                              //                           .status ==
                                              //                       2
                                              //                   ? "合成完成"
                                              //                   : "失败",
                                              //       textAlign: TextAlign.right,
                                              //       style: TextStyles.medium
                                              //           .copyWith(
                                              //               fontSize: 10.sp,
                                              //               color:
                                              //                   Colours.white),
                                              //     ),
                                              //   ),
                                              // ),
                                              // if (logic.videosDataList[index]
                                              //         .status ==
                                              //     2)
                                              WxAssets.images.selfieShotPlay
                                                  .image(
                                                      width: 32.w, height: 32.w)
                                            ],
                                          ),
                                          // Container(
                                          //   padding: EdgeInsets.only(
                                          //       left: 7.w,
                                          //       right: 7.w,
                                          //       top: 10.w,
                                          //       bottom: 5.w),
                                          //   decoration: BoxDecoration(
                                          //       color: Colours.color50000000,
                                          //       borderRadius: BorderRadius.only(
                                          //           topLeft:
                                          //               Radius.circular(4.r),
                                          //           topRight:
                                          //               Radius.circular(4.r),
                                          //           bottomLeft:
                                          //               Radius.circular(4.r),
                                          //           bottomRight:
                                          //               Radius.circular(12.r))),
                                          //   child: Text(
                                          //     logic.videosDataList[index]
                                          //             .time ??
                                          //         "",
                                          //     textAlign: TextAlign.left,
                                          //     maxLines: 1,
                                          //     style: TextStyles.medium.copyWith(
                                          //         fontSize: 14.sp,
                                          //         color: Colours.color5C5C6E),
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    );
                                  });
                                }),
                          ),
                    SizedBox(
                      height: 50.w,
                    )
                  ],
                ),
              ),
            ],
          );
  }

  //ai选球弹窗
  void getAiOptionGoalDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 632,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.Please_select_your_photo,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      GridView.builder(
                          scrollDirection: Axis.vertical,
                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                          shrinkWrap: true,
                          physics:
                              const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 15,
                            mainAxisSpacing: 15,
                            childAspectRatio: 102 / 179,
                          ),
                          padding: EdgeInsets.only(bottom: 60.w, top: 0.w),
                          itemCount: logic.aiDataList.length,
                          itemBuilder: (context, index) {
                            return Obx(() {
                              return GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  logic.aiDataList[index].isSelect =
                                      !(logic.aiDataList[index].isSelect ??
                                          false);
                                  logic.aiDataList.refresh();
                                },
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    MyImage(
                                      logic.aiDataList[index].photo ?? '',
                                      fit: BoxFit.fill,
                                      width: 102.w,
                                      height: 179.w,
                                      isAssetImage: false,
                                      // errorImg: "home/index/df_banner_top"
                                      radius: 8.r,
                                    ),
                                    Container(
                                      width: 102.w,
                                      height: 179.w,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          color: !(logic.aiDataList[index]
                                                      .isSelect ??
                                                  false)
                                              ? null
                                              : Colours.color50000000),
                                      child:
                                          !(logic.aiDataList[index].isSelect ??
                                                  false)
                                              ? null
                                              : Container(
                                                  width: 20.w,
                                                  height: 20.w,
                                                  margin: EdgeInsets.only(
                                                      right: 8.w,
                                                      bottom: 3.w,
                                                      top: 8.w),
                                                  child: const Icon(
                                                    Icons.check,
                                                    color: Colours.white,
                                                    size: 20,
                                                  ),
                                                ),
                                    )
                                  ],
                                ),
                              );
                            });
                          }),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 46.w,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: Colours.colorEBEBEB,
                        borderRadius: BorderRadius.circular(23.r),
                      ),
                      margin: EdgeInsets.only(bottom: 20.w),
                      padding: EdgeInsets.only(
                          left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                      child: TextField(
                        controller: logic.nickNameController,
                        style: TextStyle(
                            color: Colours.color333333, fontSize: 14.sp),
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                              RegExp(r'[" "]')), // 只允许输入数字
                          LengthLimitingTextInputFormatter(15), // 限制输入长度为7
                        ],
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.current.selfile_shot_info3,
                          contentPadding:
                              EdgeInsets.only(left: 10.w, bottom: 10.w),
                          hintStyle: TextStyle(
                            color: Colours.color999999,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        List<ShootAiModel> filteredNumbers =
                            logic.aiDataList.where((value) {
                          bool result = value.isSelect ?? false;
                          return result;
                        }).toList();
                        if (filteredNumbers.isEmpty) {
                          WxLoading.showToast(S.current.select_your_photo_tips);
                          return;
                        }
                        if (logic.nickNameController.text.trim().isEmpty) {
                          WxLoading.showToast(S.current.selfile_shot_info3);
                          return;
                        }
                        AppPage.back();
                        List<String> ids = filteredNumbers
                            .map((item) => item.id ?? "")
                            .toList();

                        logic.getCreateReport(
                            ids,
                            logic.nickNameController.text.trim(),
                            logic.trainingId.value);
                      },
                      child: Container(
                        height: 46.w,
                        width: double.infinity,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(bottom: 10.w),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          S.current.select_your_photo_tips2,
                          style: TextStyles.titleMedium18
                              .copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20.w,
                ),
                // 其他部件
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
              ],
            ),
          );
        });
      },
    );
  }
}
