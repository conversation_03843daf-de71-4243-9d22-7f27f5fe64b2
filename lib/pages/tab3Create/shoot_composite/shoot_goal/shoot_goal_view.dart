// ignore_for_file: use_build_context_synchronously
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_goal/shoot_goal_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场自由投篮->去剪辑 选球片段
class ShootGoalPage extends StatelessWidget {
  ShootGoalPage({super.key});

  final logic = Get.put(ShootGoalLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.option_player_goal_title),
      ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : KeepAliveWidget(
                child: Stack(
                  children: [
                    SafeArea(
                      bottom: false,
                      child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //视频播放器
                            _videoWidget(),
                            //选择时间
                            _chooseTime(context),
                            //选球
                            Expanded(child: _optionGoalWidget(context)),
                          ]),
                    ),
                  ],
                ),
              );
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? const SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    bottom: 25.w, left: 5.w, right: 15.w, top: 10.w),
                child: Row(
                  children: [
                    Container(
                      width: 44.w,
                      height: 44.w,
                      margin: EdgeInsets.only(left: 25.w, right: 10.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.r),
                          color: Colours.color282735),
                      child:
                          WxAssets.images.cart.image(width: 44.w, height: 44.w),
                    ),
                    Container(
                      height: 44.w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            S.current.selected,
                            style: TextStyles.regular.copyWith(
                                fontSize: 15.sp, color: Colours.color9393A5),
                          ),
                          SizedBox(
                            height: 6.w,
                          ),
                          RichText(
                            textAlign: TextAlign.left,
                            text: TextSpan(
                                text: "${logic.dataList.where((value) {
                                      return value.checked == true;
                                    }).toList().length}\t",
                                style: TextStyle(
                                    color: Colours.colorA44EFF,
                                    fontSize: 15.sp,
                                    height: 1,
                                    fontWeight: FontWeight.w600),
                                children: <InlineSpan>[
                                  TextSpan(
                                      text: S.current.option_player_goal_tip1,
                                      style: TextStyle(
                                          color: Colours.color9393A5,
                                          fontSize: 15.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400)),
                                ]),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        if (await Utils.isToLogin()) {
                          if (await logic.getVideosUsedShots2(
                              logic.arenaID.value,
                              type: 1)) {
                            //没有合成次数和vip的时候弹窗
                            getVIPDialog();
                            // logic.getShareWx(0);
                          } else {
                            var list = logic.dataList.where((value) {
                              return value.checked == true;
                            }).toList();
                            if (list.isEmpty) {
                              WxLoading.showToast("请至少选择1个视频片段");
                            } else {
                              logic.videoController.pause();
                              log("optionPlayerGoalModel3=${logic.dataList[0].checked}");
                              //去合成进球
                              AppPage.to(Routes.shootCompositeVideoPage,
                                  arguments: {
                                    "trainingId": logic.trainingId.value,
                                    "reportId": logic.reportId.value,
                                    "type": logic.type.value,
                                    "list": list,
                                  }).then((v) {});
                              var list2 = logic.dataList.where((value) {
                                return value.checked == true;
                              }).toList();
                              log("optionPlayerGoalModel31=${list2.length}");
                            }
                          }
                        }
                      },
                      child: Container(
                        height: 46.w,
                        width: 175.w,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(left: 14.w, right: 0),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          S.current.option_player_goal_tip2,
                          style: TextStyles.display16.copyWith(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),

      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     // 按钮点击事件处理代码
      //     //logic.getDateInfo();
      //     logic.aIoptionCheckID.value =
      //         logic.aIoptionCheckID.value == "" ? "1" : "";
      //   },
      //   tooltip: 'Increment Counter', // 提示信息（长按显示）
      //   child: Icon(Icons.add), // 按钮内部的图标
      //   backgroundColor: Colors.pink, // 按钮背景颜色
      // ),
      floatingActionButtonLocation:
          FloatingActionButtonLocation.endFloat, // 默认位置
    );
  }

  //没有合成次数和vip的时候弹窗
  void getVIPDialog() {
    return getMyDialog2(
      "",
      S.current.sure,
      () {
        AppPage.back();
      },
      // btnText2: S.current.Go_points,
      // onPressed2: () {
      //   AppPage.back();
      //   AppPage.to(Routes.pointsPage);
      // },
      isShowClose: true,
      imageAsset: "vip_dialog10.png",
      imgHeight: 165.w,
      btnIsHorizontal: true,
      imgWidth: double.infinity,
      bottomBtnWidget: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                AppPage.back();
                AppPage.to(Routes.vipPage).then((v) async {
                  await Future.delayed(const Duration(milliseconds: 1000));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    if (!await logic.getVideosUsedShots2(logic.arenaID.value,
                        type: 1)) {
                      //去合成进球
                      AppPage.to(Routes.compositeVideoPage, arguments: {
                        "arenaID": logic.arenaID.value,
                      }).then((v) {});
                    }
                  }
                });
                //int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
                UserManager.instance.postApmTracking(0,
                    nowPage: Routes.shootGoalPage,
                    toPage: Routes.vipPage,
                    subPage: "",
                    remark: "自由半场-一键成片按钮",
                    content: "未开通会员");
              },
              child: Container(
                height: 46.w,
                width: double.infinity,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  left: 20.w,
                  top: 15.w,
                  right: 7.5.w,
                ),
                decoration: BoxDecoration(
                  color: Colours.color282735,
                  borderRadius: BorderRadius.all(Radius.circular(28.r)),
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Text(
                  S.current.Open_immediately,
                  style: TextStyles.regular
                      .copyWith(fontSize: 14.sp, color: Colours.white),
                ),
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () async {
              AppPage.back();
              await Future.delayed(const Duration(milliseconds: 500));
              getVIPInvitationDialog2();
            },
            child: Column(
              children: [
                Transform.translate(
                  offset: const Offset(-10, 10),
                  child: MyImage(
                    "vip_dialog11.png",
                    width: 154.w,
                    // height: 30.w,
                    fit: BoxFit.fitWidth,
                    isAssetImage: true,
                  ),
                ),
                Container(
                  height: 46.w,
                  width: 144,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                    left: 7.5.w,
                    right: 20.w,
                  ),
                  decoration: BoxDecoration(
                    color: Colours.color282735,
                    borderRadius: BorderRadius.all(Radius.circular(28.r)),
                    gradient: const LinearGradient(
                      colors: [Colours.colorBFFF9C, Colours.colorEEFC62],
                      begin: Alignment.bottomLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Text(
                    S.current.vip_dialog_text10,
                    style: TextStyles.regular
                        .copyWith(fontSize: 14.sp, color: Colours.color333333),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          margin: EdgeInsets.only(left: 40.w, right: 40.w, top: 10.w),
          child: Wrap(
            spacing: 21.w,
            runSpacing: 10.w,
            children: List.generate(logic.state.vipDialogList.length, (index) {
              return Container(
                width: 54.w,
                alignment: Alignment.center,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 44.w,
                      height: 44.w,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          MyImage(
                            "vip_dialog12.png",
                            width: 44.w,
                            height: 44.w,
                            isAssetImage: true,
                            radius: 11.r,
                          ),
                          MyImage(
                            logic.state.vipDialogList[index]["img"] ?? "",
                            width: 26.w,
                            height: 26.w,
                            isAssetImage: true,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 4.w,
                    ),
                    Text(
                      logic.state.vipDialogList[index]["name"] ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 10.sp, color: Colours.color7732ED),
                    )
                  ],
                ),
              );
            }),
          )),
    );
  }

//规则说明 邀请新人
  void getVIPInvitationDialog2() {
    return getMyDialog2(
      "",
      S.current.sure,
      () {
        AppPage.back();
      },
      isShowClose: false,
      btnIsHorizontal: true,
      imgWidth: double.infinity,
      contentTopRadius: 16.r,
      bottomBtnWidget: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.getShareWx(0);
                },
                child: Column(
                  children: [
                    MyImage(
                      "vip_Invitation_dialog5.png",
                      width: 36.w,
                      height: 36.w,
                      fit: BoxFit.fitWidth,
                      isAssetImage: true,
                    ),
                    SizedBox(
                      height: 6.w,
                    ),
                    Text(
                      S.current.vip_Invitation_dialog_text9,
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp, color: Colours.color333333),
                    )
                  ],
                ),
              ),
              // GestureDetector(
              //   behavior: HitTestBehavior.translucent,
              //   onTap: () {
              //     logic.getShareWx(1);
              //   },
              //   child: Column(
              //     children: [
              //       MyImage(
              //         "vip_Invitation_dialog6.png",
              //         width: 36.w,
              //         height: 36.w,
              //         fit: BoxFit.fitWidth,
              //         isAssetImage: true,
              //       ),
              //       SizedBox(
              //         height: 6.w,
              //       ),
              //       Text(
              //         S.current.vip_Invitation_dialog_text10,
              //         style: TextStyles.regular.copyWith(
              //             fontSize: 13.sp, color: Colours.color333333),
              //       )
              //     ],
              //   ),
              // ),
            ],
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.back();
            },
            child: Container(
              width: 100.w,
              height: 40.w,
              alignment: Alignment.bottomCenter,
              child: Text(
                S.current.cancel,
                style: TextStyles.regular
                    .copyWith(fontSize: 11.sp, color: Colours.colorA44EFF),
              ),
            ),
          )
        ],
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.only(left: 40.w, right: 40.w),
          decoration: BoxDecoration(
            color: Colours.color282735,
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
            gradient: const LinearGradient(
              colors: [Colours.colorD2ABFF, Colours.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            children: [
              SizedBox(
                height: 20.w,
              ),
              MyImage(
                "vip_Invitation_dialog7.png",
                width: 187.w,
                height: 26.w,
                isAssetImage: true,
              ),
              SizedBox(
                height: 20.w,
              ),
              Column(
                children: List.generate(logic.state.invitationDialogList.length,
                    (index) {
                  return Container(
                    width: double.infinity,
                    height: 44.w,
                    alignment: Alignment.center,
                    child: Row(
                      children: [
                        MyImage(
                          logic.state.invitationDialogList[index]["img"] ?? "",
                          width: 13.w,
                          height: 13.w,
                          isAssetImage: true,
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.only(
                                left: 10.w, right: 10.w, top: 6.w, bottom: 4.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8.r),
                                color: Colours.color7732ED),
                            child: Row(
                              children: [
                                RichText(
                                  textAlign: TextAlign.left,
                                  text: TextSpan(
                                      text: index == 0
                                          ? S.current
                                              .vip_Invitation_dialog_text1
                                          : index == 1
                                              ? S.current
                                                  .vip_Invitation_dialog_text3
                                              : index == 2
                                                  ? S.current
                                                      .vip_Invitation_dialog_text5
                                                  : S.current
                                                      .vip_Invitation_dialog_text6,
                                      style: TextStyle(
                                          color: Colours.white,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400),
                                      children: <InlineSpan>[
                                        TextSpan(
                                            text: index == 0
                                                ? S.current
                                                    .vip_Invitation_dialog_text2
                                                : index == 1
                                                    ? S.current
                                                        .vip_Invitation_dialog_text4
                                                    : index == 2
                                                        ? ""
                                                        : S.current
                                                            .vip_Invitation_dialog_text7,
                                            style: TextStyle(
                                                color: Colours.colorCDFDDE,
                                                fontSize: 12.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400)),
                                        TextSpan(
                                            text: index == 3
                                                ? S.current
                                                    .vip_Invitation_dialog_text8
                                                : "",
                                            style: TextStyle(
                                                color: Colours.white,
                                                fontSize: 12.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400)),
                                      ]),
                                ),
                                Expanded(
                                    child: Text(
                                        " -----------------------------",
                                        maxLines: 1,
                                        style: TextStyle(
                                            color: Colours.white,
                                            fontSize: 12.sp,
                                            height: 1,
                                            overflow: TextOverflow.clip,
                                            fontWeight: FontWeight.w400)))
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ],
          )),
    );
  }

  Container _chooseTime(BuildContext context) {
    return Container(
      height: 50.w,
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 5.w, bottom: 5.w, right: 10.w, left: 15.w),
      child: Row(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //选择进球的时间
              _getTypeDialog(context, logic.state.typeDialogList, (v) {
                switch (v) {
                  case "两分":
                    logic.chooseType.value = "1";
                    break;
                  case "三分":
                    logic.chooseType.value = "2";
                    break;
                  case "罚球":
                    logic.chooseType.value = "4";
                    break;
                  default:
                    logic.chooseType.value = "";
                    break;
                }
              });
            },
            child: Container(
              height: 30.w,
              padding: EdgeInsets.only(
                  left: 16.w, right: 10.w, top: 5.w, bottom: 8.w),
              decoration: BoxDecoration(
                  border: Border.all(
                    width: 1.w,
                    color: logic.chooseType.value == "1" ||
                            logic.chooseType.value == "0"
                        ? Colours.colorA44EFF
                        : Colours.color2F2F3B,
                  ),
                  borderRadius: BorderRadius.circular(20.r)),
              child: Row(
                children: [
                  Text(
                    logic.chooseType.value == "1"
                        ? "两分"
                        : logic.chooseType.value == "2"
                            ? "三分"
                            : logic.chooseType.value == "4"
                                ? "罚球"
                                : "类型",
                    textAlign: TextAlign.right,
                    style: TextStyle(
                      color: logic.chooseType.value == "1" ||
                              logic.chooseType.value == "0"
                          ? Colours.colorA44EFF
                          : Colours.color9393A5,
                      fontSize: 12.sp,
                    ),
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  WxAssets.images.icArrowDown.image(
                    height: 10.w,
                    width: 10.w,
                    color: logic.chooseType.value == "1" ||
                            logic.chooseType.value == "0"
                        ? Colours.white
                        : Colours.color9393A5,
                  ),
                ],
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //选择进球的时间
              _getTypeDialog(context, logic.state.stateDialogList, (String v) {
                switch (v) {
                  case "进球":
                    logic.chooseState.value = "1";
                    break;
                  case "打铁":
                    logic.chooseState.value = "2";
                    break;
                  default:
                    logic.chooseState.value = "";
                    break;
                }
              });
            },
            child: Container(
              height: 30.w,
              margin: EdgeInsets.only(left: 15.w),
              padding: EdgeInsets.only(
                  left: 16.w, right: 10.w, top: 5.w, bottom: 8.w),
              decoration: BoxDecoration(
                  border: Border.all(
                    width: 1.w,
                    color: logic.chooseState.value == "1" ||
                            logic.chooseState.value == "2"
                        ? Colours.colorA44EFF
                        : Colours.color2F2F3B,
                  ),
                  borderRadius: BorderRadius.circular(20.r)),
              child: Row(
                children: [
                  Text(
                    logic.chooseState.value == "1"
                        ? "进球"
                        : logic.chooseState.value == "2"
                            ? "打铁"
                            : "状态",
                    textAlign: TextAlign.right,
                    style: TextStyle(
                      color: logic.chooseState.value == "1" ||
                              logic.chooseState.value == "2"
                          ? Colours.colorA44EFF
                          : Colours.color9393A5,
                      fontSize: 12.sp,
                    ),
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  WxAssets.images.icArrowDown.image(
                    height: 10.w,
                    width: 10.w,
                    color: logic.chooseState.value == "1" ||
                            logic.chooseState.value == "2"
                        ? Colours.white
                        : Colours.color9393A5,
                  ),
                ],
              ),
            ),
          ),
          const Spacer(),
          // GestureDetector(
          //   behavior: HitTestBehavior.translucent,
          //   onTap: () {
          //     MyShareH5.getShareH5(ShareGoalVideos(
          //         fragmentId: logic.dataList[logic.state.indexVideo.value].id
          //             .toString(),
          //         sharedFrom:
          //             UserManager.instance.userInfo.value?.userId ?? ""));
          //   },
          //   child: SizedBox(
          //       width: 42.w,
          //       height: 50.w,
          //       child: WxAssets.images.optionShare
          //           .image(height: 22.w, width: 22.w)),
          // ),
          // GestureDetector(
          //   onTap: () {
          //     logic.getDownLoad();
          //   },
          //   child: SizedBox(
          //       width: 42.w,
          //       height: 50.w,
          //       child: WxAssets.images.optionDownload
          //           .image(height: 22.w, width: 22.w)),
          // ),
        ],
      ),
    );
  }

  Widget _videoWidget() {
    return SizedBox(
      width: double.infinity,
      height: ScreenUtil().screenWidth / 375 * 211,
      child: AspectRatio(
        aspectRatio: 375 / 211, // 宽高比
        child: VideoView(
          controller: logic.videoController,
        ),
      ),
    );
  }

  Widget _optionGoalWidget(BuildContext context) {
    return Obx(() {
      return logic.dataList.isEmpty
          ? myNoDataView(
              context,
              msg: S.current.no_goal,
              imagewidget:
                  WxAssets.images.noGoal.image(width: 100.w, height: 84.w),
            )
          : ListView(
              padding: EdgeInsets.only(left: 7.5.w, right: 7.5.w),
              children: [
                Wrap(
                  runSpacing: 15.w,
                  children: List.generate(logic.dataList.length, (position) {
                    return (logic.chooseType.value != "" &&
                                logic.dataList[position].shootType.toString() !=
                                    logic.chooseType.value) ||
                            (logic.chooseState.value != "" &&
                                ((logic.dataList[position].hit ?? false) !=
                                    (logic.chooseState.value ==
                                        "1"))) //1进球  2打铁
                        ? const SizedBox(
                            width: 0,
                          )
                        : GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () async {
                              if (position == logic.state.indexVideo.value) {
                                if (await Utils.isToLogin()) {
                                  logic.checkVideo(position);
                                }
                              } else {
                                logic.changeVideoIndex(position);
                              }
                            },
                            child: Container(
                              height: 72.w,
                              margin:
                                  EdgeInsets.only(left: 7.5.w, right: 7.5.w),
                              width: (ScreenUtil().screenWidth - 63.w) / 3,
                              decoration: BoxDecoration(
                                color: position == logic.state.indexVideo.value
                                    ? Colours.color291A3B
                                    : Colours.color191921,
                                borderRadius: BorderRadius.circular(18.r),
                                image: position == logic.state.indexVideo.value
                                    ? (logic.dataList[position].checked ??
                                            false)
                                        ? const DecorationImage(
                                            image: AssetImage(
                                                "assets/images/goal_bg2.png"),
                                            fit: BoxFit.fill)
                                        : const DecorationImage(
                                            image: AssetImage(
                                                "assets/images/goal_bg1.png"),
                                            fit: BoxFit.fill)
                                    : const DecorationImage(
                                        image: AssetImage(
                                            "assets/images/goal_bg.png"),
                                        fit: BoxFit.fill),
                                // border: position == logic.state.indexVideo.value
                                //     ? Border.all(
                                //         width: 1, color: Colours.color7732ED)
                                //     : null
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Positioned(
                                    top: 15.w,
                                    child: Visibility(
                                      visible: position ==
                                          logic.state.indexVideo.value,
                                      child: (logic
                                                  .dataList[position].checked ??
                                              false)
                                          ? WxAssets.images.goalRemove
                                              .image(height: 30.w, width: 30.w)
                                          : WxAssets.images.goalAdd
                                              .image(height: 30.w, width: 30.w),
                                    ),
                                  ),
                                  Positioned(
                                      bottom: 10.w,
                                      child: Container(
                                        width: 103.w,
                                        padding: EdgeInsets.only(
                                            left: 10.w, right: 10.w),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            if ( //logic.videostate.value == 1 &&
                                                position ==
                                                    logic
                                                        .state.indexVideo.value)
                                              Container(
                                                height: 10.w,
                                                width: 10.w,
                                                alignment: Alignment.center,
                                                child: const LoadingIndicator(
                                                  pathBackgroundColor:
                                                      Colors.black26,
                                                  indicatorType:
                                                      Indicator.lineScaleParty,
                                                  colors: [Colours.white],
                                                ),
                                              ),
                                            if ( //logic.videostate.value != 1 &&
                                                (logic.dataList[position]
                                                            .checked ??
                                                        false) &&
                                                    position !=
                                                        logic.state.indexVideo
                                                            .value)
                                              WxAssets.images.document2.image(
                                                  height: 12.w, width: 12.w),
                                            Expanded(
                                              child: Text(
                                                logic.dataList[position].time ??
                                                    "",
                                                textAlign: TextAlign.right,
                                                style:
                                                    TextStyles.medium.copyWith(
                                                  fontSize: 14.sp,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )),
                                  Positioned(
                                      left: 0,
                                      top: 0,
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            left: 5.w,
                                            right: 5.w,
                                            top: 3.w,
                                            bottom: 3.w),
                                        decoration: BoxDecoration(
                                            color:
                                                (logic.dataList[position].hit ??
                                                        false)
                                                    ? Colours.colorA44EFF
                                                    : Colours.color999999,
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(8.r),
                                                bottomRight:
                                                    Radius.circular(8.r))),
                                        child: Text(
                                          logic.dataList[position].shootType ==
                                                  1
                                              ? "两分"
                                              : logic.dataList[position]
                                                          .shootType ==
                                                      2
                                                  ? "三分"
                                                  : logic.dataList[position]
                                                              .shootType ==
                                                          4
                                                      ? "罚球"
                                                      : "",
                                          style: TextStyles.regular
                                              .copyWith(fontSize: 10.sp),
                                        ),
                                      ))
                                ],
                              ),
                            ),
                          );
                  }),
                ),
              ],
            );
    });
  }

  //选择类型
  void _getTypeDialog(BuildContext context,
      RxList<Map<String, String>> dataList, void Function(String v) onTap2) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Container(
          width: double.infinity,
          //height: 632,
          margin: EdgeInsets.only(bottom: 25.w),
          decoration: BoxDecoration(
            color: Colours.color282735,
            borderRadius: BorderRadius.all(Radius.circular(8.r)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(dataList.length, (index) {
              return Container(
                width: double.infinity,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    onTap2((dataList[index]["name"] ?? ""));
                    Get.back();
                  },
                  child: Container(
                    height: 48.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: index == dataList.length - 1
                            ? null
                            : Border(
                                bottom: BorderSide(
                                    width: 1.w, color: Colours.color10D8D8D8))),
                    child: Text(
                      dataList[index]["name"] ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 14.sp,
                          color: (dataList[index]["name"] ?? "") == "取消"
                              ? Colours.colorD8D8D8
                              : Colours.white),
                    ),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
