import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class ShootCompositeVideoLogicState {
  TextEditingController codeController = TextEditingController();

  var indexVideo = 9999.obs; //选择视频下标
  var allCheck = false.obs; //全选
  var checkVideosCount = 0.obs; //选中的视频数量
  //合成选项
  var compositeOption1 = 0.obs; //单片时长  0  5s    1  10s
  var compositeOption2 = ["1", "0", "1", "0"].obs; //视频效果与个性化 0选中  1选中 多选
  // var compositeOption3 = 1.obs; //视角 0仅侧面  1全部视角
  // var compositeOption4 = 0.obs; //移除所选的球 0是  1否
  var rememberOption = "0".obs; //记住我的选择
  var deleteOption = true.obs; //记住我的选择
}
