// database_helper.dart
import 'package:floor/floor.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'package:path_provider/path_provider.dart' as ccc;
import 'package:path/path.dart' as p;

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static AppDatabase? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() {
    return _instance;
  }
  // final databasePath = await ccc.getApplicationDocumentsDirectory();
  //   await $FloorAppDatabase
  //     .databaseBuilder(p.join(databasePath.path, 'app_database.db'))
  //     //  .addMigrations(_migration)
  //     .build();
  Future<AppDatabase> get database async {
    if (_database != null) return _database!;
    final databasePath = await ccc.getApplicationDocumentsDirectory();
    _database = await $FloorAppDatabase
        .databaseBuilder(p.join(databasePath.path, 'app_database.db'))
        .addMigrations([
      Migration(1, 2, _createShotRecordTable), // 添加迁移
    ]).build();

    return _database!;
  }

  // 创建 ShotRecordModel 表的迁移函数
  static Future<void> _createShotRecordTable(sqflite.Database database) async {
    // 创建新表
    await database.execute('''
      CREATE TABLE ShotRecordModel (
        player_confidence REAL,
        file_path TEXT,
        network_file_path TEXT,
        start_time TEXT,
        shoot_time REAL,
        training_id TEXT,
        user_id TEXT,
        event_id TEXT PRIMARY KEY,
        is_goal INTEGER,
        player_image_path TEXT,
        goal_time REAL,
        shoot_coord TEXT,
        img_load_ok TEXT,
        video_load_ok TEXT,
        created_at REAL,
        type TEXT
      )
    ''');

    print('ShotRecordModel 表创建成功');
  }

  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
