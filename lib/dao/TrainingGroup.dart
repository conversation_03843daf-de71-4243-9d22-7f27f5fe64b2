// 完整的分组数据服务
import 'package:shoot_z/network/model/shot_record_model.dart';

// 分组记录的数据结构
class TrainingGroup {
  final String trainingId;
  final List<ShotRecordModel> records;
  final DateTime latestDate;
  final int recordCount;
  final String siteName;
  final String siteId;

  TrainingGroup({
    required this.trainingId,
    required this.records,
    required this.latestDate,
    required this.recordCount,
    required this.siteId,
    required this.siteName,
  });
}
