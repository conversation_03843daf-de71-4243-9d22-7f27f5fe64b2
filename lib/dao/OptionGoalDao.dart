// dao/person_dao.dart

import 'package:floor/floor.dart';
import 'package:shoot_z/network/model/option_goal_model.dart';
import 'package:intl/intl.dart';

@dao
abstract class OptionGoalDao {
  @Query(
      'SELECT * FROM OptionGoalModel WHERE arenaID = :arenaID AND userId = :userId ORDER BY videoDateTimeStr ASC')
  Future<List<OptionGoalModel>> findAllGoal(String arenaID, String userId);

  // @Query('SELECT name FROM OptionGoalModel')
  // Stream<List<String>> findAllPeopleName();

  // @Query(
  //     'SELECT * FROM OptionGoalModel WHERE id = :id AND arenaID = :arenaID AND userId = :userId')
  // Stream<OptionGoalModel?> findGoalById(
  //     String id, String arenaID, String userId);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertGoal2(OptionGoalModel optionGoalModel);

  // 插入多个书籍的便捷方法
  Future<void> insertGoalList(
      List<OptionGoalModel> datalist, String arenaID, String userId) async {
    for (var optionGoalModel in datalist) {
      optionGoalModel.arenaID = arenaID;
      optionGoalModel.userId = userId;
      await insertGoal2(optionGoalModel);
    }
  }

  //新增
  Future<void> insertGoal(
      OptionGoalModel optionGoalModel, String arenaID, String userID) async {
    optionGoalModel.arenaID = arenaID;
    optionGoalModel.userId = userID;
    // 解析 ISO 8601 格式的日期时间部分
    DateTime baseTime = DateTime.parse(optionGoalModel.videoDate!);
    // 解析时间字符串为 DateTime 对象
    final timeFormat = DateFormat("HH:mm:ss");
    final parsedTime = timeFormat.parse(optionGoalModel.videoTime!, false);
    DateTime combinedDateTime = DateTime(
      baseTime.year,
      baseTime.month,
      baseTime.day,
      parsedTime.hour,
      parsedTime.minute,
      parsedTime.second,
    );
    int timestamp = combinedDateTime.millisecondsSinceEpoch;
    optionGoalModel.videoDateTimeStr = timestamp;
    await insertGoal2(optionGoalModel);
  }

  //修改
  Future<void> updateGoal(
      OptionGoalModel optionGoalModel, String arenaID, String userID) async {
    optionGoalModel.arenaID = arenaID;
    optionGoalModel.userId = userID;

    // 解析 ISO 8601 格式的日期时间部分
    DateTime baseTime = DateTime.parse(optionGoalModel.videoDate!);
    // 解析时间字符串为 DateTime 对象
    final timeFormat = DateFormat("HH:mm:ss");
    final parsedTime = timeFormat.parse(optionGoalModel.videoTime!, false);
    DateTime combinedDateTime = DateTime(
      baseTime.year,
      baseTime.month,
      baseTime.day,
      parsedTime.hour,
      parsedTime.minute,
      parsedTime.second,
    );
    int timestamp = combinedDateTime.millisecondsSinceEpoch;
    optionGoalModel.videoDateTimeStr = timestamp;
    await updateGoal2(optionGoalModel);
  }

  @update
  Future<void> updateGoal2(OptionGoalModel optionGoalModel);

  @delete
  Future<void> deleteGoal2(OptionGoalModel optionGoalModel);

  @Query(
      'DELETE FROM OptionGoalModel WHERE arenaID = :arenaID AND userId = :userId AND id = :id')
  Future<void> deleteGoal1(String arenaID, String userId, String id);

  @Query(
      'DELETE FROM OptionGoalModel WHERE arenaID = :arenaID AND userId = :userId')
  Future<void> deleteAll(String arenaID, String userId);

  @Query('DELETE FROM OptionGoalModel WHERE userId = :userId')
  Future<void> deleteAllUserId(String userId);

  // // 删除早于8天的数据
  // @Query('DELETE FROM OptionGoalModel WHERE dateTime < datetime(:eightDaysAgo)')
  // Future<void> deleteBooksOlderThanEightDays(DateTime eightDaysAgo);
}
