class WebviewRouter {
  String? url;
  Function? backHandle;
  Function(dynamic)? closeHandle;
  bool needBaseHttp = true;
  String? title;
  bool showNavigationBar = true;
  Function? completeHandle;
  Function(String?)? crfJsonHandle;
  bool resizeToAvoidBottomInset = false;

  WebviewRouter({
    this.showNavigationBar = true,
    this.url,
    this.title,
    this.backHandle,
    this.completeHandle,
    this.needBaseHttp = true,
    this.crfJsonHandle,
    this.closeHandle,
    this.resizeToAvoidBottomInset = false,
  });
}
