// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:shoot_z/inappwebview/logich5.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';
import 'router.dart';
import 'state.dart';

class AppWebViewH5Page extends StatefulWidget {
  WebviewRouter router;

  AppWebViewH5Page({
    super.key,
    required this.router,
  });

  @override
  State<AppWebViewH5Page> createState() => _WebviewPageState();
}

class _WebviewPageState extends State<AppWebViewH5Page>
    with AutomaticKeepAliveClientMixin {
  final logic = WebviewH5Logic();

  @override
  void initState() {
    logic.onInit(widget.router);
    super.initState();
    logic.onReady();
    logic.state.refreshHandle = () {
      if (mounted) {
        setState(() {});
      }
    };
  }

  @override
  void dispose() {
    logic.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return WillPopScope(
      onWillPop: () async {
        if (await logic.state.webViewController!.canGoBack()) {
          logic.state.webViewController!.goBack();
          return false; // 阻止默认返回行为
        } else {
          return true; // 允许退出页面
        }
      },
      child: Scaffold(
        backgroundColor: Colours.color000000,
        resizeToAvoidBottomInset:
            logic.router?.resizeToAvoidBottomInset ?? false,
        appBar: widget.router.showNavigationBar
            ? MyAppBar(
                title: Text(widget.router.title ?? ""),
                // leading: IconButton(
                //   onPressed: () {
                //     logic.back();
                //   },
                //   icon: const Icon(Icons.arrow_back_ios_new),
                // ),
              )
            : PreferredSize(
                preferredSize:
                    Size.fromHeight(MediaQuery.of(context).padding.top),
                child: SizedBox(
                  height: MediaQuery.of(context).padding.top,
                ),
              ),
        body: SafeArea(
          child: Obx(() => logic.state.loadState.value == WebviewLoadState.error
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.only(left: 25.w, right: 25.w),
                        child: Text(
                          "网络连接失败，请检查网络后重试",
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: AppFontWeight.regular(),
                            color: Colours.dark_text_gray,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 32.w,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          WxButton(
                            width: 110.w,
                            height: 40.w,
                            text: "返回",
                            textStyle: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: AppFontWeight.medium(),
                              color: Colours.color8995C7,
                            ),
                            linearGradient: LinearGradient(
                              colors: [
                                Colours.color9AA3F4.withOpacity(0.2),
                                Colours.colorC9CEFA.withOpacity(0.2),
                              ],
                              begin: Alignment.topRight,
                              end: Alignment.topLeft,
                            ),
                            onPressed: () {
                              logic.back();
                            },
                          ),
                          SizedBox(
                            width: 20.w,
                          ),
                          WxButton(
                            width: 110.w,
                            height: 40.w,
                            text: "刷新",
                            onPressed: () {
                              logic.onRefresh();
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              : FutureBuilder(
                  future: logic.loadWebview(),
                  builder: (context, snp) => snp.hasData
                      ? InAppWebView(
                          initialSettings: InAppWebViewSettings(
                              applicationNameForUserAgent: "webview_flutter",
                              useShouldOverrideUrlLoading: false,
                              supportZoom: false),
                          initialUrlRequest: URLRequest(url: logic.uri!),
                          onWebViewCreated: (controller) {
                            logic.onWebViewCreated(controller);
                          },
                          onLoadStart: (controller, url) {
                            debug("加载网页：$url");
                            WxLoading.show();
                            // logic.onPageStarted();
                          },
                          onLoadStop: (controller, url) {
                            WxLoading.dismiss();
                            // logic.onPageFinished();
                          },
                          onReceivedError: (controller, request, error) {
                            WxLoading.dismiss();
                            // logic.onReceivedError();
                          },
                          // shouldOverrideUrlLoading: (controller,action) async {
                          //   return NavigationActionPolicy.ALLOW;
                          //   },
                          onConsoleMessage: (controller, msg) {
                            debug("Console----M$msg");
                          },
                        )
                      : const SizedBox(),
                )),
        ),
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
