import UIKit
import Vision
import Accelerate

/// GoalDetectorService代理协议
protocol GoalDetectorServiceDelegate: AnyObject {
    /// 当检测到进球时调用
    func goalDetectorService(_ service: GoalDetectorService, didDetectGoal timestamp: TimeInterval)
}

/// 篮球进球检测服务
class GoalDetectorService {
    // MARK: - 类型定义
    
    /// 表示一个矩形区域
    typealias Area = (x1: Int, y1: Int, x2: Int, y2: Int)
    
    /// 进球检测回调
    typealias GoalDetectionCallback = (Bool, TimeInterval) -> Void
    
    // MARK: - 属性
    
    /// 代理
    weak var delegate: GoalDetectorServiceDelegate?
    
    /// 篮网区域坐标
    private let basketPos: Area
    
    /// 算法平均阈值
    private let diffThreshold: Float
    
    /// 单帧判断阈值
    private let singleFrameThreshold: Float
    
    /// 满足单帧条件的最小帧数
    private let minFramesAboveThreshold: Int
    
    /// 最小进球间隔帧数
    private let minGoalInterval: Int
    
    /// 最小持续时间帧数
    private let minDuration: Int
    
    /// 每隔几帧处理一次
    private let processingInterval: Int
    
    /// 参考区域列表
    private var corners: [Area] = []
    
    /// 状态变量
    private var state: Bool = false
    private var lastGoalFrame: Int = -60
    private var frameIdx: Int = 0
    private var startFrame: Int = 0
    
    /// 窗口间隔帧数
    private let windowGap: Int
    
    /// 历史图像缓存
    private var historicalImages: [[CGImage?]] = []
    
    /// 差异值滑动窗口
    private var diffWindow: [Float] = []
    
    /// 从检测开始到现在满足单帧阈值的帧数
    private var singleFrameCount: Int = 0
    
    /// 性能统计
    private var fps: Float = 0
    private var frameTimes: [TimeInterval] = []
    private let maxFrameHistorySize: Int
    
    // 添加跳帧处理
    private var processFrameCount: Int = 0
    
    // MARK: - 初始化方法
    
    /// 初始化进球检测器
    /// - Parameters:
    ///   - pos: 篮网区域坐标 (x1, y1, x2, y2)
    ///   - algoThreshold: 算法平均阈值
    ///   - singleFrameThreshold: 单帧判断阈值
    ///   - minFramesAboveThreshold: 满足单帧条件的最小帧数
    ///   - minDuration: 最小持续时间帧数
    ///   - windowGap: 窗口间隔帧数
    ///   - processingInterval: 每隔几帧处理一次
    ///   - minGoalInterval: 最小进球间隔帧数
    ///   - maxFrameHistorySize: 帧时间历史最大容量
    init(pos: Area,
         algoThreshold: Float = 0.12,
         singleFrameThreshold: Float = 0.18,
         minFramesAboveThreshold: Int = 3,
         minDuration: Int = 5,
         windowGap: Int = 4,
         processingInterval: Int = 2,
         minGoalInterval: Int = 25,
         maxFrameHistorySize: Int = 30) {
        
        self.basketPos = pos
        self.diffThreshold = algoThreshold
        self.singleFrameThreshold = singleFrameThreshold
        self.minFramesAboveThreshold = minFramesAboveThreshold
        self.minGoalInterval = minGoalInterval
        self.minDuration = minDuration
        self.windowGap = windowGap
        self.processingInterval = processingInterval
        self.maxFrameHistorySize = maxFrameHistorySize
        
        // 计算参考区域
        let w = pos.x2 - pos.x1
        self.corners = [
            pos,                                       // 篮网区域
            (pos.x1-w, pos.y1, pos.x2-w, pos.y2),      // 左侧参考区域
            (pos.x1+w, pos.y1, pos.x2+w, pos.y2)       // 右侧参考区域
        ]
        
        // 初始化数据结构
        let numCorners = corners.count
        self.historicalImages = Array(repeating: Array(repeating: nil, count: windowGap + 1), count: numCorners)
        self.diffWindow = Array(repeating: 0.0, count: minDuration)
      
//        LogService.shared.info("进球检测服务初始化完成")
        print(pos)
    }
    
    deinit {
        // 清理资源
        clearAllImageData()
    }
    
    // MARK: - 资源管理
    
    /// 清理所有图像数据
    private func clearAllImageData() {
        for i in 0..<historicalImages.count {
            for j in 0..<historicalImages[i].count {
                historicalImages[i][j] = nil
            }
        }
        
        frameTimes.removeAll()
        diffWindow = Array(repeating: 0.0, count: minDuration)
    }
    
    // MARK: - 公共方法
    
    /// 处理单帧图像
    /// - Parameters:
    ///   - pixelBuffer: 输入图像帧
    ///   - completion: 完成回调，返回是否检测到进球和当前FPS
    func processFrame(_ pixelBuffer: CVPixelBuffer, completion: @escaping GoalDetectionCallback) {
        // 跳帧处理，减少CPU使用
        processFrameCount += 1
        if processFrameCount % processingInterval != 0 {
            completion(false, Double(fps))
            return
        }
        
        autoreleasepool {
            // 计算FPS
            let startTime = CACurrentMediaTime()
            
            // 转换CVPixelBuffer为UIImage
            guard let image = pixelBufferToUIImage(pixelBuffer) else {
                LogService.shared.error("无法转换图像格式")
                completion(false, 0)
                return
            }
            
            // 从图像中提取三个区域并转换为灰度
            var similarities: [Float] = []
            
            for i in 0..<corners.count {
                let corner = corners[i]
                
                // 确保区域在图像范围内
                let x1 = max(0, corner.x1)
                let y1 = max(0, corner.y1)
                let x2 = min(Int(image.size.width), corner.x2)
                let y2 = min(Int(image.size.height), corner.y2)
                
                guard x2 > x1 && y2 > y1 else {
                    LogService.shared.error("无效的区域: (\(x1), \(y1), \(x2), \(y2)), 图像尺寸: \(image.size)")
                    completion(false, 0)
                    return
                }
                
                // 提取区域
                let rect = CGRect(x: x1, y: y1, width: x2 - x1, height: y2 - y1)
                
                guard let croppedImage = cropImage(image, toRect: rect),
                      let greyImage = convertToGreyscale(croppedImage),
                      let greyCGImage = greyImage.cgImage else {
                    LogService.shared.error("区域提取或转换灰度失败: \(rect)")
                    completion(false, 0)
                    return
                }
                
                // 更新历史图像
                // 将所有历史图像向前移动一位
                for j in 0..<(windowGap) {
                    historicalImages[i][j] = historicalImages[i][j+1]
                }
                // 添加当前图像到历史末尾
                historicalImages[i][windowGap] = greyCGImage
                
                // 只有当我们积累了足够的历史图像后才计算相似度
                if frameIdx >= windowGap {
                    // 计算当前帧和windowGap帧之前的相似度
                    if let historicalImage = historicalImages[i][0] {
                        let sim = calculateSSIM(historicalImage, greyCGImage)
                        similarities.append(sim)
                    }
                }
            }
            
            // 进球检测逻辑
            var goalDetected = false
            
            if similarities.count == corners.count && frameIdx >= windowGap {
                let netSim = similarities[0]  // 篮网区域相似度
                let refSims = [similarities[1], similarities[2]]  // 参考区域相似度
                let maxRefSim = refSims.max() ?? 1.0
                
                // 计算当前帧的差异值 (参考区域相似度 - 篮网区域相似度)
                let currentDiff = maxRefSim - netSim
                
                // 更新差异值滑动窗口
                diffWindow.removeFirst()
                diffWindow.append(currentDiff)
                
                // 计算窗口内的平均差异值
                let avgDiff = diffWindow.reduce(0.0, +) / Float(diffWindow.count)
                
                // 计算窗口内超过单帧阈值的帧数（仅用于参考）
                // let framesAboveThreshold = diffWindow.filter { $0 > singleFrameThreshold }.count
                
                // 检查当前帧是否满足单帧阈值条件
                let currentFrameSatisfied = currentDiff > singleFrameThreshold
                
                // 检查是否超过进球间隔帧数
                if frameIdx - lastGoalFrame <= minGoalInterval {
                    // 未满足间隔要求，不进行进球检测
                    // 但仍然可以重置状态
                    if state && avgDiff <= diffThreshold {
                        state = false
                        singleFrameCount = 0
//                        LogService.shared.info("[帧 \(frameIdx)] 差异值低于阈值，重置状态")
                    }
                } else {
                    // 日志调试
                    if avgDiff > 0.1 {
//                         LogService.shared.debug("[\(frameIdx)]\(currentFrameSatisfied ? "✅" : "❌") AD\(String(format: "%.2f", avgDiff)), D\(String(format: "%.2f", currentDiff)), \(singleFrameCount)/\(minFramesAboveThreshold)")
                         // LogService.shared.info("[\(frameIdx)]\(currentFrameSatisfied ? "✅" : "❌") AD\(String(format: "%.2f", avgDiff)), D\(String(format: "%.2f", currentDiff)), \(singleFrameCount)/\(minFramesAboveThreshold), HD\(String(format: "%.2f", netSim)), RD\(String(format: "%.2f", maxRefSim))")
                    }
                    // 进球检测逻辑 - 使用平均差异值和单帧差异计数
                    if avgDiff > diffThreshold {
                        // 检查当前帧是否满足单帧阈值条件，如果满足则增加计数
                        if currentFrameSatisfied {
                            singleFrameCount += 1
                        }
                        
                        if !state {
                            state = true
                            startFrame = frameIdx
                            singleFrameCount = currentFrameSatisfied ? 1 : 0
                             LogService.shared.warning("[\(frameIdx)] 疑似进球！AD: \(String(format: "%.2f", avgDiff)), D: \(String(format: "%.2f", currentDiff))")
                        } else {
                            // 如果已经处于可能进球状态
                            let duration = frameIdx - startFrame
                            
                            // 判断单帧条件是否满足
                            if singleFrameCount >= minFramesAboveThreshold && duration >= minDuration {
                                state = false
                                lastGoalFrame = frameIdx
//                                 LogService.shared.info("[\(frameIdx)] 确认进球！持续\(duration)帧, \(singleFrameCount)帧满足")
                                goalDetected = true
                                
                                // 通知代理检测到进球
                                let timestamp = Date().timeIntervalSince1970
                                delegate?.goalDetectorService(self, didDetectGoal: timestamp)
                                
                                // 重置单帧计数
                                singleFrameCount = 0
                            } else if duration >= minDuration * 2 {
                                // 如果持续时间过长但还未满足单帧条件，重置状态
                                state = false
                                singleFrameCount = 0
                            } else if singleFrameCount < minFramesAboveThreshold && duration >= minDuration {
                                // 如果持续时间到达但单帧条件不满足，继续等待
                            }
                        }
                    } else {
                        // 只有当平均差异值低于阈值时，才重置状态
                        if state {
                            state = false
                            singleFrameCount = 0
                        }
                    }
                }
            }
            
            frameIdx += 1
            
            // 计算并更新FPS
            let frameTime = CACurrentMediaTime() - startTime
            frameTimes.append(frameTime)
            if frameTimes.count > maxFrameHistorySize {
                frameTimes.removeFirst()
            }
            fps = 1.0 / Float(frameTimes.reduce(0, +) / Double(frameTimes.count))
            
            completion(goalDetected, Double(fps))
        }
    }
    
    // MARK: - 辅助方法
    
    /// 将CVPixelBuffer转换为UIImage
    private func pixelBufferToUIImage(_ pixelBuffer: CVPixelBuffer) -> UIImage? {
        var resultImage: UIImage? = nil
        
        autoreleasepool {
            CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
            defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly) }
            
            let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer)
            let width = CVPixelBufferGetWidth(pixelBuffer)
            let height = CVPixelBufferGetHeight(pixelBuffer)
            let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedFirst.rawValue | CGBitmapInfo.byteOrder32Little.rawValue)
            
            if let context = CGContext(data: baseAddress,
                                     width: width,
                                     height: height,
                                     bitsPerComponent: 8,
                                     bytesPerRow: bytesPerRow,
                                     space: colorSpace,
                                     bitmapInfo: bitmapInfo.rawValue),
               let cgImage = context.makeImage() {
                resultImage = UIImage(cgImage: cgImage)
            }
        }
        
        return resultImage
    }
    
    /// 裁剪图像到指定矩形区域
    private func cropImage(_ image: UIImage, toRect rect: CGRect) -> UIImage? {
        var resultImage: UIImage? = nil
        
        autoreleasepool {
            if let cgImage = image.cgImage,
               let croppedCgImage = cgImage.cropping(to: rect) {
                resultImage = UIImage(cgImage: croppedCgImage)
            }
        }
        
        return resultImage
    }
    
    /// 将图像转换为灰度
    private func convertToGreyscale(_ image: UIImage) -> UIImage? {
        var resultImage: UIImage? = nil
        
        autoreleasepool {
            guard let cgImage = image.cgImage else { return }
            
            // 使用Core Graphics进行灰度转换
            let width = cgImage.width
            let height = cgImage.height
            
            let colorSpace = CGColorSpaceCreateDeviceGray()
            let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.none.rawValue)
            
            if let context = CGContext(data: nil,
                                     width: width,
                                     height: height,
                                     bitsPerComponent: 8,
                                     bytesPerRow: width,
                                     space: colorSpace,
                                     bitmapInfo: bitmapInfo.rawValue) {
                context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
                
                if let grayImage = context.makeImage() {
                    resultImage = UIImage(cgImage: grayImage)
                }
            }
        }
        
        return resultImage
    }
    
    /// 计算两个图像之间的结构相似性指数 (SSIM)，使用Accelerate框架优化性能
    func calculateSSIM(_ imageA: CGImage, _ imageB: CGImage) -> Float {
        guard let dataA = imageA.dataProvider?.data,
              let dataB = imageB.dataProvider?.data,
              let bytesA = CFDataGetBytePtr(dataA),
              let bytesB = CFDataGetBytePtr(dataB) else {
            LogService.shared.error("SSIM计算错误：无法获取图像数据")
            return 1.0
        }

        let width = min(imageA.width, imageB.width)
        let height = min(imageA.height, imageB.height)
        let count = width * height
        
        // 添加采样步长，减少计算量
        let samplingStride = 4 // 每隔4个像素采样一次
        let sampledCount = (count + samplingStride - 1) / samplingStride
        
        var meanA: Float = 0, meanB: Float = 0
        var varianceA: Float = 0, varianceB: Float = 0, covariance: Float = 0

        // 第一次遍历计算均值 - 采样
        for i in stride(from: 0, to: count, by: samplingStride) {
            let a = Float(bytesA[i]) / 255.0
            let b = Float(bytesB[i]) / 255.0
            meanA += a
            meanB += b
        }

        meanA /= Float(sampledCount)
        meanB /= Float(sampledCount)

        // 第二次遍历计算方差和协方差 - 采样
        for i in stride(from: 0, to: count, by: samplingStride) {
            let a = Float(bytesA[i]) / 255.0 - meanA
            let b = Float(bytesB[i]) / 255.0 - meanB
            varianceA += a * a
            varianceB += b * b
            covariance += a * b
        }

        varianceA /= Float(sampledCount - 1)
        varianceB /= Float(sampledCount - 1)
        covariance /= Float(sampledCount - 1)

        let C1: Float = 0.01 * 0.01
        let C2: Float = 0.03 * 0.03
        let ssim = ((2 * meanA * meanB + C1) * (2 * covariance + C2)) /
               ((meanA * meanA + meanB * meanB + C1) * (varianceA + varianceB + C2))
        
        // 确保结果在[0,1]范围内
        return max(0.0, min(1.0, ssim))
    }
}
