//
//  VideoRecordingService.swift
//  Demo
//
//  Created by <PERSON>ien on 2025/4/25.
//

import AVFoundation
import Photos
import CoreMedia
import CoreLocation
import UIKit
typealias FilePathSuccessBlock = (String) -> ()
/// 视频录制服务，负责处理视频帧和保存视频文件
class VideoRecordingService: NSObject {
    // MARK: - 属性
    
    /// 是否启用视频录制
    private var isRecordingEnabled = true
    
    /// 是否保存进球视频
    private var shouldSaveGoalVideos = true
    
    /// 是否保存未进球视频
    private var shouldSaveNoGoalVideos = true
    
    /// 视频处理队列
    private let videoQueue = DispatchQueue(label: "com.shootz.videoQueue", qos: .utility)
    
    /// 缓冲区处理队列
    private let videoBufferQueue = DispatchQueue(label: "com.shootz.videoBufferQueue", attributes: .concurrent)
    
    /// 像素缓冲区缓存
    private var pixelBufferCache = NSCache<NSString, NSData>()
    
    /// 当前视频写入器
    private var currentVideoWriter: AVAssetWriter?
    
    /// 当前视频输入
    private var currentVideoInput: AVAssetWriterInput?
    
    /// 像素缓冲区适配器
    private var pixelBufferAdaptor: AVAssetWriterInputPixelBufferAdaptor?
    
    /// 录制开始时间
    private var recordingStartTime: CMTime?
    
    /// 最后一帧时间
    private var lastFrameTime: CMTime = .zero
    
    /// 当前视频URL
    private var currentVideoURL: URL?
    
    /// 待处理的保存操作计数
    private var pendingSaveOperations = 0
    
    /// 视频分辨率
    private var videoResolution: CGSize = .zero
    
    ///已写入视频片段数组
    private var videoCacheList:Array<URL> = []
    // MARK: - 初始化方法
    
    override init() {
        // 必须首先调用super.init
        super.init()
        // 申请相册权限
        requestPhotoLibraryAccess()
    }
    
    // MARK: - 公共方法
    
    /// 设置视频分辨率
    /// - Parameter resolution: 视频分辨率
    func setVideoResolution(_ resolution: CGSize) {
        // 添加日志，记录调用情况
        LogService.shared.debug("视频录制服务：设置分辨率请求 - \(resolution.width) x \(resolution.height)")
        
        // 验证分辨率是否有效
        guard resolution.width > 0 && resolution.height > 0 else {
            LogService.shared.warning("视频录制服务：无效的视频分辨率 \(resolution)，已忽略")
            return
        }
        
        // 验证分辨率是否在合理范围内
        let maxDimension: CGFloat = 4096 // 4K分辨率上限
        guard resolution.width <= maxDimension && resolution.height <= maxDimension else {
            LogService.shared.warning("视频录制服务：视频分辨率超出合理范围 \(resolution)，已忽略")
            return
        }
        
        videoResolution = resolution
        LogService.shared.debug("视频录制服务：分辨率设置成功 - \(resolution.width) x \(resolution.height)")
        
        // 如果当前没有活动的视频录制，并且分辨率有效，则设置新的录制
        if currentVideoWriter == nil && isResolutionValid() {
            setupNewVideoRecording()
        }
    }
    
    /// 设置是否保存进球视频
    /// - Parameter enabled: 是否启用
    func setShouldSaveGoalVideos(_ enabled: Bool) {
        shouldSaveGoalVideos = enabled
//        LogService.shared.info("保存进球视频功能: \(enabled ? "已启用" : "已禁用")")
    }
    
    /// 获取是否保存进球视频的设置
    /// - Parameter completion: 回调，返回当前状态
    func getShouldSaveGoalVideos(completion: @escaping (Bool) -> Void) {
        completion(shouldSaveGoalVideos)
    }
    
    /// 设置是否保存未进球视频
    /// - Parameter enabled: 是否启用
    func setShouldSaveNoGoalVideos(_ enabled: Bool) {
        shouldSaveNoGoalVideos = enabled
//        LogService.shared.info("保存未进球视频功能: \(enabled ? "已启用" : "已禁用")")
    }
    
    /// 获取是否保存未进球视频的设置
    /// - Parameter completion: 回调，返回当前状态
    func getShouldSaveNoGoalVideos(completion: @escaping (Bool) -> Void) {
        completion(shouldSaveNoGoalVideos)
    }
    
    /// 处理视频帧
    /// - Parameters:
    ///   - sampleBuffer: 采样缓冲区
    ///   - timestamp: 时间戳
    func processVideoFrame(sampleBuffer: CMSampleBuffer) {
        guard isRecordingEnabled else { return }
        
        // 确保有有效的视频分辨率
        if !isResolutionValid() {
            // 尝试从sampleBuffer获取分辨率
            if let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) {
                let width = CGFloat(CVPixelBufferGetWidth(pixelBuffer))
                let height = CGFloat(CVPixelBufferGetHeight(pixelBuffer))
                if width > 0 && height > 0 && (videoResolution.width == 0 || videoResolution.height == 0) {
//                    LogService.shared.info("视频录制服务：从视频帧自动获取分辨率 - \(width) x \(height)")
                    setVideoResolution(CGSize(width: width, height: height))
                }
            }
            
            // 如果仍然无效，直接返回
            if !isResolutionValid() {
                return
            }
        }
        
        // 只复制像素缓冲区地址，不创建新的缓冲区
        if let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) {
            // 获取当前时间戳
            let timestamp = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
            
            // 如果没有活动的视频写入器，创建一个
            if currentVideoWriter == nil {
                setupNewVideoRecording()
            }
            
            // 把写入操作放到专用队列，避免阻塞主要检测流程
            videoQueue.async { [weak self] in
                self?.writeVideoFrame(pixelBuffer: pixelBuffer, timestamp: timestamp)
            }
        }
    }
    
    /// 保存投篮事件视频
    /// - Parameters:
    ///   - isGoal: 是否进球
    func saveShootEventVideo(isGoal: Bool, _ success: @escaping FilePathSuccessBlock) {
        // 根据设置决定是否保存视频
        let shouldSaveVideo = isGoal ? shouldSaveGoalVideos : shouldSaveNoGoalVideos
        if !shouldSaveVideo {
            let eventType = isGoal ? "进球" : "未进球"
            LogService.shared.info("\(eventType)事件视频跳过保存（根据用户设置）")
            return
        }
        // 主线程延迟,再多录2秒再结束当前写入
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
            guard let self = self else { return }
            self.saveCurrentVideo(isGoal: isGoal, success)
        }
    }
    
    // MARK: - 私有方法
    
    /// 将视频帧写入文件
    /// - Parameters:
    ///   - pixelBuffer: 像素缓冲区
    ///   - timestamp: 时间戳
    private func writeVideoFrame(pixelBuffer: CVPixelBuffer, timestamp: CMTime) {
        // 确保writer存在且处于写入状态
        guard let writer = currentVideoWriter, 
              let input = currentVideoInput,
              let adaptor = pixelBufferAdaptor,
              writer.status == .writing,
              input.isReadyForMoreMediaData else {
            
            // 如果writer不存在或状态不正确，创建新的
            if currentVideoWriter == nil || 
               currentVideoWriter?.status != .writing {
                setupNewVideoRecording()
                return
            }
            return
        }
        
        // 计算时间戳
        if recordingStartTime == nil {
            recordingStartTime = timestamp
        }
        
        let relativeTime = CMTimeSubtract(timestamp, recordingStartTime ?? timestamp)
        
        // 直接写入原始帧，通过AVAssetWriterInput的transform处理旋转
        adaptor.append(pixelBuffer, withPresentationTime: relativeTime)
        lastFrameTime = relativeTime
        
        // 每10秒轮换一次视频文件
        if relativeTime.seconds > 10 {
            // 在后台轮换视频文件
            self.rotateVideoFile()
        }
    }
    
    /// 旋转方向枚举
    private enum RotationAngle {
        case left      // 向左旋转90度
        case right     // 向右旋转90度
        case upsideDown // 上下翻转180度
    }
    
    /// 旋转像素缓冲区
    /// - Parameters:
    ///   - pixelBuffer: 原始像素缓冲区
    ///   - angle: 旋转角度
    /// - Returns: 旋转后的像素缓冲区
    private func rotatePixelBuffer(_ pixelBuffer: CVPixelBuffer, angle: RotationAngle) -> CVPixelBuffer? {
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        
        // 锁定像素缓冲区以便读取
        CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly) }
        
        // 创建旋转后的CIImage
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        var rotatedImage: CIImage?
        
        switch angle {
        case .left:
            // 向左旋转90度
            rotatedImage = ciImage.oriented(.left)
        case .right:
            // 向右旋转90度
            rotatedImage = ciImage.oriented(.right)
        case .upsideDown:
            // 上下翻转180度
            rotatedImage = ciImage.oriented(.down)
        }
        
        guard let rotatedImage = rotatedImage else { return nil }
        
        // 获取原始像素格式
        let pixelFormat = CVPixelBufferGetPixelFormatType(pixelBuffer)
        
        // 创建目标像素缓冲区
        var rotatedPixelBuffer: CVPixelBuffer?
        let attributes: [String: Any] = [
            kCVPixelBufferCGImageCompatibilityKey as String: true,
            kCVPixelBufferCGBitmapContextCompatibilityKey as String: true,
            kCVPixelBufferIOSurfacePropertiesKey as String: [:]
        ]
        
        // 注意：旋转90度后宽高互换
        let status: CVReturn
        if angle == .left || angle == .right {
            status = CVPixelBufferCreate(kCFAllocatorDefault, height, width, 
                                      pixelFormat, 
                                      attributes as CFDictionary, &rotatedPixelBuffer)
        } else {
            status = CVPixelBufferCreate(kCFAllocatorDefault, width, height, 
                                      pixelFormat, 
                                      attributes as CFDictionary, &rotatedPixelBuffer)
        }
        
        guard status == kCVReturnSuccess, let rotatedPixelBuffer = rotatedPixelBuffer else {
            LogService.shared.error("创建旋转后的像素缓冲区失败")
            return nil
        }
        
        // 创建正确的颜色空间
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        
        // 渲染到新的像素缓冲区
        let context = CIContext(options: [.useSoftwareRenderer: false])
        
        // 锁定目标像素缓冲区以便写入
        CVPixelBufferLockBaseAddress(rotatedPixelBuffer, [])
        defer { CVPixelBufferUnlockBaseAddress(rotatedPixelBuffer, []) }
        
        // 渲染旋转后的图像到新的像素缓冲区，保持原始亮度和对比度
        context.render(rotatedImage, to: rotatedPixelBuffer, bounds: rotatedImage.extent, colorSpace: colorSpace)
        
        return rotatedPixelBuffer
    }
    
    /// 设置新的视频录制
    private func setupNewVideoRecording() {
        // 确保有有效的视频分辨率
        guard isResolutionValid() else {
            LogService.shared.warning("视频录制服务：无法设置视频录制，分辨率无效 (\(videoResolution.width)x\(videoResolution.height))")
            return
        }
        
        // 生成新的视频文件路径
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let videoFileName = "cache_\(dateFormatter.string(from: Date())).mp4"
        let videoURL = FileManager.default.temporaryDirectory.appendingPathComponent(videoFileName)
        
        do {
            // 创建视频写入器
            let writer = try AVAssetWriter(outputURL: videoURL, fileType: .mp4)
            
            // 使用固定的横屏分辨率 1920x1080
            let videoWidth: CGFloat = 1080
            let videoHeight: CGFloat = 1920
            
            // 使用高质量设置
            let videoSettings: [String: Any] = [
                AVVideoCodecKey: AVVideoCodecType.h264,
                AVVideoWidthKey: videoWidth,
                AVVideoHeightKey: videoHeight,
                AVVideoCompressionPropertiesKey: [
                    AVVideoAverageBitRateKey: 8000000, // 增加到8Mbps以提高画质
                    AVVideoMaxKeyFrameIntervalKey: 30, // 每秒30帧时，约每1秒一个关键帧
                    AVVideoProfileLevelKey: AVVideoProfileLevelH264HighAutoLevel, // 使用高配置
                    AVVideoAllowFrameReorderingKey: true, // 允许帧重排序以提高压缩效率
                    AVVideoExpectedSourceFrameRateKey: 30 // 预期输入帧率
                ]
            ]
            
            let writerInput = AVAssetWriterInput(mediaType: .video, outputSettings: videoSettings)
            writerInput.expectsMediaDataInRealTime = true
            
            // 不应用旋转，尝试保持原始方向
            writerInput.transform = CGAffineTransform(rotationAngle: -CGFloat.pi / 2)
            
            // 像素缓冲区适配器 - 使用原始像素缓冲区的尺寸
            let attributes: [String: Any] = [
                kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA, // 使用更常见的像素格式
                kCVPixelBufferWidthKey as String: videoResolution.width,
                kCVPixelBufferHeightKey as String: videoResolution.height,
                kCVPixelBufferIOSurfacePropertiesKey as String: [:], // 添加IO Surface支持
                kCVPixelBufferMetalCompatibilityKey as String: true // 添加Metal兼容性
            ]
            
            let adaptor = AVAssetWriterInputPixelBufferAdaptor(
                assetWriterInput: writerInput, 
                sourcePixelBufferAttributes: attributes
            )
            
            // 添加输入
            if writer.canAdd(writerInput) {
                writer.add(writerInput)
            }
            
            // 开始写入
            writer.startWriting()
            writer.startSession(atSourceTime: .zero)
            
            // 保存引用
            currentVideoWriter = writer
            currentVideoInput = writerInput
            pixelBufferAdaptor = adaptor
            currentVideoURL = videoURL
            recordingStartTime = nil
            lastFrameTime = .zero
            
            // 清理旧文件
//            cleanupOldVideoFiles()
            
            // LogService.shared.debug("设置高画质横屏视频录制 - 分辨率: \(videoWidth)x\(videoHeight), 比特率: 8Mbps")
            
        } catch {
            LogService.shared.error("创建视频写入器失败: \(error)")
        }
    }
    
    /// 轮换视频文件
    private func rotateVideoFile() {
        // 保存当前文件的URL
        let savedURL = currentVideoURL
        
        // 完成当前写入
        if let writer = currentVideoWriter, let input = currentVideoInput {
            input.markAsFinished()
            
            // 重置状态
            currentVideoWriter = nil
            currentVideoInput = nil
            pixelBufferAdaptor = nil
            recordingStartTime = nil
            
            // 异步完成写入/记录结束的时间戳，并记录该视频的路径
            videoCacheList.append(savedURL!)
            writer.finishWriting {
                // 清理可能不再需要的资源
            }
        }
        
        // 立即设置新的录制
        setupNewVideoRecording()
    }
    
    /// 清理旧视频文件
    private func cleanupOldVideoFiles() {
        DispatchQueue.global(qos: .background).async {
            do {
                let tempDir = FileManager.default.temporaryDirectory
                let contents = try FileManager.default.contentsOfDirectory(at: tempDir, includingPropertiesForKeys: nil)
                
                // 只清理缓存视频文件
                let cacheFiles = contents.filter { $0.lastPathComponent.hasPrefix("cache_") && $0.pathExtension == "mp4" }
                let extendedCacheFiles = contents.filter { $0.lastPathComponent.hasPrefix("extended_last_") && $0.pathExtension == "mp4" }
                // 按修改时间排序
                let sortedFiles = cacheFiles.sorted { file1, file2 in
                    let attr1 = try? FileManager.default.attributesOfItem(atPath: file1.path)
                    let attr2 = try? FileManager.default.attributesOfItem(atPath: file2.path)
                    let date1 = attr1?[.modificationDate] as? Date ?? Date()
                    let date2 = attr2?[.modificationDate] as? Date ?? Date()
                    return date1 > date2
                }
                let extendedSortedFiles = extendedCacheFiles.sorted { file1, file2 in
                    let attr1 = try? FileManager.default.attributesOfItem(atPath: file1.path)
                    let attr2 = try? FileManager.default.attributesOfItem(atPath: file2.path)
                    let date1 = attr1?[.modificationDate] as? Date ?? Date()
                    let date2 = attr2?[.modificationDate] as? Date ?? Date()
                    return date1 > date2
                }
                // 保留最新的5个文件，删除其余的
                if sortedFiles.count > 5 {
                    for file in sortedFiles[5...] {
                        try? FileManager.default.removeItem(at: file)
                    }
                }
                if extendedSortedFiles.count > 10 {
                    for file in extendedSortedFiles[10...] {
                        try? FileManager.default.removeItem(at: file)
                    }
                }
            } catch {
                LogService.shared.error("清理旧视频文件失败: \(error)")
            }
        }
    }
    
    /// 申请相册访问权限
    private func requestPhotoLibraryAccess() {
        PHPhotoLibrary.requestAuthorization { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    LogService.shared.info("相册访问权限已授权，视频可以保存")
                case .denied, .restricted:
                    LogService.shared.warning("相册访问权限被拒绝，视频无法保存到相册")
                case .notDetermined:
                    LogService.shared.info("相册权限未确定")
                case .limited:
                    LogService.shared.info("相册访问权限有限，但可以保存视频")
                @unknown default:
                    LogService.shared.warning("相册权限状态未知")
                }
            }
        }
    }
    
    /// 保存当前视频到相册
    /// - Parameter isGoal: 是否进球
    private func saveCurrentVideo(isGoal: Bool, _ success: @escaping FilePathSuccessBlock) {
        // 如果录制功能被禁用，直接返回
        if !isRecordingEnabled {
            LogService.shared.info("视频录制功能已禁用，投篮事件保存成功（无视频）")
            return
        }
        
        pendingSaveOperations += 1
        
        // 获取当前正在写入的视频URL
        guard let currentURL = currentVideoURL else {
            LogService.shared.warning("没有找到可用的视频缓存")
            pendingSaveOperations -= 1
            return
        }
        
        // 在后台线程中保存视频
        videoQueue.async { [weak self] in
            guard let self = self else { return }
            
            if let writer = self.currentVideoWriter {
                // 记住要保存的URL
                let urlToSave = currentURL
                // 结束当前写入并开始新的录制
                self.currentVideoInput?.markAsFinished()
                // 重置状态
                currentVideoWriter = nil
                currentVideoInput = nil
                pixelBufferAdaptor = nil
                recordingStartTime = nil
                writer.finishWriting { [weak self] in
                    guard let self = self else { return }
                    videoCacheList.append(urlToSave)
                    if videoCacheList.count > 0 {
                        //视频片段处理，不足10秒的片段向前截取
                        VideoSegmentHelper.processLastSegment(segments: videoCacheList) { result in
                            switch result {
                                case .success(let processedSegment):
                                //将视频本地路径保存到对应事件的数据库中
                                success(processedSegment.path)
                                // 保存到相册
                                self.saveVideoToPhotoLibrary(url: processedSegment, isGoal: isGoal)
                                case .failure(let error):
                                    print("处理失败: \(error.localizedDescription)")
                                }
                        }
                    }
//                    self.saveVideoToPhotoLibrary(url: urlToSave, isGoal: isGoal)
                    // 添加事件类型标记到日志
                    let eventType = isGoal ? "进球" : "未进球"
//                    LogService.shared.info("正在保存\(eventType)事件视频...")
                    
                    // 设置新的录制
                    if self.currentVideoWriter == nil {
                        self.setupNewVideoRecording()
                    }
                    
                    self.pendingSaveOperations -= 1
                }
            } else {
                // 没有活动的writer，直接保存
//                self.saveVideoToPhotoLibrary(url: currentURL, isGoal: isGoal)
                self.pendingSaveOperations -= 1
            }
        }
    }
    
    /// 保存视频到相册
    /// - Parameters:
    ///   - url: 视频文件URL
    ///   - isGoal: 是否进球
    private func saveVideoToPhotoLibrary(url: URL, isGoal: Bool = false) {
        PHPhotoLibrary.requestAuthorization { status in
            if status == .authorized {
                PHPhotoLibrary.shared().performChanges({
                    // 创建视频资产请求
                    let request = PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: url)
                    // 添加标签以区分进球和未进球视频
                    let eventType = isGoal ? "进球" : "未进球"
                    request?.location = CLLocation(latitude: 0, longitude: 0) // 使用位置存储元数据
                    request?.creationDate = Date()
                    
                    // 尝试设置标题
                    if let request = request {
                        let options = PHContentEditingInputRequestOptions()
                        options.canHandleAdjustmentData = { _ in return true }
                        
                        // 可以在这里添加更多的元数据或修改文件名
                        // 由于PHAsset不直接支持标题，这里只作记录
//                        LogService.shared.debug("保存视频: \(eventType)")
                    }
                }) { success, error in
                    if success {
                        let eventType = isGoal ? "进球" : "未进球"
                        LogService.shared.info("\(eventType)视频成功保存到相册: \(url.lastPathComponent)")
                    } else if let error = error {
                        LogService.shared.error("保存视频失败: \(error.localizedDescription)")
                    }
                }
            } else {
                LogService.shared.warning("没有权限访问相册")
            }
        }
    }
    
    /// 检查分辨率是否有效
    /// - Returns: 分辨率是否有效
    private func isResolutionValid() -> Bool {
        return videoResolution.width > 0 && videoResolution.height > 0
    }
}
class VideoSegmentHelper {
    
    /// 处理最后一个视频片段，不足10秒则从前面补足
    /// - Parameters:
    ///   - segments: 原始视频片段数组
    ///   - completion: 返回处理后的新数组（最后一个片段保证≥10秒）
    static func processLastSegment(segments: [URL],
                                 completion: @escaping (Result<URL, Error>) -> Void) {
        
        guard !segments.isEmpty else {
            completion(.success(URL(string: "")!))
            return
        }
        
        DispatchQueue.global(qos: .utility).async {
            do {
                
                // 2. 检查最后一个片段
                let lastIndex = segments.count - 1
                let lastSegmentUrl = segments[lastIndex]
                let lastAsset = AVAsset(url: lastSegmentUrl)
                let lastDuration = try awaitLastAssetDuration(lastAsset)
                
                // 3. 如果最后一个片段≥10秒，直接返回
                if lastDuration.seconds >= 10 {
                    DispatchQueue.main.async {
                        completion(.success(lastSegmentUrl))
                    }
                    return
                }
                
                // 4. 计算需要补足的时长
                let neededSeconds = 10.0 - lastDuration.seconds
                var collectedRanges = [CMTimeRange(start: .zero, end: lastDuration)]
                var collectedAssets = [lastAsset]
                
                // 5. 从后往前遍历前面的片段
                for i in (0..<lastIndex).reversed() {
                    let asset = AVAsset(url: segments[i])
                    let duration = try awaitLastAssetDuration(asset)
                    
                    // 计算可以提供的时长
                    let availableSeconds = min(duration.seconds, neededSeconds - collectedRanges.reduce(0) { $0 + $1.duration.seconds })
                    if availableSeconds <= 0 { break }
                    
                    // 记录需要截取的范围（从结束位置往前取）
                    let startTime = CMTime(seconds: duration.seconds - availableSeconds,
                                        preferredTimescale: duration.timescale)
                    let range = CMTimeRange(start: startTime, end: duration)
                    
                    collectedRanges.insert(range, at: 0)
                    collectedAssets.insert(asset, at: 0)
                    
                    // 如果已经补足，退出循环
                    if collectedRanges.reduce(0, { $0 + $1.duration.seconds }) >= 10 {
                        break
                    }
                }
                
                // 6. 合并片段
                let tempDir = FileManager.default.temporaryDirectory
                let outputURL = tempDir.appendingPathComponent("extended_last_\(Date().timeIntervalSince1970).mp4")
                
                guard let mergedURL = mergeSegments(assets: collectedAssets,
                                                  timeRanges: collectedRanges,
                                                  outputURL: outputURL) else {
                    throw NSError(domain: "VideoMergeError", code: 0, userInfo: [NSLocalizedDescriptionKey: "合并视频失败"])
                }
                
                DispatchQueue.main.async {
                    completion(.success(mergedURL))
                }
                
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    /// 异步获取视频时长
    private static func awaitLastAssetDuration(_ asset: AVAsset) throws -> CMTime {
        let semaphore = DispatchSemaphore(value: 0)
        var duration: CMTime?
        var loadError: Error?
        
        asset.loadValuesAsynchronously(forKeys: ["duration"]) {
            defer { semaphore.signal() }
            do {
                var error: NSError?
                let status = asset.statusOfValue(forKey: "duration", error: &error)
                if status == .failed, let error = error {
                    throw error
                }
                duration = asset.duration
            } catch {
                loadError = error
            }
        }
        
        semaphore.wait()
        
        if let error = loadError { throw error }
        guard let duration = duration else { throw NSError(domain: "VideoError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法获取视频时长"]) }
        return duration
    }
    
    /// 合并多个视频片段
    private static func mergeSegments(assets: [AVAsset],
                                    timeRanges: [CMTimeRange],
                                    outputURL: URL) -> URL? {
        // 1. 验证输入
        guard !assets.isEmpty, assets.count == timeRanges.count else {
            print("Error: 输入参数不匹配")
            return nil
        }
        let composition = AVMutableComposition()
        
        // 2. 添加视频轨道
        guard let videoTrack = composition.addMutableTrack(
            withMediaType: .video,
            preferredTrackID: kCMPersistentTrackID_Invalid
        ) else {
            print("Error: 无法创建视频轨道")
            return nil
        }
        // 3. 添加音频轨道（可选）
        let audioTrack = composition.addMutableTrack(
            withMediaType: .audio,
            preferredTrackID: kCMPersistentTrackID_Invalid
        )
        var currentTime = CMTime.zero
        // 4. 插入各段轨道
        for (asset, timeRange) in zip(assets, timeRanges) {
            do {
                // 验证时间范围有效性
                guard timeRange.start.isValid && timeRange.duration.isValid else {
                    print("Warning: 跳过无效时间范围 \(timeRange)")
                    continue
                }

                // 插入视频轨道
                if let assetVideoTrack = asset.tracks(withMediaType: .video).first {
                    try videoTrack.insertTimeRange(
                        timeRange,
                        of: assetVideoTrack,
                        at: currentTime
                    )
                }
                // 插入音频轨道
                if let assetAudioTrack = asset.tracks(withMediaType: .audio).first,
                   let audioTrack = audioTrack {
                    try audioTrack.insertTimeRange(
                        timeRange,
                        of: assetAudioTrack,
                        at: currentTime
                    )
                }

                currentTime = CMTimeAdd(currentTime, timeRange.duration)
            } catch {
                print("插入轨道失败: \(error.localizedDescription)")
                return nil
            }
        }

        // 5. 配置导出（关键修改）
        guard let exportSession = AVAssetExportSession(
            asset: composition,
            presetName: AVAssetExportPresetPassthrough // 修改为兼容性更好的预设
        ) else {
            print("Error: 无法创建导出会话")
            return nil
        }

        // 6. 确保输出目录可用
        do {
            try FileManager.default.createDirectory(
                at: outputURL.deletingLastPathComponent(),
                withIntermediateDirectories: true
            )
            if FileManager.default.fileExists(atPath: outputURL.path) {
                try FileManager.default.removeItem(at: outputURL)
            }
        } catch {
            print("文件操作失败: \(error.localizedDescription)")
            return nil
        }

        // 7. 处理视频方向（新增）
        if let videoTrack = composition.tracks(withMediaType: .video).first {
            let naturalSize = videoTrack.naturalSize
            let portraitSize = CGSize(width: naturalSize.height, height: naturalSize.width)
            
            let videoComposition = AVMutableVideoComposition()
            videoComposition.renderSize = portraitSize
            videoComposition.frameDuration = CMTime(value: 1, timescale: 30)
            
            let instruction = AVMutableVideoCompositionInstruction()
            instruction.timeRange = CMTimeRange(start: .zero, duration: composition.duration)
            
            let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: videoTrack)
            videoTrack.preferredTransform = CGAffineTransform(a: 0.0, b: -1.0, c: 1.0, d: 0.0, tx: 0.0, ty: 0.0)
            instruction.layerInstructions = [layerInstruction]
            videoComposition.instructions = [instruction]
            
            exportSession.videoComposition = videoComposition
        }
        exportSession.outputURL = outputURL
        exportSession.outputFileType = .mp4
        exportSession.shouldOptimizeForNetworkUse = false // 关闭优化提高兼容性
        // 8. 执行导出（同步方式）
        let semaphore = DispatchSemaphore(value: 0)
        var resultURL: URL?
        
        exportSession.exportAsynchronously {
            switch exportSession.status {
            case .completed:
                resultURL = outputURL
            case .failed:
                print("导出失败: \(exportSession.error?.localizedDescription ?? "未知错误")")
            case .cancelled:
                print("导出取消")
            default: break
            }
            semaphore.signal()
        }
        
        _ = semaphore.wait(timeout: .distantFuture)
        return resultURL
    }
}
