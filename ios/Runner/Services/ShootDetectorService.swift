//
//  ShootDetectorService.swift
//  Demo
//
//  Created by Vien on 2025/3/29.
//

import Foundation
import UIKit

/// 投篮出手检测服务代理协议
protocol ShootDetectorServiceDelegate: AnyObject {
    /// 当检测到投篮出手时调用
    func shootDetectorService(_ service: ShootDetectorService, didDetectShoot timestamp: TimeInterval)
    
}

/// 投篮出手检测服务
class ShootDetectorService {
    // MARK: - 常量
    
    private struct Constants {
        static let minIntersectionRatio: Float = 0.8 // 最小相交比例
        static let timeWindow: TimeInterval = 0.5 // 时间窗口（秒）
        static let requiredFrames: Int = 3 // 所需有效帧数
        static let cooldownTime: TimeInterval = 1 // 冷却时间（秒）
        static let minVarianceThreshold: CGFloat = 99 // 最小方差阈值，用于判定篮球移动
    }
    
    // MARK: - 属性
    
    /// 代理
    weak var delegate: ShootDetectorServiceDelegate?
    
    /// 有效帧记录数组，每个元素为时间戳
    private var validFrames: [TimeInterval] = []
    
    /// 篮球位置记录，存储时间窗口内的篮球中心点坐标
    private var basketballPositions: [(timestamp: TimeInterval, center: CGPoint)] = []
    
    /// 上一次检测到出手的时间
    private var lastShootTime: TimeInterval = 0
    
    // MARK: - 公共方法
    
    /// 处理新的一帧
    /// - Parameters:
    ///   - basketballs: 检测到的篮球位置数组
    ///   - backboardRect: 篮板位置
    ///   - timestamp: 当前时间戳
    func processFrame(basketballs: [CGRect], backboardRect: CGRect,hoopRect: CGRect , timestamp: TimeInterval) {
        // 检查冷却时间
        guard timestamp - lastShootTime >= Constants.cooldownTime else {
            return
        }
        
        // 清理超过时间窗口的数据
        let cutoffTime = timestamp - Constants.timeWindow
        validFrames.removeAll { $0 < cutoffTime }
        basketballPositions.removeAll { $0.timestamp < cutoffTime }
        
        // 如果没有检测到篮球或篮板，直接返回
        guard !basketballs.isEmpty else {
            return
        }
        
        // 扩展篮板区域
        let extendedBackboardRect = CGRect(
            x: hoopRect.minX,
            y: backboardRect.minY-backboardRect.height/6,
            width: backboardRect.maxX - hoopRect.minX,
            height: backboardRect.height + backboardRect.height/3
        )
        
        // 篮球分析
        var bestBasketball: CGRect? = nil
        var maxIntersectionRatio: Float = 0
        
        for basketball in basketballs {
            let ratioOfHoop = calculateIntersectionRatio(basketball, hoopRect)
            // 处理篮筐内误识别
            if ratioOfHoop > 0.99 {
                continue
            }
            // 使用扩展后的篮板区域计算交集比例
            let ratio = calculateIntersectionRatio(basketball, extendedBackboardRect)
            if ratio > maxIntersectionRatio {
                maxIntersectionRatio = ratio
                bestBasketball = basketball
            }
        }
      
        // print("时间窗口内检测到篮球位于篮板帧数量: \(validFrames.count)")
        
        // 如果相交比例满足条件，记录当前帧和篮球中心位置
        if maxIntersectionRatio >= Constants.minIntersectionRatio, let basketball = bestBasketball {
            validFrames.append(timestamp)
            
            // 记录篮球中心位置
            let center = CGPoint(x: basketball.midX, y: basketball.midY)
            basketballPositions.append((timestamp: timestamp, center: center))
            
//            print("记录篮球位置: x=\(center.x), y=\(center.y)")
            
            // 如果有效帧数达到要求，检查方差
            if validFrames.count >= Constants.requiredFrames {
                // 计算方差
                let (xVariance, yVariance) = calculatePositionVariance()
                let totalVariance = xVariance + yVariance
                
                if totalVariance > 10 {
                  print("篮球位置方差: xVar=\(xVariance), yVar=\(yVariance), 总方差=\(totalVariance)")
                }
                // 判断是否为真实投篮
                if totalVariance >= Constants.minVarianceThreshold {
                    print("检测到真实投篮动作，总方差=\(totalVariance)")
                    delegate?.shootDetectorService(self, didDetectShoot: timestamp)
                    lastShootTime = timestamp
                    validFrames.removeAll() // 清空记录
                    basketballPositions.removeAll()
                } else {
//                    print("疑似误检测（静止物体），总方差=\(totalVariance)")
                    // 如果方差过小，只移除最早的一个记录，保持滑动窗口效果
                    if validFrames.count > Constants.requiredFrames {
                        validFrames.removeFirst()
                    }
                    if basketballPositions.count > Constants.requiredFrames {
                        basketballPositions.removeFirst()
                    }
                }
//                print("validFrames: ", validFrames)
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 计算两个矩形的相交比例
    private func calculateIntersectionRatio(_ rect1: CGRect, _ rect2: CGRect) -> Float {
        let intersectionRect = rect1.intersection(rect2)
        
        // 如果没有交集，比例为0
        if intersectionRect.isEmpty {
            return 0.0
        }
        
        let intersectionArea = intersectionRect.width * intersectionRect.height
        let rect1Area = rect1.width * rect1.height
        
        return Float(intersectionArea / rect1Area)
    }
    
    /// 计算篮球位置的方差
    /// - Returns: X和Y方向的方差
    private func calculatePositionVariance() -> (xVariance: CGFloat, yVariance: CGFloat) {
        guard basketballPositions.count > 1 else {
            return (0, 0) // 至少需要2个点才能计算方差
        }
        
        // 提取X和Y坐标
        let xValues = basketballPositions.map { $0.center.x }
        let yValues = basketballPositions.map { $0.center.y }
        
        // 计算均值
        let xMean = xValues.reduce(0, +) / CGFloat(xValues.count)
        let yMean = yValues.reduce(0, +) / CGFloat(yValues.count)
        
        // 计算方差
        let xVariance = xValues.map { pow($0 - xMean, 2) }.reduce(0, +) / CGFloat(xValues.count)
        let yVariance = yValues.map { pow($0 - yMean, 2) }.reduce(0, +) / CGFloat(yValues.count)
        
        return (xVariance, yVariance)
    }
}

