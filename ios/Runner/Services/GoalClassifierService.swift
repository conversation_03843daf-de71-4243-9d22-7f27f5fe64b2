//
//  GoalService.swift
//  Demo
//
//  Created by <PERSON><PERSON> on 2025/4/19.
//


import UIKit
import MediaPipeTasksVision
import AVFoundation

/**
 This protocol must be adopted by any class that wants to get the classification results of the image classifier in live stream mode.
 */
protocol GoalClassifierServiceLiveStreamDelegate: AnyObject {
  func goalClassifierService(_ goalClassifierService: GoalClassifierService,
                             didFinishClassification result: ClassifierResultBundle?,
                             error: Error?)
  func goalClassifierService(_ goalClassifierService: GoalClassifierService, 
                             didDetectGoal timestamp: TimeInterval)
}

/**
 This protocol must be adopted by any class that wants to take appropriate actions during  different stages of image classification on videos.
 */
protocol GoalClassifierServiceVideoDelegate: AnyObject {
 func goalClassifierService(_ goalClassifierService: GoalClassifierService,
                                  didFinishClassificationOnVideoFrame index: Int)
 func goalClassifierService(_ goalClassifierService: GoalClassifierService,
                             willBeginClassification totalframeCount: Int)
}


// Initializes and calls the MediaPipe APIs for classification.
class GoalClassifierService: NSObject {

  weak var liveStreamDelegate: GoalClassifierServiceLiveStreamDelegate?
  weak var videoDelegate: GoalClassifierServiceVideoDelegate?

  var imageClassifier: ImageClassifier?
  private(set) var runningMode: RunningMode
  private var scoreThreshold: Float
  private var maxResult: Int
  private var modelPath: String
  private var delegate: GoalClassifierClassifierDelegate
  
  // 窗口缓存数组，用于存储分类结果
  private var resultWindow: [(category: String?, score: Float, timestamp: TimeInterval)] = []
  private let windowSize = 6
  private let minGoalFrames = 2
  private let minGoalScore: Float = 0.61
  private let maxGoalScore: Float = 0.82
  private var skipFrameCount = 0
  private var isSkippingFrames = false
  private let framesToSkipAfterGoal = 20
  // 用于处理静态误识别情况，例如篮网挂框时goal得分接近阈值
  private var consecutiveGoals = 0
  private let maxConsecutiveGoals = 12
  
  private var frameIdx = 0
  private var frameInterval = 2
  private var lastFrameIsGoal = false
  

  // MARK: - Custom Initializer
  private init?(model: ClassifierModel,
                scoreThreshold: Float,
                maxResult: Int,
                runningMode:RunningMode,
                delegate: GoalClassifierClassifierDelegate) {
    guard let modelPath = model.modelPath else { return nil }
    self.modelPath = modelPath
    self.scoreThreshold = scoreThreshold
    self.runningMode = runningMode
    self.maxResult = maxResult
    self.delegate = delegate
    super.init()

    createImageClassifier()
  }

  private func createImageClassifier() {
    let imageClassifierOptions = ImageClassifierOptions()
    imageClassifierOptions.runningMode = runningMode
    imageClassifierOptions.scoreThreshold = scoreThreshold
    imageClassifierOptions.maxResults = maxResult
    imageClassifierOptions.baseOptions.modelAssetPath = modelPath
    imageClassifierOptions.baseOptions.delegate = delegate.delegate
    if runningMode == .liveStream {
      imageClassifierOptions.imageClassifierLiveStreamDelegate = self
    }
    do {
      imageClassifier = try ImageClassifier(options: imageClassifierOptions)
    }
    catch {
      LogService.shared.error("创建图像分类器失败: \(error)")
    }
  }

  // MARK: - Static Initializers
  static func videoGoalClassifierService(
    model: ClassifierModel,
    scoreThreshold: Float,
    maxResult: Int,
    videoDelegate: GoalClassifierServiceVideoDelegate?,
    delegate: GoalClassifierClassifierDelegate) -> GoalClassifierService? {
    let goalClassifierService = GoalClassifierService(
      model: model,
      scoreThreshold: scoreThreshold,
      maxResult: maxResult,
      runningMode: .video,
      delegate: delegate)
    goalClassifierService?.videoDelegate = videoDelegate

    return goalClassifierService
  }

  static func liveStreamClassifierService(
    model: ClassifierModel,
    scoreThreshold: Float,
    maxResult: Int,
    liveStreamDelegate: GoalClassifierServiceLiveStreamDelegate?,
    delegate: GoalClassifierClassifierDelegate) -> GoalClassifierService? {
    let goalClassifierService = GoalClassifierService(
      model: model,
      scoreThreshold: scoreThreshold,
      maxResult: maxResult,
      runningMode: .liveStream,
      delegate: delegate)
    goalClassifierService?.liveStreamDelegate = liveStreamDelegate

    return goalClassifierService
  }

  static func stillGoalClassifierService(
    model: ClassifierModel,
    scoreThreshold: Float,
    maxResult: Int,
    delegate: GoalClassifierClassifierDelegate) -> GoalClassifierService? {
      let goalClassifierService = GoalClassifierService(
        model: model,
        scoreThreshold: scoreThreshold,
        maxResult: maxResult,
        runningMode: .image,
        delegate: delegate)

      return goalClassifierService
  }

  // MARK: - Classification Methods for Different Modes
  /**
   This method return ImageClassifierResult and infrenceTime when receive an image
   **/
  func classify(image: UIImage) -> ClassifierResultBundle? {
    guard let mpImage = try? MPImage(uiImage: image) else {
      return nil
    }
    do {
      let startDate = Date()
      let result = try imageClassifier?.classify(image: mpImage)
      let inferenceTime = Date().timeIntervalSince(startDate) * 1000
      return ClassifierResultBundle(inferenceTime: inferenceTime, imageClassifierResults: [result])
    } catch {
        LogService.shared.error("分类错误: \(error)")
        return nil
    }
  }

  func classifyAsync(
    sampleBuffer: CMSampleBuffer,
    orientation: UIImage.Orientation,
    timeStamps: Int) {
    guard let image = try? MPImage(sampleBuffer: sampleBuffer, orientation: orientation) else {
      return
    }
    do {
      try imageClassifier?.classifyAsync(image: image, timestampInMilliseconds: timeStamps)
    } catch {
      LogService.shared.error("异步分类错误: \(error)")
    }
  }
  
  func classifyAsync(
    image: UIImage,
    timeStamps: Int) {
    
    self.frameIdx += 1
    // 跳帧，若为进球则不跳帧
    if self.frameIdx % self.frameInterval == 0 && self.lastFrameIsGoal == false {
      return
    }
    
    // 如果正在冷却跳帧，则跳过处理
    if isSkippingFrames {
      skipFrameCount += 1
      if skipFrameCount >= framesToSkipAfterGoal {
        isSkippingFrames = false
        skipFrameCount = 0
      }
      // 设置 consecutiveGoals 进球后因为冷却跳帧导致 还是会多误识别一次进球
      consecutiveGoals = maxConsecutiveGoals
      return
    }
    
    guard let mpImage = try? MPImage(uiImage: image) else {
      return
    }
    do {
      try imageClassifier?.classifyAsync(image: mpImage, timestampInMilliseconds: timeStamps)
    } catch {
      LogService.shared.error("异步分类错误: \(error)")
    }
  }

}

// MARK: - ImageClassifierLiveStreamDelegate
extension GoalClassifierService: ImageClassifierLiveStreamDelegate {

  func imageClassifier(
    _ imageClassifier: ImageClassifier,
    didFinishClassification result: ImageClassifierResult?,
    timestampInMilliseconds: Int,
    error: Error?) {
      // 如果正在跳帧，则不处理结果
      if isSkippingFrames {
        return
      }
      
      guard let result = result else {
        liveStreamDelegate?.goalClassifierService(self, didFinishClassification: nil, error: error)
        return
      }
      
      let resultBundle = ClassifierResultBundle(
        inferenceTime: Date().timeIntervalSince1970 * 1000 - Double(timestampInMilliseconds),
        imageClassifierResults: [result])
      
      // 处理分类结果
      processClassificationResult(result, timestamp: Date().timeIntervalSince1970)
      
      liveStreamDelegate?.goalClassifierService(self, didFinishClassification: resultBundle, error: nil)
  }
  
  // 处理分类结果并更新窗口缓存
  private func processClassificationResult(_ result: ImageClassifierResult, timestamp: TimeInterval) {

    guard let classification =  result.classificationResult.classifications.first else {
      return
    }
    
    guard let category = classification.categories.first else {
      return
    }
    
    let categoryName = category.categoryName
    let score = category.score
    
    // 格式化分类结果
    // let formattedCategory = categoryName != nil ? "\(categoryName!)" : "unknown"
    // let formattedResult = "[\(formattedCategory):\(String(format: "%.3f", score))]"
    // LogService.shared.debug("分类结果: \(formattedResult)")
    
    // 添加到窗口缓存
    resultWindow.append((category: categoryName, score: score, timestamp: timestamp))
    
    // 记录consecutiveGoals
    if categoryName == "goal" && consecutiveGoals < maxConsecutiveGoals {
      consecutiveGoals += 1
    }
    if categoryName == "miss" && consecutiveGoals > 0 {
      consecutiveGoals -= 1
    }
    
    // 保持窗口大小
    if resultWindow.count > windowSize {
      resultWindow.removeFirst()
    }
    
    // 如果窗口已满，检查是否满足进球条件
    if resultWindow.count == windowSize {
      // 计算窗口中分类为"goal"且分数超过阈值的帧数
      let goalFrames: [(category: String?, score: Float, timestamp: TimeInterval)] = resultWindow.filter { $0.category == "goal" && $0.score >= minGoalScore }
      let goalFrames1: [(category: String?, score: Float, timestamp: TimeInterval)] = resultWindow.filter { $0.category == "goal" && $0.score >= maxGoalScore }
      let goalFrames2: [(category: String?, score: Float, timestamp: TimeInterval)] = resultWindow.filter { $0.category == "goal" && $0.score >= 0.5 }

      // 格式化窗口内容
      let formattedWindow = resultWindow.map { item -> String in 
        let categoryString = item.category != nil ? "\(item.category!)" : "unknown"
        return "[\(categoryString):\(String(format: "%.3f", item.score))]" 
      }.joined(separator: ", ")

      // 打印日志控制
      if goalFrames2.count >= 1 && consecutiveGoals < maxConsecutiveGoals {
//        LogService.shared.debug("窗口内容: \(formattedWindow)")
      }
      if consecutiveGoals == maxConsecutiveGoals {
        LogService.shared.warning("出现误识别 consecutiveGoals: \(consecutiveGoals)")
      }
      
      
      // 如果满足条件，通知进球并清空窗口
      if (goalFrames.count >= minGoalFrames || goalFrames1.count >= 1) && consecutiveGoals < maxConsecutiveGoals {
        // LogService.shared.info("检测到进球！有\(goalFrames.count)帧分类为goal且分数超过\(minGoalScore)")
        // 使用描述性字符串直接输出，避免复杂对象序列化问题
//        LogService.shared.debug("窗口内容: \(formattedWindow)")
        // 通知代理检测到进球
        liveStreamDelegate?.goalClassifierService(self, didDetectGoal: timestamp)
        
        // 清空窗口缓存
        resultWindow.removeAll()
        
        // 开始跳帧
        isSkippingFrames = true
        skipFrameCount = 0
      }
    }
  }
}

/// A result from inference, the time it takes for inference to be
/// performed.
struct ClassifierResultBundle {
  let inferenceTime: Double
  let imageClassifierResults: [ImageClassifierResult?]
  var size: CGSize = .zero
}
