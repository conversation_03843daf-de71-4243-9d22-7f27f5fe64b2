import Foundation
import GRDB
import UIKit
typealias SaveToDBSuccessBlock = (Bool) -> ()
// MARK: - ShootEvent 记录模型
struct ShootEventRecord: Codable, FetchableRecord, PersistableRecord {
    var id: Int64?
    let trainingId:String
    var startTime: String // 投篮会话开始时间
    var shootTime: Double? // 投篮时间
    var isGoal: Bool // 是否进球
    var goalTime: Double? // 进球时间
    var playerImagePath: String? // 投篮人图像存储路径
    var playerImgUploaded: Bool // 投篮人图像是否已上传
    var playerConfidence: Double // 投篮人置信度
    var filePath: String? //视频保存的本地地址
    var filePathUploaded: Bool //投篮视频是否已上传
    var shootCoord: CGPoint? //投篮的点位
    var createdAt: Date // 记录创建时间
    
    // 表名
    static var databaseTableName: String { "shoot_events" }
    
    // 列定义
    enum Columns {
        static let id = Column("id")
        static let trainingId = Column("training_id")
        static let startTime = Column("start_time")
        static let shootTime = Column("shoot_time")
        static let isGoal = Column("is_goal")
        static let goalTime = Column("goal_time")
        static let playerImagePath = Column("player_image_path")
        static let playerImgUploaded = Column("player_image_uploaded")
        static let playerConfidence = Column("player_confidence")
        static let filePath = Column("file_path")
        static let filePathUploaded = Column("file_path_uploaded")
        static let shootCoord = Column("shoot_coord")
        static let createdAt = Column("created_at")
    }
    
    // 为每个属性提供对应的列名
    enum CodingKeys: String, CodingKey {
        case id
        case trainingId = "training_id"
        case startTime = "start_time"
        case shootTime = "shoot_time"
        case isGoal = "is_goal"
        case goalTime = "goal_time"
        case playerImagePath = "player_image_path"
        case playerImgUploaded = "player_image_uploaded"
        case playerConfidence = "player_confidence"
        case filePath = "file_path"
        case filePathUploaded = "file_path_uploaded"
        case shootCoord = "shoot_coord"
        case createdAt = "created_at"
    }
    
    // 初始化方法
    init(id: Int64? = nil,
         trainingId: String = CameraViewController.fixedTrainingId,
         startTime: String,
         shootTime: Double?, 
         isGoal: Bool,
         goalTime: Double?, 
         playerImagePath: String?,
         playerImgUploaded: Bool = false,
         playerConfidence: Double,
         filePath:String?,
         filePathUploaded: Bool = false,
         shootCoord:CGPoint?,
         createdAt: Date = Date()) {
        self.id = id
        self.startTime = startTime
        self.shootTime = shootTime
        self.isGoal = isGoal
        self.goalTime = goalTime
        self.playerImagePath = playerImagePath
        self.playerImgUploaded = playerImgUploaded
        self.playerConfidence = playerConfidence
        self.filePath = filePath
        self.filePathUploaded = filePathUploaded
        self.shootCoord = shootCoord
        self.createdAt = createdAt
        self.trainingId = trainingId
    }
}

// MARK: - 数据库管理器
class DatabaseManager {
    // 单例模式
    static let shared = DatabaseManager()
    
    // 数据库连接池
    private var dbPool: DatabasePool!
    
    // 私有初始化方法
    private init() {
        setupDatabase()
    }
    
    // 设置数据库
    private func setupDatabase() {
        do {
            // 获取文档目录路径
            let databaseURL = try FileManager.default
                .url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
                .appendingPathComponent("basketball.sqlite")
            
            // 创建数据库连接池
            dbPool = try DatabasePool(path: databaseURL.path)
            
            // 创建表
            try dbPool.write { db in
                // 删除旧表
//                try db.drop(table: ShootEventRecord.databaseTableName)
                try db.create(table: ShootEventRecord.databaseTableName, ifNotExists: true) { t in
                    t.autoIncrementedPrimaryKey(ShootEventRecord.Columns.id.name)
                    t.column(ShootEventRecord.Columns.trainingId.name, .text).notNull()
                    t.column(ShootEventRecord.Columns.startTime.name, .text).notNull()
                    t.column(ShootEventRecord.Columns.shootTime.name, .double)
                    t.column(ShootEventRecord.Columns.isGoal.name, .boolean).notNull()
                    t.column(ShootEventRecord.Columns.goalTime.name, .double)
                    t.column(ShootEventRecord.Columns.playerImagePath.name, .text)
                    t.column(ShootEventRecord.Columns.playerImgUploaded.name, .boolean).notNull()
                    t.column(ShootEventRecord.Columns.playerConfidence.name, .double).notNull()
                    t.column(ShootEventRecord.Columns.filePath.name, .text)
                    t.column(ShootEventRecord.Columns.filePathUploaded.name, .boolean).notNull()
                    t.column(ShootEventRecord.Columns.shootCoord.name, .any)
                    t.column(ShootEventRecord.Columns.createdAt.name, .datetime).notNull()
                }
                
                // 单独创建索引
                try db.execute(sql: "CREATE INDEX IF NOT EXISTS index_shoot_events_on_start_time ON \(ShootEventRecord.databaseTableName) (\(ShootEventRecord.Columns.startTime.name))")
            }
            print("数据库初始化成功")
            try dbPool.read { db in
                let columns = try db.columns(in: "shoot_events")
                columns.forEach { column in
                    print("""
                    列名: \(column.name)
                    类型: \(column.type)
                    是否非空: \(column.isNotNull)
                    默认值: \(column.defaultValueSQL ?? "无")
                    """)
                }
            }
        } catch {
            print("数据库初始化失败: \(error)")
        }
    }
    
    // 保存图片到文档目录
    func saveImage(_ image: UIImage) -> String? {
        guard let data = image.jpegData(compressionQuality: 0.8) else {
            return nil
        }
        
        let fileName = "\(UUID().uuidString).jpg"
        let fileURL = getDocumentsDirectory().appendingPathComponent(fileName)
        
        do {
            try data.write(to: fileURL)
            return fileURL.path
        } catch {
            print("保存图片失败: \(error)")
            return nil
        }
    }
    
    // 获取文档目录
    func getDocumentsDirectory() -> URL {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
    }
    
    // 保存投篮事件
    func saveShootEvent(_ event: ShootEventRecord, _ success: @escaping (ShootEventRecord) -> Void) {
        backgroundQueue.async { [weak self] in
            guard let self = self else { return }
            
//            var playerImagePath: String? = nil
//            
//            // 如果有投篮人图像，保存到文件系统
//            if let playerImage = event.playerImage {
//                playerImagePath = self.saveImage(playerImage)
//            }
            
//            // 创建记录
//            let record = ShootEventRecord(
//                startTime: startTime,
//                shootTime: event.shootTime.map { Double($0) },
//                isGoal: event.isGoal,
//                goalTime: event.goalTime.map { Double($0) },
//                playerImagePath: playerImagePath,
//                playerConfidence: Double(event.playerConfidence),
//                filePath: filePath,
//                shootCoord: event.shootCoord
//            )
            
            // 保存到数据库
            do {
                try self.dbPool.write { db in
                    try event.insert(db)
                    // 获取最后插入的ID
                    let lastID = db.lastInsertedRowID
                    print("获取的投篮数据lastIDlastID: \(lastID)")

//                     查询刚插入的数据
                    if let shootEventRecord = try ShootEventRecord.fetchOne(db, key: lastID) {
                        print("获取的投篮数据: \(shootEventRecord)")
                        success(shootEventRecord)
                    }
                }
                print("投篮事件保存成功")
            } catch {
                print("投篮事件保存失败: \(error)")
            }
        }
    }
    
    // 根据开始时间获取投篮事件组
    func getShootEvents(byStartTime startTime: String) -> [ShootEventRecord] {
        do {
            return try dbPool.read { db in
                return try ShootEventRecord
                    .filter(ShootEventRecord.Columns.startTime == startTime)
                    .order(ShootEventRecord.Columns.createdAt)
                    .fetchAll(db)
            }
        } catch {
            print("查询投篮事件失败: \(error)")
            return []
        }
    }
    
    // MARK: - 根据比赛id获取投篮事件组
    func getShootEventList(byTrainingID trainingId: String) -> [ShootEventRecord] {
        do {
            return try dbPool.read { db in
                return try ShootEventRecord
                    .filter(ShootEventRecord.Columns.trainingId == trainingId)
                    .order(ShootEventRecord.Columns.createdAt)
                    .fetchAll(db)
            }
        } catch {
            print("查询投篮事件失败: \(error)")
            return []
        }
    }
    // MARK: - 根据事件id获取投篮事件
    func getShootEvents(byID id: String) -> [ShootEventRecord] {
        do {
            return try dbPool.read { db in
                return try ShootEventRecord
                    .filter(ShootEventRecord.Columns.id == id)
                    .order(ShootEventRecord.Columns.createdAt)
                    .fetchAll(db)
            }
        } catch {
            print("查询投篮事件失败: \(error)")
            return []
        }
    }
    // MARK: - 更新事件的状态
    func updateEventStatus(shootEvent: ShootEventRecord) {
        // 更新示例
        do {
            try dbPool.write { db in
                try shootEvent.update(db)
                print("数据库更新成功")
            }
        } catch {
            print("数据库操作失败: \(error)")
        }
    }
    // 获取所有开始时间（用于分组）
    func getAllStartTimes() -> [String] {
        do {
            return try dbPool.read { db in
                let startTimes = try String.fetchAll(
                    db,
                    sql: "SELECT DISTINCT \(ShootEventRecord.Columns.startTime.name) FROM \(ShootEventRecord.databaseTableName) ORDER BY \(ShootEventRecord.Columns.startTime.name) DESC"
                )
                return startTimes
            }
        } catch {
            print("查询开始时间失败: \(error)")
            return []
        }
    }
    
    // 获取图片
    func getImage(fromPath path: String) -> UIImage? {
        let fileURL = getDocumentsDirectory().appendingPathComponent(path)
        do {
            let data = try Data(contentsOf: fileURL)
            return UIImage(data: data)
        } catch {
            print("获取图片失败: \(error)")
            return nil
        }
    }
    
    // 后台队列
    private let backgroundQueue = DispatchQueue(label: "com.basketball.databaseQueue", qos: .background)
} 
