import Foundation

/// 日志级别
enum LogLevel {
    case debug
    case info
    case warning
    case error
    
    var description: String {
        switch self {
        case .debug: return "调试"
        case .info: return "信息"
        case .warning: return "警告"
        case .error: return "错误"
        }
    }
}

/// 日志服务代理协议
protocol LogServiceDelegate: AnyObject {
    /// 当有新日志时调用
    func logService(_ service: LogService, didGenerateLog message: String, level: LogLevel, timestamp: Date)
}

/// 日志条目模型
struct LogEntry {
    let message: String
    let level: LogLevel
    let timestamp: Date
    
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter.string(from: timestamp)
    }
    
    var formattedMessage: String {
        return "[\(level.description)] \(message)"
    }
    
    var fullFormattedMessage: String {
        return "\(formattedTimestamp) [\(level.description)] \(message)"
    }
}

/// 日志服务
class LogService {
    // MARK: - 单例
    static let shared = LogService()
    
    // MARK: - 属性
    
    /// 代理集合
    private var delegates = NSHashTable<AnyObject>.weakObjects()
    
    /// 日志历史
    private(set) var logHistory: [LogEntry] = []
    
    /// 最大日志条目数量
    private let maxLogEntries = 1000
    
    /// 日志文件URL
    private var logFileURL: URL? {
        let fileManager = FileManager.default
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        return documentsDirectory.appendingPathComponent("app_logs.txt")
    }
    /// 是否保存到文件
    var saveToFile = true
    /// 日志文件最大大小（字节），默认1MB
    var maxLogFileSize: Int = 1_048_576
    
    /// 是否打印到控制台
    var printToConsole = true
    
    // MARK: - 初始化方法
    
    private init() {
        // 私有初始化，确保单例
    }
    
    // MARK: - 公共方法
    
    /// 添加代理
    /// - Parameter delegate: 日志服务代理
    func addDelegate(_ delegate: LogServiceDelegate) {
        delegates.add(delegate)
    }
    
    /// 移除代理
    /// - Parameter delegate: 日志服务代理
    func removeDelegate(_ delegate: LogServiceDelegate) {
        delegates.remove(delegate)
    }
    
    /// 记录调试级别日志
    /// - Parameter message: 日志消息
    func debug(_ message: String) {
        log(message, level: .debug)
    }
    
    /// 记录信息级别日志
    /// - Parameter message: 日志消息
    func info(_ message: String) {
        log(message, level: .info)
    }
    
    /// 记录警告级别日志
    /// - Parameter message: 日志消息
    func warning(_ message: String) {
        log(message, level: .warning)
    }
    
    /// 记录错误级别日志
    /// - Parameter message: 日志消息
    func error(_ message: String) {
        log(message, level: .error)
    }
    
    /// 记录日志
    /// - Parameters:
    ///   - message: 日志消息
    ///   - level: 日志级别
    func log(_ message: String, level: LogLevel) {
        let timestamp = Date()
        let entry = LogEntry(message: message, level: level, timestamp: timestamp)
        
        // 添加到历史记录
        logHistory.append(entry)
        
        // 如果超过最大数量，删除最旧的
        if logHistory.count > maxLogEntries {
            logHistory.removeFirst(logHistory.count - maxLogEntries)
        }
        
        // 打印到控制台
        if printToConsole {
            let levelStr = "[\(level.description)]"
            print("\(entry.formattedTimestamp) \(levelStr) \(message)")
        }
        // 保存到文件
        if saveToFile {
            saveLogToFile(entry: entry)
        }
        // 通知所有代理
        for delegate in delegates.allObjects {
            if let delegate = delegate as? LogServiceDelegate {
                delegate.logService(self, didGenerateLog: message, level: level, timestamp: timestamp)
            }
        }
    }
    // MARK: - 文件操作
        
    /// 将日志保存到文件
    private func saveLogToFile(entry: LogEntry) {
        guard let fileURL = logFileURL else { return }
        
        let logString = entry.fullFormattedMessage + "\n"
        print("!!!!!!!!\(logString)")
        do {
            // 检查文件是否存在
            if !FileManager.default.fileExists(atPath: fileURL.path) {
                // 创建新文件
                try logString.write(to: fileURL, atomically: true, encoding: .utf8)
            } else {
                // 检查文件大小
                let fileSize = try FileManager.default.attributesOfItem(atPath: fileURL.path)[.size] as? Int ?? 0
                
                if fileSize > maxLogFileSize {
                    // 文件过大，清空文件
                    try logString.write(to: fileURL, atomically: true, encoding: .utf8)
                } else {
                    // 追加到现有文件
                    let fileHandle = try FileHandle(forWritingTo: fileURL)
                    fileHandle.seekToEndOfFile()
                    fileHandle.write(logString.data(using: .utf8)!)
                    fileHandle.closeFile()
                }
            }
        } catch {
            print("保存日志到文件失败: \(error.localizedDescription)")
        }
    }
    /// 清除所有日志
    func clearLogs() {
        logHistory.removeAll()
    }
} 
