//
//  DocumentManager.swift
//  Runner
//
//  Created by NickTang on 2025/7/4.
//

import UIKit

class DocumentManager {
    // MARK: - 单例
    static let shared = DocumentManager()
    // MARK: - 初始化方法
    
    private init() {
        // 私有初始化，确保单例
    }
    // MARK: - 公共方法
    
    /// 删除已上传成功的视频
    func deleteLocalVideo(_ documentPath: String) {
        self.safelyAccessFile(at: documentPath, completion: { url, error in
            if let error = error {
                    print("访问文件出错: \(error)")
                    return
            }
            do {
                try FileManager.default.removeItem(at: url!)
                print("文件已成功删除")
            } catch {
                print("删除文件时出错: \(error.localizedDescription)")
            }
        })
    }
    /// 检查文件路径是否存在
    private func safelyAccessFile(at path: String, completion: (URL?, Error?) -> Void) {
        let fileURL = URL(fileURLWithPath: path)
        
        // 1. 检查文件是否存在
        guard FileManager.default.fileExists(atPath: path) else {
            completion(nil, NSError(domain: "File not found", code: 404, userInfo: nil))
            return
        }
        
        // 2. 检查是否可读
        guard FileManager.default.isReadableFile(atPath: path) else {
            completion(nil, NSError(domain: "No read permission", code: 403, userInfo: nil))
            return
        }
        
        // 3. 返回安全的文件URL
        completion(fileURL, nil)
    }
}
