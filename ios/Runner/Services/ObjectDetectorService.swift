// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import UIKit
import MediaPipeTasksVision
import AVFoundation

/**
 This protocol must be adopted by any class that wants to get the detection results of the object detector in live stream mode.
 */
protocol ObjectDetectorServiceLiveStreamDelegate: AnyObject {
  func objectDetectorService(_ objectDetectorService: ObjectDetectorService,
                             didFinishDetection result: ResultBundle?,
                             error: Error?)
}


// Initializes and calls the MediaPipe APIs for detection.
class ObjectDetectorService: NSObject {

  weak var liveStreamDelegate: ObjectDetectorServiceLiveStreamDelegate?

  var objectDetector: ObjectDetector?
  private(set) var runningMode = RunningMode.image
  private var maxResults = 10
  private var scoreThreshold: Float = 0.23
  private var modelPath: String
  private var delegate: Delegate

  // MARK: - Custom Initializer
  private init?(model: Model, maxResults: Int, scoreThreshold: Float, runningMode:RunningMode, delegate: Delegate) {
    guard let modelPath = model.modelPath else {
      return nil
    }
    self.modelPath = modelPath
    self.maxResults = maxResults
    self.scoreThreshold = scoreThreshold
    self.runningMode = runningMode
    self.delegate = delegate
    super.init()
    
    createObjectDetector()
  }
  
  private func createObjectDetector() {
    let objectDetectorOptions = ObjectDetectorOptions()
    objectDetectorOptions.runningMode = runningMode
    objectDetectorOptions.maxResults = self.maxResults
    objectDetectorOptions.scoreThreshold = self.scoreThreshold
    objectDetectorOptions.baseOptions.modelAssetPath = modelPath
    objectDetectorOptions.baseOptions.delegate = delegate
    if runningMode == .liveStream {
      objectDetectorOptions.objectDetectorLiveStreamDelegate = self
    }
    do {
      objectDetector = try ObjectDetector(options: objectDetectorOptions)
    }
    catch {
      print(error)
    }
  }
  
  // MARK: - Static Initializers
  static func liveStreamDetectorService(
    model: Model, maxResults: Int,
    scoreThreshold: Float,
    liveStreamDelegate: ObjectDetectorServiceLiveStreamDelegate?,
    delegate: Delegate) -> ObjectDetectorService? {
    let objectDetectorService = ObjectDetectorService(
      model: model,
      maxResults: maxResults,
      scoreThreshold: scoreThreshold,
      runningMode: .liveStream,
      delegate: delegate)
    objectDetectorService?.liveStreamDelegate = liveStreamDelegate
    
    return objectDetectorService
  }
  
  // MARK: - Detection Methods for Different Modes
  /**
   This method return ObjectDetectorResult and infrenceTime when receive an image
   **/
  func detectAsync(
    sampleBuffer: CMSampleBuffer,
    orientation: UIImage.Orientation,
    timeStamps: Int) {
    guard let image = try? MPImage(sampleBuffer: sampleBuffer, orientation: orientation) else {
      return
    }
    do {
      try objectDetector?.detectAsync(image: image, timestampInMilliseconds: timeStamps)
    } catch {
      print(error)
    }
  }
  
}

// MARK: - ObjectDetectorLiveStreamDelegate
extension ObjectDetectorService: ObjectDetectorLiveStreamDelegate {
  func objectDetector(
    _ objectDetector: ObjectDetector,
    didFinishDetection result: ObjectDetectorResult?,
    timestampInMilliseconds: Int,
    error: Error?) {
    guard let result = result else {
      liveStreamDelegate?.objectDetectorService(self, didFinishDetection: nil, error: error)
      return
    }
    let resultBundle = ResultBundle(
      inferenceTime: Date().timeIntervalSince1970 * 1000 - Double(timestampInMilliseconds),
      objectDetectorResults: [result])
    liveStreamDelegate?.objectDetectorService(self, didFinishDetection: resultBundle, error: nil)
  }
}

/// A result from the `ObjectDetectorHelper`.
struct ResultBundle {
  let inferenceTime: Double
  let objectDetectorResults: [ObjectDetectorResult?]
  var size: CGSize = .zero
}
