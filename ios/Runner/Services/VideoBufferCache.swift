//
//  VideoBufferCache.swift
//  Demo
//
//  Created by Apple on 2025/3/26
//  
//
        

import Foundation
import CoreMedia
import AVFoundation
class VideoBufferCache {
    private let bufferQueue = DispatchQueue(label: "com.example.videoBufferQueue")
    private var buffer: [(CMSampleBuffer, CMTime)] = []
    private let maxDuration: CMTime = CMTime(seconds: 6, preferredTimescale: 600) // 6 秒
    private let maxBufferSize = 180 // 假设30fps，6秒的帧数
    
    // 添加计数器来跟踪帧的处理
    private var appendCount = 0
    private var lastLogTime = Date()
    private var lastAppendTime = Date()

    func append(_ sampleBuffer: CMSampleBuffer) {
        // 降低采样率 - 每秒最多处理15帧
        let now = Date()
        if now.timeIntervalSince(lastAppendTime) < 1.0/15.0 {
            return
        }
        lastAppendTime = now
        
        // 在主线程执行时间戳获取，减少在异步队列中的工作量
        let timestamp = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
        
        // 深拷贝 sampleBuffer 以避免引用问题
        var copied: CMSampleBuffer?
        CMSampleBufferCreateCopy(allocator: kCFAllocatorDefault, sampleBuffer: sampleBuffer, sampleBufferOut: &copied)
        
        guard let copiedBuffer = copied else { 
            print("无法拷贝视频帧")
            return 
        }
        
        // 使用同步队列避免队列堆积
        bufferQueue.sync {
            // 先检查缓冲区大小，如果已超过最大值的80%，直接移除一些帧
            if self.buffer.count > Int(Double(self.maxBufferSize) * 0.8) {
                let removeCount = self.buffer.count / 4 // 一次性移除1/4的帧以减少频繁移除
                if removeCount > 0 {
                    self.buffer.removeFirst(removeCount)
                }
            }
            
            // 增加计数器
            self.appendCount += 1
            
            // 记录性能日志（每100帧或5秒）
            if self.appendCount % 100 == 0 || now.timeIntervalSince(self.lastLogTime) > 5 {
                print("VideoBufferCache: 已处理 \(self.appendCount) 帧，当前缓冲区大小: \(self.buffer.count)")
                self.lastLogTime = now
            }
            
            // 添加帧到缓冲区
            self.buffer.append((copiedBuffer, timestamp))
            
            // 定期清理过期帧（保留最近6秒的帧）
            if self.appendCount % 15 == 0 { // 每15帧执行一次清理
                let minTime = timestamp - self.maxDuration
                self.buffer.removeAll { $0.1 < minTime }
            }
        }
    }

    func getFrames(around time: CMTime, duration: CMTime) -> [CMSampleBuffer] {
        return bufferQueue.sync {
            let minTime = time - duration
            let maxTime = time + duration
            let frames = buffer.filter { $0.1 >= minTime && $0.1 <= maxTime }.map { $0.0 }
            print("找到 \(frames.count) 帧（在 \(duration.seconds * 2) 秒范围内，缓冲区总帧数: \(buffer.count)）")
            return frames
        }
    }
    
    func saveClip(frames: [CMSampleBuffer], outputURL: URL) {
        guard !frames.isEmpty,
              let firstBuffer = frames.first,
              let formatDescription = CMSampleBufferGetFormatDescription(firstBuffer) else {
            print("无效的帧")
            return
        }
        
        print("开始保存视频，共 \(frames.count) 帧")
        
        do {
            // 确保目录存在
            try FileManager.default.createDirectory(at: outputURL.deletingLastPathComponent(), withIntermediateDirectories: true)
            
            // 如果文件已存在，先删除
            if FileManager.default.fileExists(atPath: outputURL.path) {
                try FileManager.default.removeItem(at: outputURL)
            }
            
            let writer: AVAssetWriter
            
            do {
                writer = try AVAssetWriter(outputURL: outputURL, fileType: .mp4)
            } catch {
                print("创建 AVAssetWriter 失败: \(error.localizedDescription)")
                return
            }
            
            // 获取第一帧的尺寸
            guard let pixelBuffer = CMSampleBufferGetImageBuffer(firstBuffer) else {
                print("无法获取图像缓冲区")
                return
            }
            
            let width = CVPixelBufferGetWidth(pixelBuffer)
            let height = CVPixelBufferGetHeight(pixelBuffer)
            
            let videoSettings = [
                AVVideoCodecKey: AVVideoCodecType.h264,
                AVVideoWidthKey: width,
                AVVideoHeightKey: height
            ] as [String: Any]
            
            let input = AVAssetWriterInput(mediaType: .video, outputSettings: videoSettings)
            input.expectsMediaDataInRealTime = false
            
            let adaptor = AVAssetWriterInputPixelBufferAdaptor(assetWriterInput: input, sourcePixelBufferAttributes: nil)
            
            writer.add(input)
            writer.startWriting()
            writer.startSession(atSourceTime: CMSampleBufferGetPresentationTimeStamp(firstBuffer))
            
            let frameQueue = DispatchQueue(label: "com.example.frameWritingQueue")
            
            frameQueue.async {
                var successFrames = 0
                for (index, buffer) in frames.enumerated() {
                    guard let pixelBuffer = CMSampleBufferGetImageBuffer(buffer) else {
                        print("无法获取第 \(index) 帧的图像缓冲区")
                        continue
                    }
                    
                    let time = CMSampleBufferGetPresentationTimeStamp(buffer)
                    
                    // 添加超时机制
                    let timeout = DispatchTime.now() + 0.5 // 500ms超时
                    var success = false
                    
                    while !input.isReadyForMoreMediaData && DispatchTime.now() < timeout {
                        usleep(10000) // 等待10ms
                    }
                    
                    if input.isReadyForMoreMediaData {
                        success = adaptor.append(pixelBuffer, withPresentationTime: time)
                        if success {
                            successFrames += 1
                        } else {
                            print("无法添加第 \(index) 帧: \(writer.error?.localizedDescription ?? "未知错误")")
                        }
                    } else {
                        print("超时等待 AVAssetWriterInput 准备就绪")
                    }
                }
                
                input.markAsFinished()
                print("成功写入 \(successFrames)/\(frames.count) 帧")
                
                let writerDispatchGroup = DispatchGroup()
                writerDispatchGroup.enter()
                
                writer.finishWriting {
                    let status = writer.status
                    if status == .completed {
                        print("视频保存成功: \(outputURL.path)")
                    } else if let error = writer.error {
                        print("视频保存失败: \(error.localizedDescription)")
                    }
                    writerDispatchGroup.leave()
                }
                
                // 等待最多5秒钟完成写入
                _ = writerDispatchGroup.wait(timeout: .now() + 5.0)
            }
        } catch {
            print("保存视频时发生错误: \(error.localizedDescription)")
        }
    }
}
