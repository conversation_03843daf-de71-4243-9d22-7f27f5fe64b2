//
//  AIService.swift
//  Demo
//
//  Created by Vien on 2025/3/26.
//

import AVFoundation
import UIKit
import CoreMedia
import MediaPipeTasksVision
import Photos

/// 投篮人信息
struct AttemptPlayerInfo {
    let timestamp: TimeInterval
    let image: UIImage?
    let confidence: Float
    let rect: CGRect
    let isNearBall: Bool // 标记投篮人是否靠近球
}
/// 投篮事件信息
struct ShootEvent {
    let shootTime: TimeInterval        // 投篮出手时间
    let goalTime: TimeInterval?         // 进球时间
    let isGoal: Bool                    // 是否进球
    let playerImage: UIImage?           // 投篮人图像
    let playerConfidence: Float         // 投篮人置信度
    let filePath: String?              //投篮视频本地路径
    let shootCoord: CGPoint?           // 投篮点位
}

/// AIService代理协议
protocol AIServiceDelegate: AnyObject {
    /// 当检测到篮筐和篮板区域并满足条件时调用
    func didDetectHoopAndBackboard(hoopRect: CGRect, backboardRect: CGRect, isValid: Bool)
    
    /// 当检测到投篮事件时调用（综合投篮、进球和投篮人信息）
    func didDetectShootEvent(event: ShootEvent)
    
    /// 当检测到目标并完成处理时调用
    func aiService(_ service: AIService, didDetectObjects result: ResultBundle?, imageSize: CGSize)
    
    /// 当需要显示或隐藏篮筐合法区域时调用（仅在未开始检测状态）
    func shouldDisplayHoopRegion(_ region: CGRect, imageSize: CGSize)
    
    /// 当截取到篮筐区域图像时调用
    func didCaptureHoopImage(_ image: UIImage)
}

/// 篮球投篮事件检测服务
class AIService: NSObject {
    
    // MARK: - 常量
    
    /// 检测阈值常量
    private struct Constants {
        static let minHoopBackboardDetectionDuration: TimeInterval = 1.0 // 连续检测篮筐和篮板的最小时间（秒）
        static let minIouThreshold: Float = 0.8 // 前后帧区域重叠的最小IOU阈值
        static let playerLookbackTime: TimeInterval = 1.8 // 检测到投篮后向前查找投篮人的时间范围（秒）
        static let playerNearBallFactor: Float = 2.2 // 投篮人头顶到球的最大距离因子
        static let dataRetentionTime: TimeInterval = 4.0 // 数据保留时间（秒）
        static let maxArraySize = 100 // 数组最大长度限制
        static let maxFrameCacheSize = 3 // 帧缓存最大数量
//        static let validHoopRegion: CGRect = CGRect(x: 0.38, y: 0.2, width: 0.24, height: 0.26) // 有效篮筐区域
        static let validHoopRegion: CGRect = CGRect(x: 0.413793103, y: 0.221, width: 0.172413793, height: 0.30651341) // 有效篮筐区域
    }
    
    // MARK: - 属性
    
    /// AIService代理
    weak var delegate: AIServiceDelegate?
    
    /// 后台处理队列
    private let backgroundQueue = DispatchQueue(
        label: "com.shootz.aiservice.backgroundQueue",
        qos: .userInitiated,
        attributes: .concurrent
    )
    
    /// 目标检测服务
    private let objectDetectorServiceQueue = DispatchQueue(
        label: "com.shootz.aiservice.objectDetectorServiceQueue",
        attributes: .concurrent)
    private var _objectDetectorService: ObjectDetectorService?
    private var objectDetectorService: ObjectDetectorService? {
        get {
          objectDetectorServiceQueue.sync {
            return self._objectDetectorService
          }
        }
        set {
            let newservice = newValue
          objectDetectorServiceQueue.async(flags: .barrier) {
            self._objectDetectorService = newservice
          }
        }
    }
    
    /// 投篮分类检测服务
    private let shootClassifierServiceQueue = DispatchQueue(
      label: "com.shootz.aiservice.shootClassifierServiceQueue",
      attributes: .concurrent)
    private var _shootClassifierService: ShootClassifierService?
    private var shootClassifierService: ShootClassifierService? {
      get {
        shootClassifierServiceQueue.sync {
          return self._shootClassifierService
        }
      }
      set {
          let newservice = newValue
        shootClassifierServiceQueue.async(flags: .barrier) {
          self._shootClassifierService = newservice
        }
      }
    }
    
    /// 检测状态
    private var detectionStatus = DetectionStatus.notStarted
    
    /// 开始检测时间
    private var startTime: Date?
    
    /// 投篮人信息数组
    private var attemptPlayers: [AttemptPlayerInfo] = []
    
    /// 最后一次有效的篮筐区域
    private var lastValidHoopRect: CGRect?
    
    /// 最后一次有效的篮板区域
    private var lastValidBackboardRect: CGRect?
    
    /// 是否已经检测到有效的篮筐和篮板
    private var hasValidHoopAndBackboard: Bool = false
    
    /// 连续未检测到篮筐和篮板的帧数
    private var consecutiveInvalidFrames: Int = 0
    
    /// 最近帧图像缓存
    private var frameCache: [UIImage] = []
    
    /// 图像缓存锁
    private let frameCacheLock = NSLock()
    
    /// 视频分辨率
    public var videoResolution: CGSize = .zero
    
    /// 调试定时器
    private var debugTimer: Timer?

    /// 跳帧处理 仅对目标检测生效
    private let frameInterval: Int = 2 // 每2帧处理一次目标检测
    private var frameIdx: Int = 0
    
    /// 单应性矩阵（私有）
    private var homographyMatrix: [[Double]]?

    /// 是否开启调试
    private var isDebug: Bool = false
    
    // MARK: - 初始化方法
    
    override init() {
        super.init()
        setupServices()
    }
    
    deinit {
        stopDetection()
    }
    
    // MARK: - 公共方法
    func setupHomography(screenPoints: [CGPoint], screenSize: CGSize) -> [CGPoint]? {
        let courtPoints: [CGPoint] = [
            CGPoint(x: 0,    y: 0),
  //            CGPoint(x: 75,   y: 0),
            CGPoint(x: 505,  y: 0),
            CGPoint(x: 995,  y: 0),
  //            CGPoint(x: 1425, y: 0),
            CGPoint(x: 1500, y: 0),
            CGPoint(x: 505,  y: 580),
            CGPoint(x: 995,  y: 580)
        ]
        guard screenPoints.count == courtPoints.count, screenPoints.count >= 4 else {
            LogService.shared.error("点数量不匹配，应为\(courtPoints.count)")
            return nil
        }
        // 横向填满屏幕的缩放因子
        let widthScale = screenSize.width / self.videoResolution.width
        let scaledVideoHeight = self.videoResolution.height * widthScale
        // 视频高度大于屏幕时，上下会被裁剪，计算裁剪的 top 偏移
        let verticalPadding = (scaledVideoHeight - screenSize.height) / 2.0
        // 最终转换后的点
        var cameraPoints: [CGPoint] = []
        for point in screenPoints {
            // x 坐标直接除以缩放因子，恢复到视频坐标
            let xInVideo = point.x / widthScale
            // y 坐标先加上被裁剪的 top，再除以缩放因子
            let yInVideo = (point.y + verticalPadding) / widthScale
          cameraPoints.append(CGPoint(x: xInVideo, y: yInVideo))
        }
      
        print("📐 screenSize: \(screenSize)")
        print("🎞️ videoResolution: \(self.videoResolution)")
        print("🔍 widthScale: \(widthScale)")
        print("🟩 scaledVideoHeight: \(scaledVideoHeight)")
        print("⬆️ verticalPadding: \(verticalPadding)")
        print("🔵 screenPoints: \(screenPoints)")

        // 打印验证
        LogService.shared.debug("Converted points to video coordinates: \(cameraPoints)")
      
        // 测试单应性矩阵转换
//        let cameraPoints: [CGPoint] = [
//            CGPoint(x: 1915, y: 331),
//            CGPoint(x: 1803, y: 339),
//            CGPoint(x: 1329, y: 395),
//            CGPoint(x: 873,  y: 435),
//            CGPoint(x: 595,  y: 462),
//            CGPoint(x: 546,  y: 466),
//            CGPoint(x: 849,  y: 225),
//            CGPoint(x: 380,  y: 324)
//        ]
        
//        let userTouch = CGPoint(x: 524, y: 232)
        _ = self.configureHomography(from: cameraPoints, to: courtPoints)
//        if let mapped = self.mapToCourt(point: userTouch) {
//            LogService.shared.info("映射后场地坐标为: \(mapped.x), \(mapped.y)")
//        }
        //将 cameraPoints 映射到 court 坐标（使用 mapToCourt）
        var mappedCourtPoints: [CGPoint] = []
        for camPoint in cameraPoints {
            if let courtPoint = self.mapToCourt(point: camPoint) {
                mappedCourtPoints.append(courtPoint)
            } else {
                LogService.shared.error("映射失败：\(camPoint)")
                return nil
            }
        }
        
        LogService.shared.debug("需标注场地坐标为: \(courtPoints)")
        LogService.shared.debug("映射后场地坐标为: \(mappedCourtPoints)")
        return mappedCourtPoints
    }
  
    /// 配置摄像头图像到2D场地图的映射（只需调用一次）
    func configureHomography(from cameraPoints: [CGPoint], to courtPoints: [CGPoint]) -> Bool {
        guard let H = Transform.Homography.findHomography(from: cameraPoints, to: courtPoints) else {
//            LogService.shared.error("⚠️ 无法求解单应性矩阵")
            return false
        }
        homographyMatrix = H
        return true
    }
  
    /// 映射单个点（前提是已配置 homographyMatrix = H）
    func mapToCourt(point: CGPoint) -> CGPoint? {
        guard let H = homographyMatrix else {
//            LogService.shared.error("❌ Homography 尚未初始化，请先调用 configureHomography()")
            return nil
        }
        return Transform.Homography.apply(to: point, using: H)
    }

    /// 设置视频分辨率
    /// - Parameter resolution: 视频分辨率
    func setVideoResolution(_ resolution: CGSize) {
        print("设置视频分辨率: \(resolution)")
        
        // 验证分辨率是否有效
        guard resolution.width > 0 && resolution.height > 0 else {
            print("错误：无效的视频分辨率: \(resolution)")
            return
        }
        
        // 验证分辨率是否合理（例如不超过最大限制）
        let maxDimension: CGFloat = 4096 // 4K分辨率
        guard resolution.width <= maxDimension && resolution.height <= maxDimension else {
            print("错误：视频分辨率超出合理范围: \(resolution)")
            return
        }
        
        videoResolution = resolution
        print("视频分辨率设置成功: \(videoResolution)")
    }
    //MARK: - 篮筐区域是否隐藏
    func isHiddenHoopRegion(_ isHidden:Bool) {
        if isHidden {
            // 隐藏篮筐区域
            delegate?.shouldDisplayHoopRegion(.zero, imageSize: videoResolution)
        }else {
            delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: videoResolution)
        }
    }
    /// 开始检测投篮事件
    func startDetection() {
        // 检查是否已经检测到有效的篮筐和篮板
        guard hasValidHoopAndBackboard else {
            print("错误：在开始检测前必须先检测到有效的篮筐和篮板")
            return
        }
        
        // 清理旧数据
        cleanupAllData()
        
        detectionStatus = .started
        startTime = Date()
        
        // 隐藏篮筐合法区域
//        DispatchQueue.main.async { [weak self] in
//            self?.delegate?.shouldDisplayHoopRegion(.zero, imageSize: self?.videoResolution ?? .zero)
//        }
        
        // 初始化投篮分类检测服务
        clearAndInitializeImageClassifierService()

        if isDebug {
            // 启动调试定时器
            startDebugTimer()
        }
        
        print("开始检测投篮事件")
    }
    
    /// 暂停检测
    func pauseDetection() {
        detectionStatus = .paused
        if isDebug {
            debugTimer?.invalidate()
            debugTimer = nil
        }
        
        // 隐藏投篮分类区域，显示篮筐合法区域
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: self.videoResolution)
            if self.videoResolution != .zero {
                self.delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: self.videoResolution)
            }
        }
        
        print("暂停检测投篮事件")
    }
    
    /// 恢复检测
    func resumeDetection() {
        detectionStatus = .started
        
        // 隐藏篮筐合法区域
        DispatchQueue.main.async { [weak self] in
            self?.delegate?.shouldDisplayHoopRegion(.zero, imageSize: self?.videoResolution ?? .zero)
        }
        
        if isDebug {
            startDebugTimer()
        }
        print("恢复检测投篮事件")
    }
    
    /// 停止检测
    func stopDetection() {
        detectionStatus = .notStarted
        startTime = nil
        
        // 清理所有数据
        cleanupAllData()
        
        // 停止调试定时器
        if isDebug {
            debugTimer?.invalidate()
            debugTimer = nil
        }
        
        // 显示篮筐区域
        if videoResolution != .zero {
            delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: videoResolution)
        }
        
        print("停止检测投篮事件")
    }
    
    /// 清理所有数据
    private func cleanupAllData() {
        attemptPlayers.removeAll()
        frameCache.removeAll()
    }

    func processVideoFrame(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation) {
        if let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) {
            updateFrameCache(pixelBuffer)
        }

        let currentTimeMs = Int(Date().timeIntervalSince1970 * 1000)

        backgroundQueue.async { [weak self] in
            guard let self = self else { return }

            if self.frameIdx % self.frameInterval == 0 {
                self.objectDetectorService?.detectAsync(
                    sampleBuffer: sampleBuffer,
                    orientation: orientation,
                    timeStamps: currentTimeMs
                )

                // 后面需要考虑疑似进球（分类为goal）时后几帧不跳帧
//                if self.detectionStatus == .started {
//                    self.processGoalDetection(currentTimeMs: currentTimeMs)
//                }
            }
            // 跳帧放到投篮分类内部处理
            if self.detectionStatus == .started {
              self.processShootDetection(currentTimeMs: currentTimeMs)
            }
            self.frameIdx += 1
        }
    }

    private func processShootDetection(currentTimeMs: Int) {
        guard let hoopRect = self.lastValidHoopRect,
            let backboardRect = self.lastValidBackboardRect,
            let currentFrame = self.getCurrentFrame() else {
//            LogService.shared.debug("⚠️ processShootDetection - 缺少必要数据")
            return
        }

        // 使用ShootClassifierService处理整个篮筐+篮板区域
        let timestamp = TimeInterval(currentTimeMs) / 1000.0
        self.shootClassifierService?.processFrame(
            image: currentFrame,
            hoopRect: hoopRect,
            backboardRect: backboardRect,
            timestamp: timestamp
        )
        
        // 截取篮筐+篮板区域用于显示
//        let combinedRect = hoopRect.union(backboardRect)
//        if let croppedHoopImage = cropImage(currentFrame, toRect: combinedRect) {
//            DispatchQueue.main.async {
//                self.delegate?.didCaptureHoopImage(croppedHoopImage)
//            }
//        } else {
//            // 如果截取失败，传递原图片作为后备方案
//            DispatchQueue.main.async {
//                self.delegate?.didCaptureHoopImage(currentFrame)
//            }
//        }
    }
    
    
    // MARK: - 私有方法
    
    /// 设置服务
    private func setupServices() {
        // 设置目标检测服务
        objectDetectorService = ObjectDetectorService.liveStreamDetectorService(
            model: InferenceConfigurationManager.sharedInstance.model,
            maxResults: InferenceConfigurationManager.sharedInstance.maxResults,
            scoreThreshold: InferenceConfigurationManager.sharedInstance.scoreThreshold,
            liveStreamDelegate: self,
            delegate: InferenceConfigurationManager.sharedInstance.delegate
        )
        // 设置进球分类检测服务
        clearAndInitializeImageClassifierService()
    }
    
    /// 处理目标检测结果
    /// - Parameters:
    ///   - detections: 目标检测结果
    ///   - imageSize: 图像尺寸
    private func processDetections(_ detections: [Detection], imageSize: CGSize) {
        var hoopRects: [CGRect] = []
        var backboardRects: [CGRect] = []
        var basketballs: [CGRect] = []
        var players: [(rect: CGRect, confidence: Float)] = []
        
        // 当前时间戳
        let currentTimestamp = Date().timeIntervalSince1970
        
        // 清理过期数据
        cleanupExpiredData(currentTimestamp)
        
        // 判断坐标类型（通过检查第一个检测结果的坐标范围）
        let isNormalized = detections.first.map { detection in
            let rect = detection.boundingBox
            return rect.origin.x <= 1 && rect.origin.y <= 1 &&
                   rect.size.width <= 1 && rect.size.height <= 1
        } ?? true
        
        for detection in detections {
            let rect = detection.boundingBox
            
            // 确保图像尺寸有效
            guard imageSize.width > 0 && imageSize.height > 0 else {
                print("错误：无效的图像尺寸: \(imageSize)")
                continue
            }

            // 根据坐标类型进行转换
            var finalRect: CGRect
            if isNormalized {
                // 如果是归一化坐标，转换为像素坐标
                finalRect = CGRect(
                    x: rect.origin.x * imageSize.width,
                    y: rect.origin.y * imageSize.height,
                    width: rect.size.width * imageSize.width,
                    height: rect.size.height * imageSize.height
                )
            } else {
                // 如果已经是像素坐标，直接使用
                finalRect = rect
            }
            
            switch detection.categories.first?.categoryName {
            case "hoop":
                    if detection.categories.first?.score ?? 0.0 > 0.32 {
                        hoopRects.append(finalRect)
                    }
            case "backboard":
                    if detection.categories.first?.score ?? 0.0 > 0.32 {
                        backboardRects.append(finalRect)
                    }
            case "basketball":
                    if detection.categories.first?.score ?? 0.0 > 0.25 {
                        basketballs.append(finalRect)
                    }
            case "attempt_player":
                    let confidence = detection.categories.first?.score ?? 0.0
                    if confidence > 0.25 {
                        players.append((rect: finalRect, confidence: confidence))
                    }
            default:
                break
            }
        }
        
        // 只在开始检测状态下处理投篮人
        if detectionStatus == .started {
            processAttemptPlayers(players, basketballs: basketballs, timestamp: currentTimestamp)
        }
        
        // 只在未开始检测状态下处理篮筐和篮板检测
        if detectionStatus != .started {
            processHoopAndBackboardDetection(hoopRects: hoopRects, backboardRects: backboardRects, imageSize: imageSize)
        } else {
            // 在检测状态下监控篮筐和篮板状态
            monitorHoopAndBackboardState(hoopRects: hoopRects, backboardRects: backboardRects, imageSize: imageSize)
        }
    }
    
    /// 清理过期数据
    /// - Parameter currentTimestamp: 当前时间戳
    private func cleanupExpiredData(_ currentTimestamp: TimeInterval) {
        // 清理过期的投篮人数据
        attemptPlayers.removeAll { currentTimestamp - $0.timestamp > Constants.dataRetentionTime }
        if attemptPlayers.count > Constants.maxArraySize {
            attemptPlayers.removeFirst(attemptPlayers.count - Constants.maxArraySize)
        }
    }
    
    /// 处理投篮人检测
    /// - Parameters:
    ///   - players: 检测到的投篮人数组，每个元素包含矩形和置信度
    ///   - basketballs: 检测到的篮球位置数组
    ///   - timestamp: 当前时间戳
    private func processAttemptPlayers(_ players: [(rect: CGRect, confidence: Float)], basketballs: [CGRect], timestamp: TimeInterval) {
        guard !players.isEmpty else { return }
        
        // 计算每个投篮人是否靠近球
        var playersWithBallProximity: [(rect: CGRect, confidence: Float, isNearBall: Bool)] = []
        
        for player in players {
            var isNearBall = false
            
            // 计算投篮人是否靠近任何一个球
            for basketball in basketballs {
                let basketballHeight = basketball.height
                let playerTopMidX = player.rect.midX
                let playerTopY = player.rect.minY
                let basketballMidX = basketball.midX
                let basketballMidY = basketball.midY
                
                // 计算投篮人头顶中点到球中心的距离
                let horizontalDistance = abs(playerTopMidX - basketballMidX)
                let verticalDistance = abs(playerTopY - basketballMidY)
                
                // 如果水平距离在球宽度内，且垂直距离不超过3个球高度，认为投篮人靠近球
                if horizontalDistance - player.rect.width / 2 <= basketball.width * CGFloat(Constants.playerNearBallFactor) &&
                   verticalDistance <= basketballHeight * CGFloat(Constants.playerNearBallFactor) && verticalDistance > 0 {
                    isNearBall = true
                    break
                }
            }
            
            playersWithBallProximity.append((rect: player.rect,
                                             confidence: player.confidence,
                                             isNearBall: isNearBall))
        }
        
        // 选择合适的投篮人
        var selectedPlayer: (rect: CGRect, confidence: Float, isNearBall: Bool)? = nil
        
        // 如果有靠近球的投篮人，选择其中置信度最高的
        let nearBallPlayers = playersWithBallProximity.filter { $0.isNearBall }
        if !nearBallPlayers.isEmpty {
            selectedPlayer = nearBallPlayers.max { $0.confidence < $1.confidence }
        } else {
            // 否则选择置信度最高的投篮人
            selectedPlayer = playersWithBallProximity.max { $0.confidence < $1.confidence }
        }

        // 处理选中的投篮人
        if let selected: (rect: CGRect, confidence: Float, isNearBall: Bool) = selectedPlayer,
           let currentFrame = getCurrentFrame(),
           let playerCroppedImage = cropImage(currentFrame, toRect: selected.rect) {
//            print("currentFrame.size: \(currentFrame.size)")
//            print("selected: \(selected.rect), width: \(playerCroppedImage.size.width), height: \(playerCroppedImage.size.height)")
            let playerInfo = AttemptPlayerInfo(
                timestamp: timestamp,
                image: playerCroppedImage,
                confidence: selected.confidence,
                rect: selected.rect,
                isNearBall: selected.isNearBall
            )
//            print("playerInfo: confidence: \(selected.confidence), timestamp: \(timestamp)")
            
            attemptPlayers.append(playerInfo)
        }
    }
    
    /// 处理篮筐和篮板检测
    /// - Parameters:
    ///   - hoopRects: 检测到的所有篮筐区域
    ///   - backboardRects: 检测到的所有篮板区域
    ///   - imageSize: 图像尺寸
    private func processHoopAndBackboardDetection(hoopRects: [CGRect], backboardRects: [CGRect], imageSize: CGSize) {
        // 在非start状态下显示篮筐区域
        if detectionStatus != .started {
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: imageSize)
            }
        }
        
        // print("筛选篮筐：总共 \(hoopRects.count) 个")
        // 筛选合法区域内的篮筐
        let filteredHoops = hoopRects.filter { hoopRect in
            // 将像素坐标转换为归一化坐标 hoopRect: y x h w
            let normalizedRect = CGRect(
                x: hoopRect.origin.y / imageSize.width,
                y: (imageSize.height - hoopRect.origin.x - hoopRect.size.width) / imageSize.height,
                width: hoopRect.size.height / imageSize.width,
                height: hoopRect.size.width / imageSize.height
            )
            // 判断篮筐中心是否在指定区域内
            let hoopCenter = CGPoint(x: normalizedRect.midX, y: normalizedRect.midY)
            // print("imageSize: \(imageSize.width), \(imageSize.height), hoopRect: \(hoopRect), hoopCenter: \(hoopCenter), validHoopRegion: \(Constants.validHoopRegion), normalizedRect: \(normalizedRect)")
            return Constants.validHoopRegion.contains(hoopCenter)
        }
        
        // 如果没有符合要求的篮筐，直接返回
        if filteredHoops.isEmpty {
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didDetectHoopAndBackboard(hoopRect: .zero, backboardRect: .zero, isValid: false)
            }
            return
        }
        
        // 存储所有符合条件的篮筐篮板组合
        var validCombinations: [(hoop: CGRect, backboard: CGRect, totalArea: CGFloat)] = []
        
        // 对每个篮筐，检查是否有篮板与之相交
        for hoopRect in filteredHoops {
            for backboardRect in backboardRects {
                // 检查篮筐和篮板是否相交
                if hoopRect.intersects(backboardRect) {
                    // 计算总面积
                    let totalArea = hoopRect.width * hoopRect.height + backboardRect.width * backboardRect.height
                    validCombinations.append((hoop: hoopRect, backboard: backboardRect, totalArea: totalArea))
                }
            }
        }
        
        // 选择总面积最大的一组
        if let bestCombination = validCombinations.max(by: { $0.totalArea < $1.totalArea }) {
            let hoopRect = bestCombination.hoop
            let backboardRect = bestCombination.backboard
            
            // 更新最后一次有效的位置
            lastValidHoopRect = hoopRect
            lastValidBackboardRect = backboardRect
            hasValidHoopAndBackboard = true
            
            // 通知代理当前检测状态
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didDetectHoopAndBackboard(
                    hoopRect: hoopRect,
                    backboardRect: backboardRect,
                    isValid: self?.hasValidHoopAndBackboard ?? false
                )
            }
        } else {
            // 没有找到合法的篮筐篮板组合
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didDetectHoopAndBackboard(hoopRect: .zero, backboardRect: .zero, isValid: false)
            }
        }
    }
    
    /// 监控篮筐和篮板状态，检测遮挡或位移情况
    /// - Parameters:
    ///   - hoopRects: 检测到的所有篮筐区域
    ///   - backboardRects: 检测到的所有篮板区域
    ///   - imageSize: 图像尺寸
    private func monitorHoopAndBackboardState(hoopRects: [CGRect], backboardRects: [CGRect], imageSize: CGSize) {
        // 筛选合法区域内的篮筐
        let filteredHoops = hoopRects.filter { hoopRect in
            // 将像素坐标转换为归一化坐标
            let normalizedRect = CGRect(
                x: hoopRect.origin.y / imageSize.width,
                y: (imageSize.height - hoopRect.origin.x - hoopRect.size.width) / imageSize.height,
                width: hoopRect.size.height / imageSize.width,
                height: hoopRect.size.width / imageSize.height
            )
            // 判断篮筐中心是否在指定区域内
            let hoopCenter = CGPoint(x: normalizedRect.midX, y: normalizedRect.midY)
            return Constants.validHoopRegion.contains(hoopCenter)
        }
        
        // 存储所有符合条件的篮筐篮板组合
        var validCombinations: [(hoop: CGRect, backboard: CGRect, totalArea: CGFloat)] = []
        
        // 对每个篮筐，检查是否有篮板与之相交
        for hoopRect in filteredHoops {
            for backboardRect in backboardRects {
                // 检查篮筐和篮板是否相交
                if hoopRect.intersects(backboardRect) {
                    // 计算总面积
                    let totalArea = hoopRect.width * hoopRect.height + backboardRect.width * backboardRect.height
                    validCombinations.append((hoop: hoopRect, backboard: backboardRect, totalArea: totalArea))
                }
            }
        }
        
        // 如果找不到符合条件的篮筐篮板组合
        if validCombinations.isEmpty {
            // 增加连续无效帧计数
            consecutiveInvalidFrames += 1
            
            // 如果连续10帧未检测到有效组合，重置投篮分类状态
            if consecutiveInvalidFrames >= 30 {
                LogService.shared.warning("连续30帧未检测到有效篮筐和篮板，疑似镜头遮挡或位移，重置投篮分类状态")
                
                // 显示篮筐合法区域，提示用户
                DispatchQueue.main.async { [weak self] in
                    self?.delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: imageSize)
                }
                
                // 投篮分类服务将在下次检测到有效篮筐时重置
                
                consecutiveInvalidFrames = 0 // 重置计数器
            }
            return
        }
        
        // 选择总面积最大的一组
        if let bestCombination = validCombinations.max(by: { $0.totalArea < $1.totalArea }) {
            let currentHoopRect = bestCombination.hoop
            let currentBackboardRect = bestCombination.backboard
            
            // 检查当前检测到的篮筐位置与上次记录的篮筐位置的IOU
            if let lastHoopRect = lastValidHoopRect {
                let iou = calculateIOU(currentHoopRect, lastHoopRect)
                
                // 如果IOU小于0.5，说明镜头发生了轻微位移
                if iou < 0.5 {
                    // 增加连续无效帧计数
                    consecutiveInvalidFrames += 1
                    
                    // 如果连续10帧IOU不满足条件，重置投篮分类状态
                    if consecutiveInvalidFrames >= 10 {
                        LogService.shared.warning("连续10帧检测到篮筐位置变化 (IOU: \(String(format: "%.2f", iou)))，更新篮筐位置并重置投篮分类状态")
                        
                        // 显示篮筐合法区域，提示用户
                        DispatchQueue.main.async { [weak self] in
                            self?.delegate?.shouldDisplayHoopRegion(Constants.validHoopRegion, imageSize: imageSize)
                        }
                        
                        // 更新最后一次有效的篮筐和篮板位置
                        lastValidHoopRect = currentHoopRect
                        lastValidBackboardRect = currentBackboardRect
                        
                        // 重置投篮分类状态
                        
                        // 重置计数器
                        consecutiveInvalidFrames = 0
                    }
                } else {
                    // 检测到有效的篮筐和篮板，隐藏篮筐合法区域
                    DispatchQueue.main.async { [weak self] in
                        self?.delegate?.shouldDisplayHoopRegion(.zero, imageSize: imageSize)
                    }
                    
                    // IOU满足条件，重置计数器
                    consecutiveInvalidFrames = 0
                }
            } else {
                // 如果是第一次检测到有效篮筐，直接更新位置
                lastValidHoopRect = currentHoopRect
                lastValidBackboardRect = currentBackboardRect
                
                // 隐藏篮筐合法区域
                DispatchQueue.main.async { [weak self] in
                    self?.delegate?.shouldDisplayHoopRegion(.zero, imageSize: imageSize)
                }
                
                consecutiveInvalidFrames = 0
            }
        }
    }
    
    /// 计算两个矩形的交并比(IOU)
    /// - Parameters:
    ///   - rect1: 第一个矩形
    ///   - rect2: 第二个矩形
    /// - Returns: IOU值
    private func calculateIOU(_ rect1: CGRect, _ rect2: CGRect) -> Float {
        let intersectionRect = rect1.intersection(rect2)
        
        // 如果没有交集，IOU为0
        if intersectionRect.isEmpty {
            return 0.0
        }
        
        let intersectionArea = intersectionRect.width * intersectionRect.height
        let rect1Area = rect1.width * rect1.height
        let rect2Area = rect2.width * rect2.height
        let unionArea = rect1Area + rect2Area - intersectionArea
        
        return Float(intersectionArea / unionArea)
    }
    
    /// 获取当前帧图像
    /// - Returns: 当前帧图像
    private func getCurrentFrame() -> UIImage? {
        frameCacheLock.lock()
        defer { frameCacheLock.unlock() }
        guard !frameCache.isEmpty else { return nil }
        return frameCache.last
    }
    
    /// 更新帧缓存
    /// - Parameter pixelBuffer: 像素缓冲区
    private func updateFrameCache(_ pixelBuffer: CVPixelBuffer) {
        let image = pixelBufferToUIImage(pixelBuffer)
        
        frameCacheLock.lock()
        defer { frameCacheLock.unlock() }
        
        if let image = image {
            frameCache.append(image)
            // 限制缓存大小
            if frameCache.count > Constants.maxFrameCacheSize {
                frameCache.removeFirst()
            }
        }
    }
    
    /// 将CVPixelBuffer转换为UIImage
    private func pixelBufferToUIImage(_ pixelBuffer: CVPixelBuffer) -> UIImage? {
        CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly) }
        
        let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer)
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedFirst.rawValue | CGBitmapInfo.byteOrder32Little.rawValue)
        
        guard let context = CGContext(data: baseAddress,
                                      width: width,
                                      height: height,
                                      bitsPerComponent: 8,
                                      bytesPerRow: bytesPerRow,
                                      space: colorSpace,
                                      bitmapInfo: bitmapInfo.rawValue) else {
            return nil
        }
        
        guard let cgImage = context.makeImage() else { return nil }
        return UIImage(cgImage: cgImage)
    }
    
    /// 裁剪图像
    /// - Parameters:
    ///   - image: 原始图像
    ///   - rect: 裁剪区域
    /// - Returns: 裁剪后的图像
    private func cropImage(_ image: UIImage, toRect rect: CGRect) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let imageRect = CGRect(x: 0, y: 0, width: cgImage.width, height: cgImage.height)
        let safeRect = rect.intersection(imageRect)

        guard let croppedCgImage = cgImage.cropping(to: safeRect) else { return nil }

        // return UIImage(cgImage: croppedCgImage, scale: image.scale, orientation: image.imageOrientation)
        return UIImage(cgImage: croppedCgImage, scale: image.scale, orientation: .left)
    }
    
    /// 检测状态枚举
    private enum DetectionStatus {
        case notStarted
        case started
        case paused
    }
    
    /// 处理已匹配的事件
    /// - Parameter event: 来自ShootClassifierService的投篮事件
    private func processShootClassifierEvent(_ event: ShootEvent) -> ShootEvent {
        // 根据开始时间戳查找最符合的投篮人
        let lookupStartTime = event.shootTime - Constants.playerLookbackTime
        let lookupEndTime = event.shootTime
        let relevantPlayers = attemptPlayers.filter { $0.timestamp >= lookupStartTime && $0.timestamp <= lookupEndTime }
        
        var bestPlayerImage: UIImage? = nil
        var bestPlayerConfidence: Float = 0.0
        var bestPlayerShootCoord: CGPoint? = nil
        
        // 首先尝试从靠近球的投篮人中选择置信度最高的
        let nearBallPlayers = relevantPlayers.filter { $0.isNearBall }
        if let bestPlayer = nearBallPlayers.max(by: { $0.confidence < $1.confidence }) {
            bestPlayerImage = bestPlayer.image
            bestPlayerConfidence = bestPlayer.confidence
            bestPlayerShootCoord = CGPoint(x: bestPlayer.rect.minY + bestPlayer.rect.height / 2, y: self.videoResolution.height - bestPlayer.rect.minX)
        }
        // 如果没有靠近球的投篮人，则从所有投篮人中选择置信度最高的
        else if let bestPlayer = relevantPlayers.max(by: { $0.confidence < $1.confidence }) {
            bestPlayerImage = bestPlayer.image
            bestPlayerConfidence = bestPlayer.confidence
            bestPlayerShootCoord = CGPoint(x: bestPlayer.rect.minY + bestPlayer.rect.height / 2, y: self.videoResolution.height - bestPlayer.rect.minX)
        }
        
        // 计算2D点位
        var shootCoord: CGPoint?
        if let coord = bestPlayerShootCoord, let mapped = self.mapToCourt(point: coord) {
            LogService.shared.info("投篮人原始坐标为: \(coord.x), \(coord.y)")
            LogService.shared.info("映射后场地坐标为: \(mapped.x), \(mapped.y)")
            shootCoord = mapped
        }
        
        // 创建最终的投篮事件（包含投篮人信息）
        let finalShootEvent = ShootEvent(
            shootTime: event.shootTime,
            goalTime: event.goalTime,
            isGoal: event.isGoal,
            playerImage: bestPlayerImage,
            playerConfidence: bestPlayerConfidence,
            filePath: nil,
            shootCoord: shootCoord
        )
        
        // 立即通知代理（确保在主线程）
        DispatchQueue.main.async { [weak self] in
            self?.delegate?.didDetectShootEvent(event: finalShootEvent)
        }
        
        // 日志
        LogService.shared.info("投篮事件：\(event.isGoal ? "goal" : "miss"), 开始时间=\(String(format: "%.2f", event.shootTime)), 投篮人置信度=\(bestPlayerConfidence)")
        
        return finalShootEvent
    }
    
    /// 启动调试定时器
    private func startDebugTimer() {
        // 停止可能已经运行的调试定时器
        debugTimer?.invalidate()
        
        // 创建新的调试定时器，每秒触发一次
        debugTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            // 打印当前数据状态
            LogService.shared.debug("调试信息: 投篮人数量=\(self.attemptPlayers.count)")
        }
    }
    
    // 投篮分类检测算法
    @objc private func clearAndInitializeImageClassifierService() {
        // 在后台线程初始化服务，避免阻塞UI
        backgroundQueue.async { [weak self] in
            self?.shootClassifierService = nil
            self?.shootClassifierService = ShootClassifierService
                .liveStreamClassifierService(
                  model: ShootClassifierInferenceConfigurationManager.sharedInstance.model,
                  scoreThreshold: ShootClassifierInferenceConfigurationManager.sharedInstance.scoreThreshold,
                  maxResult: ShootClassifierInferenceConfigurationManager.sharedInstance.maxResults,
                  liveStreamDelegate: self,
                  delegate: ShootClassifierInferenceConfigurationManager.sharedInstance.delegate)
        }
    }
}

// MARK: - ObjectDetectorServiceLiveStreamDelegate
extension AIService: ObjectDetectorServiceLiveStreamDelegate {
    func objectDetectorService(_ objectDetectorService: ObjectDetectorService, didFinishDetection result: ResultBundle?, error: Error?) {
        guard let result = result else { return }
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 打印视频分辨率和检测结果
//            print("当前视频分辨率: \(self.videoResolution)")
            
            // 通知代理检测结果
            self.delegate?.aiService(self, didDetectObjects: result, imageSize: self.videoResolution)
            
            // 处理检测结果
            if let objectDetectorResult = result.objectDetectorResults.first as? ObjectDetectorResult {
            self.processDetections(objectDetectorResult.detections, imageSize: self.videoResolution)
            }
        }
    }
}

// MARK: - ShootClassifierServiceLiveStreamDelegate
extension AIService: ShootClassifierServiceLiveStreamDelegate {
    func didCaptureHoopImage(_ image: UIImage) {
        
    }
    
    func shootClassifierService(_ service: ShootClassifierService, didDetectShootEvent event: ShootClassifierEvent) {
        // 将 ShootClassifierEvent 转换为基础 ShootEvent
        let basicShootEvent = ShootEvent(
            shootTime: event.startTimestamp,
            goalTime: event.goalTimestamp,
            isGoal: event.shotType == "goal",
            playerImage: nil,
            playerConfidence: 0.0,
            filePath: nil,
            shootCoord: nil
        )
        
        // 处理投篮分类检测事件（填充投篮人信息并通知代理）
        _ = processShootClassifierEvent(basicShootEvent)
    }
    
    func shootClassifierService(_ service: ShootClassifierService, didFinishClassification result: ClassifierResultBundle?, error: Error?) {
        // 可选：处理分类完成回调，用于调试
        if let error = error {
            LogService.shared.error("投篮分类检测错误: \(error)")
        }
    }
}
