//
//  ShootClassifierService.swift
//  Demo
//
//  Created by Vien on 2025/3/26.
//

import UIKit
import MediaPipeTasksVision
import AVFoundation

/// 投篮分类器内部事件信息
struct ShootClassifierEvent {
    let startFrame: Int             // 开始帧索引
    let shotType: String            // "goal" 或 "miss"
    let confidence: Float           // 最高置信度
    let duration: Int               // 持续帧数
    let goalConfirmationFrame: Int? // 进球确认帧（仅进球时有值）
    let startTimestamp: TimeInterval // 开始时间戳
    let goalTimestamp: TimeInterval? // 进球时间戳（仅进球时有值）
}

/// 分类结果信息
struct ClassificationResult {
    let label: String
    let score: Float
    let timestamp: TimeInterval
}

/**
 投篮分类器服务代理协议
 */
protocol ShootClassifierServiceLiveStreamDelegate: AnyObject {
    /// 当检测到投篮事件时调用
    func shootClassifierService(_ service: ShootClassifierService, didDetectShootEvent event: ShootClassifierEvent)
    
    /// 当完成分类时调用（可选，用于调试）
    func shootClassifierService(_ service: ShootClassifierService, didFinishClassification result: ClassifierResultBundle?, error: Error?)
    
    /// 当截取到篮筐区域图像时调用
    func didCaptureHoopImage(_ image: UIImage)
}

/**
 投篮分类器服务
 结合投篮和进球检测，使用分类模型对篮筐+篮板区域进行三分类（miss、goal、background）
 */
class ShootClassifierService: NSObject {
    
    // MARK: - 代理和属性
    
    weak var liveStreamDelegate: ShootClassifierServiceLiveStreamDelegate?
    
    var imageClassifier: ImageClassifier?
    private(set) var runningMode: RunningMode
    private var scoreThreshold: Float
    private var maxResult: Int
    private var modelPath: String
    private var delegate: ShootClassifierDelegate
    
    // MARK: - 投篮检测配置参数（内部定义）
    
    private struct ShootDetectionConfig {
        static let shotSingleFrameThreshold: Float = 0.65        // 单帧进入投篮检测的阈值
        static let shotConfirmThreshold: Float = 0.75           // 投篮确认阈值
        static let goalHighThreshold: Float = 0.9               // 高置信度进球阈值
        static let goalMidThreshold: Float = 0.75               // 中等置信度进球阈值
        static let goalMidFramesRequired: Int = 2               // 需要多少帧中等置信度才判定为进球
        static let consecutiveBgThreshold: Int = 10             // 连续多少帧背景判定投篮结束
        static let intervalFrames: Int = 2                      // 每多少帧检测一次投篮
        static let minShotFrames: Int = 5                       // 最小投篮事件长度（帧数）
        static let cooldownFrames: Int = 15                     // 投篮事件结束后的冷却帧数
        static let baseFps: Float = 30.0                        // 基准帧率，用于计算基础跳帧数
        static let windowSize: Int = 6                          // 结果窗口大小
    }
    
    // MARK: - 状态跟踪
    
    /// 结果窗口缓存
    private var resultWindow: [ClassificationResult] = []
    
    /// 投篮事件跟踪
    private var shotInProgress: Bool = false
    private var currentShotStart: Int = 0
    private var backgroundFrameCount: Int = 0
    private var activeShotFrames: Int = 0
    
    /// 冷却机制
    private var currentCooldown: Int = 0
    
    /// 当前投篮事件信息
    private var currentShotType: String? = nil
    private var currentShotConfidence: Float = 0.0
    private var goalConfirmationFrame: Int? = nil
    
    /// 时间戳跟踪
    private var currentShotStartTimestamp: TimeInterval = 0.0
    private var currentGoalTimestamp: TimeInterval? = nil
    
    /// 帧索引跟踪
    private var frameIdx: Int = 0
    private var actualFrameIdx: Int = 0
    
    /// 基础跳帧数（根据帧率计算）
    private var basicIntervalFrames: Int = 1
    
    /// 视频帧率
    private var fps: Float = 30.0
    
    // MARK: - 初始化
    
    private init?(model: ShootClassifierModel,
                  scoreThreshold: Float,
                  maxResult: Int,
                  runningMode: RunningMode,
                  delegate: ShootClassifierDelegate) {
        guard let modelPath = model.modelPath else { return nil }
        
        self.modelPath = modelPath
        self.scoreThreshold = scoreThreshold
        self.runningMode = runningMode
        self.maxResult = maxResult
        self.delegate = delegate
        
        super.init()
        
        createImageClassifier()
    }
    
    private func createImageClassifier() {
        let imageClassifierOptions = ImageClassifierOptions()
        imageClassifierOptions.runningMode = runningMode
        imageClassifierOptions.scoreThreshold = scoreThreshold
        imageClassifierOptions.maxResults = maxResult
        imageClassifierOptions.baseOptions.modelAssetPath = modelPath
        imageClassifierOptions.baseOptions.delegate = delegate.delegate
        
        if runningMode == .liveStream {
            imageClassifierOptions.imageClassifierLiveStreamDelegate = self
        }
        
        do {
            imageClassifier = try ImageClassifier(options: imageClassifierOptions)
        } catch {
            LogService.shared.error("创建投篮分类器失败: \(error)")
        }
    }
    
    // MARK: - 静态初始化方法
    
    static func liveStreamClassifierService(
        model: ShootClassifierModel,
        scoreThreshold: Float,
        maxResult: Int,
        liveStreamDelegate: ShootClassifierServiceLiveStreamDelegate?,
        delegate: ShootClassifierDelegate) -> ShootClassifierService? {
            
        let service = ShootClassifierService(
            model: model,
            scoreThreshold: scoreThreshold,
            maxResult: maxResult,
            runningMode: .liveStream,
            delegate: delegate)
        
        service?.liveStreamDelegate = liveStreamDelegate
        return service
    }
    
    // MARK: - 公共方法
    
    /// 设置视频帧率，用于计算基础跳帧数
    func setVideoFps(_ fps: Float) {
        self.fps = fps > 0 ? fps : ShootDetectionConfig.baseFps
        basicIntervalFrames = max(1, Int(round(self.fps / ShootDetectionConfig.baseFps)))
        LogService.shared.debug("设置视频帧率: \(self.fps), 基础跳帧数: \(basicIntervalFrames)")
    }
    
    /// 重置状态
    func reset() {
        resultWindow.removeAll()
        shotInProgress = false
        currentShotStart = 0
        backgroundFrameCount = 0
        activeShotFrames = 0
        currentCooldown = 0
        currentShotType = nil
        currentShotConfidence = 0.0
        goalConfirmationFrame = nil
        currentShotStartTimestamp = 0.0
        currentGoalTimestamp = nil
        frameIdx = 0
        actualFrameIdx = 0
        LogService.shared.debug("投篮分类器状态已重置")
    }
    
    /// 处理视频帧
    func processFrame(image: UIImage, hoopRect: CGRect, backboardRect: CGRect, timestamp: TimeInterval) {
        // 计算用于裁剪的自定义截取区域
        let croppingBbox = calculateCustomBbox(hoopRect: hoopRect, backboardRect: backboardRect, imageSize: image.size)
        
        // 计算用于显示的bbox（不进行坐标转换）
//        let displayBbox = calculateDisplayBbox(hoopRect: hoopRect, backboardRect: backboardRect, imageSize: image.size)
        
        // 注释掉：不再需要通知前端显示customBbox区域
        // liveStreamDelegate?.shootClassifierService(self, shouldDisplayCustomBbox: displayBbox, imageSize: image.size)
        
        // 应用基础跳帧
        if frameIdx % basicIntervalFrames != 0 {
            frameIdx += 1
            
            // 基础跳帧也计入背景帧和冷却帧
            if shotInProgress {
                backgroundFrameCount += 1
            }
            if currentCooldown > 0 {
                currentCooldown -= 1
            }
            return
        }
        
        // 更新实际处理的帧索引
        actualFrameIdx = frameIdx
        
        // 处理冷却倒计时
        if currentCooldown > 0 {
            currentCooldown -= 1
            frameIdx += 1
            return
        }
        
        // 每隔intervalFrames帧处理一次，除非上一帧是goal或miss
        let shouldSkipInterval = (actualFrameIdx / basicIntervalFrames) % ShootDetectionConfig.intervalFrames != 0
        let lastFrameWasSignificant = !resultWindow.isEmpty && 
            (resultWindow.last!.label != "background" && resultWindow.last!.score >= ShootDetectionConfig.shotSingleFrameThreshold)
        
        if shouldSkipInterval && !lastFrameWasSignificant {
            frameIdx += 1
            if shotInProgress {
                backgroundFrameCount += 1
            }
            return
        }
        
        // 裁剪图像
        guard let croppedImage = cropImage(image, toRect: croppingBbox,  orientation: .left) else {
            frameIdx += 1
            return
        }
      
        self.liveStreamDelegate?.didCaptureHoopImage(croppedImage)
        
        // 进行分类
        classifyImageAsync(croppedImage, timestamp: timestamp)
        
        frameIdx += 1
    }
    
    // MARK: - 私有方法
    
    /// 计算自定义截取区域
    private func calculateCustomBbox(hoopRect: CGRect, backboardRect: CGRect, imageSize: CGSize) -> CGRect {
        // 由于横屏拍摄，需要进行坐标转换
        // hoopRect和backboardRect已经是经过目标检测转换后的像素坐标
        // 但在横屏模式下，这些坐标需要进一步转换
        
        // 对于横屏拍摄(.left方向)，需要将坐标转换回正确的图像坐标系
        // 原始坐标系：(0,0)在左上角
        // 横屏坐标系：需要进行90度旋转变换
        
        let transformedHoopRect = CGRect(
            x: hoopRect.minY,
            y: imageSize.width - hoopRect.maxX,
            width: hoopRect.height,
            height: hoopRect.width
        )
        
        let transformedBackboardRect = CGRect(
            x: backboardRect.minY,
            y: imageSize.width - backboardRect.maxX,
            width: backboardRect.height,
            height: backboardRect.width
        )
        
        // 创建自定义截取区域，参考Python版本逻辑
        let customBbox = CGRect(
            x: transformedBackboardRect.minX - (transformedBackboardRect.width / 20),
            y: transformedBackboardRect.minY,
            width: transformedBackboardRect.width + (transformedBackboardRect.width / 10),
            height: (transformedHoopRect.maxY + transformedHoopRect.height / 4) - transformedBackboardRect.minY
        )
        
        // 确保边界框在图像范围内
        let safeBbox = CGRect(
            x: max(0, customBbox.minX),
            y: max(0, customBbox.minY),
            width: min(imageSize.height - max(0, customBbox.minX), customBbox.width), // 注意：横屏时width对应imageSize.height
            height: min(imageSize.width - max(0, customBbox.minY), customBbox.height)  // 注意：横屏时height对应imageSize.width
        )
        
        // 将坐标转换回原始图像坐标系用于裁剪
        let finalBbox = CGRect(
            x: imageSize.width - safeBbox.maxY,
            y: safeBbox.minX,
            width: safeBbox.height,
            height: safeBbox.width
        )
        
        return finalBbox
    }
    
    /// 裁剪图像
    private func cropImage(_ image: UIImage, toRect rect: CGRect, orientation: UIImage.Orientation? = nil) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }
        
        let imageRect = CGRect(x: 0, y: 0, width: cgImage.width, height: cgImage.height)
        let safeRect = rect.intersection(imageRect)
        
        guard !safeRect.isEmpty else { return nil }
        guard let croppedCgImage = cgImage.cropping(to: safeRect) else { return nil }
        
        let finalOrientation = orientation ?? image.imageOrientation
        return UIImage(cgImage: croppedCgImage, scale: image.scale, orientation: finalOrientation)
    }
    
    /// 异步分类图像
    private func classifyImageAsync(_ image: UIImage, timestamp: TimeInterval) {
        guard let mpImage = try? MPImage(uiImage: image) else { return }
        
        let timestampMs = Int(timestamp * 1000)
        
        do {
            try imageClassifier?.classifyAsync(image: mpImage, timestampInMilliseconds: timestampMs)
        } catch {
            LogService.shared.error("投篮分类异步处理错误: \(error)")
        }
    }
    
    /// 处理分类结果
    private func processClassificationResult(_ result: ImageClassifierResult, timestamp: TimeInterval) {
        guard let classification = result.classificationResult.classifications.first,
              let category = classification.categories.first else { return }
        
        let categoryName = category.categoryName ?? "unknown"
        let score = category.score
        
        let classificationResult = ClassificationResult(
            label: categoryName,
            score: score,
            timestamp: timestamp
        )
        
        // 添加到窗口缓存
        resultWindow.append(classificationResult)
        
        // 保持窗口大小
        if resultWindow.count > ShootDetectionConfig.windowSize {
            resultWindow.removeFirst()
        }
        
        // 更新连续背景帧计数和活跃帧计数
        if shotInProgress {
            if categoryName == "background" || (categoryName != "background" && score < ShootDetectionConfig.shotSingleFrameThreshold) {
                backgroundFrameCount += 1
            } else {
                backgroundFrameCount = 0
                activeShotFrames += 1
            }
        }
        
        // 检测投篮事件
        processShootDetection(currentResult: classificationResult)
        
        // 调试日志
        if score >= ShootDetectionConfig.shotSingleFrameThreshold && categoryName != "background" && categoryName != "invalid" {
            LogService.shared.debug("帧 \(actualFrameIdx): \(categoryName) (\(String(format: "%.2f", score)))")
        }
    }
    
    /// 处理投篮检测逻辑
    private func processShootDetection(currentResult: ClassificationResult) {
        // 检查冷却期
        guard currentCooldown == 0 else { return }
        
        // 检查当前帧是否满足投篮检测条件
        let isShotDetected = (currentResult.label == "goal" || currentResult.label == "miss") && 
                            currentResult.score >= ShootDetectionConfig.shotSingleFrameThreshold
        
        if isShotDetected {
            // 进一步判断是否为进球
            let goalHigh = resultWindow.contains { $0.label == "goal" && $0.score >= ShootDetectionConfig.goalHighThreshold }
            let goalMidCount = resultWindow.filter { $0.label == "goal" && $0.score >= ShootDetectionConfig.goalMidThreshold }.count
            
            let isGoal = goalHigh || goalMidCount >= ShootDetectionConfig.goalMidFramesRequired
            
            if !shotInProgress {
                // 开始新的投篮事件
                startNewShotEvent(isGoal: isGoal, confidence: currentResult.score)
            } else {
                // 更新现有投篮事件
                updateShotEvent(isGoal: isGoal, confidence: currentResult.score)
            }
        }
        
        // 检查投篮事件是否结束
        checkShotEventEnd()
    }
    
    /// 开始新的投篮事件
    private func startNewShotEvent(isGoal: Bool, confidence: Float) {
        shotInProgress = true
        currentShotStart = actualFrameIdx
        currentShotType = isGoal ? "goal" : "miss"
        currentShotConfidence = confidence
        backgroundFrameCount = 0
        activeShotFrames = 1
        goalConfirmationFrame = isGoal ? actualFrameIdx : nil
        currentShotStartTimestamp = Date().timeIntervalSince1970
        currentGoalTimestamp = isGoal ? Date().timeIntervalSince1970 : nil
        
        LogService.shared.debug("🏀 开始新的\(isGoal ? "进球" : "未命中")投篮事件，帧: \(actualFrameIdx)")
    }
    
    /// 更新投篮事件
    private func updateShotEvent(isGoal: Bool, confidence: Float) {
        // 如果检测到进球，更新事件类型
        if isGoal && currentShotType == "miss" {
            currentShotType = "goal"
            goalConfirmationFrame = actualFrameIdx
            currentGoalTimestamp = Date().timeIntervalSince1970
            LogService.shared.debug("⚠️ 更新投篮事件为进球，帧: \(actualFrameIdx)")
        }
        
        // 更新最高置信度
        if confidence > currentShotConfidence {
            currentShotConfidence = confidence
        }
        
        // 重置背景帧计数
        backgroundFrameCount = 0
    }
    
    /// 检查投篮事件是否结束
    private func checkShotEventEnd() {
        guard shotInProgress && backgroundFrameCount >= ShootDetectionConfig.consecutiveBgThreshold else { return }
        
        let framesSinceStart = actualFrameIdx - currentShotStart
        
        if activeShotFrames >= ShootDetectionConfig.minShotFrames {
            // 检查置信度是否达到记录阈值
            if currentShotConfidence >= ShootDetectionConfig.shotConfirmThreshold {
                // 创建投篮事件
                let event = ShootClassifierEvent(
                    startFrame: currentShotStart,
                    shotType: currentShotType ?? "miss",
                    confidence: currentShotConfidence,
                    duration: framesSinceStart,
                    goalConfirmationFrame: goalConfirmationFrame,
                    startTimestamp: currentShotStartTimestamp,
                    goalTimestamp: currentGoalTimestamp
                )
                
                // 通知代理
                liveStreamDelegate?.shootClassifierService(self, didDetectShootEvent: event)
                
                LogService.shared.debug("🏀 投篮事件结束：\(event.shotType)，开始帧: \(event.startFrame)，持续: \(event.duration)帧，置信度: \(String(format: "%.2f", event.confidence))")
            } else {
                LogService.shared.debug("⚠️ 忽略置信度不足的投篮事件（置信度: \(String(format: "%.2f", currentShotConfidence))）")
            }
            
            // 设置冷却期
            currentCooldown = ShootDetectionConfig.cooldownFrames
        } else {
            LogService.shared.debug("⚠️ 忽略过短的投篮事件（活跃帧数: \(activeShotFrames)）")
            currentCooldown = ShootDetectionConfig.cooldownFrames / 2
        }
        
        // 重置投篮状态
        resetShotState()
    }
    
    /// 重置投篮状态
    private func resetShotState() {
        shotInProgress = false
        currentShotStart = 0
        currentShotType = nil
        currentShotConfidence = 0.0
        backgroundFrameCount = 0
        activeShotFrames = 0
        goalConfirmationFrame = nil
        currentShotStartTimestamp = 0.0
        currentGoalTimestamp = nil
    }
    
    /// 计算用于显示的bbox（不进行坐标转换）
    private func calculateDisplayBbox(hoopRect: CGRect, backboardRect: CGRect, imageSize: CGSize) -> CGRect {
// 检查传入的坐标是否合理
        if hoopRect.width <= 0 || hoopRect.height <= 0 {
            LogService.shared.warning("⚠️ hoopRect尺寸无效: \(hoopRect)")
            return CGRect.zero
        }
        
        if backboardRect.width <= 0 || backboardRect.height <= 0 {
            LogService.shared.warning("⚠️ backboardRect尺寸无效: \(backboardRect)")
            return CGRect.zero
        }
        
        // 创建包含篮筐和篮板的显示区域，参考Python版本逻辑
        let displayBbox = CGRect(
            x: backboardRect.minX - (backboardRect.width / 10),
            y: backboardRect.minY,
            width: backboardRect.width + (backboardRect.width / 5),
            height: (hoopRect.maxY + hoopRect.height / 4) - backboardRect.minY
        )
        
        // 确保边界框在图像范围内
        let safeBbox = CGRect(
            x: max(0, displayBbox.minX),
            y: max(0, displayBbox.minY),
            width: min(imageSize.width - max(0, displayBbox.minX), displayBbox.width),
            height: min(imageSize.height - max(0, displayBbox.minY), displayBbox.height)
        )
        
        return safeBbox
    }
}

// MARK: - ImageClassifierLiveStreamDelegate

extension ShootClassifierService: ImageClassifierLiveStreamDelegate {
    func imageClassifier(_ imageClassifier: ImageClassifier, 
                        didFinishClassification result: ImageClassifierResult?, 
                        timestampInMilliseconds: Int, 
                        error: Error?) {
        
        guard let result = result else {
            liveStreamDelegate?.shootClassifierService(self, didFinishClassification: nil, error: error)
            return
        }
        
        let resultBundle = ClassifierResultBundle(
            inferenceTime: Date().timeIntervalSince1970 * 1000 - Double(timestampInMilliseconds),
            imageClassifierResults: [result]
        )
        
        // 处理分类结果
        let timestamp = Double(timestampInMilliseconds) / 1000.0
        processClassificationResult(result, timestamp: timestamp)
        
        // 通知代理
        liveStreamDelegate?.shootClassifierService(self, didFinishClassification: resultBundle, error: nil)
    }
}

