import Flutter
import UIKit
@main
@objc class AppDelegate: FlutterAppDelegate, FlutterStreamHandler, DataCallbackDelegate {
    func didReceiveData(_ data: String) {
        print("6666",data)
        DispatchQueue.main.async {
            self.eventSink?(data)
        }
    }
    func didClosePage() {
        DispatchQueue.main.async {
            // 构造关闭消息字典
            let closeMessage: [String: Any] = [
                "type": "channel_closed",
                "trainingId": CameraViewController.fixedTrainingId
            ]
            // 发送关闭消息（在关闭前传递）
            self.eventSink?(closeMessage)
            
            // 发送结束标志（FlutterEndOfEventStream会自动触发onDone）
//            self.eventSink?(FlutterEndOfEventStream)
            
            // 清空引用
//            self.eventSink = nil
        }
    }
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        eventSink = events
        return nil
    }
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        eventSink = nil
        return nil
    }
    
    private var isLandscape = false
    private var eventSink:FlutterEventSink?
    override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
) -> Bool {
    
    let controller = window?.rootViewController as! FlutterViewController
    
    // 第一个 Method Channel：用于控制横竖屏
    let orientationChannel = FlutterMethodChannel(name: "com.shootz.orientation",
                                                   binaryMessenger: controller.binaryMessenger)
    orientationChannel.setMethodCallHandler { [weak self] (call, result) in
        if call.method == "setLandscape" {
            self?.isLandscape = true
            result(nil)
        } else if call.method == "setPortrait" {
            self?.isLandscape = false
            result(nil)
        } else {
            result(FlutterMethodNotImplemented)
        }
    }
    
    // 第二个 Method Channel：用于展示原生界面
    let nativeViewChannel = FlutterMethodChannel(name: "com.example.my_flutter_app/native_method",
                                                  binaryMessenger: controller.binaryMessenger)
    //第一个 Event Channel：用于传送视频信息给flutter
    let eventChannel = FlutterEventChannel(name: "com.shootz.video_recorder_envent", binaryMessenger: controller.binaryMessenger)
    eventChannel.setStreamHandler(self)
    nativeViewChannel.setMethodCallHandler { (call, result) in
        if call.method == "showNativeView" {
            //  let vc = MyCustomViewController()
            // 1. 获取参数
            guard let args = call.arguments as? [String: Any],
                  let trainingId = args["trainingId"] as? String, let sampleVideoUrl = args["sampleVideoUrl"] as? String else {
                result(FlutterError(
                    code: "INVALID_ARGUMENTS",
                    message: "Missing or invalid parameter",
                    details: nil
                ))
                return
            }
            CameraViewController.fixedTrainingId = trainingId
            CameraViewController.fixedSampleVideoUrl = sampleVideoUrl
            let vc = CameraViewController()
            vc.appDelegate = self
            vc.modalPresentationStyle = .fullScreen // 强制全屏
            controller.present(vc, animated: true, completion: nil)
            result("iOS view presented")
        } else if call.method == "getRecordList" {
            self.handleGetRecordList(call: call, result: result)
        } else if call.method == "updateRecordStatus" {
            self.handleUpdateRecordStatus(call: call, result: result)
        } else if call.method == "uploadResult" {
            self.handleUploadResult(call: call, result: result)
        } else {
            result(FlutterMethodNotImplemented)
        }
    }

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
}
    // 保存上传图片/视频结果
    private func handleUploadResult(call: FlutterMethodCall, result: @escaping FlutterResult) {
        // 1. 获取参数
        guard let args = call.arguments as? [String: Any],
              let isSuccess = args["success"] as? Bool else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing or invalid success parameter",
                details: nil
            ))
            return
        }
        if isSuccess {
            if args["accessUrl"] != nil {
                var str = "上传成功，地址是\(args["accessUrl"] as! String)"
                if args["filePath"] != nil {
                    str = str + "本地地址是\(args["filePath"] ?? "")"
                }
                LogService.shared.info(str)
            }
            if args["filePath"] != nil {
                let filePath:String = args["filePath"] as! String
                if (filePath.contains(".jpg") || filePath.contains("extended_last_")) { //图片和合成后的视频
                    //上传成功，删除本地文件
                    DocumentManager.shared.deleteLocalVideo(filePath)
                }
            }
        }else {
            if args["message"] != nil {
                LogService.shared.warning("\(args["message"] ?? "")")
            }
        }
    }
    // 处理获取录制列表请求
    private func handleGetRecordList(call: FlutterMethodCall, result: @escaping FlutterResult) {
        // 1. 获取参数
        guard let args = call.arguments as? [String: Any],
              let trainingId = args["trainingId"] as? String else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing or invalid trainingId parameter",
                details: nil
            ))
            return
        }
        CameraViewController.fixedTrainingId = trainingId
        // 2. 查询数据
        let records:[ShootEventRecord] = DatabaseManager.shared.getShootEventList(byTrainingID: trainingId)
        if records.count > 0 {
            do {
                let encoder = JSONEncoder()
                encoder.outputFormatting = .prettyPrinted // 可选：美化输出
                
                let jsonData = try encoder.encode(records)
                let jsonString = String(data: jsonData, encoding: .utf8)
                print(jsonString ?? "")
                // 3. 返回结果
                result(jsonString)
            } catch {
                print("编码错误: \(error)")
                result([]) // 返回空数组而不是nil
            }
        } else {
            result([]) // 返回空数组而不是nil
        }
    }
    //更新数据状态
    private func handleUpdateRecordStatus(call: FlutterMethodCall, result: @escaping FlutterResult) {
        // 1. 获取参数
        guard let args = call.arguments as? [String: Any],
              let recordId = args["id"] as? String else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing or invalid id parameter",
                details: nil
            ))
            return
        }
        let eventList = DatabaseManager.shared.getShootEvents(byID: recordId)
        
        if eventList.count > 0 {
            var eventDic = eventList[0]
            // 1. 获取投篮人图像是否上传参数
            guard let args = call.arguments as? [String: Any],
                  let playImgUpload = args["playImgUpload"] as? Bool else {
                // 2. 获取投篮视频是否上传参数
                guard let args = call.arguments as? [String: Any],
                      let filePathUploaded = args["filePathUploaded"] as? Bool else {
                    return
                }
                eventDic.filePathUploaded = filePathUploaded
                return
            }
            eventDic.playerImgUploaded = playImgUpload
            DatabaseManager.shared.updateEventStatus(shootEvent: eventDic)
        }
    }
    override func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        if isLandscape {
            return .allButUpsideDown
        } else {
            return .portrait
        }
    }
}
