//
//  AnnotationView.swift
//  Runner
//
//  Created by NickTang on 2025/5/12.
//

import Foundation
// MARK: AnnotationViewDelegate Declaration
protocol AnnotationViewDelegate: AnyObject {
    //标记完成
    func marksFinishAction(pointArr: [CGPoint])

}
class AnnotationView:UIView {
    weak var delegate: AnnotationViewDelegate?
    var circleLayerArr:[CALayer] = []
    var circlePointArr:[CGPoint] = []
    lazy var backImageV:UIImageView = {
        let imgV = UIImageView()
        imgV.isUserInteractionEnabled = true
        addSubview(imgV)
        imgV.contentMode = .scaleAspectFit
        imgV.snp.makeConstraints { make in
            make.center.width.equalToSuperview()
        }
        return imgV
    }()
    lazy var exampleImageV:UIImageView = {
        let imgV = UIImageView()
        imgV.isUserInteractionEnabled = true
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(magnifyAction))
        imgV.addGestureRecognizer(tapGesture)
        addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.leading.equalTo(0)
            make.top.equalTo(0)
            make.width.equalTo(166)
            make.height.equalTo(156)
        }
        return imgV
    }()
    lazy var finishBtn:UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("标记完成", for: .normal)
        btn.backgroundColor = UIColor(hex: "#22222D")
        btn.layer.cornerRadius = 20
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(finishAction), for: .touchUpInside)
        addSubview(btn)
        btn.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(25)
            make.width.equalTo(200)
            make.height.equalTo(40)
        }
        return btn
    }()
    lazy var resetBtn:UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("重置", for: .normal)
        btn.backgroundColor = .white
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        btn.layer.cornerRadius = 20
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(resetAction), for: .touchUpInside)
        addSubview(btn)
        btn.snp.makeConstraints { make in
            make.leading.equalTo(finishBtn.snp.trailing).offset(10)
            make.centerY.equalTo(finishBtn)
            make.width.equalTo(100)
            make.height.equalTo(finishBtn.snp.height)
        }
        return btn
    }()
    lazy var backBtn:UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("返回", for: .normal)
        btn.backgroundColor = .white
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        btn.layer.cornerRadius = 20
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(backAction), for: .touchUpInside)
        addSubview(btn)
        btn.snp.makeConstraints { make in
            make.trailing.equalTo(finishBtn.snp.leading).offset(-10)
            make.centerY.equalTo(finishBtn)
            make.width.equalTo(resetBtn.snp.width)
            make.height.equalTo(finishBtn.snp.height)
        }
        return btn
    }()
    lazy var tipsLabel:UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.text = "请根据左上角的示意图在球场上的位置按顺序点击标注6个点"
        addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        return label
    }()
    // MARK: - 初始化方法
    init(captureImage: UIImage) {
        super.init(frame: CGRect(x: 0, y: 0, width: screenWidth, height: screenHeight))
        backImageV.image = captureImage
        exampleImageV.image = UIImage(named: "annotation_example")
        finishBtn.setTitleColor(.white, for: .normal)
        resetBtn.setTitleColor(UIColor(hex: "#22222D"), for: .normal)
        backBtn.setTitleColor(UIColor(hex: "#22222D"), for: .normal)
        tipsLabel.textColor = .white
    }
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
                
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)
        if exampleImageV.frame.contains(location) {
            return
        }
        // 创建脉冲动画效果
        let pulse = PulsingCircle(center: location)
        circleLayerArr.append(pulse)
        circlePointArr.append(location)
        self.layer.addSublayer(pulse)
    }
    //MARK: - 放大标注图
    @objc func magnifyAction() {
        // 创建全屏视图控制器
        let fullScreenVC = FullScreenImageViewController()
        fullScreenVC.image = exampleImageV.image
        fullScreenVC.modalPresentationStyle = .fullScreen
        if let parentVC = self.parentViewController {
            parentVC.present(fullScreenVC, animated: false)
        }
        
    }
    //MARK: - 返回
    @objc func backAction() {
        self.removeFromSuperview()
    }
    //MARK: - 标记完成
    @objc func finishAction() {
        if circlePointArr.count < 6 {
            //需标注6个点
            return
        }
        self.removeFromSuperview()
        delegate?.marksFinishAction(pointArr: circlePointArr)
    }
    //MARK: - 重置
    @objc func resetAction() {
        if circleLayerArr.count > 0 {
            for circleLayer in circleLayerArr {
                circleLayer.removeFromSuperlayer()
                circlePointArr.removeAll()
            }
        }
    }
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
}
extension UIView {
    var parentViewController: UIViewController? {
        // 通过响应者链查找
        var parentResponder: UIResponder? = self
        while parentResponder != nil {
            parentResponder = parentResponder?.next
            if let viewController = parentResponder as? UIViewController {
                return viewController
            }
        }
        return nil
    }
}
// 带脉冲动画的小圆点
class PulsingCircle: CALayer {
    init(center: CGPoint) {
        super.init()
        
        self.backgroundColor = UIColor.systemBlue.cgColor
        self.cornerRadius = 7.5
        self.frame = CGRect(x: center.x - 7.5, y: center.y - 7.5, width: 15, height: 15)
        
        // 添加脉冲动画
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.toValue = 1.5
        animation.duration = 0.3
        animation.timingFunction = CAMediaTimingFunction(name: .easeOut)
        animation.autoreverses = true
        animation.isRemovedOnCompletion = true
        
        let fadeAnimation = CABasicAnimation(keyPath: "opacity")
        fadeAnimation.toValue = 1
        fadeAnimation.duration = 0.5
        fadeAnimation.isRemovedOnCompletion = true
        
        self.add(animation, forKey: "pulse")
        self.add(fadeAnimation, forKey: "fade")
        
        // 动画完成后移除
//        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
//            self.removeFromSuperlayer()
//        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
class FullScreenImageViewController: UIViewController {
    var image: UIImage?
    let imageView = UIImageView()
    let closeButton = UIButton()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black
        self.modalPresentationStyle = .overFullScreen
        self.modalTransitionStyle = .crossDissolve
        // 设置图片视图
        imageView.image = image
        imageView.contentMode = .scaleAspectFit
        imageView.frame = view.bounds
        imageView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        view.addSubview(imageView)
    }
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        close()
    }
    @objc func close() {
        dismiss(animated: false, completion: nil)
    }
}
class ShootMarks: UIView {
//    lazy var pointImgV:UIImageView = {
//        let imgV = UIImageView()
//        addSubview(imgV)
//        return imgV
//    }()
    lazy var pointLabel:UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 10)
        label.textColor = .white
        addSubview(label)
        return label
    }()
    lazy var backImageV:UIImageView = {
        let imgV = UIImageView()
        imgV.isUserInteractionEnabled = true
        addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.center.width.height.equalToSuperview()
        }
        return imgV
    }()
    lazy var exampleImageV:UIImageView = {
        let imgV = UIImageView()
        imgV.isUserInteractionEnabled = true
        // 添加点击手势
        addSubview(imgV)
        return imgV
    }()
    // MARK: - 初始化方法
    init() {
        super.init(frame: CGRect(x: 35, y: 10, width: 129, height: 129*1400/1500))
        backImageV.image = UIImage(named: "half_shoot1")
    }
    func addSubImage(shoot:Bool, point:CGPoint) {
        let pointImgV = UIImageView()
        backImageV.addSubview(pointImgV)
        pointImgV.image = UIImage(named: shoot ? "half_shoot2" : "half_shoot3")
        let calPointX = point.x*129/1500
        let calPointY = point.y*120.4/1400
        let pointX = calPointX < 0 ? 0 : calPointX > 129 ? 129 : calPointX
        let pointY = calPointY < 0 ? 0 : calPointY > 120.4 ? 120.4 : calPointY
        pointImgV.frame = CGRect(x: pointX-2, y: pointY-2, width: 4, height: 4)
//        pointLabel.text = "(\(String(format: "%.2f", point.x)), \(String(format: "%.2f", point.y)))"
//        pointLabel.snp.makeConstraints { make in
//            make.leading.equalTo(point.x*0.133333-10)
//            make.top.equalTo(point.y*0.133333+5)
//        }
    }
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    func convertToScreenPoint(fromVideoPoint point: CGPoint, screenSize: CGSize, videoResolution: CGSize) -> CGPoint {
        let widthScale = screenSize.width / videoResolution.width
        let scaledVideoHeight = videoResolution.height * widthScale
        let verticalPadding = (scaledVideoHeight - screenSize.height) / 2.0

        let xOnScreen = point.x * widthScale
        let yOnScreen = point.y * widthScale - verticalPadding

        return CGPoint(x: xOnScreen, y: yOnScreen)
    }
}
