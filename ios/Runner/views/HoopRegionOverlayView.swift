import UIKit

/**
 * 篮筐区域覆盖视图，用于在非检测状态下显示有效篮筐检测区域
 */
class HoopRegionOverlayView: UIView {
    
    // MARK: - 属性
    
    /// 篮筐区域（归一化坐标，0-1范围）
    private var hoopRegion: CGRect = .zero
    
    /// 图像尺寸
    private var imageSize: CGSize = .zero
    
    /// 是否应该显示区域
    private var shouldDisplay: Bool = false
    
    // MARK: - 常量
    
    private struct Constants {
        static let lineWidth: CGFloat = 3.0
        static let regionColor = UIColor.white
        static let cornerLength: CGFloat = 20.0
        static let cornerRadius: CGFloat = 20.0
        static let textColor = UIColor(hex: "#258EFF", alpha: 1)
        static let textBackgroundColor = UIColor.clear
        static let textFont = UIFont.systemFont(ofSize: 16, weight: .regular)
        static let textPadding: CGFloat = 25.0
    }
    
    // MARK: - 初始化方法
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        // 设置视图为透明
        backgroundColor = .clear
    }
    
    // MARK: - 公共方法
    
    /// 设置要显示的篮筐区域
    /// - Parameters:
    ///   - region: 篮筐区域（归一化坐标）
    ///   - imageSize: 图像尺寸
    func setHoopRegion(_ region: CGRect, imageSize: CGSize) {
        hoopRegion = region
        self.imageSize = imageSize
        shouldDisplay = !region.isEmpty
        setNeedsDisplay()
    }
    
    /// 清除区域显示
    func clearDisplay() {
        shouldDisplay = false
        setNeedsDisplay()
    }
    
    // MARK: - 绘制方法
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        // 如果不应该显示或区域为空，则不绘制
        guard shouldDisplay && !hoopRegion.isEmpty else {
            return
        }
        
        let context = UIGraphicsGetCurrentContext()
        context?.saveGState()
        
        // 计算实际绘制坐标
        let drawRect = calculateDrawRect()
        
        // 绘制四角
        Constants.regionColor.setStroke()
        
        // 使用四个角绘制而不是完整矩形
        drawCorners(for: drawRect)
        
        // 绘制文本说明
//        drawHintText(in: drawRect)
        context?.restoreGState()
    }
    
    // MARK: - 私有方法
    
    /// 计算实际绘制区域
    private func calculateDrawRect() -> CGRect {
        // 如果图像尺寸或区域无效，返回空矩形
        guard !hoopRegion.isEmpty && imageSize.width > 0 && imageSize.height > 0 else {
            return .zero
        }
        
        // 参考OverlayView的实现计算偏移量和缩放因子
        let currentSize = self.bounds.size
        let offsetsAndScaleFactor = OverlayView.offsetsAndScaleFactor(
            forImageOfSize: imageSize,
            tobeDrawnInViewOfSize: currentSize,
            withContentMode: .scaleAspectFill)
        
        // 将归一化坐标转换为图像坐标
        let regionInImageCoords = CGRect(
            x: hoopRegion.minX * imageSize.width,
            y: hoopRegion.minY * imageSize.height,
            width: hoopRegion.width * imageSize.width,
            height: hoopRegion.height * imageSize.height
        )
        
        // 参考OverlayView的方式应用变换
        let transformedRect = regionInImageCoords
            .applying(CGAffineTransform(scaleX: offsetsAndScaleFactor.scaleFactor, y: offsetsAndScaleFactor.scaleFactor))
            .applying(CGAffineTransform(translationX: offsetsAndScaleFactor.xOffset, y: offsetsAndScaleFactor.yOffset))
        
        // print("区域转换: 原始归一化=\(hoopRegion), 图像坐标=\(regionInImageCoords), 变换后=\(transformedRect), 缩放=\(offsetsAndScaleFactor.scaleFactor), 偏移=(\(offsetsAndScaleFactor.xOffset), \(offsetsAndScaleFactor.yOffset))")
        
        return transformedRect
    }
    
    /// 绘制四个角
    private func drawCorners(for rect: CGRect) {
        let cornerLength = min(Constants.cornerLength, min(rect.width, rect.height) / 3)
        let lineWidth = Constants.lineWidth
        let radius = Constants.cornerRadius
        
        // 左上角
        let leftTopPath = UIBezierPath()
        leftTopPath.move(to: CGPoint(x: rect.minX + cornerLength, y: rect.minY))
        leftTopPath.addLine(to: CGPoint(x: rect.minX + radius, y: rect.minY))
        leftTopPath.addArc(withCenter: CGPoint(x: rect.minX + radius, y: rect.minY + radius), 
                          radius: radius, 
                          startAngle: 3 * .pi / 2, 
                          endAngle: .pi, 
                          clockwise: false)
        UIColor(hex: "#258EFF").setStroke() // 设置描边颜色
        leftTopPath.addLine(to: CGPoint(x: rect.minX, y: rect.minY + cornerLength))
        leftTopPath.lineWidth = lineWidth
        leftTopPath.stroke()
        
        // 右上角
        let rightTopPath = UIBezierPath()
        rightTopPath.move(to: CGPoint(x: rect.maxX - cornerLength, y: rect.minY))
        rightTopPath.addLine(to: CGPoint(x: rect.maxX - radius, y: rect.minY))
        rightTopPath.addArc(withCenter: CGPoint(x: rect.maxX - radius, y: rect.minY + radius), 
                           radius: radius, 
                           startAngle: 3 * .pi / 2, 
                           endAngle: 0, 
                           clockwise: true)
        rightTopPath.addLine(to: CGPoint(x: rect.maxX, y: rect.minY + cornerLength))
        rightTopPath.lineWidth = lineWidth
        rightTopPath.stroke()
        
        // 左下角
        let leftBottomPath = UIBezierPath()
        leftBottomPath.move(to: CGPoint(x: rect.minX, y: rect.maxY - cornerLength))
        leftBottomPath.addLine(to: CGPoint(x: rect.minX, y: rect.maxY - radius))
        leftBottomPath.addArc(withCenter: CGPoint(x: rect.minX + radius, y: rect.maxY - radius), 
                             radius: radius, 
                             startAngle: .pi, 
                             endAngle: .pi / 2, 
                             clockwise: false)
        leftBottomPath.addLine(to: CGPoint(x: rect.minX + cornerLength, y: rect.maxY))
        leftBottomPath.lineWidth = lineWidth
        leftBottomPath.stroke()
        
        // 右下角
        let rightBottomPath = UIBezierPath()
        rightBottomPath.move(to: CGPoint(x: rect.maxX, y: rect.maxY - cornerLength))
        rightBottomPath.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - radius))
        rightBottomPath.addArc(withCenter: CGPoint(x: rect.maxX - radius, y: rect.maxY - radius), 
                              radius: radius, 
                              startAngle: 0, 
                              endAngle: .pi / 2, 
                              clockwise: true)
        rightBottomPath.addLine(to: CGPoint(x: rect.maxX - cornerLength, y: rect.maxY))
        rightBottomPath.lineWidth = lineWidth
        rightBottomPath.stroke()
    }
    
    /// 绘制提示文本
    private func drawHintText(in rect: CGRect) {
        // 如果区域太小或位置异常，不显示文本
        if rect.isEmpty || rect.height < 20 {
            return
        }
        
        let text = "请将手机视野对准篮筐和球场位置"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: Constants.textFont,
            .foregroundColor: Constants.textColor
        ]
        
        let textSize = text.size(withAttributes: attributes)
        
        // 计算文本位置：区域正下方，居中对齐
        // 确保文本显示在视图边界内
        let textX = min(max(rect.midX - textSize.width / 2, Constants.textPadding), 
                       bounds.width - textSize.width - Constants.textPadding * 2)
        let textY = min(rect.maxY + Constants.textPadding, 
                        bounds.height - textSize.height - Constants.textPadding * 2)
        
        let textRect = CGRect(
            x: textX,
            y: textY,
            width: textSize.width + Constants.textPadding * 2,
            height: textSize.height + Constants.textPadding * 2
        )
        
        // 绘制文本背景
        let backgroundPath = UIBezierPath(roundedRect: textRect, cornerRadius: 4)
        Constants.textBackgroundColor.setFill()
        backgroundPath.fill()
        
        // 绘制文本
        text.draw(
            at: CGPoint(
                x: textRect.minX + Constants.textPadding,
                y: textRect.minY + Constants.textPadding
            ),
            withAttributes: attributes
        )
    }
} 
