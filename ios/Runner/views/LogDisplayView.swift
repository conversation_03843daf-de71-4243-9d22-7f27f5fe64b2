import UIKit

/// 日志显示视图可见性代理协议
protocol LogDisplayVisibilityDelegate: AnyObject {
    /// 当日志显示视图被隐藏时调用
    func logDisplayViewDidHide(_ logDisplayView: LogDisplayView)
}

/**
 * 日志显示视图，用于在屏幕上展示日志信息
 * - 支持自动滚动到底部显示最新日志
 * - 支持手动滑动查看历史日志
 * - 提供按钮跳转到最新日志
 */
class LogDisplayView: UIView, LogServiceDelegate {
    
    // MARK: - 属性
    
    /// 表格视图用于显示日志
    private let tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = true
        tableView.indicatorStyle = .white
        tableView.allowsSelection = false
        tableView.translatesAutoresizingMaskIntoConstraints = false
        return tableView
    }()
    
    /// 跳转到底部按钮
    private let scrollToBottomButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "arrow.down.circle.fill"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.darkGray.withAlphaComponent(0.7)
        button.layer.cornerRadius = 20
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isHidden = true
        return button
    }()
    
    /// 清除按钮
    private let clearButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "trash"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.darkGray.withAlphaComponent(0.7)
        button.layer.cornerRadius = 20
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    /// 隐藏按钮
    private let hideButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "eye.slash"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.darkGray.withAlphaComponent(0.7)
        button.layer.cornerRadius = 20
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    /// 是否自动滚动到底部
    private var shouldAutoScroll = true
    
    /// 是否用户正在交互
    private var isUserScrolling = false
    
    /// 隐藏/显示代理
    weak var visibilityDelegate: LogDisplayVisibilityDelegate?
    
    // MARK: - 初始化方法
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
        registerToLogService()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
        registerToLogService()
    }
    
    deinit {
        // 取消注册
        LogService.shared.removeDelegate(self)
    }
    
    // MARK: - 私有方法
    
    private func setupView() {
        // 设置视图
        backgroundColor = UIColor.black.withAlphaComponent(0.5)
        layer.cornerRadius = 10
        clipsToBounds = true
        
        // 添加表格视图
        addSubview(tableView)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(LogEntryCell.self, forCellReuseIdentifier: "LogEntryCell")
        
        // 添加滚动按钮
        addSubview(scrollToBottomButton)
        scrollToBottomButton.addTarget(self, action: #selector(scrollToBottom), for: .touchUpInside)
        
        // 添加清除按钮
        addSubview(clearButton)
        clearButton.addTarget(self, action: #selector(clearLogs), for: .touchUpInside)
        
        // 添加隐藏按钮
        addSubview(hideButton)
        hideButton.addTarget(self, action: #selector(hideLogDisplayView), for: .touchUpInside)
        
        // 设置约束
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: topAnchor),
            tableView.leadingAnchor.constraint(equalTo: leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            scrollToBottomButton.heightAnchor.constraint(equalToConstant: 40),
            scrollToBottomButton.widthAnchor.constraint(equalToConstant: 40),
            scrollToBottomButton.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -10),
            scrollToBottomButton.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -10),
            
            clearButton.heightAnchor.constraint(equalToConstant: 40),
            clearButton.widthAnchor.constraint(equalToConstant: 40),
            clearButton.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -10),
            clearButton.topAnchor.constraint(equalTo: topAnchor, constant: 10),
            
            hideButton.heightAnchor.constraint(equalToConstant: 40),
            hideButton.widthAnchor.constraint(equalToConstant: 40),
            hideButton.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -10),
            hideButton.topAnchor.constraint(equalTo: clearButton.bottomAnchor, constant: 10)
        ])
    }
    
    private func registerToLogService() {
        // 注册为LogService的代理
        LogService.shared.addDelegate(self)
    }
    
    @objc private func scrollToBottom() {
        guard !LogService.shared.logHistory.isEmpty else { return }
        shouldAutoScroll = true
        
        // 修复索引越界问题 - 注意索引从0开始
        let lastRow = LogService.shared.logHistory.count - 1
        if lastRow >= 0 {
            let indexPath = IndexPath(row: lastRow, section: 0)
            // 使用安全的滚动方法
            DispatchQueue.main.async { [weak self] in
                guard let self = self, self.tableView.numberOfRows(inSection: 0) > lastRow else { return }
                self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: true)
            }
        }
        
        // 短暂延迟后隐藏按钮
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.scrollToBottomButton.isHidden = true
        }
    }
    
    @objc private func clearLogs() {
        // 清除日志
        LogService.shared.clearLogs()
        tableView.reloadData()
    }
    
    @objc private func hideLogDisplayView() {
        // 隐藏日志显示视图
        visibilityDelegate?.logDisplayViewDidHide(self)
    }
    
    // MARK: - LogServiceDelegate
    
    func logService(_ service: LogService, didGenerateLog message: String, level: LogLevel, timestamp: Date) {
        // 在主线程更新UI
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 刷新表格
            self.tableView.reloadData()
            
            // 如果应该自动滚动，滚动到底部
            if self.shouldAutoScroll {
                let lastRow = service.logHistory.count - 1
                if lastRow >= 0 {
                    // 使用安全的滚动方法
                    guard self.tableView.numberOfRows(inSection: 0) > lastRow else { return }
                    let indexPath = IndexPath(row: lastRow, section: 0)
                    self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: true)
                }
            }
        }
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension LogDisplayView: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return LogService.shared.logHistory.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "LogEntryCell", for: indexPath) as! LogEntryCell
        let logEntry = LogService.shared.logHistory[indexPath.row]
        cell.configure(with: logEntry)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 44
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        // 用户开始滑动
        isUserScrolling = true
        shouldAutoScroll = false
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        // 用户结束滑动
        isUserScrolling = false
        
        // 检查是否滑动到底部
        let bottomEdge = scrollView.contentOffset.y + scrollView.frame.size.height
        let threshold: CGFloat = 20 // 容差范围
        
        if bottomEdge >= scrollView.contentSize.height - threshold {
            // 如果滑动到底部，恢复自动滚动
            shouldAutoScroll = true
            scrollToBottomButton.isHidden = true
        } else {
            // 如果不在底部，显示滚动按钮
            scrollToBottomButton.isHidden = false
        }
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 如果不是由用户引起的滚动，不处理
        if !isUserScrolling { return }
        
        let bottomEdge = scrollView.contentOffset.y + scrollView.frame.size.height
        let threshold: CGFloat = 20 // 容差范围
        
        if bottomEdge >= scrollView.contentSize.height - threshold {
            shouldAutoScroll = true
            scrollToBottomButton.isHidden = true
        } else {
            shouldAutoScroll = false
            scrollToBottomButton.isHidden = false
        }
    }
}

// MARK: - 日志单元格
class LogEntryCell: UITableViewCell {
    private let messageLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        label.numberOfLines = 0
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let timestampLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.lightGray
        label.font = UIFont.monospacedSystemFont(ofSize: 10, weight: .regular)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCell()
    }
    
    private func setupCell() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        
        contentView.addSubview(timestampLabel)
        contentView.addSubview(messageLabel)
        
        NSLayoutConstraint.activate([
            timestampLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            timestampLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 8),
            
            messageLabel.topAnchor.constraint(equalTo: timestampLabel.bottomAnchor, constant: 2),
            messageLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 8),
            messageLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            messageLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4)
        ])
    }
    
    func configure(with entry: LogEntry) {
        // 设置颜色
        switch entry.level {
        case .info:
            messageLabel.textColor = UIColor.green
        case .warning:
            messageLabel.textColor = UIColor.yellow
        case .error:
            messageLabel.textColor = UIColor.red
        case .debug:
            messageLabel.textColor = .white
        }
        
        // 设置文本
        messageLabel.text = entry.message
        
        // 设置时间戳
        timestampLabel.text = entry.formattedTimestamp
    }
}

// MARK: - 扩展 UIViewController 添加日志视图的便捷方法
extension UIViewController {
    /// 添加日志显示视图到当前控制器的视图
    func addLogDisplayView() -> LogDisplayView {
        let logView = LogDisplayView()
        logView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(logView)
        
        NSLayoutConstraint.activate([
            logView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
            logView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -60),
            logView.widthAnchor.constraint(equalToConstant: 300),
            logView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.75) // 再增加1/8高度
        ])
        
        // 设置视图初始状态为隐藏
        logView.isHidden = true
        
        // 添加显示日志的按钮
        let showLogButton = createShowLogButton(for: logView)
        view.addSubview(showLogButton)
        
        NSLayoutConstraint.activate([
            showLogButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
            showLogButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -60),
            showLogButton.widthAnchor.constraint(equalToConstant: 40),
            showLogButton.heightAnchor.constraint(equalToConstant: 40)
        ])
        
        // 设置日志视图代理
        logView.visibilityDelegate = self
        
        return logView
    }
    
    /// 创建显示日志按钮
    private func createShowLogButton(for logView: LogDisplayView) -> UIButton {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "list.bullet.rectangle"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.darkGray.withAlphaComponent(0.7)
        button.layer.cornerRadius = 20
        button.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加点击事件
        button.addTarget(self, action: #selector(showLogDisplayView(_:)), for: .touchUpInside)
        
        // 存储关联引用
        objc_setAssociatedObject(button, &AssociatedKeys.logDisplayViewKey, logView, .OBJC_ASSOCIATION_ASSIGN)
        
        return button
    }
    
    /// 显示日志视图
    @objc private func showLogDisplayView(_ sender: UIButton) {
        guard let logView = objc_getAssociatedObject(sender, &AssociatedKeys.logDisplayViewKey) as? LogDisplayView else {
            return
        }
        
        // 显示日志视图
        logView.isHidden = false
        
        // 隐藏显示按钮
        sender.isHidden = true
    }
}

// MARK: - LogDisplayVisibilityDelegate
extension UIViewController: LogDisplayVisibilityDelegate {
    func logDisplayViewDidHide(_ logDisplayView: LogDisplayView) {
        // 隐藏日志视图
        logDisplayView.isHidden = true
        
        // 查找并显示关联的显示按钮
        for subview in view.subviews {
            if let button = subview as? UIButton,
               let associatedLogView = objc_getAssociatedObject(button, &AssociatedKeys.logDisplayViewKey) as? LogDisplayView,
               associatedLogView === logDisplayView {
                button.isHidden = false
                break
            }
        }
    }
}

// 关联对象的Key
private struct AssociatedKeys {
    static var logDisplayViewKey = "LogDisplayViewKey"
} 
