//
//  TimerManager.swift
//  Runner
//
//  Created by NickTang on 2025/5/12.
//

import Foundation

class TimerManager {
    private var timer: Timer?
    private var totalSeconds: Int = 0
    private var isPaused: Bool = false
    private var pausedSeconds: Int = 0
    
    var onTimeUpdated: ((String) -> Void)?
    
    // 开始或继续计时
    func startOrResume() {
        if isPaused {
            // 从暂停状态恢复
            isPaused = false
            totalSeconds = pausedSeconds
            startTimer()
        } else if timer == nil {
            // 全新开始
            totalSeconds = 0
            startTimer()
        }
    }
    
    // 暂停计时
    func pause() {
        guard !isPaused, timer != nil else { return }
        
        timer?.invalidate()
        timer = nil
        isPaused = true
        pausedSeconds = totalSeconds
    }
    
    // 重置计时
    func reset() {
        timer?.invalidate()
        timer = nil
        totalSeconds = 0
        pausedSeconds = 0
        isPaused = false
        updateTimeDisplay()
    }
    
    // 私有方法：启动定时器
    private func startTimer() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(updateTimer), userInfo: nil, repeats: true)
        RunLoop.current.add(timer!, forMode: .common)
    }
    
    // 更新时间
    @objc private func updateTimer() {
        totalSeconds += 1
        updateTimeDisplay()
    }
    
    // 更新显示
    private func updateTimeDisplay() {
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        let timeString: String
        
        if hours > 0 {
            timeString = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            timeString = String(format: "%02d:%02d", minutes, seconds)
        }
        
        onTimeUpdated?(timeString)
    }
}
