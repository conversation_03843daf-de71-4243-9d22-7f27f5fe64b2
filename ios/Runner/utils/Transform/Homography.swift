//
//  Homography.swift
//  Demo
//
//  Created by <PERSON><PERSON> on 2025/5/10.
//

import Foundation
import CoreGraphics
import Accelerate

enum Transform {
    struct Homography {
        static func findHomography(from src: [CGPoint], to dst: [CGPoint]) -> [[Double]]? {
            // 确保有足够的点对（至少需要4对）
            guard src.count == dst.count, src.count >= 4 else { return nil }
            
            // 记录原始源点和目标点用于调试
            print("Source points:", src)
            print("Destination points:", dst)
            
            // 4点法直接计算单应性矩阵
            if src.count == 4 {
                return computeHomographyExact(src: src, dst: dst)
            }
            
            // 如果点超过4个，使用最小二乘法
            return computeHomographyLeastSquares(src: src, dst: dst)
        }
        
        // 使用4点直接求解单应性矩阵
        private static func computeHomographyExact(src: [CGPoint], dst: [CGPoint]) -> [[Double]]? {
            // 构建线性系统 Ax = b
            var A = [Double](repeating: 0.0, count: 8 * 8)
            var b = [Double](repeating: 0.0, count: 8)
            
            for i in 0..<4 {
                let srcX = Double(src[i].x)
                let srcY = Double(src[i].y)
                let dstX = Double(dst[i].x)
                let dstY = Double(dst[i].y)
                
                // 每个点对生成两个方程
                let row1 = i * 2
                let row2 = i * 2 + 1
                
                // X坐标方程
                A[row1 * 8 + 0] = srcX
                A[row1 * 8 + 1] = srcY
                A[row1 * 8 + 2] = 1.0
                A[row1 * 8 + 3] = 0.0
                A[row1 * 8 + 4] = 0.0
                A[row1 * 8 + 5] = 0.0
                A[row1 * 8 + 6] = -srcX * dstX
                A[row1 * 8 + 7] = -srcY * dstX
                b[row1] = dstX
                
                // Y坐标方程
                A[row2 * 8 + 0] = 0.0
                A[row2 * 8 + 1] = 0.0
                A[row2 * 8 + 2] = 0.0
                A[row2 * 8 + 3] = srcX
                A[row2 * 8 + 4] = srcY
                A[row2 * 8 + 5] = 1.0
                A[row2 * 8 + 6] = -srcX * dstY
                A[row2 * 8 + 7] = -srcY * dstY
                b[row2] = dstY
            }
            
            // 求解方程组 Ax = b
            if let solution = solveLinearSystem(A, b, n: 8) {
                // 构建3x3单应性矩阵
                let h = [
                    [solution[0], solution[1], solution[2]],
                    [solution[3], solution[4], solution[5]],
                    [solution[6], solution[7], 1.0]
                ]
                
                // 验证结果是否合理
                let testPoint = apply(to: src[0], using: h)
                print("测试第一个点映射: 输入 \(src[0]), 预期 \(dst[0]), 实际 \(testPoint)")
                
                return h
            }
            
            return nil
        }
        
        // 基于最小二乘法计算单应性矩阵（用于超过4个点的情况）
        private static func computeHomographyLeastSquares(src: [CGPoint], dst: [CGPoint]) -> [[Double]]? {
            let n = src.count
            var A = [Double](repeating: 0.0, count: 2 * n * 8)
            var b = [Double](repeating: 0.0, count: 2 * n)
            
            for i in 0..<n {
                let srcX = Double(src[i].x)
                let srcY = Double(src[i].y)
                let dstX = Double(dst[i].x)
                let dstY = Double(dst[i].y)
                
                let row1 = i * 2
                let row2 = i * 2 + 1
                
                // X坐标方程
                A[row1 * 8 + 0] = srcX
                A[row1 * 8 + 1] = srcY
                A[row1 * 8 + 2] = 1.0
                A[row1 * 8 + 3] = 0.0
                A[row1 * 8 + 4] = 0.0
                A[row1 * 8 + 5] = 0.0
                A[row1 * 8 + 6] = -srcX * dstX
                A[row1 * 8 + 7] = -srcY * dstX
                b[row1] = dstX
                
                // Y坐标方程
                A[row2 * 8 + 0] = 0.0
                A[row2 * 8 + 1] = 0.0
                A[row2 * 8 + 2] = 0.0
                A[row2 * 8 + 3] = srcX
                A[row2 * 8 + 4] = srcY
                A[row2 * 8 + 5] = 1.0
                A[row2 * 8 + 6] = -srcX * dstY
                A[row2 * 8 + 7] = -srcY * dstY
                b[row2] = dstY
            }
            
            // 计算 A^T * A 和 A^T * b
            var ATA = [Double](repeating: 0.0, count: 8 * 8)
            var ATb = [Double](repeating: 0.0, count: 8)
            
            for i in 0..<8 {
                for j in 0..<8 {
                    var sum = 0.0
                    for k in 0..<(2 * n) {
                        sum += A[k * 8 + i] * A[k * 8 + j]
                    }
                    ATA[i * 8 + j] = sum
                }
                
                var sum = 0.0
                for k in 0..<(2 * n) {
                    sum += A[k * 8 + i] * b[k]
                }
                ATb[i] = sum
            }
            
            // 求解 ATA * x = ATb
            if let solution = solveLinearSystem(ATA, ATb, n: 8) {
                // 构建3x3单应性矩阵
                let h = [
                    [solution[0], solution[1], solution[2]],
                    [solution[3], solution[4], solution[5]],
                    [solution[6], solution[7], 1.0]
                ]
                
                // 验证结果是否合理
                let testPoint = apply(to: src[0], using: h)
                print("测试第一个点映射: 输入 \(src[0]), 预期 \(dst[0]), 实际 \(testPoint)")
                
                return h
            }
            
            return nil
        }
        
        // 求解线性方程组 Ax = b
        private static func solveLinearSystem(_ A: [Double], _ b: [Double], n: Int) -> [Double]? {
            // 创建增广矩阵 [A|b]
            var augmentedMatrix = [Double](repeating: 0.0, count: n * (n + 1))
            
            for i in 0..<n {
                for j in 0..<n {
                    augmentedMatrix[i * (n + 1) + j] = A[i * n + j]
                }
                augmentedMatrix[i * (n + 1) + n] = b[i]
            }
            
            // 高斯消元法
            for i in 0..<n {
                // 主元选择
                var maxRow = i
                var maxVal = abs(augmentedMatrix[i * (n + 1) + i])
                
                for j in (i + 1)..<n {
                    let val = abs(augmentedMatrix[j * (n + 1) + i])
                    if val > maxVal {
                        maxRow = j
                        maxVal = val
                    }
                }
                
                // 如果主元接近零，矩阵可能是奇异的
                if maxVal < 1e-10 {
                    print("矩阵可能是奇异的，主元太小: \(maxVal)")
                    return nil
                }
                
                // 交换行
                if maxRow != i {
                    for j in 0...(n) {
                        let temp = augmentedMatrix[i * (n + 1) + j]
                        augmentedMatrix[i * (n + 1) + j] = augmentedMatrix[maxRow * (n + 1) + j]
                        augmentedMatrix[maxRow * (n + 1) + j] = temp
                    }
                }
                
                // 归一化当前行
                let pivot = augmentedMatrix[i * (n + 1) + i]
                for j in 0...(n) {
                    augmentedMatrix[i * (n + 1) + j] /= pivot
                }
                
                // 消元
                for j in 0..<n {
                    if j != i {
                        let factor = augmentedMatrix[j * (n + 1) + i]
                        for k in 0...(n) {
                            augmentedMatrix[j * (n + 1) + k] -= factor * augmentedMatrix[i * (n + 1) + k]
                        }
                    }
                }
            }
            
            // 提取解
            var solution = [Double](repeating: 0.0, count: n)
            for i in 0..<n {
                solution[i] = augmentedMatrix[i * (n + 1) + n]
            }
            
            return solution
        }
        
        static func apply(to point: CGPoint, using H: [[Double]]) -> CGPoint {
            // 确保H是3x3矩阵
            guard H.count == 3, H[0].count == 3, H[1].count == 3, H[2].count == 3 else {
                return .zero
            }
            
            // 应用单应性变换
            let x = Double(point.x)
            let y = Double(point.y)
            
            let w = H[2][0] * x + H[2][1] * y + H[2][2]
            if abs(w) < 1e-10 {
                return .zero
            }
            
            let transformedX = (H[0][0] * x + H[0][1] * y + H[0][2]) / w
            let transformedY = (H[1][0] * x + H[1][1] * y + H[1][2]) / w
            
            return CGPoint(x: transformedX, y: transformedY)
        }
    }
}

