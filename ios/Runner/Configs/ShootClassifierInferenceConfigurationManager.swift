//
//  ShootClassifierInferenceConfigurationManager.swift
//  Demo
//
//  Created by <PERSON><PERSON> on 2025/3/26.
//

import Foundation
import MediaPipeTasksVision

/**
 * Singleton storing the configs needed to initialize an MediaPipe Tasks object and run inference.
 * Controllers can observe the `InferenceConfigManager.notificationName` for any changes made by the user.
 */
class ShootClassifierInferenceConfigurationManager: NSObject {
    var model: ShootClassifierModel = ShootClassifierConstants.model {
        didSet { postConfigChangedNotification() }
    }
    var delegate: ShootClassifierDelegate = ShootClassifierConstants.delegate {
        didSet { postConfigChangedNotification() }
    }

    var maxResults: Int = ShootClassifierConstants.maxResults {
        didSet { postConfigChangedNotification() }
    }

    var scoreThreshold: Float = ShootClassifierConstants.scoreThreshold {
        didSet { postConfigChangedNotification() }
    }

    static let sharedInstance = ShootClassifierInferenceConfigurationManager()

    static let notificationName = Notification.Name.init(rawValue: "com.google.mediapipe.shootClassifierInferenceConfigChanged")

    private func postConfigChangedNotification() {
        NotificationCenter.default
            .post(name: ShootClassifierInferenceConfigurationManager.notificationName, object: nil)
    }
} 