//
//  ShootClassifierConstants.swift
//  Demo
//
//  Created by <PERSON><PERSON> on 2025/3/26.
//

import Foundation
import MediaPipeTasksVision

// MARK: Define default constants
struct ShootClassifierConstants {
    static let maxResults: Int = 3
    static let scoreThreshold: Float = 0.2
    static let model: ShootClassifierModel = .efficientnetLite0
    static let delegate: ShootClassifierDelegate = .GPU
}

// MARK: Tflite Model
enum ShootClassifierModel: String, CaseIterable {
    case efficientnetLite0 = "EfficientNet-Lite0"
    case efficientnetLite2 = "EfficientNet-Lite2"
    case ssd_mobilenet_v2 = "SSD_Mobilenet_V2"

    var modelPath: String? {
        switch self {
        case .efficientnetLite0:
            return Bundle.main.path(
                forResource: "clsf_goal", ofType: "tflite")
        case .efficientnetLite2:
            return Bundle.main.path(
                forResource: "clsf_goal_efficientnet_lite2", ofType: "tflite")
        case .ssd_mobilenet_v2:
            return Bundle.main.path(
                forResource: "clsf_goal_mobilessd_v2", ofType: "tflite")
        }
    }
}

// MARK: ImageClassifierDelegate
enum ShootClassifierDelegate: CaseIterable {
    case GPU
    case CPU

    var name: String {
        switch self {
        case .GPU:
            return "GPU"
        case .CPU:
            return "CPU"
        }
    }

    var delegate: Delegate {
        switch self {
        case .GPU:
            return .GPU
        case .CPU:
            return .CPU
        }
    }

    init?(name: String) {
        switch name {
        case ShootClassifierDelegate.CPU.name:
            self = ShootClassifierDelegate.CPU
        case ShootClassifierDelegate.GPU.name:
            self = ShootClassifierDelegate.GPU
        default:
            return nil
        }
    }
} 