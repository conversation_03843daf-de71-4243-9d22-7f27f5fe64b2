//
//  ClassifierConstants.swift
//  Demo
//
//  Created by <PERSON><PERSON> on 2025/4/19.
//

import Foundation
import UIKit
import MediaPipeTasksVision

// MARK: Define default constants
struct ClassifierConstants {
  static let maxResults: Int = 3
  static let scoreThreshold: Float = 0.2
  static let labelColors = [
    UIColor.red,
    UIColor(displayP3Red: 90.0/255.0, green: 200.0/255.0, blue: 250.0/255.0, alpha: 1.0),
    UIColor.green,
    UIColor.orange,
    UIColor.blue,
    UIColor.purple,
    UIColor.magenta,
    UIColor.yellow,
    UIColor.cyan,
    UIColor.brown
  ]
  static let ovelayColor = UIColor(red: 0, green: 127/255.0, blue: 139/255.0, alpha: 1)
  static let displayFont = UIFont.systemFont(ofSize: 14.0, weight: .medium)
  static let model: ClassifierModel = .efficientnetLite0
//  static let model: ClassifierModel = .ssd_mobilenet_v2
  static let delegate: GoalClassifierClassifierDelegate = .GPU
}

// MARK: Tflite Model
enum ClassifierModel: String, CaseIterable {
    case efficientnetLite0 = "EfficientNet-Lite0"
    case efficientnetLite2 = "EfficientNet-Lite2"
    case ssd_mobilenet_v2 = "SSD_Mobilenet_V2"

    var modelPath: String? {
        switch self {
        case .efficientnetLite0:
            return Bundle.main.path(
                forResource: "goal_classifier_efficientnet_lite0", ofType: "tflite")
        case .efficientnetLite2:
            return Bundle.main.path(
                forResource: "goal_classifier_efficientnet_lite2", ofType: "tflite")
        case .ssd_mobilenet_v2:
            return Bundle.main.path(
                forResource: "goal_classifier_mobilessd_v2", ofType: "tflite")
        }
    }
}

// MARK: ImageClassifierDelegate
enum GoalClassifierClassifierDelegate: CaseIterable {
  case GPU
  case CPU

  var name: String {
    switch self {
    case .GPU:
      return "GPU"
    case .CPU:
      return "CPU"
    }
  }

  var delegate: Delegate {
    switch self {
    case .GPU:
      return .GPU
    case .CPU:
      return .CPU
    }
  }

  init?(name: String) {
    switch name {
    case GoalClassifierClassifierDelegate.CPU.name:
      self = GoalClassifierClassifierDelegate.CPU
    case GoalClassifierClassifierDelegate.GPU.name:
      self = GoalClassifierClassifierDelegate.GPU
    default:
      return nil
    }
  }
}
