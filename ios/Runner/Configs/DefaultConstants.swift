// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Foundation
import UIKit
import MediaPipeTasksVision

// MARK: Define default constants
struct DefaultConstants {
  static let maxResults = 10
  static let scoreThreshold: Float = 0.25
  static let labelColors = [
    UIColor.red,
    UIColor(displayP3Red: 90.0/255.0, green: 200.0/255.0, blue: 250.0/255.0, alpha: 1.0),
    UIColor.green,
    UIColor.orange,
    UIColor.blue,
    UIColor.purple,
    UIColor.magenta,
    UIColor.yellow,
    UIColor.cyan,
    UIColor.brown
  ]
  static let displayFont = UIFont.systemFont(ofSize: 14.0, weight: .medium)
  static let model: Model = .efficientdetLite0
//  static let model: Model = .yolo11n_float16
  static let delegate: Delegate = .CPU
}

// MARK: Model
enum Model: Int, CaseIterable {
  case efficientdetLite0
  case efficientdetLite1
  case efficientdetLite2
  case ssd_mobilenet_v2
  case shootz_model
  case yolo11n_float16

  var name: String {
    switch self {
    case .efficientdetLite0:
      return "EfficientDet-Lite0"
    case .efficientdetLite1:
      return "EfficientDet-Lite1"
    case .efficientdetLite2:
      return "EfficientDet-Lite2"
    case .ssd_mobilenet_v2:
      return "SSD_Mobilenet_V2"
    case .shootz_model:
      return "Shootz_Model"
    case .yolo11n_float16:
      return "YOLO11n_Float16"
    }
  }
  
  var modelPath: String? {
    switch self {
    case .efficientdetLite0:
      return Bundle.main.path(
        forResource: "efficientdet_lite0", ofType: "tflite")
    case .efficientdetLite1:
      return Bundle.main.path(
        forResource: "efficientdet_lite1", ofType: "tflite")
    case .efficientdetLite2:
      return Bundle.main.path(
        forResource: "efficientdet_lite2", ofType: "tflite")
    case .ssd_mobilenet_v2:
      return Bundle.main.path(
        forResource: "ssd_mobilenet_v2", ofType: "tflite")
    case .shootz_model:
      return Bundle.main.path(
        forResource: "shootz_model", ofType: "tflite")
    case .yolo11n_float16:
      return Bundle.main.path(
        forResource: "yolo11n_float16", ofType: "tflite")
    }
  }
  
  init?(name: String) {
    switch name {
    case "EfficientDet-Lite0":
      self.init(rawValue: 0)
    case "EfficientDet-Lite1":
      self.init(rawValue: 1)
    case "EfficientDet-Lite2":
      self.init(rawValue: 2)
    case "SSD_Mobilenet_V2":
      self.init(rawValue: 3)
    case "Shootz_Model":
      self.init(rawValue: 4)
    case "YOLO11n_Float16":
      self.init(rawValue: 5)
    default:
      return nil
    }
  }
  
}
