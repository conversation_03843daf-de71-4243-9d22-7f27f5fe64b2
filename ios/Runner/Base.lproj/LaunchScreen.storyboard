<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_launch_center" translatesAutoresizingMaskIntoConstraints="NO" id="RZS-49-C1N">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Uzl-qQ-esh"/>
                        <color key="backgroundColor" red="0.058823529411764705" green="0.058823529411764705" blue="0.086274509803921567" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="RZS-49-C1N" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="9GL-aW-wC1"/>
                            <constraint firstItem="RZS-49-C1N" firstAttribute="leading" secondItem="Uzl-qQ-esh" secondAttribute="leading" id="npm-QL-UhD"/>
                            <constraint firstAttribute="bottom" secondItem="RZS-49-C1N" secondAttribute="bottom" id="nwF-Kh-HH8"/>
                            <constraint firstItem="Uzl-qQ-esh" firstAttribute="trailing" secondItem="RZS-49-C1N" secondAttribute="trailing" id="rZc-ek-EwG"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="80.916030534351137" y="264.08450704225356"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_launch_center" width="375" height="812"/>
    </resources>
</document>
