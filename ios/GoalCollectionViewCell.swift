//
//  GoalCollectionViewCell.swift
//  Demo
//
//  Created by Apple on 2025/4/1
//
//
        

import UIKit

class GoalCollectionViewCell: UICollectionViewCell {
    // 显示投篮图像
    let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFit
        iv.clipsToBounds = true
        iv.backgroundColor = .lightGray
        iv.layer.cornerRadius = 5
        return iv
    }()
    
    // 显示是否进球的标签
    let goalLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .bold)
        label.textAlignment = .center
        label.textColor = .white
        label.backgroundColor = UIColor(red: 0.18, green: 0.18, blue: 0.18, alpha: 0.7)
        label.layer.cornerRadius = 3
        label.layer.masksToBounds = true
        return label
    }()
    
    // 显示投篮信心度的标签
    let confidenceLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .center
        label.textColor = .darkGray
        return label
    }()
    
    // 显示投篮时间的标签
    let timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .center
        label.textColor = .darkGray
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 8
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOpacity = 0.1
        layer.shadowOffset = CGSize(width: 0, height: 1)
        layer.shadowRadius = 2
        
        // 添加子视图
        contentView.addSubview(imageView)
        contentView.addSubview(goalLabel)
        contentView.addSubview(confidenceLabel)
        contentView.addSubview(timeLabel)
        
        // 设置布局约束
        imageView.translatesAutoresizingMaskIntoConstraints = false
        goalLabel.translatesAutoresizingMaskIntoConstraints = false
        confidenceLabel.translatesAutoresizingMaskIntoConstraints = false
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 5),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 5),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -5),
            imageView.heightAnchor.constraint(equalToConstant: 150),
            
            goalLabel.topAnchor.constraint(equalTo: imageView.topAnchor, constant: 5),
            goalLabel.trailingAnchor.constraint(equalTo: imageView.trailingAnchor, constant: -5),
            goalLabel.widthAnchor.constraint(equalToConstant: 40),
            goalLabel.heightAnchor.constraint(equalToConstant: 20),
            
            confidenceLabel.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 5),
            confidenceLabel.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            
            timeLabel.topAnchor.constraint(equalTo: confidenceLabel.bottomAnchor, constant: 3),
            timeLabel.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            timeLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -5)
        ])
    }
    
    // 配置Cell数据
    func configure(with record: ShootEventRecord) {
        // 设置是否进球
        goalLabel.text = record.isGoal ? "进球" : "未进"
        goalLabel.backgroundColor = record.isGoal ?
            UIColor(red: 0.12, green: 0.6, blue: 0.19, alpha: 0.7) :
            UIColor(red: 0.83, green: 0.18, blue: 0.18, alpha: 0.7)
        
        // 设置投篮信心度
        confidenceLabel.text = String(format: "%.2f", record.playerConfidence)
        
        // 设置投篮时间
        if let shootTime = record.shootTime {
            // 将时间戳转换为Date对象
            let date = Date(timeIntervalSince1970: shootTime)
            
            // 创建日期格式化器
            let formatter = DateFormatter()
            formatter.timeZone = TimeZone(identifier: "Asia/Shanghai") // 东八区
            formatter.dateFormat = "HH:mm:ss"
            
            // 格式化时间
            timeLabel.text = formatter.string(from: date)
        } else {
            timeLabel.text = "无时间"
        }
        
        // 加载图片
        if let imagePath = record.playerImagePath {
            let image = DatabaseManager.shared.getImage(fromPath: imagePath)
            imageView.image = image ?? UIImage(named: "placeholder_image")
        } else {
            imageView.image = UIImage(named: "placeholder_image")
        }
    }
}
