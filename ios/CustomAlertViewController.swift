//
//  CustomAlertViewController.swift
//  Runner
//
//  Created by NickTang on 2025/5/16.
//

import UIKit

class CustomAlertViewController: UIViewController {
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        label.textColor = UIColor(hex: "#3D3D3D")
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private let messageLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#3D3D3D")
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        return view
    }()
    
    private let actionButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("确定", for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 14)
        button.setTitleColor(UIColor(hex: "#EA0000"), for: .normal)
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#EA0000").cgColor
        button.layer.cornerRadius = 22
        return button
    }()
    
    private let cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("取消", for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 14)
        button.setTitleColor(UIColor(hex: "#22222D"), for: .normal)
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#22222D").cgColor
        button.layer.cornerRadius = 22
        return button
    }()
    private var alertTitle: String?
    private var message: String?
    private var actionHandler: (() -> Void)?
    private var cancelHandler: (() -> Void)?
    
    convenience init(title: String, message: String, actionTitle: String = "确定", actionHandler: (() -> Void)? = nil, cancelHandler: (() -> Void)? = nil) {
        self.init()
        self.alertTitle = title
        self.message = message
        self.actionHandler = actionHandler
        self.cancelHandler = cancelHandler
        self.modalPresentationStyle = .overFullScreen
        self.modalTransitionStyle = .crossDissolve
        actionButton.setTitle(actionTitle, for: .normal)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        configureContent()
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        
        // 容器视图
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview() // 居中
            make.width.equalTo(360) // 最大宽度限制
            make.height.equalTo(203)
        }
        
        // 标题
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(30)
        }
        
        // 消息
        containerView.addSubview(messageLabel)
        messageLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.lessThanOrEqualTo(320)
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
        }
        
        // 按钮
        containerView.addSubview(actionButton)
        actionButton.snp.makeConstraints { make in
            make.top.equalTo(messageLabel.snp.bottom).offset(20)
            make.trailing.equalTo(-20)
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
        
        containerView.addSubview(cancelButton)
        cancelButton.snp.makeConstraints { make in
            make.centerY.equalTo(actionButton.snp.centerY)
            make.leading.equalTo(20)
//            make.trailing.equalTo(actionButton.snp.leading).offset(-20)
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
        
        actionButton.addTarget(self, action: #selector(didTapActionButton), for: .touchUpInside)
        cancelButton.addTarget(self, action: #selector(didTapCacelButton), for: .touchUpInside)
    }
    
    private func configureContent() {
        titleLabel.text = alertTitle
//        messageLabel.text = message
        let style = NSMutableParagraphStyle()
        /// 间隙
        style.lineSpacing = 5
        messageLabel.attributedText = NSAttributedString(string: message ?? "", attributes: [NSAttributedString.Key.paragraphStyle: style])
    }
    
    @objc private func didTapActionButton() {
        dismiss(animated: true) {
            self.actionHandler?()
        }
    }
    @objc private func didTapCacelButton() {
        dismiss(animated: true) {
            self.cancelHandler?()
        }
    }
}
