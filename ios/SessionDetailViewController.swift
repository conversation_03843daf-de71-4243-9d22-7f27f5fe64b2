import UIKit
import SnapKit

class SessionDetailViewController: UIViewController {
    
    // MARK: - 属性
    
    private let session: String // 会话开始时间
    private var shootRecords: [ShootEventRecord] = [] // 会话投篮记录
    private var selectedIndex: Int = 0 // 当前选中的索引
    
    // MARK: - UI组件
    
    // 左侧数据展示
    private lazy var leftDataContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 命中/投篮数据标签
    private lazy var madeShootLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .black
        label.text = "1/1"
        return label
    }()
    
    // 命中/投篮文本标签
    private lazy var madeShootTextLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .darkGray
        label.text = "命中/投篮"
        return label
    }()
    
    // 投篮人/投篮数据标签
    private lazy var playerShootLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .black
        label.text = "0/1"
        return label
    }()
    
    // 投篮人/投篮文本标签
    private lazy var playerShootTextLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .darkGray
        label.text = "投篮人/投篮"
        return label
    }()
    
    // 关闭按钮
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("X", for: .normal)
        button.backgroundColor = .white
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .bold)
        button.layer.cornerRadius = 20
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 视频播放器
    private lazy var videoView: UIView = {
        let view = UIView()
        view.backgroundColor = .gray
        let label = UILabel()
        label.textColor = .black
        label.text = "视频播放器"
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        return view
    }()
    
    // 保存到相册按钮
    private lazy var saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("保存到相册", for: .normal)
        button.backgroundColor = .white
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16)
        button.layer.cornerRadius = 8
        return button
    }()
    
    // 当前/总计标签
    private lazy var countLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .black
        label.text = "1/1"
        return label
    }()
    
    // 当前/总计文本标签
    private lazy var countTextLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .darkGray
        label.text = "当前/总计"
        return label
    }()
    
    // 投篮记录集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
        layout.estimatedItemSize = .zero
        
        let view = UICollectionView(frame: .zero, collectionViewLayout: layout)
        view.register(GoalCollectionViewCell.self, forCellWithReuseIdentifier: "cell")
        view.dataSource = self
        view.delegate = self
        view.backgroundColor = .clear
        view.showsHorizontalScrollIndicator = false
        view.contentInsetAdjustmentBehavior = .never
        return view
    }()
    
    // MARK: - 初始化
    
    init(session: String) {
        self.session = session
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 确保以横屏模式显示
        let value = UIInterfaceOrientation.landscapeRight.rawValue
        UIDevice.current.setValue(value, forKey: "orientation")
    }
    
    // MARK: - 设置UI
    
    private func setupUI() {
        view.backgroundColor = UIColor(red: 0.94, green: 0.94, blue: 0.94, alpha: 1.0)
        
        // 添加左侧数据容器
        view.addSubview(leftDataContainer)
        leftDataContainer.addSubview(madeShootLabel)
        leftDataContainer.addSubview(madeShootTextLabel)
        leftDataContainer.addSubview(playerShootLabel)
        leftDataContainer.addSubview(playerShootTextLabel)
        leftDataContainer.addSubview(countLabel)
        leftDataContainer.addSubview(countTextLabel)
        
        // 添加其他视图组件
        view.addSubview(closeButton)
        view.addSubview(videoView)
        view.addSubview(saveButton)
        view.addSubview(collectionView)
        
        // 设置约束
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.height.equalTo(40)
        }
        
        // 左侧数据容器 - 宽度调整为150
        leftDataContainer.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalTo(150)
        }
        
        // 命中/投篮标签
        madeShootLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(100)
            make.left.equalToSuperview().offset(40)
        }
        
        madeShootTextLabel.snp.makeConstraints { make in
            make.top.equalTo(madeShootLabel.snp.bottom).offset(5)
            make.left.equalTo(madeShootLabel)
        }
        
        // 投篮人/投篮标签
        playerShootLabel.snp.makeConstraints { make in
            make.top.equalTo(madeShootTextLabel.snp.bottom).offset(25)
            make.left.equalTo(madeShootLabel)
        }
        
        playerShootTextLabel.snp.makeConstraints { make in
            make.top.equalTo(playerShootLabel.snp.bottom).offset(5)
            make.left.equalTo(madeShootLabel)
        }
        
        // 视频播放器位于中央
        videoView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(leftDataContainer.snp.right).offset(20)
            make.width.equalTo(view.snp.width).multipliedBy(0.55)
            make.height.equalTo(view.snp.height).multipliedBy(0.45)
        }
        
        // 保存到相册按钮位于视频播放器右侧
        saveButton.snp.makeConstraints { make in
            make.left.equalTo(videoView.snp.right).offset(20)
            make.bottom.equalTo(videoView).offset(-10)
            make.width.equalTo(100)
            make.height.equalTo(36)
        }
        
        // 集合视图位于视频播放器下方，左侧与屏幕左侧对齐，右侧与视频播放器右侧对齐
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(videoView.snp.bottom).offset(10)
            make.right.equalToSuperview()
            make.left.equalTo(videoView)
            make.bottom.equalToSuperview()
        }
        
        // 当前/总计标签 - 下移与UICollectionView y轴对齐
        countLabel.snp.makeConstraints { make in
            make.top.equalTo(playerShootTextLabel.snp.bottom).offset(50)
            make.left.equalTo(madeShootLabel)
        }
        
        countTextLabel.snp.makeConstraints { make in
            make.top.equalTo(countLabel.snp.bottom).offset(5)
            make.left.equalTo(madeShootLabel)
        }
    }
    
    // MARK: - 数据加载
    
    private func loadData() {
        // 从数据库加载投篮记录
        shootRecords = DatabaseManager.shared.getShootEvents(byStartTime: session)
        
        // 计算统计数据
        let totalShots = shootRecords.count
        let madeShots = shootRecords.filter { $0.isGoal }.count
        let recognizedPlayers = shootRecords.filter { $0.playerImagePath != nil }.count
        
        // 更新UI
        madeShootLabel.text = "\(madeShots)/\(totalShots)"
        playerShootLabel.text = "\(recognizedPlayers)/\(totalShots)"
        updateCountLabel()
        
        // 刷新集合视图
        collectionView.reloadData()
        
        // 默认选中第一个
        if !shootRecords.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                let indexPath = IndexPath(item: 0, section: 0)
                self.collectionView.selectItem(at: indexPath, animated: false, scrollPosition: .left)
                self.selectedIndex = 0
                self.updateCountLabel()
                self.collectionView.reloadData()
            }
        }
    }
    
    private func updateCountLabel() {
        if shootRecords.isEmpty {
            countLabel.text = "0/0"
        } else {
            countLabel.text = "\(selectedIndex + 1)/\(shootRecords.count)"
        }
    }
    
    // MARK: - 事件处理
    
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate
extension SessionDetailViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return shootRecords.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "cell", for: indexPath) as! GoalCollectionViewCell
        let record = shootRecords[indexPath.item]
        cell.configure(with: record)
        
        // 设置选中状态的高亮样式
        if indexPath.item == selectedIndex {
            cell.layer.borderWidth = 2
            cell.layer.borderColor = UIColor(red: 0.0, green: 0.7, blue: 0.0, alpha: 0.8).cgColor
        } else {
            cell.layer.borderWidth = 0
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedIndex = indexPath.item
        updateCountLabel()
        collectionView.reloadData()
        
        // 滚动到选中的cell
        collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension SessionDetailViewController: UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 100, height: 210)
    }
} 
