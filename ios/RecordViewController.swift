import UIKit
import SnapKit

class RecordViewController: UIViewController {
    
    // MARK: - 属性
    
    private var sessions: [String] = [] // 所有会话的开始时间
    // 各会话的统计数据 [startTime: (命中数, 总数, 识别投篮人数)]
    private var sessionStats: [String: (made: Int, total: Int, recognizedPlayers: Int)] = [:]
    
    // MARK: - UI组件
    
    private lazy var backButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("返回", for: .normal)
        button.backgroundColor = .white
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16)
        button.layer.cornerRadius = 20
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.register(RecordTableViewCell.self, forCellReuseIdentifier: RecordTableViewCell.identifier)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        return table
    }()
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 确保以横屏模式显示
        let value = UIInterfaceOrientation.landscapeRight.rawValue
        UIDevice.current.setValue(value, forKey: "orientation")
    }
    
    // MARK: - 设置UI
    
    private func setupUI() {
        view.backgroundColor = UIColor(red: 0.94, green: 0.94, blue: 0.94, alpha: 1.0) // 浅灰色背景
        
        view.addSubview(backButton)
        view.addSubview(tableView)
        
        // 设置约束
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.top.equalToSuperview().offset(20)
            make.width.equalTo(60)
            make.height.equalTo(40)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(backButton.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    // MARK: - 数据加载
    
    private func loadData() {
        // 获取所有会话开始时间
        sessions = DatabaseManager.shared.getAllStartTimes()
        
        // 计算每个会话的统计数据
        for session in sessions {
            let records = DatabaseManager.shared.getShootEvents(byStartTime: session)
            let madeShots = records.filter { $0.isGoal }.count
            let totalShots = records.count
            let recognizedPlayers = records.filter { $0.playerImagePath != nil }.count
            
            sessionStats[session] = (made: madeShots, total: totalShots, recognizedPlayers: recognizedPlayers)
        }
        
        tableView.reloadData()
    }
    
    // MARK: - 事件处理
    
    @objc private func backButtonTapped() {
        dismiss(animated: true)
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension RecordViewController: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sessions.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: RecordTableViewCell.identifier, for: indexPath) as! RecordTableViewCell
        
        let session = sessions[indexPath.row]
        
        // 格式化日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        if let date = dateFormatter.date(from: session) {
            dateFormatter.dateFormat = "yyyy.MM.dd"
            let formattedDate = dateFormatter.string(from: date)
            cell.dateLabel.text = formattedDate
        } else {
            cell.dateLabel.text = session
        }
        
        // 设置统计数据
        if let stats = sessionStats[session] {
            // 计算命中率
            let madePercentage = stats.total > 0 ? Double(stats.made) / Double(stats.total) * 100 : 0
            
            // 计算投篮人识别率
            let playerRecognitionPercentage = stats.total > 0 ? Double(stats.recognizedPlayers) / Double(stats.total) * 100 : 0
            
            // 设置命中率信息
            cell.madeLabel.text = "\(stats.made)命中/\(stats.total)投篮"
            cell.percentLabel.text = String(format: "%.1f%%", madePercentage)
            
            // 设置投篮人识别率信息
            cell.madeLabel2.text = "\(stats.recognizedPlayers)投篮人/\(stats.total)投篮"
            cell.percentLabel2.text = String(format: "%.1f%%", playerRecognitionPercentage)
        }
        
        // 设置查看按钮点击事件
        cell.viewButtonTapped = { [weak self] in
            self?.showSessionDetail(session: session)
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 60 // 设置cell高度
    }
    
    // 查看会话详情
    private func showSessionDetail(session: String) {
        // 创建并展示会话详情页面
        let detailVC = SessionDetailViewController(session: session)
        detailVC.modalPresentationStyle = .fullScreen
        present(detailVC, animated: true)
    }
}

// MARK: - 自定义TableViewCell
class RecordTableViewCell: UITableViewCell {
    
    static let identifier = "RecordTableViewCell"
    
    // 回调处理查看按钮点击
    var viewButtonTapped: (() -> Void)?
    
    // UI组件
    let containerView = UIView()
    let dateLabel = UILabel()
    let madeLabel = UILabel()
    let percentLabel = UILabel()
    let madeLabel2 = UILabel()
    let percentLabel2 = UILabel()
    let viewButton = UIButton()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // 容器视图
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 10
        contentView.addSubview(containerView)
        
        // 日期标签
        dateLabel.font = .systemFont(ofSize: 16)
        dateLabel.textColor = .black
        containerView.addSubview(dateLabel)
        
        // 命中标签1
        madeLabel.font = .systemFont(ofSize: 14)
        madeLabel.textColor = .darkGray
        containerView.addSubview(madeLabel)
        
        // 百分比标签1
        percentLabel.font = .systemFont(ofSize: 14)
        percentLabel.textColor = .darkGray
        containerView.addSubview(percentLabel)
        
        // 命中标签2
        madeLabel2.font = .systemFont(ofSize: 14)
        madeLabel2.textColor = .darkGray
        containerView.addSubview(madeLabel2)
        
        // 百分比标签2
        percentLabel2.font = .systemFont(ofSize: 14)
        percentLabel2.textColor = .darkGray
        containerView.addSubview(percentLabel2)
        
        // 查看按钮
        viewButton.setTitle("查看", for: .normal)
        viewButton.backgroundColor = .systemGray5
        viewButton.setTitleColor(.black, for: .normal)
        viewButton.titleLabel?.font = .systemFont(ofSize: 14)
        viewButton.layer.cornerRadius = 5
        viewButton.addTarget(self, action: #selector(viewButtonAction), for: .touchUpInside)
        containerView.addSubview(viewButton)
        
        // 设置约束
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 5, left: 0, bottom: 5, right: 0))
        }
        
        dateLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(20)
            make.width.equalTo(100)
        }
        
        madeLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(dateLabel.snp.right).offset(30)
            make.width.equalTo(120)
        }
        
        percentLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(madeLabel.snp.right).offset(10)
            make.width.equalTo(60)
        }
        
        madeLabel2.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(percentLabel.snp.right).offset(30)
            make.width.equalTo(120)
        }
        
        percentLabel2.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(madeLabel2.snp.right).offset(10)
            make.width.equalTo(60)
        }
        
        viewButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(60)
            make.height.equalTo(30)
        }
    }
    
    @objc private func viewButtonAction() {
        viewButtonTapped?()
    }
} 