import UIKit

class MyCustomViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.view.backgroundColor = UIColor.white
        
        let label = UILabel()
        label.text = "Hello from iOS"
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        self.view.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: self.view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: self.view.centerYAnchor)
        ])
        
        // 添加一个按钮用于返回
        let backButton = UIButton(type: .system)
        backButton.setTitle("Back", for: .normal)
        backButton.translatesAutoresizingMaskIntoConstraints = false
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        self.view.addSubview(backButton)
        
        NSLayoutConstraint.activate([
            backButton.bottomAnchor.constraint(equalTo: self.view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            backButton.centerXAnchor.constraint(equalTo: self.view.centerXAnchor)
        ])
        // 创建并配置按钮
        let backButton2 = UIButton(type: .system)
        backButton2.setTitle("跳转摄像头", for: .normal) // 按钮标题
        backButton2.translatesAutoresizingMaskIntoConstraints = false // 关闭自动转换遮罩布局
        backButton2.addTarget(self, action: #selector(backButtonTapped2), for: .touchUpInside) // 添加点击事件
        self.view.addSubview(backButton2)
        
        // 使用Auto Layout来设置按钮的位置
        NSLayoutConstraint.activate([
            backButton2.centerXAnchor.constraint(equalTo: self.view.centerXAnchor), // 居中对齐X轴
            backButton2.centerYAnchor.constraint(equalTo: self.view.centerYAnchor) // 居中对齐Y轴
        ])
    }
    
    @objc func backButtonTapped() {
        // 关闭当前视图控制器，返回Flutter页面
        self.dismiss(animated: true, completion: nil)
    }
        @objc func backButtonTapped2() {
        // 关闭当前视图控制器，返回Flutter页面
            let vc = CameraViewController()
            vc.modalPresentationStyle = .fullScreen // 强制全屏
            self.present(vc, animated: true, completion: nil)
    }
}