//
//  LowBatteryWarningVC.swift
//  Runner
//
//  Created by NickTang on 2025/5/13.
//
/// LowBatteryWarningDelegate代理协议
protocol LowBatteryWarningDelegate: NSObjectProtocol {
    /// 关闭拍摄页面
    func didClosePage()
}
class LowBatteryWarningVC: UIViewController {
    static var showedLowBattery:Bool = false
    private let batteryLevel: Float
    weak var delegate: LowBatteryWarningDelegate?
    lazy var closeBtn:UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "close_btn"), for: .normal)
        view.addSubview(btn)
        btn.snp.makeConstraints { make in
            make.trailing.equalTo(-20)
            make.top.equalTo(20)
            make.width.height.equalTo(45)
        }
        return btn
    }()
    lazy var knownBtn:UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("知道了", for: .normal)
        btn.backgroundColor = .white
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        btn.layer.cornerRadius = 25
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(knownAction), for: .touchUpInside)
        view.addSubview(btn)
        btn.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
            make.top.equalTo(lowBatteryImageV.snp.bottom).offset(14)
        }
        return btn
    }()
    lazy var lowBatteryImageV:UIImageView = {
        let imgV = UIImageView()
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(tipsLabel.snp.bottom).offset(13)
            make.width.height.equalTo(100)
        }
        return imgV
    }()
    lazy var tipsLabel:UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.text = "手机电量不足，请及时连接电源，避免后续拍摄片段的丢失"
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(86)
        }
        return label
    }()
    init(batteryLevel: Float) {
        self.batteryLevel = batteryLevel
        super.init(nibName: nil, bundle: nil)
        modalPresentationStyle = .overFullScreen
        modalTransitionStyle = .crossDissolve
    }
    @objc func knownAction() {
        dismiss(animated: false)
        LowBatteryWarningVC.showedLowBattery = true
    }
    @objc func closePage() {
        dismiss(animated: false) { [weak self] in
            self?.delegate?.didClosePage()
        }
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        tipsLabel.textColor = .white
        lowBatteryImageV.image = UIImage(named: "lowBattery")
        knownBtn.setTitleColor(UIColor(hex: "#22222D"), for: .normal)
        closeBtn.addTarget(self, action: #selector(closePage), for: .touchUpInside)
    }
}


class BatteryMonitor {
    static let shared = BatteryMonitor()
    
    private init() {
        // 开启电池监控
        UIDevice.current.isBatteryMonitoringEnabled = true
    }
    
    func startMonitoring(threshold: Float = 0.2,
                        warningHandler: @escaping (Float) -> Void) {
        // 添加通知观察者
        NotificationCenter.default.addObserver(
            forName: UIDevice.batteryLevelDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            let batteryLevel = UIDevice.current.batteryLevel
            if batteryLevel <= threshold && batteryLevel > 0 {
                warningHandler(batteryLevel)
            }
        }
        
        // 立即检查一次
        let currentLevel = UIDevice.current.batteryLevel
        if currentLevel <= threshold && currentLevel > 0 {
            warningHandler(currentLevel)
        }
    }
    
    func stopMonitoring() {
        NotificationCenter.default.removeObserver(
            self,
            name: UIDevice.batteryLevelDidChangeNotification,
            object: nil
        )
    }
    
    deinit {
        stopMonitoring()
    }
}
