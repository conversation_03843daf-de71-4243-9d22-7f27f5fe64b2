buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

rootProject.buildDir = "../build"

//This code is a Gradle configuration script used in Android projects. The subprojects block allows you to configure certain settings for all subprojects (modules) in the project.
// The android block inside defines Android-related configuration settings, and the compileSdkVersion 34 line allows projects to be compiled with Android API 34.
subprojects { afterEvaluate { android { compileSdkVersion 34 } } }
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
