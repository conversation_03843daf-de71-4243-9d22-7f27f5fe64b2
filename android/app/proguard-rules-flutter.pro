# Flutter 核心保留规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.embedding.** { *; }

# 保留所有原生方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留资源类
-keepclassmembers class **.R$* {
    public static <fields>;
}

# 保留Parceable实现
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# MediaPipe 保留规则
-keep class com.google.mediapipe.** { *; }
-keep class com.google.mediapipe.framework.** { *; }
-keep class com.google.mediapipe.tasks.** { *; }

# 高德地图保留规则
-keep class com.amap.api.** {*;}
-keep class com.autonavi.** {*;}

# RxFFmpeg 保留规则
-keep class com.github.microshow.** { *; }
-dontwarn io.microshow.rxffmpeg.**
-keep class io.microshow.rxffmpeg.**{*;}


# 高德地图混淆规则
-keep class com.amap.api.maps.** { *; }
-keep class com.autonavi.** { *; }
-keep class com.amap.api.trace.** { *; }
-keep class com.amap.api.location.** { *; }
-keep class com.amap.api.fence.** { *; }
-keep class com.autonavi.aps.amapapi.model.** { *; }
-keep class com.amap.api.services.** { *; }

# 3D地图
-keep class com.amap.api.maps.model.** { *; }
-keep class com.amap.api.maps.model.overlay.** { *; }

# 搜索
-keep class com.amap.api.services.core.** { *; }
-keep class com.amap.api.services.route.** { *; }
-keep class com.amap.api.services.poisearch.** { *; }

# 定位
-keep class com.amap.api.location.** { *; }
-keep class com.aps.** { *; }