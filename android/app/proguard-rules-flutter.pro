# Flutter 核心保留规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.embedding.** { *; }

# 保留所有原生方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留资源类
-keepclassmembers class **.R$* {
    public static <fields>;
}

# 保留Parceable实现
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# MediaPipe 保留规则
-keep class com.google.mediapipe.** { *; }
-keep class com.google.mediapipe.framework.** { *; }
-keep class com.google.mediapipe.tasks.** { *; }

# 高德地图保留规则
-keep class com.amap.api.** {*;}
-keep class com.autonavi.** {*;}

# RxFFmpeg 保留规则
-keep class com.github.microshow.** { *; }