package com.shootZ.app.shoot_z.interfaces

import com.shootZ.app.shoot_z.model.ClassifierResultBundle
import com.shootZ.app.shoot_z.service.ShootClassifierEvent

/**
 * 投篮分类器服务代理接口
 * 对应 Swift: protocol ShootClassifierServiceLiveStreamDelegate
 */
interface ShootClassifierServiceDelegate {
        /**
         * 当检测到投篮事件时调用
         * 对应 Swift: func shootClassifierService(_:didDetectShootEvent:)
         */
        fun onShootEventDetected(event: ShootClassifierEvent)

        /**
         * 当完成分类时调用（可选，用于调试）
         * 对应 Swift: func shootClassifierService(_:didFinishClassification:error:)
         */
        fun onClassificationFinished(resultBundle: ClassifierResultBundle?, error: Exception?)
}