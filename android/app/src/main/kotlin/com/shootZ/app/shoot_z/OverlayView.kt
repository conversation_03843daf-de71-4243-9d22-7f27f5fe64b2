/*
 * Copyright 2022 The TensorFlow Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.shootZ.app.shoot_z

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.objectdetector.ObjectDetectorResult
import kotlin.math.max
import kotlin.math.min

/**
 * OverlayView - 在相机预览或静态图像上绘制检测结果的视图组件
 *
 * 主要功能：
 * 1. 根据检测结果绘制边界框
 * 2. 显示检测对象的类别和置信度
 * 3. 处理不同显示模式下的坐标转换和缩放
 */
class OverlayView(context: Context?, attrs: AttributeSet?) :
    View(context, attrs) {

    // 存储当前的检测结果
    private var results: ObjectDetectorResult? = null

    // 绘制边界框的画笔
    private var boxPaint = Paint()

    // 绘制文本背景的画笔
    private var textBackgroundPaint = Paint()

    // 绘制文本的画笔
    private var textPaint = Paint()

    // 视图缩放因子（适配不同分辨率）
    private var scaleFactor: Float = 1f

    // 文本边界计算临时存储
    private var bounds = Rect()

    // 原始图像宽度（相机输出）
    private var outputWidth = 0

    // 原始图像高度（相机输出）
    private var outputHeight = 0

    // 原始图像旋转角度
    private var outputRotate = 0

    // 当前运行模式（影响缩放逻辑）
    private var runningMode: RunningMode = RunningMode.IMAGE

    // 初始化方法
    init {
        // 初始化绘制工具
        initPaints()
    }

    /**
     * 清空视图上的绘制内容
     */
    fun clear() {
        results = null // 清除检测结果
        textPaint.reset() // 重置文本画笔
        textBackgroundPaint.reset() // 重置文本背景画笔
        boxPaint.reset() // 重置边界框画笔
        invalidate() // 触发视图重绘
        initPaints() // 重新初始化画笔
    }

    /**
     * 设置运行模式（影响缩放计算）
     *
     * @param runningMode 运行模式（IMAGE/VIDEO/LIVE_STREAM）
     */
    fun setRunningMode(runningMode: RunningMode) {
        this.runningMode = runningMode
    }

    /**
     * 初始化各种绘制画笔的属性
     */
    private fun initPaints() {
        // 文本背景画笔设置（黑色）
        textBackgroundPaint.color = Color.BLACK
        textBackgroundPaint.style = Paint.Style.FILL // 填充模式
        textBackgroundPaint.textSize = 50f // 字体大小

        // 文本画笔设置（白色）
        textPaint.color = Color.WHITE
        textPaint.style = Paint.Style.FILL // 填充模式
        textPaint.textSize = 50f // 字体大小

        // 边界框画笔设置（使用主题颜色）
        boxPaint.color = ContextCompat.getColor(context!!, R.color.mp_primary)
        boxPaint.strokeWidth = 8F // 线宽
        boxPaint.style = Paint.Style.STROKE // 描边模式
    }

    /**
     * 自定义视图的绘制方法
     *
     * @param canvas 绘制画布
     */
    override fun draw(canvas: Canvas) {
        super.draw(canvas) // 调用父类绘制方法

        // 遍历所有检测结果并绘制
        results?.detections()?.map { detection ->
            // 获取边界框矩形
            val boxRect = RectF(
                detection.boundingBox().left,
                detection.boundingBox().top,
                detection.boundingBox().right,
                detection.boundingBox().bottom
            )

            // 创建转换矩阵
            val matrix = Matrix()
            // 先将矩形移动到原点(0,0)位置
            matrix.postTranslate(-outputWidth / 2f, -outputHeight / 2f)

            // 根据图像旋转角度旋转矩形
            matrix.postRotate(outputRotate.toFloat())

            // 处理90度或270度旋转的特殊情况
            // 旋转后图像会翻转，需要调整平移值
            if (outputRotate == 90 || outputRotate == 270) {
                matrix.postTranslate(outputHeight / 2f, outputWidth / 2f)
            } else {
                matrix.postTranslate(outputWidth / 2f, outputHeight / 2f)
            }

            // 应用矩阵变换到边界框
            matrix.mapRect(boxRect)
            boxRect
        }?.forEachIndexed { index, transformedBoxRect ->

            // 应用缩放因子到变换后的矩形坐标
            val top = transformedBoxRect.top * scaleFactor
            val bottom = transformedBoxRect.bottom * scaleFactor
            val left = transformedBoxRect.left * scaleFactor
            val right = transformedBoxRect.right * scaleFactor

            // 1. 绘制检测对象的边界框
            val drawableRect = RectF(left, top, right, bottom)
            canvas.drawRect(drawableRect, boxPaint)

            // 2. 准备显示文本（类别名称+置信度）
            val category = results?.detections()!![index].categories()[0]
            val drawableText =
                category.categoryName() + " " + String.format(
                    "%.2f",
                    category.score()
                )

            // 3. 计算文本尺寸（用于绘制背景）
            textBackgroundPaint.getTextBounds(
                drawableText,
                0,
                drawableText.length,
                bounds
            )
            val textWidth = bounds.width()
            val textHeight = bounds.height()

            // 4. 在边界框顶部绘制文本背景矩形
            canvas.drawRect(
                left,
                top,
                left + textWidth + BOUNDING_RECT_TEXT_PADDING,
                top + textHeight + BOUNDING_RECT_TEXT_PADDING,
                textBackgroundPaint
            )

            // 5. 在背景上绘制文本
            canvas.drawText(
                drawableText,
                left,
                top + bounds.height(),
                textPaint
            )
        }
    }

    /**
     * 设置检测结果并准备绘制
     *
     * @param detectionResults 检测结果对象
     * @param outputHeight 原始图像高度
     * @param outputWidth 原始图像宽度
     * @param imageRotation 图像旋转角度
     */
    fun setResults(
        detectionResults: ObjectDetectorResult,
        outputHeight: Int,
        outputWidth: Int,
        imageRotation: Int
    ) {
        // 存储结果和参数
        results = detectionResults
        this.outputWidth = outputWidth
        this.outputHeight = outputHeight
        this.outputRotate = imageRotation

        // 计算旋转后的宽高对
        val rotatedWidthHeight = when (imageRotation) {
            0, 180 -> Pair(outputWidth, outputHeight) // 0度或180度旋转保持宽高不变
            90, 270 -> Pair(outputHeight, outputWidth) // 90或270度旋转宽高互换
            else -> return // 不支持其他角度
        }

        // 计算缩放因子（适配不同视图模式）
        scaleFactor = when (runningMode) {
            // 图像和视频模式：缩放以适应视图
            RunningMode.IMAGE,
            RunningMode.VIDEO -> {
                min(
                    width * 1f / rotatedWidthHeight.first, // 视图宽度/图像宽度
                    height * 1f / rotatedWidthHeight.second // 视图高度/图像高度
                )
            }

            // 实时流模式：放大以填充视图
            RunningMode.LIVE_STREAM -> {
                max(
                    width * 1f / rotatedWidthHeight.first, // 选择最大的缩放因子
                    height * 1f / rotatedWidthHeight.second
                )
            }
        }

        // 触发视图重绘（显示新结果）
        invalidate()
    }

    // 伴生对象 - 定义常量
    companion object {
        // 文本与边界框的间距常量
        private const val BOUNDING_RECT_TEXT_PADDING = 8
    }
}