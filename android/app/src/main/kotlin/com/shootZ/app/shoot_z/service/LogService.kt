package com.shootZ.app.shoot_z.service

import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 日志级别枚举
 */
enum class LogLevel {
    DEBUG,
    INFO,
    WARNING,
    ERROR;

    val description: String
        get() = when (this) {
            DEBUG -> "调试"
            INFO -> "信息"
            WARNING -> "警告"
            ERROR -> "错误"
        }
}

/**
 * 日志服务代理接口
 */
interface LogServiceDelegate {
    /**
     * 当有新日志时调用
     *
     * @param message 日志消息
     * @param level 日志级别
     * @param timestamp 时间戳
     */
    fun onLogGenerated(message: String, level: LogLevel, timestamp: Date)
}

/**
 * 日志条目模型
 */
data class LogEntry(
    val message: String,
    val level: LogLevel,
    val timestamp: Date
) {
    /**
     * 格式化时间戳
     */
    val formattedTimestamp: String
        get() {
            val formatter = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
            return formatter.format(timestamp)
        }

    /**
     * 格式化日志消息
     */
    val formattedMessage: String
        get() = "[${level.description}] $message"
}

/**
 * 日志服务（单例实现）
 */
object LogService {  // 使用 object 关键字创建单例

    // MARK: - 单例属性

    /**
     * 代理集合（使用线程安全的集合）
     */
    private val delegates = mutableSetOf<LogServiceDelegate>()

    /**
     * 日志历史（线程安全列表）
     */
    private val logHistory = CopyOnWriteArrayList<LogEntry>()

    /**
     * 线程安全锁
     */
    private val lock = ReentrantLock()

    /**
     * 最大日志条目数量
     */
    private const val MAX_LOG_ENTRIES = 1000

    /**
     * 是否打印到控制台
     */
    var printToConsole = true

    // MARK: - 公共方法

    /**
     * 添加代理
     *
     * @param delegate 日志服务代理
     */
    fun addDelegate(delegate: LogServiceDelegate) {
        lock.withLock {
            delegates.add(delegate)
        }
    }

    /**
     * 移除代理
     *
     * @param delegate 日志服务代理
     */
    fun removeDelegate(delegate: LogServiceDelegate) {
        lock.withLock {
            delegates.remove(delegate)
        }
    }

    /**
     * 记录调试级别日志
     *
     * @param message 日志消息
     */
    fun debug(message: String) {
        log(message, LogLevel.DEBUG)
    }

    /**
     * 记录信息级别日志
     *
     * @param message 日志消息
     */
    fun info(message: String) {
        log(message, LogLevel.INFO)
    }

    /**
     * 记录警告级别日志
     *
     * @param message 日志消息
     */
    fun warning(message: String) {
        log(message, LogLevel.WARNING)
    }

    /**
     * 记录错误级别日志
     *
     * @param message 日志消息
     */
    fun error(message: String) {
        log(message, LogLevel.ERROR)
    }

    /**
     * 记录日志
     *
     * @param message 日志消息
     * @param level 日志级别
     */
    fun log(message: String, level: LogLevel) {
        val timestamp = Date()
        val entry = LogEntry(message, level, timestamp)

        // 添加到历史记录（线程安全）
        lock.withLock {
            logHistory.add(entry)

            // 如果超过最大数量，删除最旧的
            if (logHistory.size > MAX_LOG_ENTRIES) {
                logHistory.removeFirst()
            }
        }

        // 打印到控制台
        if (printToConsole) {
            println("${entry.formattedTimestamp} ${entry.formattedMessage}")
        }

        // 通知所有代理（先复制集合避免并发修改）
        val safeDelegates = lock.withLock {
            delegates.toSet()  // 创建副本
        }

        safeDelegates.forEach { delegate ->
            try {
                delegate.onLogGenerated(message, level, timestamp)
            } catch (e: Exception) {
                // 防止代理回调异常影响整个日志系统
                println("LogService delegate error: ${e.message}")
            }
        }
    }

    /**
     * 获取日志历史（只读视图）
     */
    val history: List<LogEntry>
        get() = lock.withLock {
            logHistory.toList()  // 返回不可修改的副本
        }

    /**
     * 清除所有日志
     */
    fun clearLogs() {
        lock.withLock {
            logHistory.clear()
        }
    }

    // 其他可能需要的方法...

    /**
     * 获取单例实例（Kotlin object 不需要此方法）
     *
     * 在 Kotlin 中，可以直接通过类名访问：
     * LogService.debug("message")
     */
    // 不需要手动创建实例的方法
}