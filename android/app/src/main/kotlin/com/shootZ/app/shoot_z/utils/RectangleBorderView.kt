package com.shootZ.app.shoot_z.utils

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View

class RectangleBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 矩形对象
    private val rect = RectF()

    // 边框画笔
    private val borderPaint = Paint().apply {
        color = Color.BLUE
        style = Paint.Style.STROKE
        strokeWidth = 8f
        isAntiAlias = true
    }

    // 背景图片画笔
    private val backgroundPaint = Paint().apply {
        isAntiAlias = true
    }

    // 角半径（圆角矩形）
    private var cornerRadius = 0f

    // 背景图片
    private var backgroundBitmap: Bitmap? = null
    private var bitmapShader: BitmapShader? = null
    private var bitmapRect = RectF()

    // 图片缩放模式
    private var scaleType = ScaleType.CENTER_CROP

    // 设置矩形位置和大小
    fun setRect(left: Float, top: Float, right: Float, bottom: Float) {
        rect.set(left, top, right, bottom)
        updateBitmapShader()
        invalidate() // 触发重绘
    }

    // 设置边框颜色
    fun setBorderColor(color: Int) {
        borderPaint.color = color
        invalidate()
    }

    // 设置边框宽度
    fun setBorderWidth(width: Float) {
        borderPaint.strokeWidth = width
        invalidate()
    }

    // 设置圆角半径
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }

    // 设置背景图片
    fun setBackgroundBitmap(bitmap: Bitmap?) {
        backgroundBitmap = bitmap
        updateBitmapShader()
        invalidate()
    }

    // 设置图片缩放模式
    fun setScaleType(scaleType: ScaleType) {
        this.scaleType = scaleType
        updateBitmapShader()
        invalidate()
    }

    // 更新位图着色器
    private fun updateBitmapShader() {
        backgroundBitmap?.let { bitmap ->
            bitmapShader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)

            // 根据缩放模式设置矩阵
            val matrix = Matrix()
            val bitmapWidth = bitmap.width.toFloat()
            val bitmapHeight = bitmap.height.toFloat()
            val viewWidth = rect.width()
            val viewHeight = rect.height()

            when (scaleType) {
                ScaleType.CENTER_CROP -> {
                    val scale = maxOf(viewWidth / bitmapWidth, viewHeight / bitmapHeight)
                    matrix.setScale(scale, scale)
                    matrix.postTranslate(
                        rect.left + (viewWidth - bitmapWidth * scale) / 2,
                        rect.top + (viewHeight - bitmapHeight * scale) / 2
                    )
                }
                ScaleType.CENTER_INSIDE -> {
                    val scale = minOf(viewWidth / bitmapWidth, viewHeight / bitmapHeight)
                    matrix.setScale(scale, scale)
                    matrix.postTranslate(
                        rect.left + (viewWidth - bitmapWidth * scale) / 2,
                        rect.top + (viewHeight - bitmapHeight * scale) / 2
                    )
                }
                ScaleType.FIT_XY -> {
                    matrix.setRectToRect(
                        RectF(0f, 0f, bitmapWidth, bitmapHeight),
                        rect,
                        Matrix.ScaleToFit.FILL
                    )
                }
                ScaleType.FIT_CENTER -> {
                    matrix.setRectToRect(
                        RectF(0f, 0f, bitmapWidth, bitmapHeight),
                        rect,
                        Matrix.ScaleToFit.CENTER
                    )
                }
            }

            bitmapShader?.setLocalMatrix(matrix)
            backgroundPaint.shader = bitmapShader
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 默认使用整个视图区域
        rect.set(0f, 0f, w.toFloat(), h.toFloat())
        updateBitmapShader()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制背景图片
        backgroundBitmap?.let {
            if (cornerRadius > 0) {
                // 绘制圆角矩形背景
                canvas.drawRoundRect(rect, cornerRadius, cornerRadius, backgroundPaint)
            } else {
                // 绘制直角矩形背景
                canvas.drawRect(rect, backgroundPaint)
            }
        }

        // 绘制边框
        if (cornerRadius > 0) {
            // 绘制圆角矩形边框
            canvas.drawRoundRect(rect, cornerRadius, cornerRadius, borderPaint)
        } else {
            // 绘制直角矩形边框
            canvas.drawRect(rect, borderPaint)
        }
    }

    // 图片缩放模式枚举
    enum class ScaleType {
        CENTER_CROP,    // 居中裁剪
        CENTER_INSIDE,  // 居中显示（不裁剪）
        FIT_XY,         // 拉伸填充
        FIT_CENTER      // 保持比例居中显示
    }
}
//
//import android.content.Context
//import android.graphics.*
//import android.util.AttributeSet
//import android.view.View
//
//class RectangleBorderView @JvmOverloads constructor(
//    context: Context,
//    attrs: AttributeSet? = null,
//    defStyleAttr: Int = 0
//) : View(context, attrs, defStyleAttr) {
//
//    // 矩形对象
//    private val rect = RectF()
//
//    // 边框画笔
//    private val borderPaint = Paint().apply {
//        color = Color.BLUE
//        style = Paint.Style.STROKE
//        strokeWidth = 8f
//        isAntiAlias = true
//    }
//
//    // 背景画笔（可选）
//    private val backgroundPaint = Paint().apply {
//        color = Color.argb(30, 0, 0, 255) // 半透明蓝色
//        style = Paint.Style.FILL
//    }
//
//    // 角半径（圆角矩形）
//    private var cornerRadius = 0f
//
//    // 设置矩形位置和大小
//    fun setRect(left: Float, top: Float, right: Float, bottom: Float) {
//        rect.set(left, top, right, bottom)
//        invalidate() // 触发重绘
//    }
//
//    // 设置边框颜色
//    fun setBorderColor(color: Int) {
//        borderPaint.color = color
//        invalidate()
//    }
//
//    // 设置边框宽度
//    fun setBorderWidth(width: Float) {
//        borderPaint.strokeWidth = width
//        invalidate()
//    }
//
//    // 设置圆角半径
//    fun setCornerRadius(radius: Float) {
//        cornerRadius = radius
//        invalidate()
//    }
//
//    override fun onDraw(canvas: Canvas) {
//        super.onDraw(canvas)
//
////        // 绘制背景（可选）
////        if (backgroundPaint.color != Color.TRANSPARENT) {
////            if (cornerRadius > 0) {
////                // 绘制圆角矩形背景
////                canvas.drawRoundRect(rect, cornerRadius, cornerRadius, backgroundPaint)
////            } else {
////                // 绘制直角矩形背景
////                canvas.drawRect(rect, backgroundPaint)
////            }
////        }
//
//        // 绘制边框
//        if (cornerRadius > 0) {
//            // 绘制圆角矩形边框
//            canvas.drawRoundRect(rect, cornerRadius, cornerRadius, borderPaint)
//        } else {
//            // 绘制直角矩形边框
//            canvas.drawRect(rect, borderPaint)
//        }
//    }
//}