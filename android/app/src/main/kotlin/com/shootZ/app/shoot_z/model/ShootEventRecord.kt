package com.shootZ.app.shoot_z.model

///// 投篮事件信息（对应Swift的ShootEvent）
//data class ShootEventRecord(
//    val id: Long = 0,                   // 记录ID
//    val start_time: String,               // 投篮会话开始时间
//    val shoot_time: String?,              // 投篮时间（秒）
//    val is_goal: Bo<PERSON>an,                 // 是否进球
//    val goal_time: String?,               // 进球时间（秒）
//    val player_image_path: String?,        // 投篮人图像存储路径
//    var file_path: String?,        // 投篮人图像存储路径
//    val player_confidence: Double,        // 投篮人置信度 (0.0-1.0)
//  //  val playerVideoPath:  String?,
//    val player_image_uploaded: Boolean,               // 投篮会话开始时间
//    val file_path_uploaded: Boolean,               // 投篮会话开始时间
//    // 投篮会话开始时间
//    val training_id: String,               // 投篮会话开始时间
//    val created_at:String          // 记录创建时间
//
//    //    {"player_image_uploaded":false,"player_confidence":0,"file_path":
//    //"file:\/\/\/private\/var\/mobile\/Containers\/Data\/Application\/F9F6A010-24E4-44FC-A2A8-212C0E1C9F
//    //BB\/tmp\/cache_20250515_144850.mp4","training_id":"5","start_time":"2025-05-15 14:44:01",
//    //"is_goal":true,"goal_time":1747291729.2113008,"file_path_uploaded":false,
//    //"created_at":768984531.00349796}
//    /**
//     * 将对象转换为JSON字符串
//     */
//    fun toJson(): String {
//    val gson = GsonBuilder()
//        .registerTypeAdapter(Date::class.java, object : JsonSerializer<Date> {
//            override fun serialize(
//                src: Date,
//                typeOfSrc: Type,
//                context: JsonSerializationContext
//            ): JsonElement {
//                return JsonPrimitive(src.time / 1000.0)
//            }
//        })
//        .create()
//
//    return gson.toJson(this)
//}
//)

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

data class ShootEventRecord(
    @SerializedName("id")
    val id: Long = 0,                   // 记录ID

    @SerializedName("start_time")
    val startTime: String,              // 投篮会话开始时间

    @SerializedName("shoot_time")
    val shootTime: Double?,             // 投篮时间（秒）

    @SerializedName("is_goal")
    val isGoal: Boolean,                // 是否进球

    @SerializedName("goal_time")
    val goalTime: Double?,              // 进球时间（秒）

    @SerializedName("player_image_path")
    val playerImagePath: String?,      // 投篮人图像存储路径

    @SerializedName("file_path")
    var filePath: String?,              // 投篮人图像存储路径

    @SerializedName("player_confidence")
    val playerConfidence: Double,       // 投篮人置信度 (0.0-1.0)

    @SerializedName("player_image_uploaded")
    val playerImageUploaded: Boolean,   // 投篮人图像是否已上传

    @SerializedName("file_path_uploaded")
    val filePathUploaded: Boolean,      // 视频文件是否已上传

    @SerializedName("training_id")
    val trainingId: String,             // 训练ID

    @SerializedName("created_at")
    val createdAt: Double               // 记录创建时间
) {
    /**
     * 将对象转换为JSON字符串
     */
    fun toJson(): String {
        return Gson().toJson(this)
    }

    /**
     * 从JSON字符串创建对象
     */
    companion object {
        fun fromJson(json: String): ShootEventRecord {
            return Gson().fromJson(json, ShootEventRecord::class.java)
        }
    }
}

//double? playerConfidence;
//String? filePath;
//String? startTime;
//double? shootTime;
//String? trainingId;
//String? eventId;
//bool? isGoal;
//String? playerImagePath;
//double? goalTime;
//List<double?>? shootCoord;
//String? imgLoadOK;
//String? videoLoadOK;
//double? createdAt;