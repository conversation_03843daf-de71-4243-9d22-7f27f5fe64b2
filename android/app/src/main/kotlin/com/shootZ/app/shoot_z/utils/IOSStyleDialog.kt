package com.shootZ.app.shoot_z.utils

import android.app.Dialog
import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import com.shootZ.app.shoot_z.R

class IOSStyleDialog(
    context: Context,
    private val title: String?,
    private val message: String?,
    private val actions: List<Action>,
    private val customView: View? = null
) : Dialog(context) {

    data class Action(
        val title: String,
        val color: Int = Color.BLUE,
        val handler: () -> Unit
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.setGravity(Gravity.CENTER)
        setContentView(createDialogLayout())
    }

    private fun createDialogLayout(): View {
        val isDarkTheme = isDarkTheme()
        val backgroundColor = if (isDarkTheme) Color.parseColor("#2C2C2E") else Color.WHITE
        val textColor = if (isDarkTheme) Color.WHITE else Color.BLACK
        val secondaryTextColor = if (isDarkTheme) Color.LTGRAY else Color.DKGRAY
        val dividerColor = if (isDarkTheme) Color.parseColor("#3A3A3C") else Color.LTGRAY

        val rootLayout = LinearLayout(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            orientation = LinearLayout.VERTICAL
            background = context.getDrawable(R.drawable.dialog_background) // 圆角背景
            setBackgroundColor(backgroundColor)
            elevation = 10f
        }

        // 标题
        title?.let {
            val titleView = TextView(context).apply {
                text = it
                textSize = 18f
                gravity = Gravity.CENTER
                setPadding(16, 24, 16, 8)
                setTextColor(textColor)
            }
            rootLayout.addView(titleView)
        }

        // 消息
        message?.let {
            val messageView = TextView(context).apply {
                text = it
                textSize = 16f
                gravity = Gravity.CENTER
                setPadding(16, 8, 16, 24)
                setTextColor(secondaryTextColor)
            }
            rootLayout.addView(messageView)
        }

        // 自定义视图
        customView?.let {
            rootLayout.addView(it)

            // 添加分隔线
            val divider = View(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    1
                ).apply {
                    setMargins(0, 16, 0, 16)
                }
                setBackgroundColor(dividerColor)
            }
            rootLayout.addView(divider)
        }

        // 按钮容器
        val buttonContainer = LinearLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            orientation = if (actions.size > 2) LinearLayout.VERTICAL else LinearLayout.HORIZONTAL
        }

        actions.forEachIndexed { index, action ->
            // 在按钮之间添加分隔线（除了第一个按钮）
            if (index > 0) {
                val divider = View(context).apply {
                    layoutParams = if (actions.size > 2) {
                        LinearLayout.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            1
                        )
                    } else {
                        LinearLayout.LayoutParams(
                            1,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )
                    }
                    setBackgroundColor(dividerColor)
                }
                buttonContainer.addView(divider)
            }

            // 添加按钮
            val button = Button(context).apply {
                layoutParams = if (actions.size > 2) {
                    LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    ).apply {
                        height = context.resources.getDimensionPixelSize(R.dimen.dialog_button_height)
                    }
                } else {
                    LinearLayout.LayoutParams(
                        0,
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        1f
                    ).apply {
                        height = context.resources.getDimensionPixelSize(R.dimen.dialog_button_height)
                    }
                }

                text = action.title
                setTextColor(action.color)
                background = null
                setOnClickListener {
                    action.handler.invoke()
                    dismiss()
                }
            }
            buttonContainer.addView(button)
        }

        rootLayout.addView(buttonContainer)

        return rootLayout
    }

    override fun show() {
        super.show()
        // 设置对话框大小
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.4).toInt(),
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 添加动画
        window?.setWindowAnimations(R.style.DialogAnimation)
    }

    private fun isDarkTheme(): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and
                Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }
}