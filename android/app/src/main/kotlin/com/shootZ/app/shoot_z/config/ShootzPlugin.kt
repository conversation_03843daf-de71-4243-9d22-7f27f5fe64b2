package com.shootZ.app.shoot_z.config

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.NonNull
import com.shootZ.app.shoot_z.CarmerActivity
import com.shootZ.app.shoot_z.MainApplication
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.Result
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class ShootzPlugin : FlutterPlugin, MethodChannel.MethodCallHandler, ActivityAware, EventChannel.StreamHandler {

    // 通道名称常量
    companion object {
        private const val ORIENTATION_CHANNEL = "com.shootz.orientation"
        private const val NATIVE_METHOD_CHANNEL = "com.example.my_flutter_app/native_method"
        private const val EVENT_CHANNEL = "com.shootz.video_recorder_envent"
        fun getInstance(): ShootzPlugin {
            return MainApplication.getInstance().plugin
        }
    }
    // 获取 Application 实例
    private val application: MainApplication
        get() = MainApplication.getInstance()
    // 插件状态变量
    private lateinit var context: Context
    private lateinit var methodChannel: MethodChannel
    private lateinit var orientationChannel: MethodChannel
    private lateinit var eventChannel: EventChannel
    private var eventSink: EventChannel.EventSink? = null
    private var isLandscape = false
    private var activity: FlutterActivity? = null
    private val mainHandler = Handler(Looper.getMainLooper())
    private val executor: ExecutorService = Executors.newSingleThreadExecutor()

    // 相机相关状态
    private var fixedTrainingId: String? = null
    private var fixedSampleVideoUrl: String? = null

    // 事件通道状态管理
    private var eventChannelReady = false
    private val pendingEvents = mutableListOf<String>()
    private val eventLock = Any() // 同步锁

    // 在 CameraActivity 中使用
    fun sendDataFromCamera(data: String) {
        try {
            didReceiveData(data)
        } catch (e: Exception) {
            Log.e("ShootzPlugin", "Failed to send data", e)
        }
    }
    fun configure(flutterEngine: FlutterEngine, activity: FlutterActivity) {
        context = activity.applicationContext
        this.activity = activity

        // 初始化方法通道
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, NATIVE_METHOD_CHANNEL)
        methodChannel.setMethodCallHandler(this)

        // 初始化方向通道
        orientationChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, ORIENTATION_CHANNEL)
        orientationChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "setLandscape" -> {
                    isLandscape = true
                    activity.requestedOrientation = android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                    result.success(null)
                }
                "setPortrait" -> {
                    isLandscape = false
                    activity.requestedOrientation = android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }

        // 初始化事件通道
        eventChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, EVENT_CHANNEL)
        eventChannel.setStreamHandler(this)

        Log.e("halfShootingRecording", "事件通道已初始化 eventChannel=${eventChannel==null}")
    }

    // FlutterPlugin 生命周期方法
    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
        orientationChannel.setMethodCallHandler(null)
        eventChannel.setStreamHandler(null)
        executor.shutdown()
    }

    // ActivityAware 生命周期方法
    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        activity = binding.activity as? FlutterActivity
    }

    override fun onDetachedFromActivityForConfigChanges() {
        activity = null
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        activity = binding.activity as? FlutterActivity
    }

    override fun onDetachedFromActivity() {
        activity = null
    }

    // 方法通道调用处理
    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            "showNativeView" -> handleShowNativeView(call, result)
            "getRecordList" -> handleGetRecordList(call, result)
            "updateRecordStatus" -> handleUpdateRecordStatus(call, result)
            "uploadResult" -> handleUploadResult(call, result)
            "confirmEventChannelReady" -> handleConfirmEventChannelReady(call, result)
            else -> result.notImplemented()
        }
    }

    // 处理事件通道握手确认
    private fun handleConfirmEventChannelReady(call: MethodCall, result: Result) {
        Log.e("ShootzPlugin", "收到事件通道握手确认")
        synchronized(eventLock) {
            eventChannelReady = true
            sendPendingEvents()
        }
        result.success(true)
    }

    private fun handleShowNativeView(call: MethodCall, result: Result) {
        val args = call.arguments as? Map<*, *> ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing or invalid parameter",
            null
        )
        Log.e("halfShootingRecording","handleShowNativeView:${args}")
        fixedTrainingId = args["trainingId"] as? String
        fixedSampleVideoUrl = args["sampleVideoUrl"] as? String
        Log.e("halfShootingRecording","handleShowNativeView:${fixedTrainingId}-${fixedSampleVideoUrl}")
        if (fixedTrainingId == null || fixedSampleVideoUrl == null) {
            return result.error(
                "INVALID_ARGUMENTS",
                "Missing trainingId or sampleVideoUrl",
                null
            )
        }

        // 启动相机Activity
        activity?.let {
            val intent = android.content.Intent(it, CarmerActivity::class.java).apply {
                putExtra("trainingId", fixedTrainingId)
                putExtra("sampleUrl", fixedSampleVideoUrl)
            }
            it.startActivity(intent)
            result.success("Android view presented")
        } ?: run {
            result.error("NO_ACTIVITY", "No activity available", null)
        }
    }

    private fun handleGetRecordList(call: MethodCall, result: Result) {
        val args = call.arguments as? Map<*, *> ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing or invalid parameter",
            null
        )

        val trainingId = args["trainingId"] as? String ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing trainingId parameter",
            null
        )

        executor.execute {
            try {
                // 伪代码：实际应从数据库获取记录
                // val records = DatabaseManager.getShootEventList(trainingId)
                // val json = Gson().toJson(records)
                // mainHandler.post { result.success(json) }
            } catch (e: Exception) {
                mainHandler.post { result.error("DATABASE_ERROR", e.message, null) }
            }
        }
    }

    private fun handleUpdateRecordStatus(call: MethodCall, result: Result) {
        val args = call.arguments as? Map<*, *> ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing or invalid parameter",
            null
        )

        val recordId = args["id"] as? String ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing id parameter",
            null
        )

        executor.execute {
            try {
                // 伪代码：实际应从数据库更新状态
                // val event = DatabaseManager.getShootEventById(recordId)
                // ...
                mainHandler.post { result.success(null) }
            } catch (e: Exception) {
                mainHandler.post { result.error("DATABASE_ERROR", e.message, null) }
            }
        }
    }

    private fun handleUploadResult(call: MethodCall, result: Result) {
        val args = call.arguments as? Map<*, *> ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing or invalid parameter",
            null
        )

        val isSuccess = args["success"] as? Boolean ?: return result.error(
            "INVALID_ARGUMENTS",
            "Missing success parameter",
            null
        )

        if (isSuccess) {
            // 处理上传成功
            val accessUrl = args["accessUrl"] as? String
            val filePath = args["filePath"] as? String

            if (accessUrl != null) {
                Log.e("halfShootingRecording", "上传成功，地址是$accessUrl")
            }

            if (filePath != null && (filePath.contains(".jpg") || filePath.contains("extended_last_"))) {
                // DocumentManager.deleteLocalVideo(filePath)
                Log.e("halfShootingRecording", "已删除本地文件: $filePath")
            }
        } else {
            // 处理上传失败
            val message = args["message"] as? String
            if (message != null) {
                Log.e("halfShootingRecording", message)
            }
        }

        result.success(null)
    }

    // 实现数据回调
    fun didReceiveData(data: String) {
        Log.e("ShootzPlugin", "收到数据: $data")
        Log.e("halfShootingRecording","didReceiveData:${data}")

        synchronized(eventLock) {
            if (eventSink == null || !eventChannelReady) {
                Log.e("halfShootingRecording", "事件通道未就绪，缓存数据: $data")
                pendingEvents.add(data)
                return
            }

            mainHandler.post {
                try {
                    eventSink?.success(data)
                    Log.d("halfShootingRecording", "数据发送成功: $data")
                } catch (e: Exception) {
                    Log.e("halfShootingRecording", "发送数据失败: ${e.message}")
                    pendingEvents.add(data) // 失败后重新缓存
                }
            }
        }
    }

    // 发送缓存的事件
    private fun sendPendingEvents() {
        synchronized(eventLock) {
            if (pendingEvents.isEmpty()) {
                Log.d("halfShootingRecording", "无待发缓存事件")
                return
            }

            if (eventSink == null || !eventChannelReady) {
                Log.e("halfShootingRecording", "事件通道未就绪，无法发送缓存事件")
                return
            }

            Log.d("halfShootingRecording", "发送 ${pendingEvents.size} 条缓存事件")
            mainHandler.post {
                for (event in pendingEvents) {
                    try {
                        eventSink?.success(event)
                        Log.d("halfShootingRecording", "缓存事件发送成功: $event")
                    } catch (e: Exception) {
                        Log.e("halfShootingRecording", "缓存事件发送失败: ${e.message}")
                    }
                }
                pendingEvents.clear()
            }
        }
    }

    fun didClosePage( channel_closed:String) {
        mainHandler.post {
            // 发送关闭消息
            val closeMessage = mapOf(
                "type" to channel_closed,
                "trainingId" to fixedTrainingId
            )

            if (eventSink != null && eventChannelReady) {
                try {
                    eventSink?.success(closeMessage)
                    Log.d("halfShootingRecording", "关闭消息发送成功")
                } catch (e: Exception) {
                    Log.e("halfShootingRecording", "关闭消息发送失败: ${e.message}")
                }
            } else {
                Log.w("halfShootingRecording", "事件通道未就绪，无法发送关闭消息")
            }
        }
    }

    // EventChannel.StreamHandler 实现
    override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
        Log.e("halfShootingRecording","eventSink onListen")
        synchronized(eventLock) {
            eventSink = events
            eventChannelReady = true
            sendPendingEvents()
        }
    }

    override fun onCancel(arguments: Any?) {
        Log.e("halfShootingRecording1","eventSink onCancel")
        synchronized(eventLock) {
            eventSink = null
            eventChannelReady = false
        }
    }
}