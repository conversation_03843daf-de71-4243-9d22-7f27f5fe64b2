package com.shootZ.app.shoot_z.interfaces

import com.shootZ.app.shoot_z.service.ShootClassifierEvent
import com.google.mediapipe.tasks.vision.imageclassifier.ImageClassifierResult

/**
 * 投篮分类器服务代理接口
 * 对应 Swift: ShootClassifierServiceLiveStreamDelegate
 */
interface ShootClassifierServiceLiveStreamDelegate {
    /**
     * 当检测到投篮事件时调用
     * 对应 Swift: shootClassifierService(_:didDetectShootEvent:)
     */
    fun onShootEventDetected(event: ShootClassifierEvent)

    /**
     * 当完成分类时调用（可选，用于调试）
     * 对应 Swift: shootClassifierService(_:didFinishClassification:error:)
     */
    fun onClassificationFinished(result: ImageClassifierResult?, error: Exception?)

}
