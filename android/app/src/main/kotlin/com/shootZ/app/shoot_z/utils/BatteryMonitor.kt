//package com.shootZ.app.shoot_z.utils
//
//import android.content.Context
//import android.content.Intent
//import android.content.IntentFilter
//import android.os.Build
//
//object BatteryMonitor {
//    private const val LOW_BATTERY_THRESHOLD = 20
//    private var lastAlertTime: Long = 0
//    private const val ALERT_INTERVAL = 30 * 60 * 1000 // 30分钟
//
//    fun startMonitoring(context: Context) {
//        // 注册广播接收器
//        val receiver = BatteryLevelReceiver()
//        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
//        context.registerReceiver(receiver, filter)
//
//        // 启动前台服务
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            context.startForegroundService(Intent(context, BatteryMonitorService::class.java))
//        } else {
//            context.startService(Intent(context, BatteryMonitorService::class.java))
//        }
//    }
//
//    fun stopMonitoring(context: Context) {
//        // 取消注册广播接收器
//        val receiver = BatteryLevelReceiver()
//        context.unregisterReceiver(receiver)
//
//        // 停止服务
//        context.stopService(Intent(context, BatteryMonitorService::class.java))
//    }
//
//    fun showLowBatteryAlert(context: Context) {
//        val currentTime = System.currentTimeMillis()
//        if (currentTime - lastAlertTime > ALERT_INTERVAL) {
//            lastAlertTime = currentTime
//            LowBatteryActivity.start(context)
//        }
//    }
//
//    fun getBatteryLevel(context: Context): Float {
//        val ifilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
//        val batteryIntent = context.registerReceiver(null, ifilter)
//
//        val level = batteryIntent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
//        val scale = batteryIntent?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
//
//        return level * 100f / scale
//    }
//}