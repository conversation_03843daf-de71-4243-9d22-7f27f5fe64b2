/*
 * Copyright 2022 The TensorFlow Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *             http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.shootZ.app.shoot_z.service

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.SystemClock
import android.util.Log
import androidx.annotation.VisibleForTesting
import androidx.camera.core.ImageProxy
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.core.Delegate
import com.google.mediapipe.tasks.vision.core.ImageProcessingOptions
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.objectdetector.ObjectDetector
import com.google.mediapipe.tasks.vision.objectdetector.ObjectDetectorResult
import java.util.concurrent.LinkedBlockingDeque
import java.util.concurrent.locks.ReentrantLock

/**
 * 对象检测助手类，封装了MediaPipe对象检测的核心功能
 *
 * 负责：
 * 1. 初始化和配置对象检测器
 * 2. 处理不同模式的检测任务（图像、视频、实时流）
 * 3. 管理检测结果回调
 */
class ObjectDetectorHelper(
    // 可配置参数（带默认值）
    var threshold: Float = THRESHOLD_DEFAULT,            // 检测结果置信度阈值
    var maxResults: Int = MAX_RESULTS_DEFAULT,          // 最大返回结果数量
    var currentDelegate: Int = DELEGATE_CPU,            // 使用的硬件加速器（CPU/GPU）
    var currentModel: Int = MODEL_EFFICIENTDETV0,       // 使用的模型版本
    var runningMode: RunningMode = RunningMode.IMAGE,   // 运行模式：图像/视频/实时流
    val context: Context,                                // Android上下文
    // 实时流模式下的结果监听器
    var objectDetectorListener: DetectorListener? = null
) {
    // 帧图像缓存
    private val frameCache = LinkedBlockingDeque<Bitmap>(3)
    private val frameCacheLock = ReentrantLock()
    // 对象检测器实例（变量允许配置变更时重置）
    private var objectDetector: ObjectDetector? = null

    // 图像旋转角度（用于处理设备方向）
    private var imageRotation = 0
    // 跳帧处理
    private var frameIdx: Int = 0
    // 图像处理选项（包含旋转信息）
    private lateinit var imageProcessingOptions: ImageProcessingOptions

    // 初始化块：创建对象检测器
    init {
        setupObjectDetector()
    }

    /**
     * 清理对象检测器资源
     */
    fun clearObjectDetector() {
        objectDetector?.close()
        objectDetector = null
        // 清理帧缓存
        try {
            frameCacheLock.lock()
            for (bitmap in frameCache) {
                bitmap.recycle()
            }
            frameCache.clear()
        } finally {
            frameCacheLock.unlock()
        }
    }

    /**
     * 初始化对象检测器（根据当前配置）
     *
     * 注意：GPU代理需在初始化的线程上使用
     */
    fun setupObjectDetector() {
        // 1. 基础选项配置
        val baseOptionsBuilder = BaseOptions.builder()

        // 根据选择的硬件代理配置
        when (currentDelegate) {
            DELEGATE_CPU -> baseOptionsBuilder.setDelegate(Delegate.CPU)
            DELEGATE_GPU -> baseOptionsBuilder.setDelegate(Delegate.GPU)
        }

        // 2. 选择模型文件
        val modelName = when (currentModel) {
            MODEL_EFFICIENTDETV0 -> "efficientdet_lite0.tflite"
            else -> "efficientdet_lite0.tflite"
        }
        baseOptionsBuilder.setModelAssetPath(modelName)


        // 3. 检查运行模式与监听器的兼容性
        if (runningMode == RunningMode.LIVE_STREAM && objectDetectorListener == null) {
            throw IllegalStateException("实时流模式需要设置objectDetectorListener")
        }

        try {
            // 4. 构建检测器选项
            val optionsBuilder = ObjectDetector.ObjectDetectorOptions.builder()
                .setBaseOptions(baseOptionsBuilder.build())
                .setScoreThreshold(threshold)
                .setRunningMode(runningMode)
                .setMaxResults(maxResults)

            // 5. 配置图像处理选项（旋转）
            imageProcessingOptions = ImageProcessingOptions.builder()
                .setRotationDegrees(imageRotation).build()

            // 6. 模式特定配置
            when (runningMode) {
                // 图像和视频模式只需设置运行模式
                RunningMode.IMAGE, RunningMode.VIDEO -> optionsBuilder.setRunningMode(runningMode)

                // 实时流模式需要设置结果和错误监听器
                RunningMode.LIVE_STREAM -> optionsBuilder.setRunningMode(runningMode)
                    .setResultListener(this::returnLivestreamResult)
                    .setErrorListener(this::returnLivestreamError)
            }

            // 7. 创建检测器实例
            val options = optionsBuilder.build()
            objectDetector = ObjectDetector.createFromOptions(context, options)
            Log.e(TAG, "对象检测器初始化c")
        } catch (e: IllegalStateException) {
            // 初始化错误处理
            objectDetectorListener?.onError("对象检测器初始化失败: ${e.message}")
            Log.e(TAG, "模型加载失败: ${e.message}")
        } catch (e: RuntimeException) {
            // GPU相关错误处理
            objectDetectorListener?.onError("GPU初始化失败", GPU_ERROR)
            Log.e(TAG, "GPU模型加载失败: ${e.message}")
        }
    }

    /**
     * 检查检测器是否已关闭
     */
    fun isClosed(): Boolean = objectDetector == null

    /**
     * 视频文件对象检测（运行模式必须为VIDEO）
     *
     * @param videoUri 视频文件URI
     * @param inferenceIntervalMs 采样间隔（毫秒）
     * @return 包含所有帧检测结果的ResultBundle
     */
    fun detectVideoFile(videoUri: Uri, inferenceIntervalMs: Long): ResultBundle? {
        // 模式验证
        if (runningMode != RunningMode.VIDEO) {
            throw IllegalArgumentException("视频检测需使用VIDEO模式")
        }
        if (objectDetector == null) return null
        val startTime = SystemClock.uptimeMillis()  // 计时开始
        var didErrorOccurred = false                // 错误标志
        // 1. 设置视频元数据提取器
        val retriever = MediaMetadataRetriever().apply {
            setDataSource(context, videoUri)
        }

        // 2. 获取视频基本信息
        val videoLengthMs = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLong()
        val firstFrame = retriever.getFrameAtTime(0)
        val width = firstFrame?.width
        val height = firstFrame?.height

        // 3. 验证视频有效性
        if (videoLengthMs == null || width == null || height == null) return null

        // 4. 逐帧处理
        val resultList = mutableListOf<ObjectDetectorResult>()
        val numberOfFrameToRead = videoLengthMs / inferenceIntervalMs

        for (i in 0..numberOfFrameToRead) {
            val timestampMs = i * inferenceIntervalMs

            retriever.getFrameAtTime(
                timestampMs * 1000, // 微秒转换
                MediaMetadataRetriever.OPTION_CLOSEST
            )?.let { frame ->
                // 5. 转换为ARGB_8888格式
                val argb8888Frame = if (frame.config == Bitmap.Config.ARGB_8888)
                    frame else frame.copy(Bitmap.Config.ARGB_8888, false)

                // 6. 转换为MPImage格式
                val mpImage = BitmapImageBuilder(argb8888Frame).build()

                // 7. 执行视频帧检测
                objectDetector?.detectForVideo(mpImage, timestampMs)?.let {
                    resultList.add(it)
                } ?: run {
                    didErrorOccurred = true
                    objectDetectorListener?.onError("视频帧检测失败")
                }
            } ?: run {
                didErrorOccurred = true
                objectDetectorListener?.onError("无法获取指定时间的视频帧")
            }
        }

        // 8. 资源清理
        retriever.release()

        // 9. 计算平均推理时间
        val inferenceTimePerFrameMs = (SystemClock.uptimeMillis() - startTime) / numberOfFrameToRead

        // 10. 返回结果（错误时返回null）
        return if (didErrorOccurred) null else
            ResultBundle(resultList, inferenceTimePerFrameMs, height, width)
    }

    /**
     * 实时流对象检测（运行模式必须为LIVE_STREAM）
     *
     * @param imageProxy 相机帧数据
     */
    fun detectLivestreamFrame(imageProxy: ImageProxy) {
        // 模式验证
        if (runningMode != RunningMode.LIVE_STREAM) {
            throw IllegalArgumentException("实时检测需使用LIVE_STREAM模式")
        }
        val frameTime = SystemClock.uptimeMillis()  // 当前帧时间戳
        // 1. 将帧数据复制到Bitmap缓冲区
        val bitmapBuffer = Bitmap.createBitmap(
            imageProxy.width, imageProxy.height, Bitmap.Config.ARGB_8888
        )
        Log.e(TAG, "detectLivestreamFrame: "+   imageProxy.width+"----"+imageProxy.height )
        imageProxy.use {
            bitmapBuffer.copyPixelsFromBuffer(imageProxy.planes[0].buffer)
        }
        imageProxy.close()
        // 2. 检查旋转变化（需要重新初始化检测器）
        if (imageProxy.imageInfo.rotationDegrees != imageRotation) {
            imageRotation = imageProxy.imageInfo.rotationDegrees
            clearObjectDetector()
            setupObjectDetector()
            return
        }

        // 3. 转换为MPImage格式
        val mpImage = BitmapImageBuilder(bitmapBuffer).build()

        // 4. 执行异步检测
        // 确保图像宽高与模型兼容
        if (imageProxy.width > 1280 || imageProxy.height > 720) {
            try {
                detectAsync(mpImage, frameTime,bitmapBuffer)
            } catch (e: Exception) {
                Log.e("DetectiondetectAsync", "Error in detection", e)
            }
        }
    }

    /**
     * 执行异步检测（实际调用MediaPipe API）
     *
     * @param mpImage MPImage格式的输入图像
     * @param frameTime 帧时间戳
     */
    @VisibleForTesting
    fun detectAsync(mpImage: MPImage, frameTime: Long, bitmap: Bitmap) {
        try {
            frameCacheLock.lock()
            // 更新帧缓存
            while (frameCache.size >= 3) {
                frameCache.removeFirst()
            }
            frameCache.addLast(bitmap)
        } finally {
            frameCacheLock.unlock()
        }

        // 调用检测器异步接口（结果将通过回调返回）
        // 目标检测（跳帧处理）
        if (frameIdx % 2 == 0) {
            objectDetector?.detectAsync(mpImage, imageProcessingOptions, frameTime,)
        }
        frameIdx++
    }

    /**
     * 实时流结果回调处理
     */
    private fun returnLivestreamResult(result: ObjectDetectorResult, input: MPImage) {
        val finishTimeMs = SystemClock.uptimeMillis()
        val inferenceTime = finishTimeMs - result.timestampMs()  // 计算推理耗时
        // 获取当前帧
        val currentFrame = try {
            frameCacheLock.lock()
            frameCache.lastOrNull()
        } finally {
            frameCacheLock.unlock()
        } ?: return
        // 没有检查位图是否有效
        if (currentFrame.isRecycled) { // 缺少这个检查
            return
        }
        // 包装结果并通知监听器
        objectDetectorListener?.onResults(
            ResultBundle(
                listOf(result),
                inferenceTime,
                input.height,
                input.width,
                imageRotation
            ),input,currentFrame
        )
    }

    /**
     * 实时流错误回调处理
     */
    private fun returnLivestreamError(error: RuntimeException) {
        objectDetectorListener?.onError(error.message ?: "检测过程中发生未知错误")
    }

    /**
     * 单张图像对象检测（运行模式必须为IMAGE）
     *
     * @param image 输入的Bitmap图像
     * @return 包含检测结果的ResultBundle
     */
    fun detectImage(image: Bitmap): ResultBundle? {
        // 模式验证
        if (runningMode != RunningMode.IMAGE) {
            throw IllegalArgumentException("图像检测需使用IMAGE模式")
        }
        if (objectDetector == null) return null

        val startTime = SystemClock.uptimeMillis()  // 计时开始

        // 1. 转换为MPImage格式
        val mpImage = BitmapImageBuilder(image).build()

        // 2. 执行检测
        objectDetector?.detect(mpImage)?.let { result ->
            val inferenceTimeMs = SystemClock.uptimeMillis() - startTime
            return ResultBundle(
                listOf(result),
                inferenceTimeMs,
                image.height,
                image.width
            )
        }

        // 检测失败时返回null
        return null
    }

    /**
     * 结果数据包类
     *
     * @param results 检测结果列表
     * @param inferenceTime 推理耗时（毫秒）
     * @param inputImageHeight 输入图像高度
     * @param inputImageWidth 输入图像宽度
     * @param inputImageRotation 输入图像旋转角度（可选）
     */
    data class ResultBundle(
        val results: List<ObjectDetectorResult>,
        val inferenceTime: Long,
        val inputImageHeight: Int,
        val inputImageWidth: Int,
        val inputImageRotation: Int = 0
    )

    companion object {
        // 常量定义
        const val DELEGATE_CPU = 0     // CPU代理标识
        const val DELEGATE_GPU = 1     // GPU代理标识
        const val MODEL_EFFICIENTDETV0 = 0 // 模型0标识
        const val MODEL_EFFICIENTDETV2 = 1 // 模型2标识

        // 默认参数值
        const val MAX_RESULTS_DEFAULT = 3   // 默认最大结果数
        const val THRESHOLD_DEFAULT = 0.5F  // 默认置信度阈值

        // 错误类型标识
        const val OTHER_ERROR = 0  // 常规错误
        const val GPU_ERROR = 1    // GPU相关错误

        const val TAG = "ObjectDetection-Helper" // 日志标签
    }

    /**
     * 检测结果监听器接口
     */
    interface DetectorListener {
        // 错误回调
        fun onError(error: String, errorCode: Int = OTHER_ERROR)
        // 结果回调
        fun onResults(resultBundle: ResultBundle, input:MPImage, bitmap: Bitmap)
    }
}