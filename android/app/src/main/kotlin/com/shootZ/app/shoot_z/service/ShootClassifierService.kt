package com.shootZ.app.shoot_z.service

import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import android.os.SystemClock
import android.util.Log
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.core.Delegate
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.imageclassifier.ImageClassifier
import com.google.mediapipe.tasks.vision.imageclassifier.ImageClassifierResult
import com.shootZ.app.shoot_z.interfaces.ShootClassifierServiceLiveStreamDelegate
import java.util.LinkedList
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * 投篮分类器内部事件信息
 * 对应 Swift: ShootClassifierEvent
 */
data class ShootClassifierEvent(
    val startFrame: Int,             // 开始帧索引
    val shotType: String,            // "goal" 或 "miss"
    val confidence: Float,           // 最高置信度
    val duration: Int,               // 持续帧数
    val goalConfirmationFrame: Int?, // 进球确认帧（仅进球时有值）
    val startTimestamp: Long,        // 开始时间戳 (毫秒)
    val goalTimestamp: Long?         // 进球时间戳（仅进球时有值） (毫秒)
)

/**
 * 分类结果信息
 * 对应 Swift: ClassificationResult
 */
data class ClassificationResult(
    val label: String,
    val score: Float,
    val timestamp: Long  // 毫秒
)


/**
 * 投篮分类器模型
 * 对应 Swift: ShootClassifierModel
 */
enum class ShootClassifierModel {
    CLSF_GOAL;
    /**
     * 获取模型路径（对应Swift的modelPath）
     */
    fun getModelPath(context: Context): String? {
        return when (this) {
            CLSF_GOAL -> "clsf_goal.tflite"
        }
    }
}
/**
 * 投篮分类器服务
 * 对应 Swift: ShootClassifierService
 */
class ShootClassifierService private constructor(
    context: Context,
    model: ShootClassifierModel,
    private val scoreThreshold: Float = 0.5f,
    private val maxResults: Int = 1,
    private val runningMode: RunningMode = RunningMode.LIVE_STREAM,
    private val delegate: Delegate = Delegate.CPU
) {
    companion object {
        private const val TAG = "ShootClassifier"

        /**
         * 创建实时流模式的对象检测器服务
         * 对应 Swift: liveStreamClassifierService(model:scoreThreshold:maxResult:liveStreamDelegate:delegate:)
         */
        fun createLiveStreamService(
            context: Context,
            model: ShootClassifierModel,
            scoreThreshold: Float = 0.5f,
            maxResults: Int = 1,
            delegate: Delegate = Delegate.CPU
        ): ShootClassifierService {
            return ShootClassifierService(
                context = context,
                model = model,
                scoreThreshold = scoreThreshold,
                maxResults = maxResults,
                delegate = delegate
            ).apply {
                // 确保模型正确加载
                if (imageClassifier == null) {
                    throw IllegalStateException("投篮分类器初始化失败")
                }
            }
        }
    }

    // 代理
    var liveStreamDelegate: ShootClassifierServiceLiveStreamDelegate? = null

    // 内部属性
    private var imageClassifier: ImageClassifier? = null

    // MARK: - 投篮检测配置参数
    private object ShootDetectionConfig {
        const val SHOT_SINGLE_FRAME_THRESHOLD: Float = 0.65f      // 单帧进入投篮检测的阈值
        const val SHOT_CONFIRM_THRESHOLD: Float = 0.75f            // 投篮确认阈值
        const val GOAL_HIGH_THRESHOLD: Float = 0.9f               // 高置信度进球阈值
        const val GOAL_MID_THRESHOLD: Float = 0.75f                // 中等置信度进球阈值
        const val GOAL_MID_FRAMES_REQUIRED: Int = 2                // 需要多少帧中等置信度才判定为进球
        const val CONSECUTIVE_BG_THRESHOLD: Int = 10               // 连续多少帧背景判定投篮结束
        const val INTERVAL_FRAMES: Int = 2                         // 每多少帧检测一次投篮
        const val MIN_SHOT_FRAMES: Int = 6                         // 最小投篮事件长度（帧数）
        const val COOLDOWN_FRAMES: Int = 15                        // 投篮事件结束后的冷却帧数
        const val BASE_FPS: Float = 30.0f                          // 基准帧率，用于计算基础跳帧数
        const val WINDOW_SIZE: Int = 6                             // 结果窗口大小
    }

    // MARK: - 状态跟踪
    private var resultWindow = LinkedList<ClassificationResult>() // 结果窗口缓存
    private var shotInProgress = false                            // 是否正在投篮
    private var currentShotStart = 0                               // 当前投篮开始帧
    private var backgroundFrameCount = 0                         // 连续背景帧计数
    private var activeShotFrames = 0                             // 活跃投篮帧计数
    private var currentCooldown = 0                              // 当前冷却帧数
    private var currentShotType: String? = null                   // 当前投篮类型
    private var currentShotConfidence = 0f                        // 当前投篮置信度
    private var goalConfirmationFrame: Int? = null               // 进球确认帧
    private var currentShotStartTimestamp: Long = 0L              // 投篮开始时间戳
    private var currentGoalTimestamp: Long? = null                // 进球时间戳
    private var frameIdx = 0                                       // 帧索引
    private var actualFrameIdx = 0                                // 实际处理帧索引
    private var basicIntervalFrames = 1                           // 基础跳帧数
    // 检测状态
    private enum class DetectionStatus {
        NOT_STARTED, STARTED, PAUSED
    }

    private var detectionStatus: DetectionStatus = DetectionStatus.NOT_STARTED
    // 视频帧率
    private var fps: Float = 30f

    init {
        // 配置分类器
        createImageClassifier(context, model)
    }
    /// 开始检测（对应Swift的startDetection）
    fun startDetection() {
        detectionStatus = DetectionStatus.STARTED
    }

    /// 暂停检测（对应Swift的pauseDetection）
    fun pauseDetection() {
        detectionStatus = DetectionStatus.PAUSED
    }

    /// 恢复检测（对应Swift的resumeDetection）
    fun resumeDetection() {
        detectionStatus = DetectionStatus.STARTED
    }

    /// 停止检测（对应Swift的stopDetection）
    fun stopDetection() {
        detectionStatus = DetectionStatus.NOT_STARTED
    }

    /**
     * 创建图像分类器
     * 对应 Swift: createImageClassifier()
     */
    private fun createImageClassifier(context: Context, model: ShootClassifierModel) {
        try {
            // 检查模型路径
            val modelPath = model.getModelPath(context)

            // 配置选项
            val baseOptions = BaseOptions.builder()
                .setModelAssetPath(modelPath)
                .setDelegate(delegate)
                .build()

            val options = ImageClassifier.ImageClassifierOptions.builder()
                .setBaseOptions(baseOptions)
                .setMaxResults(maxResults)
                .setScoreThreshold(scoreThreshold)
                .setRunningMode(runningMode)
                .setResultListener(this::handleClassificationResult)
                .setErrorListener(this::handleClassificationError)
                .build()

            imageClassifier = ImageClassifier.createFromOptions(context, options)
            Log.e(TAG, "投篮分类器创建成功")
        } catch (e: Exception) {
            Log.e(TAG, "创建投篮分类器失败", e)
        }
    }

    /**
     * 设置视频帧率
     * 对应 Swift: setVideoFps(_:)
     */
    fun setVideoFps(fps: Float) {
        this.fps = if (fps > 0) fps else ShootDetectionConfig.BASE_FPS
        basicIntervalFrames = max(1, (this.fps / ShootDetectionConfig.BASE_FPS).roundToInt())
        Log.e(TAG, "设置视频帧率: $fps, 基础跳帧数: $basicIntervalFrames")
    }

    /**
     * 重置状态
     * 对应 Swift: reset()
     */
    fun reset() {
        resultWindow.clear()
        shotInProgress = false
        currentShotStart = 0
        backgroundFrameCount = 0
        activeShotFrames = 0
        currentCooldown = 0
        currentShotType = null
        currentShotConfidence = 0f
        goalConfirmationFrame = null
        currentShotStartTimestamp = 0L
        currentGoalTimestamp = null
        frameIdx = 0
        actualFrameIdx = 0

        Log.e(TAG, "投篮分类器状态已重置")
    }

    /**
     * 处理视频帧
     * 对应 Swift: processFrame(image:hoopRect:backboardRect:timestamp:)
     */
    fun processFrame(
        croppedBitmap: Bitmap,
        timestamp: Long // 毫秒
    ) {
        // 应用基础跳帧
        if (frameIdx % basicIntervalFrames != 0) {
            frameIdx++
            if (shotInProgress) {
                backgroundFrameCount++
            }
            if (currentCooldown > 0) {
                currentCooldown--
            }
            return
        }

        // 更新实际处理的帧索引
        actualFrameIdx = frameIdx

        // 处理冷却倒计时
        if (currentCooldown > 0) {
            currentCooldown--
            frameIdx++
            return
        }

        // 检查间隔帧
        val shouldSkipInterval = (actualFrameIdx / basicIntervalFrames) % ShootDetectionConfig.INTERVAL_FRAMES != 0
        val lastFrameWasSignificant = resultWindow.isNotEmpty() &&
                resultWindow.last.label != "background" &&
                resultWindow.last.score >= ShootDetectionConfig.SHOT_SINGLE_FRAME_THRESHOLD

        if (shouldSkipInterval && !lastFrameWasSignificant) {
            frameIdx++
            if (shotInProgress) {
                backgroundFrameCount++
            }
            return
        }

        // 进行分类
        classifyImageAsync(croppedBitmap, timestamp)
        frameIdx++
    }

    /**
     * 计算裁剪区域
     * 对应 Swift: calculateCustomBbox(hoopRect:backboardRect:imageSize:)
     */
    private fun calculateCropRect(
        hoopRect: RectF,
        backboardRect: RectF,
        imageWidth: Float,
        imageHeight: Float
    ): RectF {
        // 转换坐标（横屏处理）
        val transformedHoopRect = RectF(
            hoopRect.top,
            imageWidth - hoopRect.right,
            hoopRect.bottom,
            imageWidth - hoopRect.left
        )

        val transformedBackboardRect = RectF(
            backboardRect.top,
            imageWidth - backboardRect.right,
            backboardRect.bottom,
            imageWidth - backboardRect.left
        )

        // 创建裁剪区域
        return RectF(
            transformedBackboardRect.left - (transformedBackboardRect.width() / 10),
            transformedBackboardRect.top,
            transformedBackboardRect.right + (transformedBackboardRect.width() / 5),
            transformedHoopRect.bottom + (hoopRect.height() / 4)
        ).apply {
            // 确保在图像范围内
            left = max(0f, left)
            top = max(0f, top)
            right = min(imageHeight, right) // 注意：横屏交换了宽高
            bottom = min(imageWidth, bottom)
        }
    }

    /**
     * 裁剪位图
     * 对应 Swift: cropImage(_:toRect:)
     */
    private fun cropBitmap(bitmap: Bitmap, cropRect: RectF): Bitmap? {
        val x = cropRect.left.toInt()
        val y = cropRect.top.toInt()
        val width = (cropRect.width()).toInt().coerceAtLeast(1)
        val height = (cropRect.height()).toInt().coerceAtLeast(1)

        if (x >= bitmap.width || y >= bitmap.height) return null
        if (x + width <= 0 || y + height <= 0) return null

        val cropX = max(0, x)
        val cropY = max(0, y)
        val cropWidth = min(width, bitmap.width - cropX)
        val cropHeight = min(height, bitmap.height - cropY)

        if (cropWidth <= 0 || cropHeight <= 0) return null

        return try {
            Bitmap.createBitmap(bitmap, cropX, cropY, cropWidth, cropHeight)
        } catch (e: Exception) {
            Log.e(TAG, "位图裁剪失败", e)
            null
        }
    }

    /**
     * 异步分类图像
     * 对应 Swift: classifyImageAsync(_:timestamp:)
     */
    private fun classifyImageAsync(bitmap: Bitmap, timestamp: Long) {
        try {
            val mpImage = BitmapImageBuilder(bitmap).build()
            imageClassifier?.classifyAsync(mpImage, timestamp)
            Log.e("ObjectDetection1115", "classifyImageAsync=投篮分类异开始")
        } catch (e: Exception) {
            Log.e(TAG, "投篮分类异步处理错误", e)
            Log.e("ObjectDetection1114", "Exception=投篮分类异步处理错误")
        }
    }

    /**
     * 处理分类结果
     * 对应 Swift: processClassificationResult(_:timestamp:)
     */
    private fun processClassificationResult(result: ImageClassifierResult, timestamp: Long) {
        Log.e("ObjectDetection1116", "processClassificationResult="+result)
        val classification = result.classificationResult().classifications().firstOrNull()
        val category = classification?.categories()?.firstOrNull() ?: return

        val categoryName = category.categoryName() ?: "unknown"
        val score = category.score()

        // 创建分类结果
        val classificationResult = ClassificationResult(
            label = categoryName,
            score = score,
            timestamp = timestamp
        )

        // 添加到结果窗口
        resultWindow.add(classificationResult)
        if (resultWindow.size > ShootDetectionConfig.WINDOW_SIZE) {
            resultWindow.removeFirst()
        }

        // 更新连续背景帧计数
        if (shotInProgress) {
            if (categoryName == "background" ||
                (categoryName != "background" && score < ShootDetectionConfig.SHOT_SINGLE_FRAME_THRESHOLD)
            ) {
                backgroundFrameCount++
            } else {
                backgroundFrameCount = 0
                activeShotFrames++
            }
        }

        // 检测投篮事件
        processShootDetection(currentResult = classificationResult)

        // 调试日志
        if (score >= ShootDetectionConfig.SHOT_SINGLE_FRAME_THRESHOLD && categoryName != "background") {
            Log.e(TAG, "帧 $actualFrameIdx: $categoryName (${"%.2f".format(score)})")
        }
    }

    /**
     * 处理投篮检测
     * 对应 Swift: processShootDetection(currentResult:)
     */
    private fun processShootDetection(currentResult: ClassificationResult) {
     //   Log.e("ObjectDetection1117", "${currentCooldown}=processShootDetection="+currentResult)
        // 检查冷却期
        if (currentCooldown > 0) return

        // 检查是否检测到投篮
        val isShotDetected = (currentResult.label == "goal" || currentResult.label == "miss") &&
                currentResult.score >= ShootDetectionConfig.SHOT_SINGLE_FRAME_THRESHOLD

        if (isShotDetected) {
          //  Log.e("ObjectDetection11170", "isShotDetected111="+isShotDetected)
            // 进一步判断是否为进球
            val goalHigh = resultWindow.any {
                it.label == "goal" && it.score >= ShootDetectionConfig.GOAL_HIGH_THRESHOLD
            }

            val goalMidCount = resultWindow.count {
                it.label == "goal" && it.score >= ShootDetectionConfig.GOAL_MID_THRESHOLD
            }

            val isGoal = goalHigh || goalMidCount >= ShootDetectionConfig.GOAL_MID_FRAMES_REQUIRED

            if (!shotInProgress) {
             //   Log.e("ObjectDetection1117", "shotInProgress1="+shotInProgress)
                // 开始新的投篮事件
                startNewShotEvent(isGoal, currentResult.score)
            } else {
            //    Log.e("ObjectDetection1117", "shotInProgress2="+shotInProgress)
                // 更新现有投篮事件
                updateShotEvent(isGoal, currentResult.score)
            }
        }else{
       //     Log.e("ObjectDetection11171", "isShotDetected111="+isShotDetected)
        }

        // 检查投篮事件是否结束
        checkShotEventEnd()
    }

    /**
     * 开始新的投篮事件
     * 对应 Swift: startNewShotEvent(isGoal:confidence:)
     */
    private fun startNewShotEvent(isGoal: Boolean, confidence: Float) {
        shotInProgress = true
        currentShotStart = actualFrameIdx
        currentShotType = if (isGoal) "goal" else "miss"
        currentShotConfidence = confidence
        backgroundFrameCount = 0
        activeShotFrames = 1
        goalConfirmationFrame = if (isGoal) actualFrameIdx else null
        currentShotStartTimestamp = System.currentTimeMillis()
        currentGoalTimestamp = if (isGoal) System.currentTimeMillis() else null

        Log.e(TAG, "🏀 开始新的${if (isGoal) "进球" else "未命中"}投篮事件，帧: $actualFrameIdx")
    }

    /**
     * 更新投篮事件
     * 对应 Swift: updateShotEvent(isGoal:confidence:)
     */
    private fun updateShotEvent(isGoal: Boolean, confidence: Float) {
        // 如果检测到进球，更新事件类型
        if (isGoal && currentShotType == "miss") {
            currentShotType = "goal"
            goalConfirmationFrame = actualFrameIdx
            currentGoalTimestamp = System.currentTimeMillis()
            Log.e(TAG, "⚠️ 更新投篮事件为进球，帧: $actualFrameIdx")
        }

        // 更新最高置信度
        if (confidence > currentShotConfidence) {
            currentShotConfidence = confidence
        }

        // 重置背景帧计数
        backgroundFrameCount = 0
    }

    /**
     * 检查投篮事件是否结束
     * 对应 Swift: checkShotEventEnd()
     */
    private fun checkShotEventEnd() {
        if (!shotInProgress || backgroundFrameCount < ShootDetectionConfig.CONSECUTIVE_BG_THRESHOLD) {
            return
        }
        val framesSinceStart = actualFrameIdx - currentShotStart
        if (activeShotFrames >= ShootDetectionConfig.MIN_SHOT_FRAMES) {
            // 检查置信度是否达到记录阈值
            if (currentShotConfidence >= ShootDetectionConfig.SHOT_CONFIRM_THRESHOLD) {
                // 创建投篮事件
                val event = ShootClassifierEvent(
                    startFrame = currentShotStart,
                    shotType = currentShotType ?: "miss",
                    confidence = currentShotConfidence,
                    duration = framesSinceStart,
                    goalConfirmationFrame = goalConfirmationFrame,
                    startTimestamp = currentShotStartTimestamp,
                    goalTimestamp = currentGoalTimestamp
                )

                // 通知代理
                liveStreamDelegate?.onShootEventDetected(event)

                Log.e(TAG, "🏀 投篮事件结束：${event.shotType}，开始帧: ${event.startFrame}，持续: ${event.duration}帧，置信度: ${"%.2f".format(event.confidence)}")
            } else {
                Log.e(TAG, "⚠️ 忽略置信度不足的投篮事件（置信度: ${"%.2f".format(currentShotConfidence)}）")
            }

            // 设置冷却期
            currentCooldown = ShootDetectionConfig.COOLDOWN_FRAMES
        } else {
            Log.e(TAG, "⚠️ 忽略过短的投篮事件（活跃帧数: $activeShotFrames）")
            currentCooldown = ShootDetectionConfig.COOLDOWN_FRAMES / 2
        }

        // 重置投篮状态
        resetShotState()
    }

    /**
     * 重置投篮状态
     * 对应 Swift: resetShotState()
     */
    private fun resetShotState() {
        shotInProgress = false
        currentShotStart = 0
        currentShotType = null
        currentShotConfidence = 0f
        backgroundFrameCount = 0
        activeShotFrames = 0
        goalConfirmationFrame = null
        currentShotStartTimestamp = 0
        currentGoalTimestamp = null
    }

    /**
     * 处理分类结果回调
     * 对应 Swift: imageClassifier(_:didFinishClassification:timestampInMilliseconds:error:)
     */
    private fun handleClassificationResult(result: ImageClassifierResult, inputImage: MPImage) {
        if(detectionStatus==DetectionStatus.STARTED){
            Log.e("ObjectDetection1118", "handleClassificationResult="+result)
            try {
                val frameTime = SystemClock.uptimeMillis()  // 当前帧时间戳
                // 处理分类结果
                processClassificationResult(result, frameTime)
                // 通知代理
                liveStreamDelegate?.onClassificationFinished(result, null)
            } catch (e: Exception) {
                Log.e(TAG, "处理分类结果失败", e)
                liveStreamDelegate?.onClassificationFinished(null, e)
            }
        }

    }

    /**
     * 处理分类错误回调
     * 对应 Swift: 通过 setErrorListener 设置的回调
     */
    private fun handleClassificationError(error: Exception) {
        Log.e(TAG, "分类错误", error)
        if(detectionStatus==DetectionStatus.STARTED) {
            liveStreamDelegate?.onClassificationFinished(null, error)
        }
    }

    /**
     * 清理资源
     */
    fun close() {
        imageClassifier?.close()
    }
}