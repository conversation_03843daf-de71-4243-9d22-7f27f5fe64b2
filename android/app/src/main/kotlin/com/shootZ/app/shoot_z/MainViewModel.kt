/*
 * Copyright 2022 The TensorFlow Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *             http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.shootZ.app.shoot_z

import androidx.lifecycle.ViewModel
import com.shootZ.app.shoot_z.service.ObjectDetectorHelper

/**
 * 主视图模型 - 用于存储对象检测器的配置设置
 *
 * 功能：
 * 1. 保存对象检测器的设置参数
 * 2. 在配置变更（如屏幕旋转）时保留设置
 * 3. 提供设置参数给其他组件
 */
class MainViewModel : ViewModel() {
    // 私有属性存储对象检测器的设置参数

    /**
     * 硬件代理类型
     * 默认值：ObjectDetectorHelper.DELEGATE_CPU (使用CPU)
     * 可能的值：ObjectDetectorHelper.DELEGATE_CPU 或 ObjectDetectorHelper.DELEGATE_GPU
     */
    private var _delegate: Int = ObjectDetectorHelper.DELEGATE_CPU

    /**
     * 检测结果置信度阈值
     * 默认值：ObjectDetectorHelper.THRESHOLD_DEFAULT (0.5F)
     * 范围：[0.0, 1.0]
     */
    private var _threshold: Float = ObjectDetectorHelper.THRESHOLD_DEFAULT

    /**
     * 最大返回结果数量
     * 默认值：ObjectDetectorHelper.MAX_RESULTS_DEFAULT (3)
     */
    private var _maxResults: Int = ObjectDetectorHelper.MAX_RESULTS_DEFAULT

    /**
     * 使用的检测模型
     * 默认值：ObjectDetectorHelper.MODEL_EFFICIENTDETV0
     * 可能的值：ObjectDetectorHelper.MODEL_EFFICIENTDETV0 或 ObjectDetectorHelper.MODEL_EFFICIENTDETV2
     */
    private var _model: Int = ObjectDetectorHelper.MODEL_EFFICIENTDETV0

    // 公共只读属性允许其他组件获取当前设置值

    /** 获取当前硬件代理设置 */
    val currentDelegate: Int get() = _delegate

    /** 获取当前置信度阈值 */
    val currentThreshold: Float get() = _threshold

    /** 获取当前最大结果数量 */
    val currentMaxResults: Int get() = _maxResults

    /** 获取当前使用的模型 */
    val currentModel: Int get() = _model

    /**
     * 设置硬件代理类型
     *
     * @param delegate 要设置的硬件代理类型
     *                 ObjectDetectorHelper.DELEGATE_CPU 或 ObjectDetectorHelper.DELEGATE_GPU
     */
    fun setDelegate(delegate: Int) {
        _delegate = delegate
    }

    /**
     * 设置置信度阈值
     *
     * @param threshold 新的置信度阈值，范围 [0.0, 1.0]
     */
    fun setThreshold(threshold: Float) {
        _threshold = threshold
    }

    /**
     * 设置最大返回结果数量
     *
     * @param maxResults 新的最大结果数量
     */
    fun setMaxResults(maxResults: Int) {
        _maxResults = maxResults
    }

    /**
     * 设置使用的检测模型
     *
     * @param model 要使用的模型
     *              ObjectDetectorHelper.MODEL_EFFICIENTDETV0 或 ObjectDetectorHelper.MODEL_EFFICIENTDETV2
     */
    fun setModel(model: Int) {
        _model = model
    }
}