package com.shootZ.app.shoot_z.interfaces

import android.graphics.Bitmap
import android.graphics.RectF
import android.util.SizeF
import com.shootZ.app.shoot_z.service.ObjectDetectorHelper
import com.shootZ.app.shoot_z.model.ShootEvent

/**
 * 检测结果监听器接口
 */
interface AIServiceDelegate {
        /// 当检测到篮筐和篮板区域并满足条件时调用（对应Swift的didDetectHoopAndBackboard）
        fun didDetectHoopAndBackboard(hoopRect: RectF, backboardRect: RectF, isValid: Boolean)

        /// 当检测到投篮事件时调用（对应Swift的didDetectShootEvent）
        fun didDetectShootEvent(event: ShootEvent)

        /// 当检测到目标并完成处理时调用（对应Swift的aiService(_:didDetectObjects:imageSize:)）
        fun onObjectsDetected(result: ObjectDetectorHelper.ResultBundle?, imageSize: SizeF)

        /// 当需要显示或隐藏篮筐合法区域时调用（对应Swift的shouldDisplayHoopRegion）
        fun shouldDisplayHoopRegion(region: RectF, imageSize: SizeF,type: Int)

        /// 当截取到篮筐区域图像时调用（对应Swift的didCaptureHoopImage）
        fun didCaptureHoopImage(image: Bitmap)
}