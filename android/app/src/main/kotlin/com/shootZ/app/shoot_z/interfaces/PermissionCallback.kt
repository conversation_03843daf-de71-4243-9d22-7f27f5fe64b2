package com.shootZ.app.shoot_z.interfaces

/**
 * 检测结果监听器接口
 */
/**
 * 权限请求结果回调接口
 */
interface PermissionCallback {
        /**
         * 所有权限已授予
         */
        fun onAllPermissionsGranted()

        /**
         * 部分或全部权限被拒绝
         * @param deniedPermissions 被拒绝的权限列表
         * @param permanentlyDenied 是否有权限被永久拒绝
         */
        fun onPermissionsDenied(deniedPermissions: List<String>, permanentlyDenied: Boolean)

        /**
         * 权限请求过程中发生错误
         * @param error 错误信息
         */
        fun onPermissionError(error: String)
}