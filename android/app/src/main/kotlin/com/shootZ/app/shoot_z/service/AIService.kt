
import android.content.Context
import android.graphics.Bitmap
import android.graphics.PointF
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.util.Log
import android.util.SizeF
import com.google.mediapipe.tasks.components.containers.Detection
import com.google.mediapipe.tasks.core.Delegate
import com.google.mediapipe.tasks.vision.imageclassifier.ImageClassifierResult
import com.shootZ.app.shoot_z.interfaces.AIServiceDelegate
import com.shootZ.app.shoot_z.interfaces.ShootClassifierServiceDelegate
import com.shootZ.app.shoot_z.interfaces.ShootClassifierServiceLiveStreamDelegate
import com.shootZ.app.shoot_z.model.AttemptPlayerInfo
import com.shootZ.app.shoot_z.model.ClassifierResultBundle
import com.shootZ.app.shoot_z.model.ShootEvent
import com.shootZ.app.shoot_z.service.LogService
import com.shootZ.app.shoot_z.service.ObjectDetectorHelper
import com.shootZ.app.shoot_z.service.ShootClassifierModel
import com.shootZ.app.shoot_z.service.ShootClassifierService
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.LinkedBlockingDeque
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.ScheduledThreadPoolExecutor
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.ReentrantLock
import kotlin.math.max
import kotlin.math.min


/// 篮球投篮事件检测服务（对应Swift的AIService）
class AIService(context: Context) : ShootClassifierServiceDelegate,
    ShootClassifierServiceLiveStreamDelegate {

    // MARK: - 常量（对应Swift的Constants）

    companion object {
        const val minHoopBackboardDetectionDuration: Long = 1000 // 1秒
        const val minIouThreshold: Float = 0.7f
        const val playerLookbackTime: Long = 1800 // 1.8秒
        const val playerNearBallFactor: Float = 2.2f
        const val dataRetentionTime: Long = 4000 // 4秒
        const val maxArraySize = 100
        const val maxFrameCacheSize = 3
        private const val EXPIRY_DURATION = 30 * 60 * 1000 // 30分钟
        private const val MAX_CLEANUP_RETRIES = 3

        // 有效篮筐区域 (归一化坐标)
        val validHoopRegion =
            RectF(0.40f, 0.13f, 0.60f, 0.35f) // x: 0.38, y: 0.2, width: 0.24, height: 0.26
    }

    // MARK: - 属性（对应Swift的属性部分）

    var delegate: AIServiceDelegate? = null

    // 后台处理队列
    private val backgroundQueue = ThreadPoolExecutor(
        2, 4, 10, TimeUnit.SECONDS,
        LinkedBlockingQueue(),
        ThreadFactory { r -> Thread(r, "AIService-BackgroundThread") }
    )


    // 投篮分类检测服务
    private var shootClassifierService: ShootClassifierService? = null

    // 检测状态
    private enum class DetectionStatus {
        NOT_STARTED, STARTED, PAUSED
    }

    private var detectionStatus: DetectionStatus = DetectionStatus.NOT_STARTED

    // 开始检测时间
    private var startTime: Long = 0

    // 投篮人信息列表
    private val attemptPlayers = ConcurrentLinkedQueue<AttemptPlayerInfo>()
    private val isCleaning = AtomicBoolean(false)

    // 最后一次有效的篮筐位置
    private var lastValidHoopRect: RectF? = null

    // 最后一次有效的篮板位置
    private var lastValidBackboardRect: RectF? = null

    // 是否检测到有效的篮筐篮板组合
    public var hasValidHoopAndBackboard: Boolean = false

    // 连续无效帧计数
    private var consecutiveInvalidFrames: Int = 0

    // 帧图像缓存
    private val frameCache = LinkedBlockingDeque<Bitmap>(maxFrameCacheSize)
    private val frameCacheLock = ReentrantLock()

    // 视频分辨率
    var videoResolution: SizeF = SizeF(0f, 0f)

    // 调试定时器
    private var debugTimer: ScheduledFuture<*>? = null
    private val scheduler = ScheduledThreadPoolExecutor(1)

    // 调试模式
    var isDebug: Boolean = false

    // 上下文
    private val appContext: Context = context.applicationContext

    // MARK: - 初始化方法（对应Swift的init）

    init {
        setupServices()
    }

    /// 释放资源（对应Swift的deinit）
    fun release() {
        stopDetection()
        backgroundQueue.shutdown()
        scheduler.shutdown()
        // 清理资源
    }

    /// 设置视频分辨率（对应Swift的setVideoResolution）
    fun setVideoResolution2(resolution: SizeF) {
        videoResolution = SizeF(resolution.width, resolution.height)
    }

    /// 开始检测（对应Swift的startDetection）
    fun startDetection() {
        // 确保已检测到有效篮筐篮板
        if (!hasValidHoopAndBackboard) return
        backgroundQueue.execute {
            // 重置状态
            cleanupAllData()
            detectionStatus = DetectionStatus.STARTED
            startTime = System.currentTimeMillis()
            // 初始化投篮分类服务
            clearAndInitializeImageClassifierService()
            // 在主线程隐藏篮筐区域
            Handler(Looper.getMainLooper()).post {
                delegate?.shouldDisplayHoopRegion(RectF(), videoResolution, 1)
            }

            // 启动调试定时器（如果需要）
            if (isDebug) {
                startDebugTimer()
            }
        }
    }

    /// 暂停检测（对应Swift的pauseDetection）
    fun pauseDetection() {
        detectionStatus = DetectionStatus.PAUSED
        shootClassifierService!!.pauseDetection()
        stopDebugTimer()
        // 在主线程显示篮筐区域
        Handler(Looper.getMainLooper()).post {
            val displayRect = RectF(
                validHoopRegion.left * videoResolution.width,
                validHoopRegion.top * videoResolution.height,
                validHoopRegion.right * videoResolution.width,
                validHoopRegion.bottom * videoResolution.height
            )
            delegate?.shouldDisplayHoopRegion(displayRect, videoResolution, 2)
        }
    }

    /// 恢复检测（对应Swift的resumeDetection）
    fun resumeDetection() {
        detectionStatus = DetectionStatus.STARTED
        shootClassifierService!!.resumeDetection()
        // 在主线程隐藏篮筐区域
        Handler(Looper.getMainLooper()).post {
            delegate?.shouldDisplayHoopRegion(RectF(), videoResolution, 3)
        }
        // 启动调试定时器（如果需要）
        if (isDebug) {
            startDebugTimer()
        }
    }

    /// 停止检测（对应Swift的stopDetection）
    fun stopDetection() {
        detectionStatus = DetectionStatus.NOT_STARTED
        shootClassifierService!!.stopDetection()
        startTime = 0
        cleanupAllData()
        stopDebugTimer()

        // 在主线程显示篮筐区域
        Handler(Looper.getMainLooper()).post {
            val displayRect = RectF(
                validHoopRegion.left * videoResolution.width,
                validHoopRegion.top * videoResolution.height,
                validHoopRegion.right * videoResolution.width,
                validHoopRegion.bottom * videoResolution.height
            )
            delegate?.shouldDisplayHoopRegion(displayRect, videoResolution, 4)
        }
    }

    private val TAG = "ObjectDetection-aiservice"

    /// 处理视频帧（对应Swift的processVideoFrame）
    fun processVideoFrame(bitmap: Bitmap) {
        try {
            frameCacheLock.lock()
            // 更新帧缓存
            while (frameCache.size >= maxFrameCacheSize) {
                frameCache.removeFirst()
            }
            frameCache.addLast(bitmap)
        } finally {
            frameCacheLock.unlock()
        }
        val finishTimeMs = SystemClock.uptimeMillis()
        //  Log.e(TAG, "processVideoFrame1" )
        // 在后台处理
        backgroundQueue.execute {
            // 投篮检测（不跳帧）
            if (detectionStatus == DetectionStatus.STARTED) {
                processShootDetection(finishTimeMs, bitmap)
                //     Log.e(TAG, "processVideoFrame4")
            }
        }
    }

    //目标检测数据处理
    fun onDetectionFinished(
        resultBundle: ObjectDetectorHelper.ResultBundle
    ) {
      //  Log.e("ObjectDetection11113", "检测结束:" + listOf(resultBundle))

        resultBundle?.let { bundle ->
            // 处理检测结果
            bundle.results.forEach { result ->
                result?.detections()?.forEach { detection ->
                    Log.e(
                        "ObjectDetection11115",
                        "检测到对象: ${detection.categories().firstOrNull()?.categoryName()} " +
                                "置信度: ${detection.categories().firstOrNull()?.score()} " +
                                "位置: ${detection.boundingBox()}"
                    )
                }
            }
        }
        resultBundle?.let { bundle ->
            bundle.results.firstOrNull()?.let { detectorResult ->
                Handler(Looper.getMainLooper()).post {
                    delegate?.onObjectsDetected(bundle, videoResolution)
                }
                // 处理检测结果
                processDetections(detectorResult.detections(), videoResolution)
            }
        }
    }

    // MARK: - ShootClassifierServiceDelegate

    override fun onShootEventDetected(event: com.shootZ.app.shoot_z.service.ShootClassifierEvent) {
        // 转换事件类型
        val shootEvent = ShootEvent(
            startFrame = event.startFrame,
            shotType = event.shotType,
            confidence = event.confidence,
            duration = event.duration,
            goalConfirmationFrame = event.goalConfirmationFrame,
            startTimestamp = event.startTimestamp.toLong(),
            goalTimestamp = event.goalTimestamp?.toLong(),
            playerImage = null,
            playerConfidence = 0f,
            shootCoord = null
        )

        // 处理投篮事件
        processShootClassifierEvent(shootEvent)
    }

    override fun onClassificationFinished(
        resultBundle: ClassifierResultBundle?,
        error: Exception?
    ) {
        TODO("Not yet implemented")
    }

    override fun onClassificationFinished(result: ImageClassifierResult?, error: Exception?) {
        // 处理分类结果
    }


    // MARK: - 私有方法（对应Swift的私有方法）

    /// 设置服务（对应Swift的setupServices）
    private fun setupServices() {
        // 重置投篮分类服务
        clearAndInitializeImageClassifierService()
    }

    /// 处理投篮检测（对应Swift的processShootDetection）
    private fun processShootDetection(currentTimeMs: Long, bitmap: Bitmap) {
//        Log.e(
//            "ObjectDetection11140",
//            "1lastValidHoopRect=" + lastValidHoopRect + "---lastValidBackboardRect=" + lastValidBackboardRect
//        )
        val hoopRect = lastValidHoopRect ?: return
        val backboardRect = lastValidBackboardRect ?: return
//        // 获取当前帧
//        val currentFrame = try {
//            frameCacheLock.lock()
//            frameCache.lastOrNull()
//        } finally {
//            frameCacheLock.unlock()
//        } ?: return
        // 使用投篮分类服务处理帧
//        Log.e(
//            "ObjectDetection11141",
//            "detectionStatus=" + (detectionStatus == DetectionStatus.NOT_STARTED) + (detectionStatus == DetectionStatus.PAUSED)
//        )
        // 截取篮筐区域并通知代理
        val combinedRect = RectF(hoopRect).apply {
            if (backboardRect != this) union(backboardRect)
        }

        val croppedImage = try {
            Bitmap.createBitmap(
                bitmap,
                max(0, combinedRect.left.toInt()),
                max(0, combinedRect.top.toInt()),
                min(bitmap.width, combinedRect.width().toInt()),
                min(bitmap.height, combinedRect.height().toInt())
            )
        } catch (e: Exception) {
            // 使用原始图像作为后备
            bitmap
        }
        shootClassifierService?.processFrame(
            croppedImage,
            currentTimeMs
        )
        Handler(Looper.getMainLooper()).post {
            delegate?.didCaptureHoopImage(croppedImage)
        }
    }

    /// 处理目标检测结果（对应Swift的processDetections）
    private fun processDetections(detections: MutableList<Detection>, imageSize: SizeF) {
        // 清理过期数据
        val currentTimestamp = System.currentTimeMillis()
        cleanupExpiredData()
        // 分类目标
        val hoopRects = mutableListOf<RectF>()
        val backboardRects = mutableListOf<RectF>()
        val basketballs = mutableListOf<RectF>()
        val players = mutableListOf<Pair<RectF, Float>>()

        for (detection in detections) {
            detection.categories()
            val rect = detection.boundingBox()
            detection.categories().firstOrNull()?.let { category ->
                when (category.categoryName()) {
                    "hoop" -> if (category.score() > 0.32) hoopRects.add(rect)
                    "backboard" -> if (category.score() > 0.32) backboardRects.add(rect)
                    "basketball" -> if (category.score() > 0.25) basketballs.add(rect)
                    "attempt_player" -> if (category.score() > 0.2) players.add(rect to category.score())
                }
            }
        }

        // 只在开始检测状态下处理投篮人
        if (detectionStatus == DetectionStatus.STARTED) {
            processAttemptPlayers(players, basketballs, currentTimestamp)
        }
//        Log.e(
//            "ObjectDetection11140",
//            "12" + (detectionStatus != DetectionStatus.STARTED) + detectionStatus
//        )
        // 只在非开始检测状态下处理篮筐篮板
        if (detectionStatus != DetectionStatus.STARTED) {
            processHoopAndBackboardDetection(hoopRects, backboardRects, imageSize)
        } else {
            monitorHoopAndBackboardState(hoopRects, backboardRects, imageSize)
        }
    }

    /// 清理过期数据（对应Swift的cleanupExpiredData）
    fun cleanupExpiredData() {   // 清理过期投篮人
        // 确保只有一个清理线程运行
        if (!isCleaning.compareAndSet(false, true)) {
            Log.d("Cleanup", "清理操作已在进行中")
            return
        }

        try {
            val currentTime = System.currentTimeMillis()
            var retryCount = 0
            var success = false

            // 带重试机制的清理
            while (retryCount < MAX_CLEANUP_RETRIES && !success) {
                try {
                    val iterator = attemptPlayers.iterator()
                    var removedCount = 0

                    while (iterator.hasNext()) {
                        val data = iterator.next()
                        if (currentTime - data.timestamp > EXPIRY_DURATION) {
                            iterator.remove()
                            removedCount++
                        }
                    }

                    Log.i("Cleanup", "清理完成: 删除 $removedCount 个过期数据项")
                    success = true
                } catch (e: ConcurrentModificationException) {
                    retryCount++
                    Log.w("Cleanup", "并发修改异常，重试 $retryCount/$MAX_CLEANUP_RETRIES")
                    Thread.sleep(50) // 短暂等待后重试
                }
            }

            if (!success) {
                Log.e("Cleanup", "清理失败: 达到最大重试次数")
            }
        } catch (e: Exception) {
            Log.e("Cleanup", "清理过程中出错", e)
        } finally {

            isCleaning.set(false)
        }
    }

    /// 处理投篮人（对应Swift的processAttemptPlayers）
    private fun processAttemptPlayers(
        players: List<Pair<RectF, Float>>,
        basketballs: List<RectF>,
        timestamp: Long
    ) {
        // 计算每个球员是否靠近篮球
        val playersWithBallProximity = mutableListOf<Triple<RectF, Float, Boolean>>()
        // 获取当前帧
        val currentFrame = try {
            frameCacheLock.lock()
            frameCache.lastOrNull()
        } finally {
            frameCacheLock.unlock()
        } ?: return
        // 遍历球员
        for ((rect, confidence) in players) {
            var isNearBall = false
            for (basketball in basketballs) {
                // 计算距离
                val playerTopMidX = rect.left + rect.width() / 2
                val playerTopY = rect.top
                val basketballMidX = basketball.left + basketball.width() / 2
                val basketballMidY = basketball.top + basketball.height() / 2

                val horizontalDistance = Math.abs(playerTopMidX - basketballMidX)
                val verticalDistance = Math.abs(playerTopY - basketballMidY)

                // 判断是否靠近篮球
                if (horizontalDistance <= basketball.width() * playerNearBallFactor &&
                    verticalDistance <= basketball.height() * playerNearBallFactor &&
                    verticalDistance > 0
                ) {
                    isNearBall = true
                    break
                }
            }
            playersWithBallProximity.add(Triple(rect, confidence, isNearBall))
        }
        // 选择合适的球员
        val nearBallPlayers = playersWithBallProximity.filter { it.third }
        val selectedPlayer = if (nearBallPlayers.isNotEmpty()) {
            nearBallPlayers.maxByOrNull { it.second }
        } else {
            playersWithBallProximity.maxByOrNull { it.second }
        }
        Log.e(TAG, "processAttemptPlayers4 ${selectedPlayer == null}+${players}")
        // 添加投篮人信息
        selectedPlayer?.let { (rect, confidence, isNearBall) ->
            if(!currentFrame.isRecycled){

            }
            // 裁剪球员图像
            val croppedImage = try {
                if(!currentFrame.isRecycled){
                    Bitmap.createBitmap(
                        currentFrame,
                        max(0, rect.left.toInt()),
                        max(0, rect.top.toInt()),
                        min(currentFrame.width, rect.width().toInt()),
                        min(currentFrame.height, rect.height().toInt())
                    )
                }else null
            } catch (e: Exception) {
                null
            }
            // Log.e(TAG, "processAttemptPlayers5"+players+"\n"+  max(0, rect.left.toInt())+" "+ max(0, rect.top.toInt())+" "+ min(currentFrame.width, rect.width().toInt())+" "+min(currentFrame.height, rect.height().toInt()))
//            Handler(Looper.getMainLooper()).post {
//                delegate?.didCaptureHoopImage(croppedImage!!)
//            }
            attemptPlayers.add(
                AttemptPlayerInfo(
                    timestamp = timestamp,
                    image = croppedImage,
                    confidence = confidence,
                    rect = rect,
                    isNearBall = isNearBall
                )
            )
        }
    }

    /// 处理篮筐和篮板检测（对应Swift的processHoopAndBackboardDetection）
    private fun processHoopAndBackboardDetection(
        hoopRects: List<RectF>,
        backboardRects: List<RectF>,
        imageSize: SizeF
    ) {
//        Log.e("ObjectDetection44444444", "hoopRects=" + hoopRects)
//        Log.e("ObjectDetection44444444", "backboardRects=" + backboardRects)
//        Log.e("ObjectDetection44444444", "imageSize" + imageSize)
        // 显示篮筐区域（在主线程）
        Handler(Looper.getMainLooper()).post {
            val displayRect = RectF(
                validHoopRegion.left * imageSize.width,
                validHoopRegion.top * imageSize.height,
                validHoopRegion.right * imageSize.width,
                validHoopRegion.bottom * imageSize.height
            )
          //  Log.e("ObjectDetection44444444", "displayRect=" + displayRect)
            delegate?.shouldDisplayHoopRegion(displayRect, imageSize, 5)
        }
        // 筛选合法篮筐
        val filteredHoops = hoopRects.filter { hoopRect ->
            // 转换坐标到归一化
            val normalizedRect = RectF(
                hoopRect.left / imageSize.width,
                hoopRect.top / imageSize.height,
                hoopRect.right / imageSize.width,
                hoopRect.bottom / imageSize.height,
            )

            // 检查中心点是否在有效区域
            val hoopCenter = PointF(
                normalizedRect.left + normalizedRect.width() / 2,
                normalizedRect.top + normalizedRect.height() / 2
            )
//            Log.e("ObjectDetection44444444", "normalizedRect=" + normalizedRect)
//            Log.e("ObjectDetection44444444", "hoopCenter=" + hoopCenter)
//            Log.e("ObjectDetection44444444", "validHoopRegion=" + validHoopRegion)
            // val validHoopRegion = RectF(0.40f, 0.13f, 0.60f, 0.35f)
            //RectF(float left, float top, float right, float bottom)
            return@filter validHoopRegion.contains(hoopCenter.x, hoopCenter.y)
        }
        // RectF(0.40f, 0.13f, 0.60f, 0.35f)
        //(0.6976563, 0.27037036)
    //    Log.e("ObjectDetection44444444", "5" + filteredHoops.isEmpty())
        // 没有合法篮筐
        if (filteredHoops.isEmpty()) {
            Handler(Looper.getMainLooper()).post {
                delegate?.didDetectHoopAndBackboard(RectF(), RectF(), false)
            }
            return
        }
    //    Log.e("ObjectDetection11140", "6")
        // 存储合法组合
        val validCombinations = mutableListOf<Triple<RectF, RectF, Float>>()

        // 寻找最优组合
        for (hoopRect in filteredHoops) {
            for (backboardRect in backboardRects) {
                // 检查是否相交
                if (RectF.intersects(hoopRect, backboardRect)) {
                    val totalArea = hoopRect.width() * hoopRect.height() +
                            backboardRect.width() * backboardRect.height()
                    validCombinations.add(Triple(hoopRect, backboardRect, totalArea.toFloat()))
                }
            }
        }

        // 选择最大组合
        val bestCombination = validCombinations.maxByOrNull { it.third }
        if (bestCombination != null) {
            val (hoopRect, backboardRect, _) = bestCombination
            lastValidHoopRect = hoopRect
            lastValidBackboardRect = backboardRect
            hasValidHoopAndBackboard = true

            Handler(Looper.getMainLooper()).post {
                delegate?.didDetectHoopAndBackboard(hoopRect, backboardRect, true)
            }
        } else {
            Handler(Looper.getMainLooper()).post {
                delegate?.didDetectHoopAndBackboard(RectF(), RectF(), false)
            }
        }
      //  Log.e("ObjectDetection11140", "7")
    }

    /// 监控篮筐状态（对应Swift的monitorHoopAndBackboardState）
    private fun monitorHoopAndBackboardState(
        hoopRects: List<RectF>,
        backboardRects: List<RectF>,
        imageSize: SizeF
    ) {
        // 筛选合法篮筐（与processHoopAndBackboardDetection相同）
        val filteredHoops = hoopRects.filter { hoopRect ->
            val normalizedRect = RectF(
                (hoopRect.left / imageSize.width).toFloat(),
                (hoopRect.top / imageSize.height).toFloat(),
                (hoopRect.width() + hoopRect.left) / imageSize.width,
                ((hoopRect.top + hoopRect.height()) / imageSize.height).toFloat()
            )
            val hoopCenter = PointF(
                normalizedRect.left + normalizedRect.width() / 2,
                normalizedRect.top + normalizedRect.height() / 2
            )
            // Log.e("ObjectDetection11142", "85"+hoopCenter+" contains:"+validHoopRegion.contains(hoopCenter.x, hoopCenter.y)+"\n"+hoopCenter.x+"------"+hoopCenter.y)
            // val validHoopRegion = RectF(0.40f, 0.13f, 0.60f, 0.35f)
            return@filter validHoopRegion.contains(hoopCenter.x, hoopCenter.y)
        }
    //    Log.e("ObjectDetection1111140", "91" + filteredHoops)
        // 存储合法组合
        val validCombinations = mutableListOf<Triple<RectF, RectF, Float>>()
        for (hoopRect in filteredHoops) {
            for (backboardRect in backboardRects) {
                if (RectF.intersects(hoopRect, backboardRect)) {
                    val totalArea = hoopRect.width() * hoopRect.height() +
                            backboardRect.width() * backboardRect.height()
                    validCombinations.add(Triple(hoopRect, backboardRect, totalArea.toFloat()))
                }
            }
        }
        // 如果找不到符合条件的篮筐篮板组合
        if (validCombinations.isEmpty()) {
            // 增加连续无效帧计数
            consecutiveInvalidFrames++

            // 如果连续30帧未检测到有效组合，重置状态
            if (consecutiveInvalidFrames >= 30) {
                Log.e(
                    "ObjectDetection1111140",
                    "连续30帧未检测到有效篮筐和篮板，疑似镜头遮挡或位移，重置投篮分类状态"
                )
                // 显示篮筐合法区域，提示用户
                val displayRect = RectF(
                    validHoopRegion.left * imageSize.width,
                    validHoopRegion.top * imageSize.height,
                    validHoopRegion.right * imageSize.width,
                    validHoopRegion.bottom * imageSize.height
                )
                delegate?.shouldDisplayHoopRegion(displayRect, imageSize, 6)
                // 重置状态
                consecutiveInvalidFrames = 0
            }
            return
        }
//        Log.e(
//            "ObjectDetection1111140",
//            "130-consecutiveInvalidFrames=${validCombinations} \n${backboardRects}"
//        )
        // 选择最大组合
        val bestCombination = validCombinations.maxByOrNull { it.third }!!
        val lastHoopRect = lastValidHoopRect
        //Log.e("ObjectDetection11140", "12")
        // 检查当前位置与上次位置的重叠情况
//        Log.e(
//            "ObjectDetection1111140",
//            "130-consecutiveInvalidFrames=${lastHoopRect != null}-${bestCombination != null}"
//        )
        if (lastHoopRect != null && bestCombination != null) {
            val (currentHoopRect, currentBackboardRect, _) = bestCombination
            val iou = calculateIOU(lastHoopRect, currentHoopRect)
          //  Log.e("ObjectDetection1111140", "13-minIouThreshold=$minIouThreshold--iou=$iou")
            if (iou < minIouThreshold) {
//
                consecutiveInvalidFrames++
//                Log.e(
//                    "ObjectDetection1111141",
//                    "132-consecutiveInvalidFrames=$consecutiveInvalidFrames"
//                )
                if (consecutiveInvalidFrames >= 10) {
                    LogService.warning("连续10帧检测到篮筐位置变化 (IOU: ${iou}))，更新篮筐位置并重置投篮分类状态")

                  //  Log.e("ObjectDetection1111141", "133-lastValidHoopRect=$lastValidHoopRect")
                    val displayRect = RectF(
                        validHoopRegion.left * imageSize.width,
                        validHoopRegion.top * imageSize.height,
                        validHoopRegion.right * imageSize.width,
                        validHoopRegion.bottom * imageSize.height
                    )
                    Handler(Looper.getMainLooper()).post {
                        delegate?.shouldDisplayHoopRegion(displayRect, imageSize, 7)
                    }
                    // 位置变化超过阈值 更新最后一次有效的篮筐和篮板位置
                    lastValidHoopRect = currentHoopRect
                    lastValidBackboardRect = currentBackboardRect
                    //Log.e("ObjectDetection1111141", "134-lastValidHoopRect=$lastValidHoopRect")
                    consecutiveInvalidFrames = 0
                }
            } else {
                Handler(Looper.getMainLooper()).post {
                    delegate?.shouldDisplayHoopRegion(RectF(), imageSize, 8)
                }
                consecutiveInvalidFrames = 0
            }
        } else if (bestCombination != null) {
          //  Log.e("ObjectDetection1111141", "135-bestCombination=$bestCombination")
            // 如果是第一次检测到有效组合
            val (hoopRect, backboardRect, _) = bestCombination
            lastValidHoopRect = hoopRect
            lastValidBackboardRect = backboardRect
           // Log.e("ObjectDetection1111141", "136-lastValidHoopRect=$lastValidHoopRect")
            Handler(Looper.getMainLooper()).post {
                delegate?.shouldDisplayHoopRegion(RectF(), imageSize, 9)
            }
           // Log.e("ObjectDetection1111141", "137-lastValidBackboardRect=$lastValidBackboardRect")
            consecutiveInvalidFrames = 0
        }
    }

    /**
     * 扩展函数：获取矩形的中心点
     */
    private fun RectF.center(): PointF {
        return PointF(
            (left + right) / 2f,
            (top + bottom) / 2f
        )
    }

    /// 计算IOU（对应Swift的calculateIOU）
    private fun calculateIOU(rect1: RectF, rect2: RectF): Float {
        val intersection = RectF()
        if (!intersection.setIntersect(rect1, rect2)) return 0f

        val intersectionArea = intersection.width() * intersection.height()
        val rect1Area = rect1.width() * rect1.height()
        val rect2Area = rect2.width() * rect2.height()
        val unionArea = rect1Area + rect2Area - intersectionArea

        return intersectionArea.toFloat() / unionArea
    }

    /// 清理所有数据（对应Swift的cleanupAllData）
    private fun cleanupAllData() {
        // 清理投篮人
        for (player in attemptPlayers) {
            player.image?.recycle()
        }
        attemptPlayers.clear()

        // 清理帧缓存
        try {
            frameCacheLock.lock()
            for (bitmap in frameCache) {
                bitmap.recycle()
            }
            frameCache.clear()
        } finally {
            frameCacheLock.unlock()
        }
    }

    /// 处理投篮分类事件（对应Swift的processShootClassifierEvent）
    private fun processShootClassifierEvent(event: ShootEvent): ShootEvent {
        // 查找相关投篮人
        val lookupStartTime = event.startTimestamp - playerLookbackTime
        val lookupEndTime = event.startTimestamp
        val relevantPlayers = attemptPlayers.filter {
            it.timestamp in lookupStartTime..lookupEndTime
        }

        // 优先选择靠近球的球员
        val nearBallPlayers = relevantPlayers.filter { it.isNearBall }
        val selectedPlayer = if (nearBallPlayers.isNotEmpty()) {
            nearBallPlayers.maxByOrNull { it.confidence }
        } else {
            relevantPlayers.maxByOrNull { it.confidence }
        }
      //  Log.e(TAG, "processShootClassifierEvent0: " + relevantPlayers + "\n" + nearBallPlayers)
        // 创建最终事件
        val finalEvent = ShootEvent(
            startFrame = event.startFrame,
            shotType = event.shotType,
            confidence = event.confidence,
            duration = event.duration,
            goalConfirmationFrame = event.goalConfirmationFrame,
            startTimestamp = event.startTimestamp,
            goalTimestamp = event.goalTimestamp,
            playerImage = selectedPlayer?.image,
            playerConfidence = selectedPlayer?.confidence ?: 0f,
            shootCoord = null
        )
        // 通知代理
        Handler(Looper.getMainLooper()).post {
            delegate?.didDetectShootEvent(finalEvent)
        }
        return finalEvent
    }

    /// 启动调试定时器（对应Swift的startDebugTimer）
    private fun startDebugTimer() {
        stopDebugTimer()

        debugTimer = scheduler.scheduleWithFixedDelay({
            // 打印调试信息
            println("Debug info - Attempt players: ${attemptPlayers.size}")
            println("Hoop state: ${lastValidHoopRect != null}, ${lastValidBackboardRect != null}")
            println("Detection status: $detectionStatus")
        }, 0, 1, TimeUnit.SECONDS)
    }

    /// 停止调试定时器（对应Swift的debugTimer.invalidate）
    private fun stopDebugTimer() {
        debugTimer?.cancel(false)
        debugTimer = null
    }

    /// 重置投篮分类服务（对应Swift的clearAndInitializeImageClassifierService）
    private fun clearAndInitializeImageClassifierService() {
        // 在后台线程初始化服务，避免阻塞UI
        // 首先释放现有资源（如果有）
        shootClassifierService?.let {
            shootClassifierService = null
        }
        // 初始化投篮分类器
        shootClassifierService = ShootClassifierService.createLiveStreamService(
            context = appContext,
            model = ShootClassifierModel.CLSF_GOAL,
            scoreThreshold = 0.5f,
            maxResults = 5,
            delegate = Delegate.CPU
        )
        shootClassifierService?.liveStreamDelegate = this
        shootClassifierService!!.startDetection()

    }
}

