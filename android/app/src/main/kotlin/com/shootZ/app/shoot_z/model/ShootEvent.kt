package com.shootZ.app.shoot_z.model

import android.graphics.Bitmap
import android.graphics.Point

/// 投篮事件信息（对应Swift的ShootEvent）
data class ShootEvent(
    val startFrame: Int, // 开始帧索引
    val shotType: String, // "goal" 或 "miss"
    val confidence: Float, // 最高置信度
    val duration: Int, // 持续帧数
    val goalConfirmationFrame: Int?, // 进球确认帧
    val startTimestamp: Long, // 开始时间戳
    val goalTimestamp: Long?, // 进球时间戳
    val playerImage: Bitmap?, // 投篮人图像
    val playerConfidence: Float, // 投篮人置信度
    val shootCoord: Point? // 投篮坐标
)