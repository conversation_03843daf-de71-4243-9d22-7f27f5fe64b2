package com.shootZ.app.shoot_z

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.util.Log
import com.shootZ.app.shoot_z.config.ShootzPlugin

class MainApplication : Application() {

    companion object {
        private const val TAG = "MainApplication"
        @Volatile private var instance: MainApplication? = null

        fun getInstance(): MainApplication {
            return instance ?: throw IllegalStateException("Application not initialized")
        }
    }

    lateinit var plugin: ShootzPlugin

    override fun onCreate() {
        super.onCreate()
        Log.e(TAG, "onCreate() called")
        instance = this

        // 添加全局异常捕获
        Thread.setDefaultUncaughtExceptionHandler { thread: Thread?, throwable: Throwable? ->
            // 将错误信息写入文件或发送到服务器
            Log.e("CRASHsetDefaultUncaughtExceptionHandler", "Uncaught exception", throwable)
        }
        // 只在主进程初始化插件
        if (isMainProcess()) {
            try {
                plugin = ShootzPlugin()
                Log.e(TAG, "Plugin initialized in main process")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to initialize plugin", e)
            }
        } else {
            Log.e(TAG, "Skipping plugin initialization in background process")
        }
    }

    private fun isMainProcess(): Boolean {
        return packageName == getProcessName2()
    }

    private fun getProcessName2(): String? {
        return runCatching {
            val pid = android.os.Process.myPid()
            val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            manager.runningAppProcesses?.firstOrNull { it.pid == pid }?.processName
        }.getOrNull()
    }
}