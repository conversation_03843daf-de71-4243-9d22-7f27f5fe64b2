<?xml version="1.0" encoding="utf-8"?>
<!--
    ~ Copyright 2022 The TensorFlow Authors. All Rights Reserved.
    ~
    ~ Licensed under the Apache License, Version 2.0 (the "License");
    ~ you may not use this file except in compliance with the License.
    ~ You may obtain a copy of the License at
    ~
    ~       http://www.apache.org/licenses/LICENSE-2.0
    ~
    ~ Unless required by applicable law or agreed to in writing, software
    ~ distributed under the License is distributed on an "AS IS" BASIS,
    ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    ~ See the License for the specific language governing permissions and
    ~ limitations under the License.
-->
<resources>

    <dimen name="stroke_small">4dp</dimen>
    <dimen name="round_button_medium">64dp</dimen>

    <!-- Bottom Sheet -->
    <dimen name="bottom_sheet_text_size">20sp</dimen>
    <dimen name="dialog_button_height">48dp</dimen>
    <dimen name="bottom_sheet_padding">16dp</dimen>
    <dimen name="bottom_sheet_peek_height">50dp</dimen>
    <dimen name="bottom_sheet_default_row_margin">16dp</dimen>
    <dimen name="bottom_sheet_control_btn_size">48dp</dimen>
    <dimen name="bottom_sheet_control_text_side_margin">10dp</dimen>
    <dimen name="bottom_sheet_spinner_delegate_min_width">160dp</dimen>
    <dimen name="bottom_sheet_spinner_model_min_width">240dp</dimen>
    <integer name="bottom_sheet_control_text_min_ems">3</integer>

    <dimen name="fab_margin">16dp</dimen>
</resources>