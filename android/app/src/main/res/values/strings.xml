<?xml version="1.0" encoding="utf-8"?><!--
    ~ Copyright 2022 The TensorFlow Authors. All Rights Reserved.
    ~
    ~ Licensed under the Apache License, Version 2.0 (the "License");
    ~ you may not use this file except in compliance with the License.
    ~ You may obtain a copy of the License at
    ~
    ~       http://www.apache.org/licenses/LICENSE-2.0
    ~
    ~ Unless required by applicable law or agreed to in writing, software
    ~ distributed under the License is distributed on an "AS IS" BASIS,
    ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    ~ See the License for the specific language governing permissions and
    ~ limitations under the License.
-->
<resources>
    <string name="alt_bottom_sheet_chevron">Bottom sheet expandable indicator</string>

    <string name="alt_bottom_sheet_max_results_button_minus">Decreasing maxium detected results
        button</string>
    <string name="alt_bottom_sheet_max_results_button_plus">Increasing maxium detected results
        button</string>
    <string name="alt_bottom_sheet_threshold_button_minus">Decreasing threshold of detected object
        results button</string>
    <string name="alt_bottom_sheet_threshold_button_plus">Increasing threshold of detected object
        results button</string>

    <string name="label_inference_time">Inference Time</string>
    <string name="label_fps">Frames per Second</string>
    <string name="label_confidence_threshold">Threshold</string>
    <string name="label_max_results">Max Results</string>
    <string name="label_delegate">Delegate</string>
    <string name="label_models">ML Model</string>
    <string name="menu_camera">Camera</string>
    <string name="menu_gallery">Gallery</string>
    <string name="tv_gallery_placeholder">Click + to add an image or a video
        to begin running the object detection.</string>

    <string-array name="delegate_spinner_titles">
        <item>CPU</item>
        <item>GPU</item>
    </string-array>

    <string-array name="models_spinner_titles">
        <item>EfficientDet Lite0</item>
        <item>EfficientDet Lite2</item>
    </string-array>
    <string name="app_name">篮球投篮检测</string>
    <string name="start">开始</string>
    <string name="pause">暂停</string>
    <string name="resume">继续</string>
    <string name="end">结束</string>
    <string name="show_goals">查看进球</string>
    <string name="records">比赛记录</string>
    <string name="hide_detection">隐藏检测框</string>
    <string name="show_detection">显示检测框</string>
    <string name="video_settings">视频设置</string>
    <string name="camera_unavailable">相机不可用</string>
    <string name="inference_time">推理时间: %dms</string>
    <string name="shot_count" formatted="false">%d/%d</string>
    <string name="shot_count_detail" formatted="false">%d/%d （命中/投篮）</string>
    <string name="permission_denied">权限被拒绝</string>
    <string name="camera_permission_required">需要相机和存储权限</string>
</resources>
