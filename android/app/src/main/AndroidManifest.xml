<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.RECEIVE_BOOT_COMPLETED"
        tools:node="remove" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" /> <!-- <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> -->
    <!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" /> -->
    <!-- •	WRITE_EXTERNAL_STORAGE：适用于 API 28（Android 9）及以下版本。 -->
    <!-- •	READ_EXTERNAL_STORAGE：用于访问外部存储的文件（如相册）内容。 -->
    <!-- •	MANAGE_EXTERNAL_STORAGE：从 API 30 开始需要此权限来访问公共存储中的所有文件。 -->
    <!-- ADD THESE PERMISSIONS -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 读取权限 -->
    <!-- 根本就没有用到MANAGE_EXTERNAL_STORAGE，app下载更新只需低版本下申请WRITE_EXTERNAL_STORAGE，getExternalStorageDirectory() 返回应用程序专用的外部存储目录（例如 /storage/emulated/0/Android/data/<package_name>/files/） -->
    <!--
 <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    -->
    <uses-feature android:name="android.hardware.camera" /> <!-- Declare permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 访问媒体文件权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
<!--    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />-->
<!--    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />-->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- 针对 Android 13+ 的媒体权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!-- 添加 Android 13 新权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!--    <uses-feature-->
    <!--        android:name="android.hardware.camera"-->
    <!--        android:required="true" />-->
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="true" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- 定位权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <!--
 Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
    -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />

            <data android:mimeType="text/plain" />
        </intent>
    </queries>

    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="球秀"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:vmSafeMode="false">
        <activity
            android:name=".CarmerActivity"
            android:clearTaskOnLaunch="true"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:resizeableActivity="true"
            android:rotationAnimation="seamless"
            android:theme="@style/AppTheme"
            android:screenOrientation="landscape"
            tools:targetApi="O"/>
        <!-- 使用旧的存储模型，在 Android 11（API 级别 30）及更高版本上，requestLegacyExternalStorage 不再适用 -->
        <!-- <meta-data -->
        <!-- android:name="android.permission.MANAGE_EXTERNAL_STORAGE" -->
        <!-- android:value="用于保存视频文件到用户设备存储" /> -->
        <!-- 增加虚拟机内存设置 -->
        <meta-data
            android:name="io.flutter.embedding.android.VmSettings"
            android:value="heapSizeMB=512" />
        <meta-data
            android:name="io.flutter.embedding.android.JavaVMInitialHeapMB"
            android:value="256" />
        <meta-data
            android:name="io.flutter.embedding.android.JavaVMMaxHeapMB"
            android:value="512" />

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">

            <!--
                 Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <!-- 1) Scheme 唤醒 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <!-- 仅按 scheme 匹配，避免 host 不一致 -->
                <data android:scheme="shootz"/>
            </intent-filter>

<!--            &lt;!&ndash; 2) Android App Link（含 autoVerify） &ndash;&gt;-->
<!--            <intent-filter android:autoVerify="true">-->
<!--                <action android:name="android.intent.action.VIEW"/>-->
<!--                <category android:name="android.intent.category.DEFAULT"/>-->
<!--                <category android:name="android.intent.category.BROWSABLE"/>-->

<!--                &lt;!&ndash; 只限制 host，放宽 path，整站都可唤醒 &ndash;&gt;-->
<!--                <data android:scheme="https"-->
<!--                    android:host="i.shootz.tech"/>-->
<!--            </intent-filter>-->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />

                <action
                    android:name="com.android_package_installer.content.SESSION_API_PACKAGE_INSTALLED"
                    android:exported="false" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
        <!--
 Don't ¬ the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!--
 Don't ¬ the meta-data below.
     This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <provider
            android:name="sk.fourq.otaupdate.OtaUpdateFileProvider"
            android:authorities="${applicationId}.ota_update_provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider> <!-- ADD THIS PROVIDER -->
        <!-- <provider -->
        <!-- android:name="androidx.core.content.FileProvider" -->
        <!-- android:authorities="${applicationId}" -->
        <!-- android:grantUriPermissions="true"> -->
        <!-- <meta-data -->
        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
        <!-- android:resource="@xml/filepaths" /> -->
        <!-- </provider> -->
        <!-- 删除 RescheduleReceiver -->
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            tools:node="remove" />
    </application>

</manifest>