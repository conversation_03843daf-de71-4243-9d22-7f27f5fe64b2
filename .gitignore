# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/lib/generated

/android/key.properties
/tmpimages

# Flutter/Dart 相关
**/linux/flutter/ephemeral/**
**/.plugin_symlinks/
**/Flutter/ephemeral/

# Android 相关
**/android/local.properties
**/example/android/local.properties

# 通用 Flutter 忽略规则
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
build/
